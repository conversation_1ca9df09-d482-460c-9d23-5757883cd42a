export default {
  head: {
    titleTemplate: '%s',
    title: 'Twimo | Private Vacation Home Sharing, Swapping & Renting',
    htmlAttrs: {
      lang: 'en',
    },
    meta: [
      { charset: 'utf-8' },
      { name: 'viewport', content: 'width=device-width, initial-scale=1' },
      {
        hid: 'description',
        name: 'description',
        content:
          'Twimo is the private way to rent, swap, or share your second home — Enjoy flexible, secure bookings with zero service fees.',
      },
      { name: 'format-detection', content: 'telephone=no' },
      {
        hid: 'keywords',
        name: 'keywords',
        content:
          'home exchange, free home swap sites, best house exchange sites, best international house swap website, vacation spots home exchange, swapping vacation home exchange, remote worker travel, retiree home exchange, travel swap, homes for exchange, home swap, homeswapper home swap, homeswapper, house exchange, houses for exchange, swap homes, swap home, swap houses, swap house, homeswap, home for exchange, home for exchanges, flat exchange, flats for exchange, flat swap, flatswapper, flat for exchange, swap flat, swap flats, free home exchange, free flat exchange, free apartment exchange, apartment exchange, apartment for exchange, swap apartment',
      },
      { hid: 'theme-color', name: 'theme-color', content: '#672093' },
      { hid: 'og:type', property: 'og:type', content: 'website' },
      { hid: 'og:site_name', property: 'og:site_name', content: 'Twimo' },
      { hid: 'og:image', property: 'og:image', content: '/logo.png' },
      {
        hid: 'og:description',
        property: 'og:description',
        content:
          'Twimo is the private way to rent, swap, or share your second home — Enjoy flexible, secure bookings with zero service fees.',
      },
      { hid: 'og:locale', property: 'og:locale', content: 'en_US' },
      { property: 'og:image:width', content: '740' },
      { property: 'og:image:height', content: '300' },
      { name: 'twitter:site', content: '@twimo' },
      { hid: 'twitter:url', name: 'twitter:url', content: 'https://twimo.com' },
      {
        hid: 'twitter:title',
        name: 'twitter:title',
        content: 'Twimo | Private Vacation Home Sharing, Swapping & Renting',
      },
      {
        hid: 'twitter:description',
        name: 'twitter:description',
        content:
          'Twimo is the private way to rent, swap, or share your second home — Enjoy flexible, secure bookings with zero service fees.',
      },
      {
        hid: 'twitter:image',
        name: 'twitter:image',
        content: '/logo.png',
      },
    ],
    link: [
      { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' },
      { rel: 'dns-prefetch', href: 'https://fonts.bunny.net' },
      { rel: 'dns-prefetch', href: 'https://www.googletagmanager.com' },
      { rel: 'dns-prefetch', href: 'https://connect.facebook.net' },
      { rel: 'preconnect', href: 'https://fonts.bunny.net', crossorigin: true },
      {
        rel: 'stylesheet',
        href: 'https://fonts.bunny.net/css?family=figtree:300,400,500,600,800&display=swap',
        preload: true,
      },
      {
        rel: 'stylesheet',
        href: 'https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;1,100;1,200;1,300;1,400;1,500;1,600;1,700&display=swap',
        preload: true,
      },
    ],
    script: [
      {
        src: 'https://cdn.pagesense.io/js/twimo/d89c6389b82c4c3c963106f622646841.js',
        defer: true,
      },
      {
        src: 'https://www.googletagmanager.com/gtag/js?id=G-5S98G2KYNV',
        async: true,
        defer: true,
      },
      {
        innerHTML: `
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'G-5S98G2KYNV');
      `,
        type: 'text/javascript',
        ssr: false,
      },
      {
        innerHTML: `(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','GTM-M5DLWW7C');`,
        type: 'text/javascript',
        ssr: false,
      },
      {
        innerHTML: `!function(f,b,e,v,n,t,s)
      {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
      n.callMethod.apply(n,arguments):n.queue.push(arguments)};
      if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
      n.queue=[];t=b.createElement(e);t.async=!0;
      t.src=v;s=b.getElementsByTagName(e)[0];
      s.parentNode.insertBefore(t,s)}(window, document,'script',
      'https://connect.facebook.net/en_US/fbevents.js');
      fbq('init', '904189901544415');
      fbq('track', 'PageView');`,
        type: 'text/javascript',
        ssr: false,
      },
    ],
    noscript: [
      {
        innerHTML:
          '<img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=904189901544415&ev=PageView&noscript=1" alt=""/>',
        ssr: false,
      },
    ],
    __dangerouslyDisableSanitizers: ['script', 'noscript'],
  },

  telemetry: false,

  devtools: true,
  debug: true,
  dev: true,

  vue: {
    config: {
      devtools: process.env.NODE_ENV === 'development',
      performance: process.env.NODE_ENV === 'development',
    },
  },

  server: {
    timing: process.env.NODE_ENV !== 'production',
  },

  loading: {
    color: '#672093',
    height: '3px',
    continuous: true,
  },

  css: ['~/assets/main.css'],

  components: true,

  plugins: [
    '~/plugins/axios',
    { src: '~/plugins/auth-persist', mode: 'client' },
    { src: '~/plugins/email-verification', mode: 'client' },
    { src: '~/plugins/gg-map.js', mode: 'client' },
    // Removed user-activity plugin to prevent automatic logouts
    { src: '~/plugins/hiver-chat.client.js', mode: 'client' },
    { src: '~/plugins/error-handler.js' },
    { src: '~/plugins/sentry-debug.js' },
  ],

  tailwindcss: {
    cssPath: ['~/assets/main.css', { injectPosition: 'last' }],
    configPath: '~/tailwind.config.js',
    exposeConfig: {
      level: 2,
    },
    viewer: process.env.NODE_ENV === 'development',
  },

  buildModules: [
    [
      '@nuxtjs/vuetify',
      {
        treeShake: true,
        defaultAssets: {
          font: {
            family: 'Figtree',
          },
        },
        theme: {
          options: { customProperties: true },
          themes: {
            light: {
              primary: '#672093',
              secondary: '#424242',
              accent: '#82B1FF',
              error: '#FF5252',
              info: '#2196F3',
              success: '#4CAF50',
              warning: '#FFC107',
            },
            dark: {
              primary: '#9c27b0',
              secondary: '#424242',
              accent: '#82B1FF',
              error: '#FF5252',
              info: '#2196F3',
              success: '#4CAF50',
              warning: '#FFC107',
            },
          },
        },
      },
    ],
    'nuxt-compress',
    '@nuxt/typescript-build',
    '@nuxtjs/composition-api/module',
    ['@pinia/nuxt', { disableVuex: false }],
    '@nuxtjs/tailwindcss',
    ...(process.env.NODE_ENV === 'development' ? ['@nuxtjs/eslint-module'] : []),
  ],

  eslint: {
    failOnError: false,
    failOnWarning: false,
  },

  modules: [
    '@nuxtjs/axios',
    'cookie-universal-nuxt',
    '@nuxtjs/toast',
    '@nuxtjs/recaptcha',
    [
      'nuxt-stripe-module',
      {
        publishableKey: process.env.STRIPE_PUBLISHABLE_KEY || '',
      },
    ],
    '@nuxtjs/sentry',
    '@nuxtjs/sitemap',
    '@nuxtjs/robots',
    'nuxt-leaflet',
  ],

  router: {
    prefetchLinks: true,
    trailingSlash: false,
  },

  target: 'server',

  typescript: {
    typeCheck: false,
  },

  axios: {
    baseURL: process.env.VUE_APP_API_BASE_URL,
    credentials: true,
    retry: { retries: 3 },
    progress: true,
  },

  publicRuntimeConfig: {
    stripePriceMonthly: process.env.STRIPE_PRICE_MONTHLY,
    stripePriceYearly: process.env.STRIPE_PRICE_YEARLY,
    appEnvironment: process.env.NODE_ENV,
    recaptchaSiteKey: process.env.RECAPTCHA_SITE_KEY,
  },

  recaptcha: {
    hideBadge: true,
    siteKey: process.env.RECAPTCHA_SITE_KEY || '',
    version: 3,
    size: 'invisible',
    autoHideBadge: true,
    mode: 'base',
    language: 'en',
    useRecaptchaNet: true,
  },

  toast: {
    position: 'bottom-right',
    duration: 4000,
    keepOnHover: true,
    theme: 'toasted-primary',
    iconPack: 'mdi',
    className: 'my-custom-toast',
    containerClass: 'toast-container',
    singleton: false,
    fitToScreen: true,
    fullWidth: false,
    offsetBottom: '80px',
    type: 'default',
    action: {
      text: 'Dismiss',
      onClick: (_, toastObject) => {
        toastObject.goAway(0)
      },
    },
    register: [
      {
        name: 'success',
        message: 'Success',
        options: {
          type: 'success',
          icon: 'check-circle',
          className: 'my-success-toast',
          duration: 4000,
        },
      },
      {
        name: 'error',
        message: 'Error',
        options: {
          type: 'error',
          icon: 'alert-circle',
          className: 'my-error-toast',
          duration: 5000,
        },
      },
      {
        name: 'info',
        message: 'Information',
        options: {
          type: 'info',
          icon: 'information',
          className: 'my-info-toast',
          duration: 4000,
        },
      },
      {
        name: 'warning',
        message: 'Warning',
        options: {
          type: 'warning',
          icon: 'alert',
          className: 'my-warning-toast',
          duration: 5000,
        },
      },
    ],
  },

  vuetify: {
    treeShake: true,
    defaultAssets: false,
  },

  sentry: {
    dsn: process.env.SENTRY_DSN,
    lazy: true,
    sourceMapStyle: 'hidden-source-map',
    config: {
      environment: process.env.NODE_ENV,
      release: process.env.npm_package_version || '1.0.0',
      attachStacktrace: true,
      debug: process.env.NODE_ENV === 'development',
      normalizeDepth: 10,
      maxBreadcrumbs: 50,
      beforeSend(event, hint) {
        // Add custom logic to filter or modify events before sending to Sentry
        const error = hint && hint.originalException

        // Skip certain errors if needed
        if (error && (error.message === 'Network Error' || error.message?.includes('fetch'))) {
          // Optionally reduce priority for network errors
          event.level = 'warning'
        }

        // Add context from your store if available
        if (typeof window !== 'undefined' && window.$nuxt && window.$nuxt.$store) {
          const state = window.$nuxt.$store.state

          // Add user information if authenticated
          if (state.auth && state.auth.user) {
            event.user = {
              id: state.auth.user.id,
              email: state.auth.user.email,
              username: state.auth.user.name,
            }
          }

          // Add route information
          if (window.$nuxt.$route) {
            event.tags = {
              ...event.tags,
              route: window.$nuxt.$route.fullPath,
              routeName: window.$nuxt.$route.name,
            }
          }
        }

        return event
      },
    },
    // Remove integrations that reference BrowserTracing
    clientConfig: {
      beforeBreadcrumb(breadcrumb) {
        // Filter out noise from breadcrumbs (like health checks)
        if (
          breadcrumb.category === 'xhr' &&
          (breadcrumb.data?.url?.includes('/health') || breadcrumb.data?.url?.includes('/ping'))
        ) {
          return null
        }
        return breadcrumb
      },
    },
    // Remove tracing section completely

    // Configure server-side reporting
    serverConfig: {
      beforeSend(event) {
        // Filter server-side errors
        return event
      },
    },
    // Source map upload for accurate stack traces in production
    sourceMaps: {
      include: ['./dist/client/'],
      ignore: ['node_modules'],
      urlPrefix: '~/_nuxt/',
    },
  },

  sitemap: {
    hostname: 'https://twimo.com',
    gzip: true,
    exclude: ['/admin/**', '/user/**'],
    defaults: {
      changefreq: 'weekly',
      priority: 0.8,
      lastmod: new Date(),
    },
  },

  robots: {
    UserAgent: '*',
    Disallow: ['/admin', '/api', '/user'],
    Sitemap: 'https://twimo.com/sitemap.xml',
  },

  redirect: [{ from: '^/home', to: '/' }],

  compress: {
    gzip: {
      threshold: 8192,
      level: 9,
    },
    brotli: {
      threshold: 8192,
      level: 11,
    },
  },

  modern: process.env.NODE_ENV === 'production' ? 'server' : false,

  build: {
    postcss: {
      preset: {
        autoprefixer: {
          grid: true,
        },
      },
    },
    extractCSS: process.env.NODE_ENV === 'production',
    optimizeCSS: {
      cssProcessorOptions: {
        safe: true,
        discardComments: { removeAll: true },
      },
    },
    cssSourceMap: process.env.NODE_ENV !== 'production',
    parallel: true,
    // hardSource: process.env.NODE_ENV === 'development',
    cache: process.env.NODE_ENV === 'development',
    splitChunks: {
      layouts: true,
      pages: true,
      commons: true,
    },
    transpile: [/^vuetify/],
    babel: {
      compact: true,
      presets({ isServer }) {
        return [
          [
            '@nuxt/babel-preset-app',
            {
              corejs: { version: 3 },
              useBuiltIns: 'usage',
              targets: isServer ? { node: 'current' } : { browsers: ['last 2 versions'] },
            },
          ],
        ]
      },
    },
    optimization: {
      runtimeChunk: true,
      minimize: process.env.NODE_ENV === 'production',
      splitChunks: {
        chunks: 'all',
        automaticNameDelimiter: '.',
        name: true,
        cacheGroups: {
          styles: {
            name: 'styles',
            test: /\.(css|vue)$/,
            chunks: 'all',
            enforce: true,
          },
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            chunks: 'all',
          },
        },
      },
    },
    html: {
      minify:
        process.env.NODE_ENV === 'production'
          ? {
              collapseBooleanAttributes: true,
              decodeEntities: true,
              minifyCSS: true,
              minifyJS: true,
              processConditionalComments: true,
              removeEmptyAttributes: true,
              removeRedundantAttributes: true,
              trimCustomFragments: true,
              useShortDoctype: true,
              minifyURLs: true,
              removeComments: true,
              removeEmptyElements: true,
              preserveLineBreaks: false,
              collapseWhitespace: true,
            }
          : false,
    },
    terser: {
      terserOptions: {
        compress: {
          drop_console: process.env.NODE_ENV === 'production',
          drop_debugger: process.env.NODE_ENV === 'production',
        },
      },
    },
    extend(config, ctx) {
      if (ctx.isDev) {
        config.devtool = 'eval-cheap-module-source-map'
      }

      config.module.rules.push({
        test: /\.(png|jpe?g|gif|svg)$/,
        loader: 'image-webpack-loader',
        enforce: 'pre',
        options: {
          disable: ctx.isDev,
        },
      })

      if (!ctx.isDev && ctx.isClient) {
        const terserPlugin = config.optimization.minimizer.find(
          plugin => plugin.constructor.name === 'TerserWebpackPlugin'
        )
        if (terserPlugin) {
          terserPlugin.options.terserOptions.compress.drop_console = true
        }
      }
    },
  },
}
