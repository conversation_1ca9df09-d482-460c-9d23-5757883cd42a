export default {
  data: () => ({
    defaultTitle: 'Share and Care for your vacation home',
  }),
  methods: {
    create(name) {
      return document.createElement(name)
    },
    addPageTitle(title) {
      document.title = title || this.defaultTitle
    },
    addPageDescription(content) {
      const description = this.create('meta')
      description.name = 'description'
      description.content = content
      document.getElementsByTagName('head')[0].appendChild(description)
    },
    addMetaOG(property, content) {
      const OGTag = this.create('meta')
      OGTag.property = property
      OGTag.content = content
      document.getElementsByTagName('head')[0].appendChild(OGTag)
    },
    createMetaTags(metaData) {
      this.addPageTitle(metaData.pageTitle)
      this.addPageDescription(metaData.pageDescription)
      this.addMetaOG('og:title', this.defaultTitle)
      this.addMetaOG('og:url', metaData.ogUrl)
      this.addMetaOG('og:description', metaData.ogTitleContent)
      this.addMetaOG('og:image', metaData.ogImage)
    },
  },
}
