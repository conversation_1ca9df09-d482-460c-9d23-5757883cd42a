export default {
  computed: {
    frontendUrl() {
      return window?.location.hostname || window?.location.origin || 'https://twimo.com'
    },

    env() {
      const hostName = this.frontendUrl

      if (hostName === 'localhost') {
        return 'dev'
      }

      if (hostName === 'qa.the48dots.com') {
        return 'qa'
      }

      if (hostName === 'twimo.com') {
        return 'prod'
      }

      return 'prod'
    },

    getBackendUrl() {
      const env = this.env

      if (env === 'dev') {
        return 'https://api.the48dots.com'
      }

      if (env === 'qa') {
        return 'https://api-qa.the48dots.com'
      }

      if (env === 'prod') {
        return 'https://api.the48dots.com'
      }
    },

    getApiUrl() {
      const backendUrl = this.getBackendUrl

      return `${backendUrl}/api`
    },
  },
}
