import { getDateRange } from '~/helpers'

export default {
  data: () => ({
    toastGoAway: 3000,
    breadcrumbItems: [],
  }),
  computed: {
    getHomeLocation() {
      function isValid(str) {
        return !(str === 'null' || str === null || str === ' ')
      }

      return data => {
        const city = isValid(data.city_long) ? `${data.city_long + ','}` : ''

        const state = isValid(data.state_long) ? `${data.state_long + ','}` : ''

        const country = isValid(data.country_long) ? `${data.country_long}` : ''

        return `${city} ${state} ${country}`
      }
    },

    getHomeState() {
      return data => {
        return data.state_long
      }
    },
  },
  methods: {
    shareHome(str, message = 'Link Has Been Copied!') {
      navigator.clipboard.writeText(str).then(
        () => {
          this.$toast.success(message).goAway(3000)
        },
        err => {
          this.$toast.error(err).goAway(3000)
        }
      )
    },
    setBreadcrumb(houseInfo) {
      this.breadcrumbItems.push({
        text: 'All homes',
        disabled: false,
        href: '/home?page=1',
      })

      if (houseInfo.country) {
        this.breadcrumbItems.push({
          text: houseInfo.country,
          disabled: false,
          href: `/home?country=${houseInfo.country}`,
        })
      }

      if (houseInfo.state) {
        this.breadcrumbItems.push({
          text: houseInfo.state,
          disabled: false,
          href: `/home?state=${houseInfo.state}`,
        })
      }

      this.breadcrumbItems.push({
        text: houseInfo.title,
        disabled: true,
      })
    },
    getDateDifference(start, end) {
      const dateDiff = getDateRange(start, end).length
      /* If it is just one day then make it 2 because it's subtracted by 1 during ui render */
      if (dateDiff === 1) {
        return 2
      }
      return dateDiff
    },
    async pushToNotFound() {
      return await this.$router.push('/404')
    },
    async toastError(e) {
      await this.$toast.error(e.response.data.message).goAway(this.toastGoAway)
    },
  },
}
