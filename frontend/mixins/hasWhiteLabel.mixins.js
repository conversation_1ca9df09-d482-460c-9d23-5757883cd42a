import { mapActions, mapGetters } from 'vuex'

export default {
  computed: {
    ...mapGetters({
      user: 'auth/getuser',
    }),
  },
  methods: {
    ...mapActions('logo', ['fetchLogo', 'setIsPoweredBy']),
    async loadLogoData(userSharedUrl = null) {
      await this.fetchLogo(userSharedUrl || this.houseInfo?.user?.shared_url)
    },
  },
  beforeRouteLeave(_0, _1, next) {
    this.setIsPoweredBy(false)
    next()
  },
}
