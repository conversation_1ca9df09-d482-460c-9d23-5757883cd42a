import { defineStore } from 'pinia'
import { ref } from '@nuxtjs/composition-api'
import type { NuxtAxiosInstance } from '@nuxtjs/axios'
import { useApi } from '~/composables/useCommon'
import type { HomeCustomNightlyRate } from '~/types'

export const useAdvancedPricesStore = defineStore('advancedPrices', () => {
  const isLoaded = ref<boolean>(false)
  const advancedPrices = ref<HomeCustomNightlyRate[]>([])

  const getAdvancedPrices = async (api: NuxtAxiosInstance, homeIdProp?: null | number) => {
    const { data } = await api.get('home-custom-nightly-rates?home_id=' + (homeIdProp || ''))

    advancedPrices.value = data

    isLoaded.value = true
  }

  const updateAdvancedPrice = async (
    api: NuxtAxiosInstance,
    id: number,
    data: {
      nightly_rate: number
      is_active: boolean
    }
  ) => {
    await api.patch('home-custom-nightly-rates/' + id, data)
  }

  const deleteAdvancedPrice = async (api: NuxtAxiosInstance, id: number) => {
    await api.delete('home-custom-nightly-rates/' + id)

    // Remove the deleted item from the local store
    advancedPrices.value = advancedPrices.value.filter(item => item.id !== id)
  }

  return {
    advancedPrices,
    getAdvancedPrices,
    updateAdvancedPrice,
    deleteAdvancedPrice,
    isLoaded,
  }
})

export const useHostHomesStore = defineStore('hostHomes', () => {
  const hostHomes = ref<any>([])

  const getHostHomes = async (query: string, api?: NuxtAxiosInstance, forSwap: boolean = false) => {
    const newApi = api || useApi()

    // Add for-swap parameter to filter out draft homes for swap selection
    const queryString = query ? `?${query}` : ''
    const separator = queryString ? '&' : '?'
    const swapParam = forSwap ? `${separator}for-swap=true` : ''

    const { data } = await newApi.$get('/users/me/homes' + queryString + swapParam)
    hostHomes.value = data
  }

  return { hostHomes, getHostHomes }
})
