import { defineStore } from 'pinia'
import { computed, reactive, ref, watch } from '@nuxtjs/composition-api'
import type { NuxtAxiosInstance } from '@nuxtjs/axios'
import { useDebounceFn } from '@vueuse/core'

import { TOAST_DURATION, MANDATORY_SAFETY_AMENITIES } from '~/constants'
import { useToast } from '~/composables/useCommon'
import { humanize } from '~/helpers'
import { useSubscriptionStore } from '~/composables/useSubscriptionStore'
import { useHostActivationStore } from '~/composables/useHostActivationStore'

export const useEditHomeStore = defineStore('editHome', () => {
  const toast = useToast()

  const isLoading = ref<boolean>(false)

  const isStatusLoading = ref<boolean>(false)

  const fetchedHomeData = ref(null)

  const fetchHomeData = async (api: NuxtAxiosInstance, slug: string) => {
    isLoading.value = true

    try {
      const { data } = await api.get(`user/homes/${slug}`)

      fetchedHomeData.value = data

      editHomeData.id = data.id
      editHomeData.title = data.title
      editHomeData.status = data.status
      editHomeData.slug = data.slug
      editHomeData.typeOfSpace = data.extra_info?.typeOfSpaces || []
      editHomeData.isBooking = !!data.allow_booking
      editHomeData.isSwap = !!data.allow_swaps
      editHomeData.address = data.address
      editHomeData.bedrooms = data.beds
      editHomeData.bathrooms = data.baths
      editHomeData.beds = data.number_of_beds
      editHomeData.guests = data.guests
      editHomeData.pets = data.extra_info?.pets || { enabled: 'no' }
      editHomeData.amenities = data.amenities
      editHomeData.photos = data.photos.map((photo: any) => ({
        media_id:
          typeof photo.media_id === 'string' ? parseInt(photo.media_id, 10) : photo.media_id,
        url: photo.url || photo.src || '',
        name: photo.name || photo.filename || '',
      }))
      editHomeData.description = data.description
      editHomeData.nightlyRate = data.nightly_rate
      editHomeData.cleaningFee = data.cleaning_fee
      editHomeData.taxRate = data.tax_rate
      editHomeData.minimumStay = data.minimum_stay_rentals
      editHomeData.petFee = data.pet_fee
      editHomeData.offerAsSeasonalLease = data.offer_as_seasonal_lease

      if (
        data.extra_info?.additionalInfos?.items &&
        Array.isArray(data.extra_info.additionalInfos.items)
      ) {
        if (
          data.extra_info.additionalInfos.items.length > 0 &&
          typeof data.extra_info.additionalInfos.items[0] === 'string'
        ) {
          editHomeData.additionalInfos.items = data.extra_info.additionalInfos.items.map(
            (item: string) => ({
              title: item,
              description: '',
            })
          )
        }
      }

      editHomeData.adjustedPrice = {
        nightlyRate: editHomeData.nightlyRate,
        cleaningFee: editHomeData.cleaningFee,
        taxRate: editHomeData.taxRate,
        minimumStay: editHomeData.minimumStay,
        petFee: editHomeData.petFee,
      }

      editHomeData.cancellationPolicies = {
        friendsAndFamily: false,
        publicRental: false,
        flexiblePublicRental: false,
        longTermStay: false,
        nonRefundable: false,
        ...(data.extra_info?.cancellationPolicies || {}),
      }

      editHomeData.checkInOut = {
        checkinTime: data.extra_info?.checkInOut?.checkinTime ?? '',
        checkoutTime: data.extra_info?.checkInOut?.checkoutTime ?? '',
      }

      editHomeData.houseRules = data.extra_info?.houseRules || {
        noEvents: false,
        noSmoking: false,
        quietHours: false,
        quietHoursTime: {
          startTime: '',
          endTime: '',
        },
        additionalRules: '',
      }

      if (!editHomeData.houseRules.quietHoursTime) {
        editHomeData.houseRules.quietHoursTime = {
          startTime: '',
          endTime: '',
        }
      }

      editHomeData.sharablePassword = data.sharable_password

      // Set Airbnb-related properties
      editHomeData.is_from_airbnb = !!data.is_from_airbnb
      editHomeData.airbnb_url = data.airbnb_url || null
      // Track backend photo processing status
      editHomeData.photo_processing_status = data.photo_processing_status || null

      // Log Airbnb-related properties for debugging
      console.log('[EditHomeStore] Home is from Airbnb:', editHomeData.is_from_airbnb)
      console.log('[EditHomeStore] Airbnb URL:', editHomeData.airbnb_url)

      // Set the fetchedHomeData ref to include Airbnb info
      fetchedHomeData.value = {
        ...fetchedHomeData.value,
        is_from_airbnb: !!data.is_from_airbnb,
        airbnb_url: data.airbnb_url || null
      }

      isLoading.value = false
    } catch (error) {
      console.error(error)

      toast.error('An error occurred while fetching the home data').goAway(TOAST_DURATION)

      isLoading.value = false
    }
  }

  const editHomeData = reactive({
    id: null,
    title: null,
    typeOfSpace: [],
    isBooking: false,
    isSwap: false,
    isRedirect: false,
    redirectUrl: null,
    status: 'draft',
    slug: null,
    sharablePassword: null,
    isPrivate: false,
    address: null,
    bedrooms: 1,
    bathrooms: 1,
    beds: 1,
    guests: 1,
    pets: {
      enabled: 'no',
    },
    amenities: [],
    photos: [],
    description: null,
    nightlyRate: null,
    cleaningFee: null,
    taxRate: 0,
    minimumStay: 0,
    petFee: 0,
    offerAsSeasonalLease: false,
    offerAsSkiLease: false,
    is_from_airbnb: false,
    airbnb_url: null,
    photo_processing_status: null,
    additionalInfos: {
      items: [],
      other: null,
    },
    adjustedPrice: {
      nightlyRate: null,
      cleaningFee: null,
      taxRate: 0,
      minimumStay: 0,
      petFee: 0,
    },
    cancellationPolicies: {
      friendsAndFamily: false,
      publicRental: false,
      flexiblePublicRental: false,
      longTermStay: false,
      nonRefundable: false,
    },
    checkInOut: {
      checkinTime: '',
      checkoutTime: '',
    },
    houseRules: {
      noEvents: false,
      noSmoking: false,
      quietHours: false,
      quietHoursTime: {
        startTime: '',
        endTime: '',
      },
      additionalRules: '',
    },
  })

  const displayedPets = computed(() => {
    const pets = editHomeData.pets || { enabled: 'no' }

    switch (pets.enabled) {
      case 'yes':
        return 'Pets allowed'
      case 'service animal only':
        return 'Service animals only'
      case 'no':
      default:
        return 'No pets allowed'
    }
  })

  const privateHomeAdjustedPriceDialog = ref<boolean>(false)

  const userWantToAdjustPrice = ref<boolean>(false)

  const privateHomeAdjustedPriceDialogData = reactive({
    nightlyRate: null,
    cleaningFee: null,
    taxRate: 0,
    minimumStay: 0,
    petFee: 0,
  })

  const clickOnPrivateHomeAdjustedPrice = () => {
    privateHomeAdjustedPriceDialogData.nightlyRate = editHomeData.adjustedPrice.nightlyRate
    privateHomeAdjustedPriceDialogData.cleaningFee = editHomeData.adjustedPrice.cleaningFee
    privateHomeAdjustedPriceDialogData.taxRate = editHomeData.adjustedPrice.taxRate
    privateHomeAdjustedPriceDialogData.minimumStay = editHomeData.adjustedPrice.minimumStay
    privateHomeAdjustedPriceDialogData.petFee = editHomeData.adjustedPrice.petFee

    userWantToAdjustPrice.value = true
  }

  const sharableLink = ref(null)

  const saveAdjustedPrice = async (api: NuxtAxiosInstance, withToast = true) => {
    try {
      const { data } = await api.post(`user/homes/${editHomeData.id}/sharable-links`, {
        price_info: {
          nightly_rate: privateHomeAdjustedPriceDialogData.nightlyRate,
          cleaning_fee: privateHomeAdjustedPriceDialogData.cleaningFee,
          tax_rate: privateHomeAdjustedPriceDialogData.taxRate,
          minimum_stay: privateHomeAdjustedPriceDialogData.minimumStay,
          pet_fee: privateHomeAdjustedPriceDialogData.petFee,
        },
      })

      sharableLink.value = data

      if (!withToast) return

      toast.success('Sharable link created successfully').goAway(TOAST_DURATION)
    } catch (error) {
      console.error(error)

      toast.error('An error occurred while creating the sharable link').goAway(TOAST_DURATION)
    }
  }

  const autogenerateSharableLink = async (api: NuxtAxiosInstance) => {
    if (sharableLink.value) return

    await saveAdjustedPrice(api, false)
  }

  const subscriptionStore = useSubscriptionStore()

  const sharePrivateHome = async (api: NuxtAxiosInstance) => {
    const paywallOpen = await subscriptionStore.checkSubscriptionStatusAndOpenPaywall(api)

    if (paywallOpen) {
      return
    }

    // Set the dialog to open
    privateHomeAdjustedPriceDialog.value = true

    // Initialize the form data with current values
    privateHomeAdjustedPriceDialogData.nightlyRate = editHomeData.adjustedPrice.nightlyRate
    privateHomeAdjustedPriceDialogData.cleaningFee = editHomeData.adjustedPrice.cleaningFee
    privateHomeAdjustedPriceDialogData.taxRate = editHomeData.adjustedPrice.taxRate
    privateHomeAdjustedPriceDialogData.minimumStay = editHomeData.adjustedPrice.minimumStay
    privateHomeAdjustedPriceDialogData.petFee = editHomeData.adjustedPrice.petFee
  }

  const copyPrivateHomeLink = async (api: NuxtAxiosInstance) => {
    await autogenerateSharableLink(api)

    const link = `${window.location.origin}/${editHomeData.slug}?sharable_link=${sharableLink.value?.link}`

    navigator.clipboard.writeText(link).then(
      () => {
        toast.success('Link Has Been Copied!').goAway(3000)
      },
      err => {
        toast.$toast.error(err).goAway(3000)
      }
    )
  }

  const privateHomeCreatePasswordDialog = ref<boolean>(false)
  const passwordChangedDialog = ref<boolean>(false)
  const password = ref(null)
  const confirmPassword = ref(null)

  // Add a watch to clean up password fields when dialog is closed
  watch(privateHomeCreatePasswordDialog, newValue => {
    if (!newValue) {
      // Only reset the fields when dialog is closed
      password.value = null
      confirmPassword.value = null
    }
  })

  // Add a watch to clean up when password changed dialog is closed
  watch(passwordChangedDialog, newValue => {
    if (!newValue) {
      // Only perform cleanup when dialog is closed
      console.log('Password changed dialog closed, cleaning up state')
    }
  })

  // Helper function to safely toggle dialogs with delayed secondary actions
  const safeToggleDialog = (primaryDialog, secondaryDialog?, delay = 300) => {
    // Close the primary dialog
    primaryDialog.value = false

    // If there's a secondary dialog to open, do it after a delay
    if (secondaryDialog) {
      // Clear any existing timeout to prevent stacking
      const timeoutId = setTimeout(() => {
        secondaryDialog.value = true
      }, delay)

      // Store the timeout ID for potential cleanup
      return timeoutId
    }
  }

  const createPassword = async (api: NuxtAxiosInstance) => {
    if (password.value !== confirmPassword.value) {
      toast.error('Passwords do not match').goAway(3000)
      return
    }

    try {
      await api.patch(`user/homes/${editHomeData.slug}`, {
        sharable_password: password.value,
      })

      editHomeData.sharablePassword = password.value

      // First ensure primary dialog is fully closed
      privateHomeCreatePasswordDialog.value = false

      // Then use setTimeout directly for better control
      setTimeout(() => {
        // Only open the next dialog if the first one is confirmed closed
        if (!privateHomeCreatePasswordDialog.value) {
          passwordChangedDialog.value = true
        }
      }, 300)

      toast.success('Password created successfully').goAway(3000)
    } catch (error) {
      toast
        .error(error?.response?.data?.message || 'Failed to create password')
        .goAway(TOAST_DURATION)

      console.error('Failed to create password:', error)
    }
  }

  const resetPasswordDialog = ref(false)

  // Add a watch to clean up password fields when reset dialog is closed
  watch(resetPasswordDialog, newValue => {
    if (!newValue) {
      // Only reset the fields when dialog is closed
      password.value = null
      confirmPassword.value = null
    }
  })

  const resetPassword = async (api: NuxtAxiosInstance) => {
    if (password.value !== confirmPassword.value) {
      toast.error('Passwords do not match').goAway(3000)
      return
    }

    try {
      await api.patch(`user/homes/${editHomeData.slug}`, {
        sharable_password: password.value,
      })

      editHomeData.sharablePassword = password.value

      // Use the safe toggle function for dialog transitions
      safeToggleDialog(resetPasswordDialog)

      toast.success('Password reset successfully').goAway(3000)
    } catch (error: any) {
      toast
        .error(error?.response?.data?.message || 'Failed to reset password')
        .goAway(TOAST_DURATION)
    }
  }

  watch(privateHomeAdjustedPriceDialog, newValue => {
    if (!newValue) {
      sharableLink.value = null
    }

    privateHomeAdjustedPriceDialogData.nightlyRate = editHomeData.adjustedPrice.nightlyRate
    privateHomeAdjustedPriceDialogData.cleaningFee = editHomeData.adjustedPrice.cleaningFee
    privateHomeAdjustedPriceDialogData.taxRate = editHomeData.adjustedPrice.taxRate
    privateHomeAdjustedPriceDialogData.minimumStay = editHomeData.adjustedPrice.minimumStay
    privateHomeAdjustedPriceDialogData.petFee = editHomeData.adjustedPrice.petFee

    userWantToAdjustPrice.value = false
  })

  const shareHome = async (api: NuxtAxiosInstance) => {
    const paywallOpen = await subscriptionStore.checkSubscriptionStatusAndOpenPaywall(api)

    if (paywallOpen) {
      return
    }

    const link = `${window.location.origin}/${editHomeData.slug}`

    navigator.clipboard.writeText(link).then(
      () => {
        toast.success('Link Has Been Copied!').goAway(3000)
      },
      err => {
        toast.$toast.error(err).goAway(3000)
      }
    )
  }

  const emailDropdown = ref(false)
  const emailPreviewDialog = ref(false)
  const emailSentDialog = ref(false)
  const email = ref(null)
  const sendEmail = async (api: NuxtAxiosInstance) => {
    if (!email.value) {
      toast.error('Please enter an email').goAway(3000)
      return
    }

    await autogenerateSharableLink(api)

    try {
      await api.post(`user/homes/${editHomeData.slug}/private-home-link-email`, {
        email: email.value,
        sharable_link_id: sharableLink.value?.id,
      })

      email.value = null
      emailDropdown.value = false
      emailPreviewDialog.value = false
      emailSentDialog.value = true
      privateHomeAdjustedPriceDialog.value = false

      toast.success('Email sent successfully').goAway(3000)
    } catch (error) {
      toast.error('An error occurred while sending the email').goAway(TOAST_DURATION)

      console.error('Failed to send email:', error)
    }
  }

  // Dialogs

  // Title
  const editTitleDialog = ref<boolean>(false)
  const editTitleDialogData = ref(null)
  const startEditTitle = () => {
    editTitleDialogData.value = editHomeData.title
    editTitleDialog.value = true
  }
  const closeEditTitleDialog = () => {
    editTitleDialogData.value = null
    editTitleDialog.value = false
  }
  const updateTitle = async (api: NuxtAxiosInstance) => {
    // Validate title is not empty
    if (!editTitleDialogData.value || editTitleDialogData.value.trim() === '') {
      toast.error('Title is required').goAway(TOAST_DURATION)
      return
    }

    try {
      await api.patch(`user/homes/${editHomeData.slug}`, {
        title: editTitleDialogData.value.trim(),
      })

      editHomeData.title = editTitleDialogData.value.trim()
      editTitleDialogData.value = null
      editTitleDialog.value = false

      toast.success('Title updated successfully').goAway(TOAST_DURATION)
    } catch (error) {
      toast.error('An error occurred while updating the title').goAway(TOAST_DURATION)
      console.error(error)
    }
  }

  // Beds, Bathrooms, Guests, Pets
  const editAmountDialog = ref<boolean>(false)
  const editAmountDialogData = reactive({
    bedrooms: 1,
    bathrooms: 1,
    beds: 1,
    guests: 1,
    pets: {
      enabled: 'no',
    },
  })
  const startEditAmount = () => {
    editAmountDialogData.bedrooms = editHomeData.bedrooms
    editAmountDialogData.bathrooms = editHomeData.bathrooms
    editAmountDialogData.beds = editHomeData.beds
    editAmountDialogData.guests = editHomeData.guests
    editAmountDialogData.pets = editHomeData.pets
    editAmountDialog.value = true
  }
  const closeEditAmountDialog = () => {
    editAmountDialogData.bedrooms = 1
    editAmountDialogData.bathrooms = 1
    editAmountDialogData.beds = 1
    editAmountDialogData.guests = 1
    editAmountDialogData.pets = { enabled: 'no' }
    editAmountDialog.value = false
  }
  const updateAmount = async (api: NuxtAxiosInstance) => {
    try {
      await api.patch(`user/homes/${editHomeData.slug}`, {
        beds: editAmountDialogData.bedrooms,
        baths: editAmountDialogData.bathrooms,
        number_of_beds: editAmountDialogData.beds,
        guests: editAmountDialogData.guests,
        extra_info: {
          pets: editAmountDialogData.pets,
        },
      })

      editHomeData.bedrooms = editAmountDialogData.bedrooms
      editHomeData.bathrooms = editAmountDialogData.bathrooms
      editHomeData.beds = editAmountDialogData.beds
      editHomeData.guests = editAmountDialogData.guests
      editHomeData.pets = editAmountDialogData.pets

      editAmountDialogData.bedrooms = 1
      editAmountDialogData.bathrooms = 1
      editAmountDialogData.beds = 1
      editAmountDialogData.guests = 1
      editAmountDialogData.pets = { enabled: 'no' }
      editAmountDialog.value = false
    } catch (error) {
      toast.error('An error occurred while updating the amount').goAway(TOAST_DURATION)
      console.error(error)
    }
  }
  // Type of Space
  const editTypeOfSpaceDialog = ref<boolean>(false)
  const editTypeOfSpaceDialogData = ref([])
  const startEditTypeOfSpace = () => {
    editTypeOfSpaceDialogData.value = [...editHomeData.typeOfSpace]
    editTypeOfSpaceDialog.value = true
  }
  const closeEditTypeOfSpaceDialog = () => {
    editTypeOfSpaceDialogData.value = []
    editTypeOfSpaceDialog.value = false
  }
  const clickOnTypeOfSpace = spaceType => {
    if (isSpaceTypeSelected(spaceType)) {
      editTypeOfSpaceDialogData.value = []
    } else {
      editTypeOfSpaceDialogData.value = [spaceType]
    }
  }

  const isSpaceTypeSelected = spaceType => {
    return editTypeOfSpaceDialogData.value.some(type => type.title === spaceType.title)
  }

  const updateTypeOfSpace = async (api: NuxtAxiosInstance) => {
    try {
      await api.patch(`user/homes/${editHomeData.slug}`, {
        extra_info: {
          typeOfSpaces: editTypeOfSpaceDialogData.value,
        },
      })

      editHomeData.typeOfSpace = editTypeOfSpaceDialogData.value
      editTypeOfSpaceDialogData.value = []
      editTypeOfSpaceDialog.value = false
    } catch (error) {
      toast.error('An error occurred while updating the type of space').goAway(TOAST_DURATION)
      console.error(error)
    }
  }

  // Address
  const editAddressDialog = ref<boolean>(false)
  const editAddressDialogData = ref(null)
  const startEditAddress = () => {
    editAddressDialogData.value = editHomeData.address
    editAddressDialog.value = true
  }
  const closeEditAddressDialog = () => {
    editAddressDialogData.value = null
    editAddressDialog.value = false
  }
  const updateAddress = async (api: NuxtAxiosInstance) => {
    try {
      await api.patch(`user/homes/${editHomeData.slug}`, {
        address: editAddressDialogData.value,
      })

      editHomeData.address = editAddressDialogData.value
      editAddressDialogData.value = null
      editAddressDialog.value = false
    } catch (error) {
      toast.error('An error occurred while updating the address').goAway(TOAST_DURATION)
      console.error(error)
    }
  }

  // Amenities
  const editAmenitiesDialog = ref<boolean>(false)
  const editAmenitiesDialogData = ref([])
  const startEditAmenities = () => {
    editAmenitiesDialogData.value = [...editHomeData.amenities]
    editAmenitiesDialog.value = true
  }
  const closeEditAmenitiesDialog = () => {
    editAmenitiesDialogData.value = []
    editAmenitiesDialog.value = false
  }
  const clickOnAmenity = (amenity: string) => {
    // Don't allow removing mandatory safety amenities
    if (MANDATORY_SAFETY_AMENITIES.includes(amenity)) {
      return
    }

    if (editAmenitiesDialogData.value.includes(amenity)) {
      editAmenitiesDialogData.value = editAmenitiesDialogData.value.filter(type => type !== amenity)
    } else {
      editAmenitiesDialogData.value.push(amenity)
    }
  }
  const isAmenitySelected = (amenity: string) => {
    return editAmenitiesDialogData.value.includes(amenity)
  }
  const updateAmenities = async (api: NuxtAxiosInstance) => {
    try {
      await api.patch(`user/homes/${editHomeData.slug}`, {
        amenities: editAmenitiesDialogData.value,
      })

      editHomeData.amenities = editAmenitiesDialogData.value
      editAmenitiesDialogData.value = []
      editAmenitiesDialog.value = false
    } catch (error) {
      toast.error('An error occurred while updating the amenities').goAway(TOAST_DURATION)
      console.error(error)
    }
  }

  // Description
  const editDescriptionDialog = ref<boolean>(false)
  const editDescriptionDialogData = ref(null)
  const startEditDescription = () => {
    editDescriptionDialogData.value = editHomeData.description
    editDescriptionDialog.value = true
  }
  const closeEditDescriptionDialog = () => {
    editDescriptionDialogData.value = null
    editDescriptionDialog.value = false
  }
  const updateDescription = async (api: NuxtAxiosInstance) => {
    try {
      await api.patch(`user/homes/${editHomeData.slug}`, {
        description: editDescriptionDialogData.value,
      })

      editHomeData.description = editDescriptionDialogData.value
      editDescriptionDialogData.value = null
      editDescriptionDialog.value = false
    } catch (error) {
      toast.error('An error occurred while updating the description').goAway(TOAST_DURATION)
      console.error(error)
    }
  }

  const photosRan = ref(true)

  const skipWatch = ref(false)

  // Photos
  const uploadProgress = ref({})
  const isUploading = ref(false)
  const maxPhotos = ref(20)

  const photosChanged = ref(false)

  const updatePhotos = async (api: NuxtAxiosInstance, showToast = true) => {
    console.log('[updatePhotos] Called with', {
      photosChanged: photosChanged.value,
      photosCount: editHomeData.photos.length,
    })

    // Skip if no changes or no photos and we're not forcing an update
    if (!photosChanged.value && editHomeData.photos.length === 0) {
      console.log('[updatePhotos] No changes to save, skipping')
      return
    }

    try {
      isLoading.value = true
      console.log('[updatePhotos] Sending PATCH request')

      // Extract media IDs from the photos array
      const photoIds = editHomeData.photos.map(photo => photo.media_id)

      // Only send the photos field to avoid duplicate updates
      await api.patch(`user/homes/${editHomeData.slug}`, {
        photos: photoIds,
      })

      if (showToast) {
        toast.success('Photos updated successfully').goAway(3000)
      }
      photosChanged.value = false

      // Return true to indicate success
      return true
    } catch (error) {
      console.error('Error updating photos:', error)
      if (showToast) {
        toast.error('Failed to update photos').goAway(3000)
      }
      return false
    } finally {
      isLoading.value = false
    }
  }

  const uploadFiles = async (api: NuxtAxiosInstance, fileList) => {
    if (!fileList || fileList.length === 0) return

    // Set uploading state
    isUploading.value = true

    const uploadPromises = []
    const errors = []

    // Show a loading toast
    const loadingToast = toast.info(`Uploading ${fileList.length} image(s)...`)

    // Create a copy of the current photos array
    const updatedPhotos = [...editHomeData.photos]

    // Check if adding these files would exceed the limit
    if (updatedPhotos.length + fileList.length > maxPhotos.value) {
      loadingToast.goAway(0)
      toast.error(`Cannot upload more than ${maxPhotos.value} images total`).goAway(3000)
      isUploading.value = false
      return
    }

    // Process each file
    for (let i = 0; i < fileList.length; i++) {
      const file = fileList[i]

      // Validate file before uploading
      if (!validatePhotoFile(file)) {
        errors.push(`File "${file.name}" is not a valid image or exceeds size limit`)
        continue
      }

      const formData = new FormData()
      formData.append('file', file)

      // Create a promise for each upload
      const uploadPromise = api
        .post('upload', formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        })
        .then(response => {
          const uploadedFile = response.data
          updatedPhotos.push(uploadedFile)
          return uploadedFile
        })
        .catch(error => {
          console.error('Upload error:', error)
          errors.push(`Failed to upload "${file.name}": ${error.message || 'Unknown error'}`)
          return null
        })

      uploadPromises.push(uploadPromise)
    }

    try {
      // Wait for all uploads to complete
      await Promise.all(uploadPromises)

      // Clear loading toast
      loadingToast.goAway(0)

      // Update the photos array with successful uploads
      editHomeData.photos = updatedPhotos

      // Show success message if no errors
      if (errors.length === 0 && fileList.length > 0) {
        toast.success(`Successfully uploaded ${fileList.length} image(s)`).goAway(3000)
      }
      // Show errors if any
      else if (errors.length > 0) {
        const errorMessage =
          errors.length === 1
            ? errors[0]
            : `${errors.length} files failed to upload. Please check file types and sizes.`
        toast.error(errorMessage).goAway(3000)
      }

      // Mark photos as changed
      photosChanged.value = true

      // Update the backend with a single call if there are photos
      if (editHomeData.photos.length > 0) {
        await updatePhotos(api, false) // Don't show another toast
      }
    } catch (error) {
      console.error('Upload failure:', error)
      loadingToast.goAway(0)
      toast.error('Upload failed. Please try again.').goAway(3000)
    } finally {
      isUploading.value = false
    }
  }

  const validatePhotoFile = (file: File): boolean => {
    // Check file type
    const validTypes = ['image/jpeg', 'image/png', 'image/jpg']
    if (!validTypes.includes(file.type)) {
      return false
    }

    // Check file size (10MB max)
    const maxSize = 10 * 1024 * 1024 // 10MB in bytes
    if (file.size > maxSize) {
      return false
    }

    return true
  }

  const deletePhoto = (_: any, index: number) => {
    // Allow deleting any photo, including the last one
    editHomeData.photos = editHomeData.photos.filter((_, i) => i !== index)

    // Mark that photos have changed and need to be saved
    photosChanged.value = true
  }

  const setAsCoverPhoto = async (index: number, api: NuxtAxiosInstance) => {
    if (index === 0) return // Already the cover photo

    const photosCopy = [...editHomeData.photos]
    const coverPhoto = photosCopy[0]
    photosCopy[0] = editHomeData.photos[index]
    photosCopy[index] = coverPhoto

    editHomeData.photos = photosCopy
    // Mark that photos have changed and need to be saved
    photosChanged.value = true

    // Update the backend
    await updatePhotos(api)
  }

  const photosString = computed(() => {
    return JSON.stringify(editHomeData.photos)
  })

  // Settings
  const updateSettingField = async (api: NuxtAxiosInstance, field: string, value: any) => {
    try {
      // Update the field in the backend
      await api.patch(`user/homes/${editHomeData.slug}`, {
        [field]: value,
      })

      // If the field is related to booking types, check image requirements
      if (
        field === 'allow_booking' ||
        field === 'allow_swaps' ||
        field === 'offer_as_seasonal_lease'
      ) {
        await updateBookingStatus(api)
      }
    } catch (error) {
      toast.error(`An error occurred while updating ${humanize(field)}`).goAway(TOAST_DURATION)
    }
  }

  const showTwimoPolicies = ref(false)
  const menuCheckIn = ref(false)
  const menuCheckOut = ref(false)
  const menuQuietHoursStart = ref(false)
  const menuQuietHoursEnd = ref(false)

  const formattedCheckIn = computed(() => formatTime(editHomeData.checkInOut.checkinTime))

  const formattedCheckOut = computed(() => formatTime(editHomeData.checkInOut.checkoutTime))

  const formattedQuietHoursStart = computed(() =>
    formatTime(editHomeData.houseRules.quietHoursTime?.startTime || '')
  )

  const formattedQuietHoursEnd = computed(() =>
    formatTime(editHomeData.houseRules.quietHoursTime?.endTime || '')
  )

  const formatTime = (time: string) => {
    if (!time) return ''
    const [hours, minutes] = time.split(':')
    const date = new Date(2000, 0, 1, parseInt(hours), parseInt(minutes))
    return date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: 'numeric',
      hour12: true,
    })
  }

  const saveCheckIn = (api: NuxtAxiosInstance) => {
    menuCheckIn.value = false
    updateCheckInOut(api).then()
  }

  const saveCheckOut = (api: NuxtAxiosInstance) => {
    menuCheckOut.value = false
    updateCheckInOut(api).then()
  }

  const saveQuietHoursStart = (api: NuxtAxiosInstance) => {
    menuQuietHoursStart.value = false
    updateHouseRules(api).then()
  }

  const saveQuietHoursEnd = (api: NuxtAxiosInstance) => {
    menuQuietHoursEnd.value = false
    updateHouseRules(api).then()
  }

  const isFriendsAndFamilyOnly = computed(() => {
    return !editHomeData.isBooking || !editHomeData.offerAsSeasonalLease || !editHomeData.isSwap
  })

  const isPublicRental = computed(() => {
    return editHomeData.isBooking
  })

  const isLongTermRental = computed(() => {
    return editHomeData.offerAsSeasonalLease
  })

  const updateCancellationPolicy = async (policy, api: NuxtAxiosInstance) => {
    try {
      // Ensure only one of Standard, Flexible, Long Term Stay, or Non-Refundable is active
      if (
        policy === 'publicRental' ||
        policy === 'flexiblePublicRental' ||
        policy === 'longTermStay' ||
        policy === 'nonRefundable'
      ) {
        editHomeData.cancellationPolicies.publicRental = policy === 'publicRental'
        editHomeData.cancellationPolicies.flexiblePublicRental = policy === 'flexiblePublicRental'
        editHomeData.cancellationPolicies.longTermStay = policy === 'longTermStay'
        editHomeData.cancellationPolicies.nonRefundable = policy === 'nonRefundable'
      }

      await api.patch(`user/homes/${editHomeData.slug}`, {
        extra_info: {
          cancellationPolicies: editHomeData.cancellationPolicies,
        },
      })
    } catch (error) {
      toast
        .error(`An error occurred while updating ${humanize(String(policy))} policy`)
        .goAway(TOAST_DURATION)
    }
  }

  const isEmpty = (value: any): boolean => {
    return value === null || value === undefined || value === ''
  }

  const setDefaultPolicies = useDebounceFn(async (api: NuxtAxiosInstance) => {
    // Always ensure friendsAndFamily is true
    editHomeData.cancellationPolicies.friendsAndFamily = true

    if (!isPublicRental.value) {
      // Reset all public policies if not a public rental
      editHomeData.cancellationPolicies.publicRental = false
      editHomeData.cancellationPolicies.flexiblePublicRental = false
      editHomeData.cancellationPolicies.longTermStay = false
      editHomeData.cancellationPolicies.nonRefundable = false
    } else {
      const publicPolicies = [
        'publicRental',
        'flexiblePublicRental',
        'longTermStay',
        'nonRefundable',
      ]

      const selectedPolicies = publicPolicies.filter(
        policy => editHomeData.cancellationPolicies[policy]
      )

      if (selectedPolicies.length === 0) {
        // If nothing selected, auto select publicRental
        editHomeData.cancellationPolicies.publicRental = true
      } else if (selectedPolicies.length === 1) {
        // If only one selected, keep it unless it's longTermStay and not a long term rental
        if (selectedPolicies[0] === 'longTermStay' && !isLongTermRental.value) {
          editHomeData.cancellationPolicies.longTermStay = false
          editHomeData.cancellationPolicies.publicRental = true
        }
      } else {
        // If more than one selected, turn only publicRental on
        publicPolicies.forEach(policy => {
          editHomeData.cancellationPolicies[policy] = policy === 'publicRental'
        })
      }
    }

    // Handle friends and family only
    if (isFriendsAndFamilyOnly.value) {
      if (!editHomeData.checkInOut) {
        editHomeData.checkInOut = {
          checkinTime: '',
          checkoutTime: '',
        }
      }
      if (!editHomeData.houseRules) {
        editHomeData.houseRules = {
          noEvents: false,
          noSmoking: false,
          quietHours: false,
          quietHoursTime: {
            startTime: '',
            endTime: '',
          },
          additionalRules: '',
        }
      }
    }

    // Handle public rental
    if (isPublicRental.value) {
      // Initialize checkInOut if it doesn't exist
      if (!editHomeData.checkInOut) {
        editHomeData.checkInOut = { checkinTime: '', checkoutTime: '' }
      }

      // Set default check-in/out times only if not already set
      if (isEmpty(editHomeData.checkInOut.checkinTime)) {
        editHomeData.checkInOut.checkinTime = '15:00'
      }
      if (isEmpty(editHomeData.checkInOut.checkoutTime)) {
        editHomeData.checkInOut.checkoutTime = '11:00'
      }

      // Initialize houseRules if it doesn't exist
      if (!editHomeData.houseRules) {
        editHomeData.houseRules = {
          noEvents: true,
          noSmoking: true,
          quietHours: true,
          quietHoursTime: {
            startTime: '22:00',
            endTime: '06:00',
          },
          additionalRules: '',
        }
      }

      // Set default noEvents only if it's empty
      if (isEmpty(editHomeData.houseRules.noEvents)) {
        editHomeData.houseRules.noEvents = true
      }

      // Set default noSmoking only if it's empty
      if (isEmpty(editHomeData.houseRules.noSmoking)) {
        editHomeData.houseRules.noSmoking = true
      }

      // Set default quietHours only if it's empty
      if (isEmpty(editHomeData.houseRules.quietHours)) {
        editHomeData.houseRules.quietHours = true
      }

      // Initialize quietHoursTime if it doesn't exist
      if (!editHomeData.houseRules.quietHoursTime) {
        editHomeData.houseRules.quietHoursTime = { startTime: '', endTime: '' }
      }

      // Set default quiet hours time only if not already set
      if (isEmpty(editHomeData.houseRules.quietHoursTime.startTime)) {
        editHomeData.houseRules.quietHoursTime.startTime = '22:00'
      }
      if (isEmpty(editHomeData.houseRules.quietHoursTime.endTime)) {
        editHomeData.houseRules.quietHoursTime.endTime = '06:00'
      }

      // Initialize additionalRules if it doesn't exist
      if (editHomeData.houseRules.additionalRules === undefined) {
        editHomeData.houseRules.additionalRules = ''
      }
    }

    if (!isPublicRental.value) {
      editHomeData.offerAsSeasonalLease = false
    }

    // Update backend data
    try {
      await api.patch(`user/homes/${editHomeData.slug}`, {
        extra_info: {
          cancellationPolicies: editHomeData.cancellationPolicies,
          checkInOut: editHomeData.checkInOut,
          houseRules: editHomeData.houseRules,
        },
        offer_as_seasonal_lease: editHomeData.offerAsSeasonalLease,
      })
    } catch (error) {
      console.error('Error updating policies:', error)
      toast.error('An error occurred while updating policies').goAway(TOAST_DURATION)
    }
  }, 1000)

  const updateCheckInOut = async (api: NuxtAxiosInstance) => {
    try {
      await api.patch(`user/homes/${editHomeData.slug}`, {
        extra_info: {
          checkInOut: editHomeData.checkInOut,
        },
      })
    } catch (error) {
      toast
        .error('An error occurred while updating check-in and check-out times')
        .goAway(TOAST_DURATION)
      console.error('Error updating check-in and check-out times:', error)
    }
  }

  const updateHouseRules = async (api: NuxtAxiosInstance) => {
    try {
      await api.patch(`user/homes/${editHomeData.slug}`, {
        extra_info: {
          houseRules: editHomeData.houseRules,
        },
      })
    } catch (error) {
      toast.error('An error occurred while updating house rules').goAway(TOAST_DURATION)
      console.error('Error updating house rules:', error)
    }
  }

  // Function to check if house rules have been explicitly set by the user
  const areHouseRulesSet = () => {
    // Check if any house rule has been explicitly set to true
    return (
      editHomeData.houseRules.noEvents === true ||
      editHomeData.houseRules.noSmoking === true ||
      editHomeData.houseRules.quietHours === true
    )
  }

  // Function to initialize house rules with default values if they haven't been set
  const initializeHouseRules = async (api: NuxtAxiosInstance) => {
    console.log('[EditHomeStore] Initializing house rules')

    // Only set defaults if rules haven't been explicitly set by the user
    if (!areHouseRulesSet()) {
      console.log('[EditHomeStore] Setting default house rules')

      // Set all house rules to true by default
      editHomeData.houseRules.noEvents = true
      editHomeData.houseRules.noSmoking = true
      editHomeData.houseRules.quietHours = true

      // Set default quiet hours if not already set
      if (!editHomeData.houseRules.quietHoursTime) {
        editHomeData.houseRules.quietHoursTime = { startTime: '', endTime: '' }
      }

      if (isEmpty(editHomeData.houseRules.quietHoursTime.startTime)) {
        editHomeData.houseRules.quietHoursTime.startTime = '22:00'
      }

      if (isEmpty(editHomeData.houseRules.quietHoursTime.endTime)) {
        editHomeData.houseRules.quietHoursTime.endTime = '06:00'
      }

      // Update the backend
      try {
        await api.patch(`user/homes/${editHomeData.slug}`, {
          extra_info: {
            houseRules: editHomeData.houseRules,
          },
        })
        console.log('[EditHomeStore] House rules initialized successfully')
      } catch (error) {
        console.error('Error initializing house rules:', error)
      }
    } else {
      console.log('[EditHomeStore] House rules already set, skipping initialization')
    }
  }

  // Add new functions to handle individual rule updates
  const updateNoEvents = async (api: NuxtAxiosInstance) => {
    await updateHouseRules(api)
  }

  const updateNoSmoking = async (api: NuxtAxiosInstance) => {
    await updateHouseRules(api)
  }

  const updateQuietHours = async (api: NuxtAxiosInstance) => {
    await updateHouseRules(api)
  }

  const updateAdditionalRules = async (api: NuxtAxiosInstance) => {
    await updateHouseRules(api)
  }

  const decreaseBedrooms = () => {
    if (editAmountDialogData.bedrooms > 1) {
      editAmountDialogData.bedrooms--
    }
  }

  const decreaseBeds = () => {
    if (editAmountDialogData.beds > 1) {
      editAmountDialogData.beds--
    }
  }

  const decreaseBathrooms = () => {
    // Only decrease if the current value (rounded down) is greater than 1
    if (Math.floor(editAmountDialogData.bathrooms) > 1) {
      // Decrease by 1 whole number
      editAmountDialogData.bathrooms = Math.floor(editAmountDialogData.bathrooms) - 1
    }
  }

  const increaseBathrooms = () => {
    // Increase by 1 whole number
    editAmountDialogData.bathrooms = Math.floor(editAmountDialogData.bathrooms) + 1
  }

  const decreaseGuests = () => {
    if (editAmountDialogData.guests > 1) {
      editAmountDialogData.guests--
    }
    // Add console log for debugging
    console.log('decreaseGuests called, new value:', editAmountDialogData.guests)
  }

  const isActive = computed((): boolean => {
    return editHomeData.status === 'active'
  })

  const toggleHomeStatus = async (api: NuxtAxiosInstance, subscriptionStore) => {
    try {
      isStatusLoading.value = true
      const newStatus = editHomeData.status === 'active' ? 'draft' : 'active'

      // Only perform these validations when activating a home
      if (newStatus === 'active') {
        const errors = []

        // Title is required
        if (!editHomeData.title) {
          errors.push('Title is required to activate your home')
        }

        // Address is required
        if (!editHomeData.address) {
          errors.push('Address is required to activate your home')
        } else {
          // Check if the address has a house number
          // We need to make a geocoding request to verify this
          try {
            const response = await fetch(
              `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(editHomeData.address)}&limit=1&addressdetails=1`
            )

            if (response.ok) {
              const data = await response.json()
              if (data && data.length > 0 && data[0].address) {
                // Check if the address has a house number
                if (!data[0].address.house_number) {
                  errors.push('Address must include a house/building number to activate your home')
                }
              } else {
                errors.push('Could not verify address completeness. Please update your address to include a house/building number')
              }
            }
          } catch (error) {
            console.error('Error verifying address:', error)
          }
        }

        // Check if the home has public rental enabled and enough images
        const hasPublicRental = editHomeData.isBooking || editHomeData.offerAsSeasonalLease
        if (hasPublicRental && editHomeData.photos.length < 3) {
          errors.push('Your home needs at least 3 images to be activated with public rental')
        }

        // If there are any errors, show them and return
        if (errors.length > 0) {
          errors.forEach(error => toast.error(error).goAway(5000))
          isStatusLoading.value = false
          return { success: false, error: 'validation_failed' }
        }
      }

      await api.patch(`user/homes/${editHomeData.id}`, {
        status: newStatus,
      })

      editHomeData.status = newStatus
      return { success: true }
    } catch (error) {
      console.error('Error toggling home status:', error)
      toast.error('Failed to update home status').goAway(3000)
      return { success: false, error }
    } finally {
      isStatusLoading.value = false
    }
  }

  const onVerificationComplete = async (api: NuxtAxiosInstance): Promise<void> => {
    try {
      isStatusLoading.value = true

      // Perform the same validations as in toggleHomeStatus
      const errors = []

      // Title is required
      if (!editHomeData.title) {
        errors.push('Title is required to activate your home')
      }

      // Address is required
      if (!editHomeData.address) {
        errors.push('Address is required to activate your home')
      } else {
        // Check if the address has a house number
        try {
          const response = await fetch(
            `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(editHomeData.address)}&limit=1&addressdetails=1`
          )

          if (response.ok) {
            const data = await response.json()
            if (data && data.length > 0 && data[0].address) {
              // Check if the address has a house number
              if (!data[0].address.house_number) {
                errors.push('Address must include a house/building number to activate your home')
              }
            } else {
              errors.push('Could not verify address completeness. Please update your address to include a house/building number')
            }
          }
        } catch (error) {
          console.error('Error verifying address:', error)
        }
      }

      // Check if the home has public rental enabled and enough images
      const hasPublicRental = editHomeData.isBooking || editHomeData.offerAsSeasonalLease
      if (hasPublicRental && editHomeData.photos.length < 3) {
        errors.push('Your home needs at least 3 images to be activated with public rental')
      }

      // If there are any errors, show them and return
      if (errors.length > 0) {
        errors.forEach(error => toast.error(error).goAway(5000))
        return
      }

      // Directly activate the home
      await api.patch(`user/homes/${editHomeData.id}`, {
        status: 'active',
      })
      editHomeData.status = 'active'

      toast.success('Your home has been activated successfully.').goAway(3000)
    } catch (error) {
      console.error('Error activating home:', error)
      toast.error('Failed to activate home. Please try again.').goAway(3000)
    } finally {
      isStatusLoading.value = false
    }
  }

  const archiveHome = async (api: NuxtAxiosInstance) => {
    try {
      await api.patch(`user/homes/${editHomeData.id}`, {
        status: 'archived',
      })

      editHomeData.status = 'archived'
      toast.success('Home archived successfully').goAway(3000)
      return { success: true }
    } catch (error) {
      console.error('Error archiving home:', error)
      toast.error('Failed to archive home').goAway(3000)
      return { success: false, error }
    }
  }

  const unarchiveHome = async (api: NuxtAxiosInstance, subscriptionStore: any) => {
    try {
      // Check activation status first
      const hostActivationStore = useHostActivationStore()
      await hostActivationStore.checkActivationStatus(api)

      // If not fully activated, open the activation modal
      if (!hostActivationStore.isFullyActivated) {
        hostActivationStore.openActivationModal()
        return { success: false, activationRequired: true }
      }

      // Perform the same validations as in toggleHomeStatus
      const errors = []

      // Title is required
      if (!editHomeData.title) {
        errors.push('Title is required to activate your home')
      }

      // Address is required
      if (!editHomeData.address) {
        errors.push('Address is required to activate your home')
      } else {
        // Check if the address has a house number
        try {
          const response = await fetch(
            `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(editHomeData.address)}&limit=1&addressdetails=1`
          )

          if (response.ok) {
            const data = await response.json()
            if (data && data.length > 0 && data[0].address) {
              // Check if the address has a house number
              if (!data[0].address.house_number) {
                errors.push('Address must include a house/building number to activate your home')
              }
            } else {
              errors.push('Could not verify address completeness. Please update your address to include a house/building number')
            }
          }
        } catch (error) {
          console.error('Error verifying address:', error)
        }
      }

      // Check if the home has public rental enabled and enough images
      const hasPublicRental = editHomeData.isBooking || editHomeData.offerAsSeasonalLease
      if (hasPublicRental && editHomeData.photos.length < 3) {
        errors.push('Your home needs at least 3 images to be activated with public rental')
      }

      // If there are any errors, show them and return
      if (errors.length > 0) {
        errors.forEach(error => toast.error(error).goAway(5000))
        return { success: false, error: 'validation_failed' }
      }

      // If fully activated and validation passes, proceed with unarchiving
      await api.patch(`user/homes/${editHomeData.id}`, {
        status: 'active',
      })

      editHomeData.status = 'active'
      toast.success('Home unarchived successfully').goAway(3000)
      return { success: true }
    } catch (error) {
      console.error('Error unarchiving home:', error)
      toast.error('Failed to unarchive home').goAway(3000)
      return { success: false, error }
    }
  }

  const getStatusColor = computed((): string => {
    if (!editHomeData.status) return 'tw-bg-gray-500'

    const map = {
      draft: 'tw-bg-orange-500',
      active: 'tw-bg-green-500',
    }

    return map[editHomeData.status as keyof typeof map] || 'tw-bg-gray-500'
  })

  // Photo management methods
  const initializeImageStore = (imageStore: any) => {
    if (editHomeData.photos && editHomeData.photos.length > 0) {
      const formattedPhotos = editHomeData.photos.map(photo => ({
        media_id: photo.media_id,
        url: photo.url || '',
      }))

      imageStore.initializeWithExistingImages(formattedPhotos)
    }
  }

  const updatePhotosFromImageStore = async (
    api: NuxtAxiosInstance,
    imageStore: any,
    toastInstance?: any,
    skipServerUpdate: boolean = false
  ): Promise<boolean> => {
    if (!editHomeData.id || !editHomeData.slug) return false

    isUploading.value = true
    let success = false

    try {
      console.log('updatePhotosFromImageStore: Starting photo update process')

      // Log the current state
      console.log('Current image state:', {
        'imageStore.images.length': imageStore.images.value.length,
        'imageStore.isDirty': imageStore.isDirty.value,
        'editHomeData.photos.length': editHomeData.photos.length,
      })

      // Save all changes through the image store
      const saveResult = await imageStore.saveChanges(api, editHomeData.slug)
      console.log('imageStore.saveChanges result:', saveResult)

      // Get the updated list of media IDs from the image store
      const mediaIds = imageStore.getMediaIds()
      console.log('New media IDs from imageStore:', mediaIds)

      // Calculate which photos were removed (for logging purposes)
      const oldMediaIds = editHomeData.photos.map(p => p.media_id)
      const removedMediaIds = oldMediaIds.filter(id => !mediaIds.includes(id))
      if (removedMediaIds.length > 0) {
        console.log('Photos removed in this update:', removedMediaIds)
      }

      // Update our editHomeData.photos with the new state - this is for UI only
      skipWatch.value = true // Prevent the watcher from triggering additional API calls
      editHomeData.photos = mediaIds.map((id: number) => ({ media_id: id }))

      // We're already saving photos via imageStore.saveChanges, so we don't need to
      // trigger another server update via watchers
      if (!skipServerUpdate) {
        // Only mark these flags if we actually want the watchers to trigger updates
        photosChanged.value = true
        photosRan.value = true
      }

      success = true
      if (toastInstance) {
        toastInstance.success('Photos updated successfully')
      }
    } catch (error) {
      console.error('Error updating photos from image store:', error)
      if (toastInstance) {
        toastInstance.error('Failed to update photos')
      }
    } finally {
      isUploading.value = false
    }

    return success
  }

  // Add this function to handle booking type changes
  const updateBookingStatus = async (api: NuxtAxiosInstance) => {
    try {
      // Check if home is active and has public rental but not enough images
      const hasPublicRental = editHomeData.isBooking || editHomeData.offerAsSeasonalLease
      const isActive = editHomeData.status === 'active'

      if (isActive && hasPublicRental && editHomeData.photos.length < 3) {
        // Set status back to draft
        editHomeData.status = 'draft'

        // Update the backend
        await api.patch(`user/homes/${editHomeData.id}`, {
          status: 'draft',
        })

        toast
          .warning(
            'Your home has been set to draft status because public rental requires at least 3 images.'
          )
          .goAway(5000)

        // Check activation status and open modal if needed
        const hostActivationStore = useHostActivationStore()
        await hostActivationStore.checkActivationStatus(api)

        if (!hostActivationStore.isFullyActivated) {
          hostActivationStore.openActivationModal()
        }
      }

      // Always update the booking status fields
      await api.patch(`user/homes/${editHomeData.id}`, {
        allow_booking: editHomeData.isBooking,
        allow_swaps: editHomeData.isSwap,
        offer_as_seasonal_lease: editHomeData.offerAsSeasonalLease,
      })

      return { success: true }
    } catch (error) {
      console.error('Error updating booking status:', error)
      toast.error('Failed to update booking status').goAway(3000)
      return { success: false, error }
    }
  }

  return {
    editHomeData,
    clickOnTypeOfSpace,
    isSpaceTypeSelected,
    deletePhoto,
    setAsCoverPhoto,
    fetchHomeData,
    isLoading,
    getStatusColor,
    editTitleDialog,
    editTitleDialogData,
    startEditTitle,
    closeEditTitleDialog,
    updateTitle,
    displayedPets,
    editAmountDialog,
    editAmountDialogData,
    startEditAmount,
    closeEditAmountDialog,
    updateAmount,
    editTypeOfSpaceDialog,
    editTypeOfSpaceDialogData,
    startEditTypeOfSpace,
    closeEditTypeOfSpaceDialog,
    updateTypeOfSpace,
    editAddressDialog,
    editAddressDialogData,
    startEditAddress,
    closeEditAddressDialog,
    updateAddress,
    editAmenitiesDialog,
    editAmenitiesDialogData,
    startEditAmenities,
    closeEditAmenitiesDialog,
    clickOnAmenity,
    isAmenitySelected,
    updateAmenities,
    editDescriptionDialog,
    editDescriptionDialogData,
    startEditDescription,
    closeEditDescriptionDialog,
    updateDescription,
    updatePhotos,
    photosString,
    updateSettingField,
    fetchedHomeData,
    photosRan,
    skipWatch,
    shareHome,
    privateHomeAdjustedPriceDialog,
    userWantToAdjustPrice,
    privateHomeAdjustedPriceDialogData,
    clickOnPrivateHomeAdjustedPrice,
    saveAdjustedPrice,
    copyPrivateHomeLink,
    sharableLink,
    autogenerateSharableLink,
    privateHomeCreatePasswordDialog,
    passwordChangedDialog,
    password,
    confirmPassword,
    createPassword,
    resetPasswordDialog,
    resetPassword,
    emailDropdown,
    emailPreviewDialog,
    emailSentDialog,
    email,
    sendEmail,
    showTwimoPolicies,
    updateCancellationPolicy,
    updateCheckInOut,
    updateHouseRules,
    menuCheckIn,
    menuCheckOut,
    formattedCheckIn,
    formattedCheckOut,
    saveCheckIn,
    saveCheckOut,
    formattedQuietHoursStart,
    formattedQuietHoursEnd,
    menuQuietHoursStart,
    menuQuietHoursEnd,
    saveQuietHoursStart,
    saveQuietHoursEnd,
    isFriendsAndFamilyOnly,
    isPublicRental,
    isLongTermRental,
    setDefaultPolicies,
    formatTime,
    updateNoEvents,
    updateNoSmoking,
    updateQuietHours,
    updateAdditionalRules,
    initializeHouseRules,
    areHouseRulesSet,
    uploadProgress,
    isUploading,
    maxPhotos,
    uploadFiles,
    decreaseBedrooms,
    decreaseBeds,
    decreaseBathrooms,
    increaseBathrooms,
    MANDATORY_SAFETY_AMENITIES,
    sharePrivateHome,
    isActive,
    isStatusLoading,
    toggleHomeStatus,
    onVerificationComplete,
    archiveHome,
    unarchiveHome,
    initializeImageStore,
    updatePhotosFromImageStore,
    updateBookingStatus,
  }
})
