import { defineStore } from 'pinia'
import { computed, reactive, toRefs, useContext, useStore } from '@nuxtjs/composition-api'
import { TOAST_DURATION } from '~/constants'
import { UserTo } from '~/types'

const REDIRECT_STORAGE_KEY = 'twimo_auth_redirect'

const storage = {
  set: (key: string, value: string) => {
    if (process.client) {
      localStorage.setItem(key, value)
    }
  },
  get: (key: string) => {
    if (process.client) {
      return localStorage.getItem(key)
    }
    return null
  },
  remove: (key: string) => {
    if (process.client) {
      localStorage.removeItem(key)
    }
  },
}

export const useAuthStore = defineStore('auth', () => {
  const store = useStore()

  const { $axios, app } = useContext()

  const state = reactive({
    isSubmitting: false,
    redirectPath: null as string | null,
  })

  const isLoggedIn = computed(() => store.getters['auth/isLoggedIn'])
  const currentUser = computed(() => store.getters['auth/getuser'])
  const isHostType = computed(() => currentUser.value?.user_type === 'host')
  const isVendorType = computed(() => currentUser.value?.user_type === 'vendor')
  const isTravelerType = computed(() => currentUser.value?.user_type === 'traveler')

  // Redirect methods
  const setRedirectPath = (path: string | null) => {
    state.redirectPath = path
    if (path) {
      storage.set(REDIRECT_STORAGE_KEY, path)
    } else {
      storage.remove(REDIRECT_STORAGE_KEY)
    }
  }

  const getRedirectPath = () => {
    return state.redirectPath || storage.get(REDIRECT_STORAGE_KEY)
  }

  const clearRedirectPath = () => {
    state.redirectPath = null
    storage.remove(REDIRECT_STORAGE_KEY)
  }

  // Actions
  const updateUser = async (user: UserTo) => {
    await store.commit('auth/updateUser', user)
  }

  const logout = async () => {
    await store.commit('auth/logout')
  }

  const login = async (credentials: { email: string; password: string }) => {
    try {
      state.isSubmitting = true

      const { data } = await $axios.post('/auth/login', {
        email: credentials.email.trim(),
        password: credentials.password,
      })

      const { user, access_token } = data

      store.commit('auth/login', {
        user,
        token: access_token,
      })

      app.$toast.success('Logged in successfully').goAway(TOAST_DURATION)

      // Handle redirect after login
      const redirectPath = getRedirectPath()
      if (redirectPath) {
        clearRedirectPath()
        app.router?.push(redirectPath)
      } else {
        if (user.user_type === 'traveler') {
          app.router?.push('/explore');
        } else if (user.user_type === 'vendor') {
          app.router?.push('/vendor');
        } else {
          app.router?.push('/home');
        }
      }

      return { success: true, user }
    } catch (error: any) {
      console.error('Login error:', error)

      app.$toast
        .error(error?.response?.data?.message || 'Login failed. Please check your credentials.')
        .goAway(TOAST_DURATION)

      return { success: false, error }
    } finally {
      state.isSubmitting = false
    }
  }

  const signup = async (userData: {
    first_name: string
    last_name: string
    email: string
    password: string
    phone_number: string
    user_type: string
    opted_in_to_communications: boolean
    recaptcha_response?: string
  }) => {
    try {
      state.isSubmitting = true

      const { data } = await $axios.post('auth/register', userData)

      app.$toast.success('Please verify your email to continue').goAway(TOAST_DURATION)

      // Store the current path in localStorage for redirection after verification
      // This is important for the booking flow
      if (process.client) {
        // Get the current path from the router
        const currentPath = app.router?.currentRoute.fullPath

        // Special handling for booking pages
        if (currentPath && currentPath.includes('/bookings/')) {
          console.log('Setting redirect path from booking signup:', currentPath)
          // Store both in our auth store and in localStorage for redundancy
          setRedirectPath(currentPath)
          localStorage.setItem('redirectAfterVerification', currentPath)
        }
        // For other pages, only set if we don't already have a redirect path
        else if (!getRedirectPath() && currentPath && !currentPath.includes('/signup') && !currentPath.includes('/verify-email')) {
          console.log('Setting redirect path from signup:', currentPath)
          setRedirectPath(currentPath)
        }
      }

      app.router?.push(`/verify-email?email=${encodeURIComponent(userData.email)}`)

      return { success: true, data }
    } catch (error: any) {
      console.error('Registration error:', error)

      app.$toast
        .error(error?.response?.data?.message || 'Registration failed. Please try again.')
        .goAway(TOAST_DURATION)

      return { success: false, error }
    } finally {
      state.isSubmitting = false
    }
  }

  const sendResetPasswordEmail = async (email: string) => {
    try {
      state.isSubmitting = true
      const { data } = await $axios.post('auth/reset/send', { email })
      if (data.success) {
        app.$toast.success('Password reset email has been sent').goAway(TOAST_DURATION)
      }
      return data
    } catch (error: any) {
      return { success: true, error }
    } finally {
      state.isSubmitting = false
    }
  }

  const verifyToken = async (token: string) => {
    try {
      state.isSubmitting = true
      const { data } = await $axios.post('auth/reset/verifyToken', { token })
      return data
    } catch (error: any) {
      console.error('Verify token error:', error)
      return { is_valid_token: false, error }
    } finally {
      state.isSubmitting = false
    }
  }

  const resetPassword = async (payload: {
    password: string;
    token: string;
    password_confirmation: string
  }) => {
    try {
      state.isSubmitting = true
      const { data } = await $axios.post('auth/reset/restorePassword', payload)
      return data
    } catch (error: any) {
      console.error('Reset password error:', error)
      return { success: false, error }
    } finally {
      state.isSubmitting = false
    }
  }

  return {
    ...toRefs(state),
    // Actions
    updateUser,
    logout,
    login,
    signup,
    sendResetPasswordEmail,
    verifyToken,
    resetPassword,
    // Redirect methods
    setRedirectPath,
    getRedirectPath,
    clearRedirectPath,
    // Computed
    isLoggedIn,
    currentUser,
    isHostType,
    isVendorType,
    isTravelerType,
  }
})
