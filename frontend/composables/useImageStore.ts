import { ref } from '@nuxtjs/composition-api'
import type { NuxtAxiosInstance } from '@nuxtjs/axios'

export interface ImageFile {
  id: string // Client-side ID (temporary)
  file?: File
  url: string // Preview URL
  uploaded: boolean
  media_id?: number // Server-side ID (once uploaded)
}

export const useImageStore = () => {
  const images = ref<ImageFile[]>([])
  const isDirty = ref(false)
  const isUploading = ref(false)

  // Add new files to store with preview URLs
  const addImages = (files: File[]) => {
    const newImages = files.map(file => ({
      id: Math.random().toString(36).substring(2, 9),
      file,
      url: URL.createObjectURL(file),
      uploaded: false,
    }))

    images.value = [...images.value, ...newImages]
    isDirty.value = true
  }

  // Initialize with existing images
  const initializeWithExistingImages = (existingImages: { media_id: number; url: string }[]) => {
    images.value = existingImages.map(img => ({
      id: Math.random().toString(36).substring(2, 9),
      url: img.url,
      uploaded: true,
      media_id: img.media_id,
    }))
    isDirty.value = false
  }

  // Reorder images
  const reorderImages = (newOrder: ImageFile[]) => {
    images.value = [...newOrder]
    isDirty.value = true
  }

  // Delete an image
  const deleteImage = (id: string) => {
    images.value = images.value.filter(img => img.id !== id)
    isDirty.value = true
  }

  // Save all changes to the server
  const saveChanges = async (api: NuxtAxiosInstance, homeId: number | string) => {
    isUploading.value = true

    try {
      // Require a valid home ID
      if (!homeId) {
        console.error('No home ID provided for saving images')
        throw new Error('Home ID is required to save images')
      }

      // Prepare data for a single request
      const formData = new FormData()

      // Add all files to upload
      const newImages = images.value.filter(img => !img.uploaded && img.file)
      console.log(
        'New images to upload:',
        newImages.length,
        newImages.map(img => img.file?.name)
      )

      if (newImages.length > 0) {
        // Specifically format files as Laravel would expect in an array
        // Using files[] makes Laravel recognize it as an array, even with a single file
        newImages.forEach((image, index) => {
          if (image.file) {
            console.log(`Adding file[${index}]:`, image.file.name, image.file.type, image.file.size)
            formData.append(`files[]`, image.file)
          }
        })
      }

      // Prepare the complete array of media IDs in the correct order for backend
      // This includes both already uploaded images + placeholders for new images
      const mediaIdsToSend = images.value
        .filter(img => img.uploaded && img.media_id)
        .map(img => img.media_id)

      console.log('Media IDs to send (for ordering and deletion):', mediaIdsToSend)

      // Add the media IDs as a JSON string to maintain order
      // This is also used to determine which photos to remove (ones not in this list)
      const mediaIdsJSON = JSON.stringify(mediaIdsToSend)
      formData.append('photos', mediaIdsJSON)

      // Also add as a regular parameter for Laravel to more easily detect
      if (mediaIdsToSend.length > 0) {
        mediaIdsToSend.forEach((id, index) => {
          formData.append(`photo_ids[${index}]`, id ? id.toString() : '')
        })
      }

      // Log the FormData contents for debugging
      console.log('FormData keys and values:', {
        photos: mediaIdsJSON,
        photo_ids: mediaIdsToSend,
      })

      // CRITICAL DEBUG: Log the exact data we're about to send for photo deletion
      console.log('PHOTO DELETION DEBUG INFO:')
      console.log('- Total images in state:', images.value.length)
      console.log(
        '- Uploaded images with media_ids:',
        images.value.filter(img => img.uploaded && img.media_id).length
      )
      console.log('- Images without media_ids:', images.value.filter(img => !img.media_id).length)
      console.log('- mediaIdsToSend:', mediaIdsToSend)
      console.log('- mediaIdsJSON:', mediaIdsJSON)

      try {
        // Test parse the JSON to ensure it's valid
        const testParse = JSON.parse(mediaIdsJSON)
        console.log('- JSON parse test successful:', Array.isArray(testParse), testParse)
      } catch (e) {
        console.error('- ERROR: Invalid JSON:', e)
      }

      // Send the update request with proper content type
      const response = await api.post(`user/homes/${homeId}/photos`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
          Accept: 'application/json',
        },
      })

      console.log('API response:', response.data)

      // Update our local state with the server response
      if (response.data.photos) {
        // Replace our images array with the server response
        // but preserve any unsaved local files
        const responseImages = response.data.photos.map((photo: any) => ({
          id: Math.random().toString(36).substring(2, 9),
          media_id: photo.media_id,
          url: photo.url,
          uploaded: true,
        }))

        // Reset our images with the server state
        images.value = responseImages

        // If we had new images but upload_count is 0, there might be an issue
        if (newImages.length > 0 && response.data.uploaded_count === 0) {
          console.warn('Warning: New images were sent but none were uploaded by the server')
        }
      }

      // Reset dirty state
      isDirty.value = false

      // Clean up object URLs to prevent memory leaks
      newImages.forEach(img => {
        if (img.file && img.url.startsWith('blob:')) {
          URL.revokeObjectURL(img.url)
        }
      })

      return true
    } catch (error) {
      console.error('Error saving images:', error)
      throw error
    } finally {
      isUploading.value = false
    }
  }

  // Get media IDs for submission
  const getMediaIds = () => {
    return images.value.map(img => img.media_id).filter(id => id)
  }

  return {
    images,
    isDirty,
    isUploading,
    addImages,
    initializeWithExistingImages,
    reorderImages,
    deleteImage,
    saveChanges,
    getMediaIds,
  }
}
