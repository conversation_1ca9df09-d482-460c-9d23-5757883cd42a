import { computed, Ref, useContext, useStore, ref, watch } from '@nuxtjs/composition-api'

import { getDateRange } from '~/helpers'
import { HomeTo, UserTo } from '~/types'
import { DEFAULT_SERVICE_FEE_RATIO, TRUVI_PROTECTION_PRICE } from '~/constants'

export const useApi = () => {
  const { $axios } = useContext()

  return $axios
}

export const useToast = () => {
  const { app } = useContext()

  return app.$toast
}

export const useScrollToTop = () => {
  window.scrollTo({
    top: 0,
    behavior: 'smooth',
  })
}

export const openNewTab = (url: string) => {
  window.open(url, '_blank')
}

export function useBookingCalculator(
  houseInfo: Ref<HomeTo>,
  startDate: Ref<string>,
  endDate: Ref<string>,
  isPrivateBooking: Ref<boolean>,
  isSwapBooking?: Ref<boolean>
) {
  const { $axios } = useContext()

  // Store custom nightly rates for the selected date range
  const customRates = ref<Record<string, number>>({})
  const totalCustomRate = ref<number>(0)
  const averageCustomRate = ref<number>(0)
  const isCustomRatesLoaded = ref<boolean>(false)
  const hasAdvancedPricing = ref<boolean>(false)

  // Fetch custom nightly rates when dates change
  watch(
    [startDate, endDate, houseInfo],
    async ([newStartDate, newEndDate, newHouseInfo]) => {
      if (newStartDate && newEndDate && newHouseInfo?.id) {
        try {
          isCustomRatesLoaded.value = false
          const { data } = await $axios.post('home-custom-nightly-rates/get-for-date-range', {
            home_id: newHouseInfo.id,
            from_date: newStartDate,
            to_date: newEndDate,
          })

          // Use the hasAdvancedPricing flag from the backend
          hasAdvancedPricing.value = data.hasAdvancedPricing || false

          if (hasAdvancedPricing.value) {
            customRates.value = data.dates || {}
            totalCustomRate.value = data.totalNightlyRate || 0
            averageCustomRate.value = data.averageNightlyRate || 0
          } else {
            customRates.value = {}
            totalCustomRate.value = 0
            averageCustomRate.value = 0
          }

          isCustomRatesLoaded.value = true
        } catch (error) {
          console.error('Error fetching custom nightly rates:', error)
          customRates.value = {}
          totalCustomRate.value = 0
          averageCustomRate.value = 0
          isCustomRatesLoaded.value = true
        }
      } else {
        customRates.value = {}
        totalCustomRate.value = 0
        averageCustomRate.value = 0
        isCustomRatesLoaded.value = true
      }
    },
    { immediate: true }
  )

  // Base nightly rate from the house info
  const baseNightlyRate = computed(() => {
    const rate = houseInfo.value?.nightly_rate
    return rate && rate !== 0
      ? typeof rate === 'number'
        ? Math.floor(rate)
        : parseInt(rate.split('.')[0], 10)
      : 0
  })

  // Use custom rate if available, otherwise use base rate
  const nightly_rate = computed(() => {
    return isCustomRatesLoaded.value && averageCustomRate.value > 0
      ? averageCustomRate.value
      : baseNightlyRate.value
  })

  const getDiffsInDays = computed(() => {
    if (!startDate.value || !endDate.value) return 0
    const dateDiff = getDateRange(startDate.value, endDate.value).length
    return dateDiff === 1 ? 1 : dateDiff - 1
  })

  // Use total custom rate if available, otherwise calculate based on nightly rate
  const getRawTotalMoney = computed(() => {
    return isCustomRatesLoaded.value && totalCustomRate.value > 0
      ? totalCustomRate.value
      : baseNightlyRate.value * getDiffsInDays.value
  })

  const getCleaningFee = computed(() => Number(houseInfo.value?.cleaning_fee) || 0)

  const getLiabilityProtection = computed(() => {
    if (isPrivateBooking.value || isSwapBooking?.value === true) {
      return 0
    }
    return getDiffsInDays.value * TRUVI_PROTECTION_PRICE
  })

  const getTaxRate = computed(() => Number(houseInfo.value?.tax_rate) || 0)

  // Service fee is no longer used (DEFAULT_SERVICE_FEE_RATIO is set to 0)
  const getServiceFee = computed(() => 0)

  const getTotalMoneyRaw = computed(
    () =>
      getRawTotalMoney.value +
      getServiceFee.value +
      getCleaningFee.value
  )

  const getTax = computed(() => (getTotalMoneyRaw.value * getTaxRate.value) / 100)

  const getTotalMoney = computed(() => getTotalMoneyRaw.value + getTax.value)

  const averageNightlyRateOnTotal = computed(() =>
    getDiffsInDays.value > 0 ? Number((getTotalMoney.value / getDiffsInDays.value).toFixed(2)) : 0
  )

  return {
    nightly_rate,
    getDiffsInDays,
    getRawTotalMoney,
    getCleaningFee,
    getLiabilityProtection,
    getTaxRate,
    getTax,
    getServiceFee,
    getTotalMoneyRaw,
    getTotalMoney,
    averageNightlyRateOnTotal,
    customRates,
    isCustomRatesLoaded,
    hasAdvancedPricing,
    baseNightlyRate,
  }
}

export const useIsUserLoggedIn = (): Ref<boolean> => {
  const store = useStore()

  return computed(() => store.getters['auth/isLoggedIn'])
}

export const useCurrentUser = (): Ref<UserTo> => {
  const store = useStore()
  const currentUser = computed(() => store.getters['auth/getuser'])

  return currentUser
}
