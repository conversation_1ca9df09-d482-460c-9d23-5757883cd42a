import { defineStore } from 'pinia'
import { reactive, computed, useContext } from '@nuxtjs/composition-api'
import type { NuxtAxiosInstance } from '@nuxtjs/axios'

import type { Subscription, SubscriptionState, SubscriptionPlan, PromoCodeValidation } from '~/types'

export const useSubscriptionStore = defineStore('subscription', () => {
  const { $config } = useContext()

  // Get Stripe price IDs from environment variables
  const SUBSCRIPTION_PLANS = computed((): SubscriptionPlan[] => {
    return [
      {
        id: $config.stripePriceMonthly,
        price: 14,
        interval: 'month',
        description: 'Monthly billing',
      },
      {
        id: $config.stripePriceYearly,
        price: 144,
        interval: 'year',
        description: 'Annual billing',
      },
    ]
  })

  const SUBSCRIPTION_FEATURES = [
    'Private Bookings with No Fees',
    'Rental Price Adjustments',
    'Home Swaps',
    'Public Bookings',
    'Vendor Management',
    'Calendar Sync',
    'Guest Verification',
    'Expense Tracking',
    'Stripe Connect',
    'Home Utilization Tracking',
    'Messaging',
  ]

  const state = reactive<SubscriptionState>({
    subscription: null,
    hasSubscription: false,
    loading: false,
    showPaywallModal: false,
    promoCode: '',
    promoCodeValidation: null,
  })

  // Computed properties
  const isActive = computed(() => state.subscription?.stripe_status === 'active')

  const currentPlan = computed(() => {
    const price = state.subscription?.stripe_price
    if (!price) return null

    const plan = SUBSCRIPTION_PLANS.value.find(p => p.id === price)
    if (!plan) return null

    return {
      ...plan,
      displayName: `${plan.interval === 'month' ? 'Monthly' : 'Annual'} ($${plan.price}/${plan.interval}${plan.interval === 'year' ? ' ' : ''})`,
    }
  })

  // Actions
  const checkSubscriptionStatus = async (api: NuxtAxiosInstance) => {
    try {
      state.loading = true
      const { data } = await api.get<{
        on_trial: boolean
        has_subscription: boolean
        subscription: Subscription
      }>('/subscription/status')

      state.subscription = data.subscription
      state.hasSubscription = data.has_subscription
    } catch (error) {
      console.error('Failed to check subscription status:', error)
      throw error
    } finally {
      state.loading = false
    }
  }

  const checkSubscriptionStatusAndOpenPaywall = async (
    api: NuxtAxiosInstance
  ): Promise<boolean> => {
    await checkSubscriptionStatus(api)
    if (!state.hasSubscription) {
      state.showPaywallModal = true
      return true
    }
    return false
  }

  const validatePromoCode = async (
    api: NuxtAxiosInstance,
    promoCode: string,
    priceId: string
  ): Promise<PromoCodeValidation> => {
    try {
      state.loading = true
      const { data } = await api.post<PromoCodeValidation>('/subscription/validate-promo-code', {
        promo_code: promoCode,
        price_id: priceId,
      })

      state.promoCodeValidation = data
      return data
    } catch (error) {
      console.error('Failed to validate promo code:', error)
      const errorResponse: PromoCodeValidation = {
        valid: false,
        message: 'Invalid promo code',
      }
      state.promoCodeValidation = errorResponse
      return errorResponse
    } finally {
      state.loading = false
    }
  }

  const createSubscription = async (
    api: NuxtAxiosInstance,
    priceId: string,
    paymentMethodId: string,
    promoCode?: string
  ) => {
    try {
      state.loading = true

      const requestData: {
        price_id: string
        payment_method_id: string
        promo_code?: string
      } = {
        price_id: priceId,
        payment_method_id: paymentMethodId,
      }

      // Include promo code if it's provided
      if (promoCode) {
        requestData.promo_code = promoCode
      }

      const { data } = await api.post('/subscription/create', requestData)

      if (data.subscription) {
        state.subscription = data.subscription
        state.hasSubscription = data.subscription.stripe_status === 'active'
      }

      // Reset promo code state after successful subscription
      state.promoCode = ''
      state.promoCodeValidation = null
      state.showPaywallModal = false

      return data
    } catch (error) {
      console.error('Failed to create subscription:', error)
      throw error
    } finally {
      state.loading = false
    }
  }

  const cancelSubscription = async (api: NuxtAxiosInstance) => {
    try {
      state.loading = true
      await api.post('/subscription/cancel')
      await checkSubscriptionStatus(api)
    } catch (error) {
      console.error('Failed to cancel subscription:', error)
      throw error
    } finally {
      state.loading = false
    }
  }

  const resumeSubscription = async (api: NuxtAxiosInstance) => {
    try {
      state.loading = true
      await api.post('/subscription/resume')
      await checkSubscriptionStatus(api)
    } catch (error) {
      console.error('Failed to resume subscription:', error)
      throw error
    } finally {
      state.loading = false
    }
  }

  return {
    // State
    loading: computed(() => state.loading),
    subscription: computed(() => state.subscription),
    hasSubscription: computed(() => state.hasSubscription),
    showPaywallModal: computed({
      get: () => state.showPaywallModal,
      set: (value: boolean) => {
        state.showPaywallModal = value
      },
    }),
    promoCode: computed({
      get: () => state.promoCode,
      set: (value: string) => {
        state.promoCode = value
      },
    }),
    promoCodeValidation: computed(() => state.promoCodeValidation),

    // Getters
    isActive,
    currentPlan,

    // Constants
    SUBSCRIPTION_PLANS,
    SUBSCRIPTION_FEATURES,

    // Actions
    checkSubscriptionStatus,
    checkSubscriptionStatusAndOpenPaywall,
    validatePromoCode,
    createSubscription,
    cancelSubscription,
    resumeSubscription,
  }
})
