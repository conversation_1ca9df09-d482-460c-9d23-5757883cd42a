import { computed, ComputedRef } from '@nuxtjs/composition-api'
import { BookingStatus } from '~/types'

export default function useAddressFormatter() {
  /**
   * Format an address based on booking status and payment status
   * Show full address AFTER payment confirmation:
   * 1. When booking is completed
   * 2. When payment is confirmed (payment_status is true)
   * 3. When booking is accepted and payment is confirmed
   *
   * BEFORE payment confirmation, only show general location (city, state, country)
   *
   * @param address - The full address
   * @param city - The city
   * @param state - The state
   * @param country - The country
   * @param bookingStatus - The booking status
   * @param paymentStatus - The payment status
   * @returns Formatted address
   */
  const formatAddress = (
    address: string | null | undefined,
    city: string | null | undefined,
    state: string | null | undefined,
    country: string | null | undefined,
    bookingStatus?: string | null,
    paymentStatus?: boolean | null
  ): string => {
    // If no address is provided, return a default message
    if (!address) {
      return 'Address not available'
    }

    // Show full address in any of these cases:
    // 1. Booking is completed (regardless of payment status)
    // 2. Payment is confirmed (payment_status is true)
    // 3. Booking is accepted and payment is confirmed
    // Note: null payment status is considered paid for non-payment bookings
    const shouldShowFullAddress =
      bookingStatus === 'completed' ||
      paymentStatus === true ||
      (bookingStatus === 'accepted' && paymentStatus === null)

    if (shouldShowFullAddress) {
      return address
    }

    // Otherwise, show partial address (city, state, country)
    const parts = [city, state, country].filter(Boolean)
    if (parts.length > 0) {
      return parts.join(', ')
    }

    // If we don't have city/state/country components, create a generic partial address
    // by removing house number and street name
    const addressParts = address.split(',')
    if (addressParts.length > 1) {
      // Remove the first part (usually house number and street)
      return addressParts.slice(1).join(',').trim()
    }

    // Fallback to a generic message
    return 'Exact location provided after booking confirmation'
  }

  /**
   * Create a computed property for formatted address
   */
  const getFormattedAddress = (
    address: string | ComputedRef<string | null | undefined> | null | undefined,
    city: string | ComputedRef<string | null | undefined> | null | undefined,
    state: string | ComputedRef<string | null | undefined> | null | undefined,
    country: string | ComputedRef<string | null | undefined> | null | undefined,
    bookingStatus?: string | ComputedRef<string | null | undefined> | null,
    paymentStatus?: boolean | ComputedRef<boolean | null | undefined> | null
  ): ComputedRef<string> => {
    return computed(() => {
      const addressValue = typeof address === 'object' ? address.value : address
      const cityValue = typeof city === 'object' ? city.value : city
      const stateValue = typeof state === 'object' ? state.value : state
      const countryValue = typeof country === 'object' ? country.value : country
      const bookingStatusValue = typeof bookingStatus === 'object' ? bookingStatus.value : bookingStatus
      const paymentStatusValue = typeof paymentStatus === 'object' ? paymentStatus.value : paymentStatus

      return formatAddress(
        addressValue,
        cityValue,
        stateValue,
        countryValue,
        bookingStatusValue,
        paymentStatusValue
      )
    })
  }

  return {
    formatAddress,
    getFormattedAddress,
  }
}
