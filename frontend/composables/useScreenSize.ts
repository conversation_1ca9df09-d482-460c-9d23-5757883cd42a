import { useBreakpoints, useWindowSize } from '@vueuse/core'
import { computed, ref } from '@nuxtjs/composition-api'

export function useScreenSize() {
  // Check if we're in a browser environment
  const isClient = process.client

  // Create a default width for server-side rendering
  // Default to desktop size (1024) for server-side rendering
  const defaultWidth = ref(1024)

  // Only use window size in client-side
  const { width: clientWidth } = isClient ? useWindowSize() : { width: defaultWidth }

  // Use the client width if available, otherwise use the default
  const width = isClient ? clientWidth : defaultWidth

  const breakpoints = useBreakpoints({
    mobile: 0,
    tablet: 768,
    desktop: 1024,
  })

  const isMobile = computed(() => width.value < 768)
  const isTablet = computed(() => width.value >= 768 && width.value < 1024)
  const isDesktop = computed(() => width.value >= 1024)

  const currentDevice = computed(() => {
    if (isMobile.value) return 'mobile'
    if (isTablet.value) return 'tablet'
    return 'desktop'
  })

  const smallerThanTablet = computed(() => width.value < 768)
  const smallerThanDesktop = computed(() => width.value < 1024)
  const largerThanMobile = computed(() => width.value >= 768)
  const largerThanTablet = computed(() => width.value >= 1024)

  return {
    width,
    breakpoints,

    isMobile,
    isTablet,
    isDesktop,
    currentDevice,

    smallerThanTablet,
    smallerThanDesktop,
    largerThanMobile,
    largerThanTablet,
  }
}
