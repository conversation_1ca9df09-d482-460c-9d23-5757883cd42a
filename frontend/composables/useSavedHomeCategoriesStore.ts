import { defineStore } from 'pinia'

import { useApi } from '~/composables/useCommon'

interface SavedHomeCategory {
  id: number
  name: string
  slug: string
  thumbnail: string | null
  homes: Array<{ id: number }>
}

export const useSavedHomeCategoriesStore = defineStore('savedHomeCategories', {
  state: () => ({
    categories: [] as SavedHomeCategory[],
    loading: true,
    initialized: false, // Add this flag to track if data has been loaded
  }),

  actions: {
    async fetchCategories() {
      // If already initialized, don't fetch again
      if (this.initialized) {
        return
      }

      this.loading = true
      const api = useApi()

      try {
        const { data } = await api.get('saved-home-categories')
        this.categories = data
        this.initialized = true // Mark as initialized after successful fetch
      } catch (error) {
        console.error('Failed to fetch saved home categories:', error)
      } finally {
        this.loading = false
      }
    },

    async refreshCategories(api: any) {
      this.loading = true

      try {
        const { data } = await api.get('saved-home-categories')
        this.categories = data
      } catch (error) {
        console.error('Failed to fetch saved home categories:', error)
      } finally {
        this.loading = false
      }
    },

    isHomeSaved(homeId: number): boolean {
      return this.categories.some(category => category.homes?.some(home => home.id === homeId))
    },

    isHomeInCategory(categoryId: number, homeId: number): boolean {
      const category = this.categories.find(c => c.id === categoryId)
      return category?.homes?.some(home => home.id === homeId) || false
    },
  },
})

export { SavedHomeCategory }
