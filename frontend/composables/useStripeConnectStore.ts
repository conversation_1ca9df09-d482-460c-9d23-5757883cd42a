import { defineStore } from 'pinia'
import { reactive, computed } from '@nuxtjs/composition-api'
import type { NuxtAxiosInstance } from '@nuxtjs/axios'

interface StripeConnectState {
  status: {
    is_connected: boolean
    charges_enabled: boolean
    payouts_enabled: boolean
  }
  loading: {
    status: boolean
    onboarding: boolean
    dashboard: boolean
  }
}

export const useStripeConnectStore = defineStore('stripeConnect', () => {
  const state = reactive<StripeConnectState>({
    status: {
      is_connected: false,
      charges_enabled: false,
      payouts_enabled: false,
    },
    loading: {
      status: false,
      onboarding: false,
      dashboard: false,
    },
  })

  // Computed properties
  const pendingRequirements = computed(() => {
    const requirements: string[] = []
    if (!state.status.is_connected) {
      requirements.push('Connect Stripe account')
    } else if (!state.status.charges_enabled) {
      requirements.push('Complete Stripe setup')
    }
    // We no longer check for payouts_enabled as per the simplification request
    return requirements
  })

  // Simplified to only check for is_connected and charges_enabled
  const isFullySetup = computed(
    () => state.status.is_connected && state.status.charges_enabled
  )

  // For backward compatibility, we keep the original status structure
  // but we'll only use is_connected and charges_enabled

  // Actions
  const fetchStatus = async (api: NuxtAxiosInstance) => {
    try {
      state.loading.status = true
      const { data } = await api.get('/payment/stripe/status')
      state.status = data
    } catch (error) {
      console.error('Error fetching Stripe Connect status:', error)
      throw error
    } finally {
      state.loading.status = false
    }
  }

  const getOnboardingUrl = async (api: NuxtAxiosInstance) => {
    try {
      state.loading.onboarding = true
      const { data } = await api.get('/payment/stripe/onboarding-url')
      return data
    } catch (error) {
      console.error('Error getting Stripe onboarding URL:', error)
      throw error
    } finally {
      state.loading.onboarding = false
    }
  }

  const getDashboardUrl = async (api: NuxtAxiosInstance) => {
    try {
      state.loading.dashboard = true
      const { data } = await api.get('/payment/stripe/dashboard-url')
      return data.url
    } catch (error) {
      console.error('Error getting Stripe dashboard URL:', error)
      throw error
    } finally {
      state.loading.dashboard = false
    }
  }

  return {
    // State
    status: computed(() => state.status),
    loading: computed(() => state.loading),

    // Getters
    pendingRequirements,
    isFullySetup,

    // Actions
    fetchStatus,
    getOnboardingUrl,
    getDashboardUrl,
  }
})
