import { computed, onMounted, ref, watch } from '@nuxtjs/composition-api'
import { convertDateStringFromDatePicker } from '~/helpers'
import { useApi, useToast } from '~/composables/useCommon'
import { TOAST_DURATION } from '~/constants'
import { useAdvancedPricesStore, useHostHomesStore } from '~/composables/useStore'
import type { HomeCustomNightlyRate } from '~/types'

export const useAdvancedPricing = (homeIdProp = null) => {
  const api = useApi()
  const toast = useToast()
  const hostHomesStore = useHostHomesStore()
  const advancedPricesStore = useAdvancedPricesStore()
  const homeId = ref<null | number>(homeIdProp)
  const startDateMenu = ref<boolean>(false)
  const endDateMenu = ref<boolean>(false)
  const startDateSelected = ref<null | Date>(null)
  const endDateSelected = ref<null | Date>(null)
  const nightlyRate = ref<null | number>(null)

  const isAppliedForAllFridays = ref<boolean>(false)
  const isAppliedForAllSaturdays = ref<boolean>(false)
  const form = ref<null | HTMLFormElement>(null)

  // Load existing pricing rules when homeId changes
  watch(homeId, (newHomeId) => {
    if (newHomeId) {
      advancedPricesStore.getAdvancedPrices(api, newHomeId)
    }
  })

  const validateDates = computed(() => {
    if (!startDateSelected.value) {
      return 'Start date is required'
    }

    if (!endDateSelected.value) {
      return 'End date is required'
    }

    if (
      startDateSelected.value &&
      endDateSelected.value &&
      startDateSelected.value > endDateSelected.value
    ) {
      return 'Start date must be less than or equal to end date'
    }

    return true
  })

  const formattedStartDateComputed = computed(() => {
    return startDateSelected.value ? convertDateStringFromDatePicker(startDateSelected.value) : null
  })

  const formattedEndDateComputed = computed(() => {
    return endDateSelected.value ? convertDateStringFromDatePicker(endDateSelected.value) : null
  })

  // Function to check if there are any overlapping date ranges
  const checkForOverlappingDates = (fromDate: Date, toDate: Date, daysOfWeek: number[]): { hasOverlap: boolean; message: string } => {
    // If store isn't loaded yet, we can't check
    if (!advancedPricesStore.isLoaded) {
      return { hasOverlap: false, message: '' }
    }

    const existingPrices = advancedPricesStore.advancedPrices

    for (const price of existingPrices) {
      // Skip if not active
      if (!price.is_active) continue

      const existingFromDate = price.conditions.from_date ? new Date(price.conditions.from_date) : null
      const existingToDate = price.conditions.to_date ? new Date(price.conditions.to_date) : null

      // Skip if no date range defined
      if (!existingFromDate || !existingToDate) continue

      // Check for date range overlap
      const hasDateOverlap = (
        // New start date falls within existing range
        (fromDate >= existingFromDate && fromDate <= existingToDate) ||
        // New end date falls within existing range
        (toDate >= existingFromDate && toDate <= existingToDate) ||
        // Existing range falls completely within new range
        (fromDate <= existingFromDate && toDate >= existingToDate)
      )

      if (hasDateOverlap) {
        // Check for day of week overlap
        const existingDaysOfWeek = price.conditions.days_of_week || [0, 1, 2, 3, 4, 5, 6]
        const overlappingDays = daysOfWeek.filter(day => existingDaysOfWeek.includes(day))

        if (overlappingDays.length > 0) {
          // Format dates for display
          const formattedExistingFromDate = convertDateStringFromDatePicker(existingFromDate)
          const formattedExistingToDate = convertDateStringFromDatePicker(existingToDate)

          return {
            hasOverlap: true,
            message: `Overlapping pricing rule detected. There is already a pricing rule for the period from ${formattedExistingFromDate} to ${formattedExistingToDate} that includes the same days of the week.`
          }
        }
      }
    }

    return { hasOverlap: false, message: '' }
  }

  const createHomeCustomNightlyRate = async (
    homeCustomNightlyRate: Omit<HomeCustomNightlyRate, 'id' | 'is_active'>
  ) => {
    return await api.post('home-custom-nightly-rates', homeCustomNightlyRate)
  }

  // Store the current dates before resetting the form
  const storeCurrentDates = () => {
    const currentStartDate = startDateSelected.value
    const currentEndDate = endDateSelected.value
    return { currentStartDate, currentEndDate }
  }

  const resetForm = () => {
    // Store current dates
    const { currentStartDate, currentEndDate } = storeCurrentDates()

    // Reset the form
    ;(form.value as any).reset()

    // Restore the date values to maintain the calendar view
    setTimeout(() => {
      startDateSelected.value = currentStartDate
      endDateSelected.value = currentEndDate
    }, 0)
  }

  const create = () => {
    if (!homeId.value || !nightlyRate.value || validateDates.value !== true) {
      return toast.error('Please fill in all the required fields').goAway(TOAST_DURATION)
    }

    const daysOfWeek: number[] = []
    const fromDate = startDateSelected.value || null
    const toDate = endDateSelected.value || null

    if (!fromDate || !toDate) {
      return toast.error('Both start and end dates are required').goAway(TOAST_DURATION)
    }

    if (isAppliedForAllFridays.value) {
      daysOfWeek.push(5)
    }

    if (isAppliedForAllSaturdays.value) {
      daysOfWeek.push(6)
    }

    if (!isAppliedForAllFridays.value && !isAppliedForAllSaturdays.value) {
      daysOfWeek.push(0, 1, 2, 3, 4, 5, 6)
    }

    // Check for overlapping dates before submitting
    const { hasOverlap, message } = checkForOverlappingDates(fromDate, toDate, daysOfWeek)

    if (hasOverlap) {
      return toast.error(message).goAway(TOAST_DURATION)
    }

    // Store the current dates before creating
    const savedDates = storeCurrentDates()

    createHomeCustomNightlyRate({
      home_id: homeId.value,
      nightly_rate: nightlyRate.value,
      conditions: {
        from_date: fromDate,
        to_date: toDate,
        days_of_week: daysOfWeek.length ? daysOfWeek : null,
      },
    })
      .then(async _ => {
        await advancedPricesStore.getAdvancedPrices(api, homeIdProp)

        await toast.success('Successfully created').goAway(TOAST_DURATION)

        // Reset form but maintain the calendar view
        resetForm()

        // Make sure we're still looking at the same month in the calendar
        startDateSelected.value = savedDates.currentStartDate
        endDateSelected.value = savedDates.currentEndDate
      })
      .catch(err => {
        console.log('err', err.response.data.message)
        return toast.error(err.response.data.message).goAway(TOAST_DURATION)
      })
  }

  onMounted(() => {
    startDateMenu.value = false
    endDateMenu.value = false

    if (!homeIdProp) {
      hostHomesStore.getHostHomes('payment-link=true').then(() => {})
    } else {
      // If we have a homeId, load the existing pricing rules
      advancedPricesStore.getAdvancedPrices(api, homeIdProp)
    }
  })

  const hostHomes = computed(() => {
    return hostHomesStore.hostHomes
  })

  return {
    hostHomes,
    homeId,
    startDateMenu,
    endDateMenu,
    startDateSelected,
    endDateSelected,
    validateDates,
    formattedStartDateComputed,
    formattedEndDateComputed,
    create,
    isAppliedForAllFridays,
    isAppliedForAllSaturdays,
    nightlyRate,
    form,
  }
}
