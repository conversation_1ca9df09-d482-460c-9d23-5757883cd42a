import { defineStore } from 'pinia'
import { ref, computed, useContext } from '@nuxtjs/composition-api'
import type { NuxtAxiosInstance } from '@nuxtjs/axios'
import { useApi } from '~/composables/useCommon'

export interface HomeData {
  id: number
  title: string
  slug: string
  address: string
  beds: number
  baths: number
  nightly_rate: number
  status: string
  // Add other properties as needed based on your API response
}

export const useUserHomesStore = defineStore('userHomes', () => {
  const { $axios } = useContext()

  // State
  const isLoading = ref<boolean>(false)
  const homes = ref<HomeData[]>([])

  // Actions
  const getUserHomes = async (api?: NuxtAxiosInstance) => {
    isLoading.value = true
    try {
      // Use the provided API instance or fall back to $axios from context
      // Don't call useApi() here as it must be called within a setup function
      const apiClient = api || $axios
      const { data } = await apiClient.get('home/user/list/my')
      homes.value = data
      return data
    } catch (error) {
      console.error('Error fetching user homes:', error)
      return []
    } finally {
      isLoading.value = false
    }
  }

  const archivedHomes = computed(() => {
    return homes.value.filter(home => home.status === 'archived')
  })

  const notArchivedHomes = computed(() => {
    return homes.value.filter(home => home.status !== 'archived')
  })

  return {
    // State
    isLoading,
    homes,

    // Actions
    getUserHomes,

    // Computed
    archivedHomes,
    notArchivedHomes,
  }
})
