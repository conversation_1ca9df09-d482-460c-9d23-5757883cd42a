import { reactive, computed, ComputedRef } from '@nuxtjs/composition-api'
import type { NuxtAxiosInstance } from '@nuxtjs/axios'

interface PaymentMethodsState {
  stripe_connect: {
    enabled: boolean
    charges_enabled: boolean
  }
  venmo: {
    enabled: boolean
    username: string | null
  }
  loading: boolean
}

interface UseHostPaymentMethods {
  stripe_connect: ComputedRef<PaymentMethodsState['stripe_connect']>
  venmo: ComputedRef<PaymentMethodsState['venmo']>
  loading: ComputedRef<boolean>
  hasAnyPaymentMethod: ComputedRef<boolean>
  fetchHostPaymentMethods: (api: NuxtAxiosInstance, hostId: number) => Promise<void>
}

export const useHostPaymentMethods = (): UseHostPaymentMethods => {
  const state = reactive<PaymentMethodsState>({
    stripe_connect: {
      enabled: false,
      charges_enabled: false,
    },
    venmo: {
      enabled: false,
      username: null,
    },
    loading: false,
  })

  const hasAnyPaymentMethod = computed(
    () =>
      (state.stripe_connect.enabled && state.stripe_connect.charges_enabled) || state.venmo.enabled
  )

  const fetchHostPaymentMethods = async (api: NuxtAxiosInstance, hostId: number) => {
    try {
      state.loading = true
      const { data } = await api.get(`/payment/host/${hostId}/payment-methods`)
      state.stripe_connect = data.stripe_connect
      state.venmo = data.venmo
    } catch (error) {
      console.error('Error fetching host payment methods:', error)
      throw error
    } finally {
      state.loading = false
    }
  }

  return {
    stripe_connect: computed(() => state.stripe_connect),
    venmo: computed(() => state.venmo),
    loading: computed(() => state.loading),
    hasAnyPaymentMethod,
    fetchHostPaymentMethods,
  }
}
