import type { NuxtAxiosInstance } from '@nuxtjs/axios'
import { computed, reactive, ref, useRouter } from '@nuxtjs/composition-api'
import { defineStore } from 'pinia'

import { useScrollToTop, useToast } from '~/composables/useCommon'
import {
  CREATE_HOME_COMPONENTS,
  CREATE_HOME_STEP_AIRBNB,
  CREATE_HOME_STEPS_NOR,
  HOME_STATUS_RENTAL_PLAN_MAPPING,
  HOME_STATUSES,
  spaceTypes,
  TOAST_DURATION,
  amenities,
  MANDATORY_SAFETY_AMENITIES,
} from '~/constants'
import { debounce, normalizeAirbnbUrl, isValidAirbnbUrl } from '~/helpers'
import { useCreateHomeImageStore } from '~/composables/useCreateHomeImageStore'

export const useCreateHomeProgressStore = defineStore('createHomeProgress', () => {
  const CREATE_HOME_STEPS = ref(CREATE_HOME_STEPS_NOR)

  const currentStep = ref(CREATE_HOME_STEPS.value.TITLE)

  const setCurrentStep = (step: string) => {
    currentStep.value = step
  }

  const totalSteps = computed(() => Object.keys(CREATE_HOME_STEPS.value).length)

  const currentStepIndex = computed(() => {
    return Object.keys(CREATE_HOME_STEPS.value).indexOf(currentStep.value) + 1
  })

  const createHomeData = reactive({
    title: null,
    typeOfSpace: null,
    rentalPlan: [],
    isBooking: false,
    isSwap: false,
    address: null,
    bedrooms: 1,
    bathrooms: 1,
    beds: 1,
    guests: 1,
    pets: {
      enabled: 'no',
    },
    amenities: [...amenities.safety],
    description: null,
    nightlyRate: 0,
    cleaningFee: 0,
    taxRate: null,
    minimumStay: null,
    petFee: null,
    offerAsSeasonalLease: false,
    additionalInfos: {
      items: [],
      other: null,
    },
    agreement: false,
    termAgreement: false,
    homeType: null,
    additionalNotes: null,
    photos: [],
  })

  const router = useRouter()

  const resetCreateHomeData = () => {
    createHomeData.title = null
    createHomeData.typeOfSpace = null
    createHomeData.rentalPlan = []
    createHomeData.isBooking = false
    createHomeData.isSwap = false
    createHomeData.address = null
    createHomeData.bedrooms = 1
    createHomeData.bathrooms = 1
    createHomeData.beds = 1
    createHomeData.guests = 1
    createHomeData.pets = {
      enabled: 'no',
    }
    createHomeData.amenities = [...amenities.safety]
    createHomeData.description = null
    createHomeData.nightlyRate = 0
    createHomeData.cleaningFee = 0
    createHomeData.taxRate = null
    createHomeData.minimumStay = null
    createHomeData.petFee = null
    createHomeData.offerAsSeasonalLease = false
    createHomeData.additionalInfos = {
      items: [],
      other: null,
    }
    createHomeData.agreement = false
    createHomeData.termAgreement = false
    createHomeData.homeType = null
    createHomeData.additionalNotes = null
    createHomeData.photos = []

    setCurrentStep(CREATE_HOME_STEPS.value.TITLE)
  }

  const currentStepComponent = computed(() => CREATE_HOME_COMPONENTS[currentStep.value])

  const validateCreateHomeData = () => {
    const errors: string[] = []

    switch (currentStep.value) {
      case CREATE_HOME_STEPS.value.TITLE:
        if (!createHomeData.title) errors.push('Title is required')
        break
      case CREATE_HOME_STEPS.value.AMENITY:
        if (!createHomeData.agreement) errors.push('You must agree to the terms and conditions')
        break
      case CREATE_HOME_STEPS.value.ADDRESS:
        if (!createHomeData.address || createHomeData.address === 'N/A') {
          errors.push('Please enter a valid address')
        }
        // We don't block progress here for missing house number, but we'll show a warning
        // The validation for activation will be stricter
        break
      case CREATE_HOME_STEPS.value.TERM_AGREEMENT:
        if (!createHomeData.termAgreement) errors.push('You must agree to the terms and conditions')
        break
      case CREATE_HOME_STEPS.value.SPACE_TYPE:
        if (!createHomeData.typeOfSpace) {
          errors.push('Please select a space type')
        }
        break
      case CREATE_HOME_STEPS.value.PRICING:
        // Validate nightly rate is provided
        const nightlyRateValue = typeof createHomeData.nightlyRate === 'string'
          ? parseFloat(createHomeData.nightlyRate)
          : createHomeData.nightlyRate;

        if (nightlyRateValue === null ||
            nightlyRateValue === undefined ||
            isNaN(nightlyRateValue) ||
            createHomeData.nightlyRate === '') {
          errors.push('Nightly Rate is required')
        }

        if (createHomeData.taxRate !== null && createHomeData.taxRate !== undefined) {
          if (createHomeData.taxRate < 0 || createHomeData.taxRate > 100) {
            errors.push('Tax rate must be between 0 and 100%')
          }
        }
        if (createHomeData.minimumStay !== null && createHomeData.minimumStay !== undefined) {
          if (createHomeData.minimumStay < 1) {
            errors.push('Minimum stay must be at least 1 night')
          }
        }
        break
      case CREATE_HOME_STEPS.value.AIRBNB_URL:
        if (airbnbUrl.value && !isValidAirbnbUrl(airbnbUrl.value)) {
          errors.push('Please enter a valid Airbnb URL or leave empty to skip')
        }
        break
    }

    if (errors.length) {
      errors.forEach(error => toast.error(error).goAway(TOAST_DURATION))
      return false
    }

    return true
  }

  const back = () => {
    const stepKeys = Object.keys(CREATE_HOME_STEPS.value)
    const stepIndex = stepKeys.indexOf(currentStep.value)
    if (stepIndex > 0) {
      if (currentStep.value === CREATE_HOME_STEPS.value.ADDRESS) {
        if (createHomeData.isBooking) {
          setCurrentStep(CREATE_HOME_STEPS.value.SWAP)

          return
        }
      }

      setCurrentStep(stepKeys[stepIndex - 1])
    } else {
      router.push('/homes')
    }

    useScrollToTop()
  }

  const next = (api: NuxtAxiosInstance) => {
    if (!validateCreateHomeData()) return

    const stepKeys = Object.keys(CREATE_HOME_STEPS.value)
    const stepIndex = stepKeys.indexOf(currentStep.value)

    // Check for required images if we're on the photos step
    if (currentStep.value === CREATE_HOME_STEPS.value.PHOTOS) {
      const imageStore = useCreateHomeImageStore()
      const hasPublicRental = createHomeData.isBooking || createHomeData.offerAsSeasonalLease
      const hasEnoughImages = imageStore.images.length >= 3

      console.log('Checking image requirements:', {
        hasPublicRental,
        imagesCount: imageStore.images.length,
        shouldGoToPhotos: shouldGoToPhotos.value,
      })

      // Only show warning for public rental without enough images
      if (hasPublicRental && !hasEnoughImages && !shouldGoToPhotos.value) {
        console.log('Showing images required modal')
        // Force reactive update
        showImagesRequiredModal.value = false
        // Use setTimeout to ensure the DOM updates properly
        setTimeout(() => {
          showImagesRequiredModal.value = true
          console.log('Modal visibility set to:', showImagesRequiredModal.value)
        }, 0)
        return
      }

      // Reset the flag if we're continuing
      shouldGoToPhotos.value = false
    }

    if (
      currentStep.value === CREATE_HOME_STEPS.value.AIRBNB_URL &&
      !airbnbFetched.value &&
      airbnbUrl.value
    ) {
      CREATE_HOME_STEPS.value = CREATE_HOME_STEP_AIRBNB

      setCurrentStep(CREATE_HOME_STEPS.value.AIRBNB_WAITING)

      fetchAirbnbData(api)
        .then(() => {
          if (airbnbFetched.value && createHomeData.address) {
            return
          }

          CREATE_HOME_STEPS.value = CREATE_HOME_STEPS_NOR
          setCurrentStep(CREATE_HOME_STEPS.value.RENTAL_PLAN)

          toast
            .error(
              "An error occurred while fetching the Airbnb data, don't worry, we will do it manually"
            )
            .goAway(TOAST_DURATION)
        })
        .catch(() => {
          CREATE_HOME_STEPS.value = CREATE_HOME_STEPS_NOR
          setCurrentStep(CREATE_HOME_STEPS.value.RENTAL_PLAN)

          toast
            .error(
              "An error occurred while fetching the Airbnb data, don't worry, we will do it manually"
            )
            .goAway(TOAST_DURATION)
        })

      return
    }

    if (currentStep.value === CREATE_HOME_STEPS.value.SWAP && !CREATE_HOME_STEPS.value.AIRBNB_URL) {
      if (createHomeData.isBooking) {
        setCurrentStep(CREATE_HOME_STEPS.value.ADDRESS)
        return
      }
    }

    if (stepIndex < stepKeys.length - 1) {
      setCurrentStep(stepKeys[stepIndex + 1])
    }

    useScrollToTop()
  }

  const isAtLastStep = computed(() => currentStep.value === CREATE_HOME_STEPS.value.DONE)

  const isAtSaveStep = computed(() => {
    return currentStep.value === CREATE_HOME_STEPS.value.TERM_AGREEMENT
  })

  const clickOnOfferAsSeasonalLease = () => {
    createHomeData.offerAsSeasonalLease = !createHomeData.offerAsSeasonalLease
  }

  const decreaseBedrooms = () => {
    if (createHomeData.bedrooms > 1) {
      createHomeData.bedrooms--
    }
  }

  const decreaseBeds = () => {
    if (createHomeData.beds > 1) {
      createHomeData.beds--
    }
  }

  const decreaseGuests = () => {
    if (createHomeData.guests > 1) {
      createHomeData.guests--
    }
  }

  const decreaseBathrooms = () => {
    if (createHomeData.bathrooms > 1) {
      createHomeData.bathrooms--
    }
  }

  const selectedRentalPlansString = computed(() => {
    const { isSwap, isBooking, offerAsSeasonalLease } = createHomeData

    const selectedPlans: string[] = []

    if (isSwap) {
      const swapPlan = HOME_STATUS_RENTAL_PLAN_MAPPING[HOME_STATUSES.SWAP]

      if (swapPlan) selectedPlans.push(swapPlan.title)
    }

    if (isBooking) {
      const rentPlan = HOME_STATUS_RENTAL_PLAN_MAPPING[HOME_STATUSES.RENT]

      if (rentPlan) selectedPlans.push(rentPlan.title)
    }

    if (offerAsSeasonalLease) {
      const seasonalRentalPlan = HOME_STATUS_RENTAL_PLAN_MAPPING[HOME_STATUSES.SEASONAL_RENTAL]

      if (seasonalRentalPlan) selectedPlans.push(seasonalRentalPlan.title)
    }

    return selectedPlans.join(', ')
  })

  const suggestHomePrivate = computed(() => {
    const validTitles = ['Share My Home', 'Fractional Owner Usage']
    const plans = createHomeData.rentalPlan

    return plans.length > 0 && plans.every(plan => validTitles.includes(plan.title))
  })

  const suggestHomePublic = computed(
    () => !suggestHomePrivate.value && createHomeData.rentalPlan.length > 0
  )

  const suggestHomeSeasonalLease = computed(() => {
    const validTitles = ['Seasonal Leases']
    const plans = createHomeData.rentalPlan
    const titles = plans.map(plan => plan.title)

    return titles.some(title => validTitles.includes(title))
  })

  const suggestHomeSwapping = computed(() => {
    const validTitles = ['Home Swap']
    const plans = createHomeData.rentalPlan
    const titles = plans.map(plan => plan.title)

    return titles.some(title => validTitles.includes(title))
  })

  const suggestAirbnbUrl = computed(() => {
    const validTitles = ['I Already Rent via Airbnb']
    const plans = createHomeData.rentalPlan
    const titles = plans.map(plan => plan.title)

    return titles.some(title => validTitles.includes(title))
  })

  const clickOnRentalPlan = (rentalPlan) => {
    if (createHomeData.rentalPlan.includes(rentalPlan)) {
      createHomeData.rentalPlan = createHomeData.rentalPlan.filter(plan => plan !== rentalPlan)
    } else {
      createHomeData.rentalPlan.push(rentalPlan)
    }
  }

  const isRentalPlanSelected = (rentalPlan) => {
    return createHomeData.rentalPlan.includes(rentalPlan)
  }

  const isAmenitySelected = (amenity: string) => {
    return createHomeData.amenities.includes(amenity)
  }

  const clickOnAmenity = (amenity: string) => {
    // Don't allow removing mandatory safety amenities
    if (MANDATORY_SAFETY_AMENITIES.includes(amenity)) {
      return
    }

    if (createHomeData.amenities.includes(amenity)) {
      createHomeData.amenities = createHomeData.amenities.filter(a => a !== amenity)
    } else {
      createHomeData.amenities.push(amenity)
    }
  }

  const isAdditionalInfoSelected = (info: string) => {
    return createHomeData.additionalInfos.items.includes(info)
  }

  const clickOnAdditionalInfo = (info: string) => {
    if (createHomeData.additionalInfos.items.includes(info)) {
      createHomeData.additionalInfos.items = createHomeData.additionalInfos.items.filter(
        i => i !== info
      )
    } else {
      createHomeData.additionalInfos.items.push(info)
    }
  }

  const errors = ref<string[]>([])

  const toast = useToast()

  const createdHomeSlug = ref('')
  const createdHomeId = ref(null)

  // Add a flag to track if we have images to upload
  const hasImagesToUpload = ref(false)

  const setHasImagesToUpload = (value: boolean) => {
    hasImagesToUpload.value = value
  }

  // Add a flag to track if we're currently processing a creation
  let isCreatingHome = false

  const createHome = async (api: NuxtAxiosInstance, isExplicitSaveOrExit = false) => {
    console.log('createHome called', {
      currentStep: currentStep.value,
      isCreatingHome,
      isExplicitSaveOrExit,
    })

    // If we're already processing a creation, don't start another one
    if (isCreatingHome) {
      console.log('Already creating a home, skipping duplicate call')
      return
    }

    // Validate current step
    const validated = validateCreateHomeData()
    if (!validated) {
      return
    }

    // Additional validation for required fields regardless of current step
    const errors: string[] = []

    // Title is always required
    if (!createHomeData.title) {
      errors.push('Title is required')
    }

    // Address is required and must be valid
    if (!createHomeData.address || createHomeData.address === 'N/A') {
      errors.push('Please enter a valid address')
    }

    if (errors.length) {
      errors.forEach(error => toast.error(error).goAway(TOAST_DURATION))
      return
    }

    try {
      isCreatingHome = true

      // Ensure mandatory safety amenities are included
      const amenities = Array.from(
        new Set([...createHomeData.amenities, ...MANDATORY_SAFETY_AMENITIES])
      )

      // Images are queued separately after creation, skip including photos in create payload

      // Initialize house rules with default values
      const houseRules = {
        noEvents: true,
        noSmoking: true,
        quietHours: true,
        quietHoursTime: {
          startTime: '22:00',
          endTime: '06:00',
        },
        additionalRules: '',
      }

      const transformedData = {
        title: createHomeData.title,
        address: createHomeData.address || '',
        beds: createHomeData.bedrooms || 1,
        baths: createHomeData.bathrooms || 1,
        number_of_beds: createHomeData.beds || 1,
        guests: createHomeData.guests || 1,
        amenities,
        description: createHomeData.description || 'Beautiful home',
        location: createHomeData.description || 'Beautiful home',
        nightly_rate: createHomeData.nightlyRate || 0,
        cleaning_fee: createHomeData.cleaningFee || 0,
        tax_rate: createHomeData.taxRate || 0,
        pet_fee: createHomeData.petFee || 0,
        allow_booking: createHomeData.isBooking,
        allow_swaps: createHomeData.isSwap,
        offer_as_seasonal_lease: createHomeData.offerAsSeasonalLease,
        minimum_stay_rentals: createHomeData.minimumStay || 1,
        extra_info: {
          additionalInfos: createHomeData.additionalInfos,
          pets: createHomeData.pets,
          typeOfSpaces: createHomeData.typeOfSpace
            ? spaceTypes.filter(type => type.title === createHomeData.typeOfSpace)
            : [],
          homeType: createHomeData.homeType,
          additionalNotes: createHomeData.additionalNotes,
          createProgressStep: CREATE_HOME_STEPS.value.DONE,
          houseRules: houseRules, // Add the default house rules
        },
      }

      console.log('Creating new home')
      transformedData.extra_info.createProgressStep = CREATE_HOME_STEPS.value.DONE

      const { data } = await api.post('user/homes', transformedData)

      createdHomeSlug.value = data.slug
      createdHomeId.value = data.id

      // Queue all images (remote & local) in the background
      const createHomeImageStore = useCreateHomeImageStore()
      console.log('createHome: invoking saveImages with', createHomeImageStore.images.length, 'images')
      createHomeImageStore.saveImages(api, data.id)
        .then(() => console.log('Background image processing initiated successfully'))
        .catch(error => console.error('Error initiating image processing:', error))
      if (createHomeImageStore.images.length > 0) {
        toast.info('Your photos are being processed in the background').goAway(TOAST_DURATION)
      }

      resetCreateHomeData()

      toast.success('Home created successfully').goAway(TOAST_DURATION)

      setCurrentStep(CREATE_HOME_STEPS.value.DONE)
    } catch (error) {
      console.error(error)

      toast.error('An error occurred while creating the home').goAway(TOAST_DURATION)
    } finally {
      isCreatingHome = false
    }
  }

  const saveProgress = async (api: NuxtAxiosInstance, isExplicitSaveOrExit = false) => {
    if (
      currentStep.value !== CREATE_HOME_STEPS.value.DONE &&
      currentStep.value !== CREATE_HOME_STEPS.value.TITLE
    ) {
      console.log('saveProgress is calling createHome')
      await createHome(api, isExplicitSaveOrExit)
    }
  }

  const previewSlug = ref('')

  const fetchPreviewSlug = debounce(async (api: NuxtAxiosInstance) => {
    const { data: slug } = await api.post('user/homes/preview-slug', {
      title: createHomeData.title,
    })

    previewSlug.value = slug
  }, 500)

  const airbnbUrl = ref('')

  const airbnbFetched = ref(false)

  const fetchAirbnbData = async (api: NuxtAxiosInstance) => {
    try {
      if (!isValidAirbnbUrl(airbnbUrl.value)) {
        throw new Error('Invalid Airbnb URL')
      }

      const normalizedUrl = normalizeAirbnbUrl(airbnbUrl.value)

      // Get the image store to set the Airbnb flag
      const imageStore = useCreateHomeImageStore()
      imageStore.isFromAirbnb = true

      const { data } = await api.post('user/homes/prefetch-airbnb', {
        url: normalizedUrl,
      })

      if (data.address) {
        createHomeData.address = data.address
      }

      if (data.description) {
        createHomeData.description = data.description
      }

      if (data.beds) {
        createHomeData.bedrooms = data.beds
      }

      if (data.baths) {
        createHomeData.bathrooms = data.baths
      }

      if (data.number_of_beds) {
        createHomeData.beds = data.number_of_beds
      }

      if (data.guests) {
        createHomeData.guests = data.guests
      }

      if (data.amenities) {
        createHomeData.amenities = data.amenities
      }

      // Process Airbnb photos
      if (data.airBnbPhotos && Array.isArray(data.airBnbPhotos) && data.airBnbPhotos.length > 0) {
        console.log('Processing Airbnb photos:', data.airBnbPhotos)
        // Clear any existing Airbnb photos from state
        createHomeData.photos = []

        // Get the image store
        const imageStore = useCreateHomeImageStore()

        // Clear any existing images in the image store
        imageStore.clearImages()

        // Format the photos for createHomeData
        const formattedPhotos = data.airBnbPhotos.map((photo: any) => ({
          id: Math.random().toString(36).substring(2, 9),
          media_id: photo.media_id || photo.url,
          url: photo.url,
          name: photo.name || photo.filename || 'Airbnb Photo',
          src: photo.src || photo.url,
          thumb: photo.thumb || photo.url,
          uploaded: false,
          remote_url: photo.url, // Add remote_url to indicate this is a remote image
          from_airbnb: true, // Mark as coming from Airbnb
          processing: false // Will be set to true after queuing
        }))

        // Add Airbnb photos to createHomeData.photos
        createHomeData.photos = formattedPhotos

        // Add the same photos to the image store
        imageStore.$patch({
          images: formattedPhotos,
          isDirty: true,
          isFromAirbnb: true,
          processingStatus: 'pending'
        })

        console.log('Added Airbnb photos to image store:', imageStore.images.length)
      } else {
        console.log('No Airbnb photos found in response')
      }

      airbnbFetched.value = true
    } catch (error) {
      console.error(error)
    }
  }

  // Dialogs

  // Title
  const editTitleDialog = ref<boolean>(false)
  const editTitleDialogData = ref(null)
  const startEditTitle = () => {
    editTitleDialogData.value = createHomeData.title
    editTitleDialog.value = true
  }
  const closeEditTitleDialog = () => {
    editTitleDialogData.value = null
    editTitleDialog.value = false
  }
  const updateTitle = () => {
    createHomeData.title = editTitleDialogData.value
    editTitleDialogData.value = null
    editTitleDialog.value = false
  }

  // Beds, Bathrooms, Guests, Pets
  const editAmountDialog = ref<boolean>(false)
  const editAmountDialogData = reactive({
    bedrooms: 1,
    bathrooms: 1,
    beds: 1,
    guests: 1,
    pets: {
      enabled: 'no',
    },
  })
  const startEditAmount = () => {
    editAmountDialogData.bedrooms = createHomeData.bedrooms
    editAmountDialogData.bathrooms = createHomeData.bathrooms
    editAmountDialogData.beds = createHomeData.beds
    editAmountDialogData.guests = createHomeData.guests
    editAmountDialogData.pets = createHomeData.pets
    editAmountDialog.value = true
  }
  const closeEditAmountDialog = () => {
    editAmountDialogData.bedrooms = 1
    editAmountDialogData.bathrooms = 1
    editAmountDialogData.beds = 1
    editAmountDialogData.guests = 1
    editAmountDialogData.pets = { enabled: 'no' }
    editAmountDialog.value = false
  }
  const updateAmount = () => {
    createHomeData.bedrooms = editAmountDialogData.bedrooms
    createHomeData.bathrooms = editAmountDialogData.bathrooms
    createHomeData.beds = editAmountDialogData.beds
    createHomeData.guests = editAmountDialogData.guests
    createHomeData.pets = editAmountDialogData.pets

    editAmountDialogData.bedrooms = 1
    editAmountDialogData.bathrooms = 1
    editAmountDialogData.beds = 1
    editAmountDialogData.guests = 1
    editAmountDialogData.pets = { enabled: 'no' }
    editAmountDialog.value = false
  }

  const displayedPets = computed(() => {
    const pets = createHomeData.pets || { enabled: 'no' }

    switch (pets.enabled) {
      case 'yes':
        return 'Pets allowed'
      case 'service animal only':
        return 'Service animals only'
      case 'no':
      default:
        return 'No pets allowed'
    }
  })

  // Address
  const editAddressDialog = ref<boolean>(false)
  const editAddressDialogData = ref(null)
  const startEditAddress = () => {
    editAddressDialogData.value = createHomeData.address
    editAddressDialog.value = true
  }
  const closeEditAddressDialog = () => {
    editAddressDialogData.value = null
    editAddressDialog.value = false
  }
  const updateAddress = () => {
    createHomeData.address = editAddressDialogData.value
    editAddressDialogData.value = null
    editAddressDialog.value = false
  }

  // Amenities
  const editAmenitiesDialog = ref<boolean>(false)
  const editAmenitiesDialogData = ref<string[]>([])
  const startEditAmenities = () => {
    editAmenitiesDialogData.value = [...createHomeData.amenities]
    editAmenitiesDialog.value = true
  }
  const closeEditAmenitiesDialog = () => {
    editAmenitiesDialogData.value = []
    editAmenitiesDialog.value = false
  }
  const clickOnAmenityEdit = (amenity: string) => {
    if (editAmenitiesDialogData.value.includes(amenity)) {
      editAmenitiesDialogData.value = editAmenitiesDialogData.value.filter(type => type !== amenity)
    } else {
      editAmenitiesDialogData.value.push(amenity)
    }
  }
  const isAmenitySelectedEdit = (amenity: string) => {
    return editAmenitiesDialogData.value.includes(amenity)
  }
  const updateAmenities = () => {
    createHomeData.amenities = editAmenitiesDialogData.value
    editAmenitiesDialogData.value = []
    editAmenitiesDialog.value = false
  }

  // Description
  const editDescriptionDialog = ref<boolean>(false)
  const editDescriptionDialogData = ref(null)
  const startEditDescription = () => {
    editDescriptionDialogData.value = createHomeData.description
    editDescriptionDialog.value = true
  }
  const closeEditDescriptionDialog = () => {
    editDescriptionDialogData.value = null
    editDescriptionDialog.value = false
  }
  const updateDescription = () => {
    createHomeData.description = editDescriptionDialogData.value
    editDescriptionDialogData.value = null
    editDescriptionDialog.value = false
  }

  const showImagesRequiredModal = ref(false)
  const shouldGoToPhotos = ref(false)

  const selectedBookingTypes = computed(() => {
    const types: string[] = []
    if (createHomeData.isBooking) types.push('Short Term Rental')
    if (createHomeData.offerAsSeasonalLease) types.push('Seasonal Rental')
    if (createHomeData.isSwap) types.push('Swap')
    return types
  })

  const closeImagesRequiredModal = () => {
    console.log('Closing images required modal')
    setTimeout(() => {
      showImagesRequiredModal.value = false
      console.log('Modal visibility after close:', showImagesRequiredModal.value)
    }, 0)
  }

  const goToPhotosFromModal = () => {
    console.log('Going to photos from modal')
    setTimeout(() => {
      showImagesRequiredModal.value = false
      shouldGoToPhotos.value = true
      setCurrentStep(CREATE_HOME_STEPS.value.DESCRIPTION)
      console.log('Modal visibility after go to photos:', showImagesRequiredModal.value)
    }, 0)
  }

  // Photo management functions
  const deletePhoto = (photoId: string | number) => {
    console.log('Deleting photo with ID:', photoId)
    if (!createHomeData.photos) return

    createHomeData.photos = createHomeData.photos.filter((photo: any) => {
      return photo.media_id !== photoId && photo.id !== photoId
    })
  }

  const setAsCoverPhoto = (photoId: string | number) => {
    console.log('Setting photo as cover:', photoId)
    if (!createHomeData.photos || createHomeData.photos.length === 0) return

    // Find the photo to set as cover
    const photoIndex = createHomeData.photos.findIndex((photo: any) =>
      photo.media_id === photoId || photo.id === photoId
    )

    if (photoIndex === -1) return

    // Move the photo to the first position
    const photo = createHomeData.photos[photoIndex]
    createHomeData.photos.splice(photoIndex, 1)
    createHomeData.photos.unshift(photo)
  }

  return {
    currentStep,
    setCurrentStep,
    totalSteps,
    currentStepIndex,
    createHomeData,
    errors,
    validateCreateHomeData,
    clickOnRentalPlan,
    isRentalPlanSelected,
    decreaseBedrooms,
    decreaseBeds,
    decreaseGuests,
    decreaseBathrooms,
    isAmenitySelected,
    clickOnAmenity,
    clickOnOfferAsSeasonalLease,
    clickOnAdditionalInfo,
    isAdditionalInfoSelected,
    back,
    next,
    isAtLastStep,
    createHome,
    isAtSaveStep,
    suggestHomeSeasonalLease,
    currentStepComponent,
    selectedRentalPlansString,
    previewSlug,
    fetchPreviewSlug,
    airbnbUrl,
    airbnbFetched,
    fetchAirbnbData,
    CREATE_HOME_STEPS,

    suggestHomePublic,
    suggestHomePrivate,
    suggestHomeSwapping,
    suggestAirbnbUrl,

    // Edit airbnb data
    editTitleDialog,
    editTitleDialogData,
    startEditTitle,
    closeEditTitleDialog,
    updateTitle,
    editAmountDialog,
    editAmountDialogData,
    startEditAmount,
    closeEditAmountDialog,
    updateAmount,
    editAddressDialog,
    editAddressDialogData,
    startEditAddress,
    closeEditAddressDialog,
    updateAddress,
    editAmenitiesDialog,
    editAmenitiesDialogData,
    startEditAmenities,
    closeEditAmenitiesDialog,
    clickOnAmenityEdit,
    isAmenitySelectedEdit,
    updateAmenities,
    editDescriptionDialog,
    editDescriptionDialogData,
    startEditDescription,
    closeEditDescriptionDialog,
    updateDescription,
    displayedPets,
    createdHomeSlug,
    createdHomeId,
    resetCreateHomeData,
    saveProgress,
    hasImagesToUpload,
    setHasImagesToUpload,
    showImagesRequiredModal,
    closeImagesRequiredModal,
    goToPhotosFromModal,
    selectedBookingTypes,
    deletePhoto,
    setAsCoverPhoto,
  }
})
