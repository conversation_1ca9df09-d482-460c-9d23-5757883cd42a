import { defineStore } from 'pinia'
import { reactive, computed } from '@nuxtjs/composition-api'
import type { NuxtAxiosInstance } from '@nuxtjs/axios'
import { useSubscriptionStore } from './useSubscriptionStore'
import { useStripeConnectStore } from './useStripeConnectStore'
import { useVerificationStore } from './useVerificationStore'
import { useUserHomesStore } from './useUserHomesStore'

export interface ActivationStep {
  id: string
  title: string
  description: string
  completed: boolean
  action: string
  icon: string
  route?: string
}

interface HostActivationState {
  steps: ActivationStep[]
  loading: boolean
  showActivationModal: boolean
  currentStepIndex: number
  preventAutoOpen: boolean // Flag to prevent automatic opening of the modal
}

export const useHostActivationStore = defineStore('hostActivation', () => {
  const subscriptionStore = useSubscriptionStore()
  const stripeConnectStore = useStripeConnectStore()
  const verificationStore = useVerificationStore()
  const userHomesStore = useUserHomesStore()

  const state = reactive<HostActivationState>({
    steps: [
      {
        id: 'add-home',
        title: 'Add Your First Home',
        description: 'Create your first home listing to get started with Twimo',
        completed: false,
        action: 'Add Home',
        icon: 'mdi-home-plus-outline',
        route: '/create'
      },
      {
        id: 'subscribe',
        title: 'Activate Host Membership',
        description: 'Subscribe to Twimo Host to unlock all features',
        completed: false,
        action: 'Subscribe',
        icon: 'mdi-account-check-outline'
      },
      {
        id: 'verify-identity',
        title: 'Verify Your Identity',
        description: 'Complete identity verification for safety and trust',
        completed: false,
        action: 'Verify',
        icon: 'mdi-shield-account-outline'
      }
    ],
    loading: false,
    showActivationModal: false,
    currentStepIndex: 0,
    preventAutoOpen: true // Default to preventing auto-open
  })

  // Computed properties
  const activationProgress = computed(() => {
    const completedSteps = state.steps.filter(step => step.completed).length
    return Math.round((completedSteps / state.steps.length) * 100)
  })

  const isFullyActivated = computed(() => {
    return state.steps.every(step => step.completed)
  })

  const currentStep = computed(() => {
    return state.steps[state.currentStepIndex]
  })

  const nextIncompleteStepIndex = computed(() => {
    const index = state.steps.findIndex(step => !step.completed)
    return index >= 0 ? index : 0
  })

  // Actions
  const checkActivationStatus = async (api: NuxtAxiosInstance, autoOpen: boolean = false) => {
    try {
      state.loading = true

      // Check if user has homes - pass the API instance
      await userHomesStore.getUserHomes(api)
      state.steps[0].completed = userHomesStore.notArchivedHomes.length > 0

      // Check subscription status
      await subscriptionStore.checkSubscriptionStatus(api)
      state.steps[1].completed = subscriptionStore.hasSubscription

      // Check identity verification status
      await verificationStore.checkVerificationStatus(api)
      state.steps[2].completed = verificationStore.isUserVerified

      // Set current step to the first incomplete step
      state.currentStepIndex = nextIncompleteStepIndex.value

      return {
        progress: activationProgress.value,
        isFullyActivated: isFullyActivated.value,
        steps: state.steps
      }
    } catch (error) {
      console.error('Failed to check activation status:', error)
      throw error
    } finally {
      state.loading = false
    }
  }

  const openActivationModal = (stepIndex?: number) => {
    // If a specific step is requested, go to that step
    if (stepIndex !== undefined) {
      state.currentStepIndex = stepIndex
    } else {
      // Otherwise, go to the first incomplete step
      // If all steps are complete, go to the completion step (index 3)
      if (isFullyActivated.value) {
        state.currentStepIndex = 3 // Completion step
      } else {
        state.currentStepIndex = nextIncompleteStepIndex.value
      }
    }

    // Always open the modal when explicitly requested
    state.showActivationModal = true
  }

  const closeActivationModal = () => {
    // Always close the modal when explicitly requested
    state.showActivationModal = false
  }

  // Function to enable/disable auto-opening of the modal
  const setPreventAutoOpen = (prevent: boolean) => {
    state.preventAutoOpen = prevent
  }

  const goToNextStep = () => {
    if (state.currentStepIndex < state.steps.length - 1) {
      state.currentStepIndex++
    }
  }

  const goToPreviousStep = () => {
    if (state.currentStepIndex > 0) {
      state.currentStepIndex--
    }
  }

  const goToStep = (index: number) => {
    if (index >= 0 && index < state.steps.length) {
      state.currentStepIndex = index
    }
  }

  return {
    // State
    steps: computed(() => state.steps),
    loading: computed(() => state.loading),
    showActivationModal: computed(() => state.showActivationModal),
    currentStepIndex: computed(() => state.currentStepIndex),

    // Getters
    activationProgress,
    isFullyActivated,
    currentStep,
    nextIncompleteStepIndex,

    // Actions
    checkActivationStatus,
    openActivationModal,
    closeActivationModal,
    setPreventAutoOpen,
    goToNextStep,
    goToPreviousStep,
    goToStep
  }
})
