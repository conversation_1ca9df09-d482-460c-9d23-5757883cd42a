import { defineStore } from 'pinia'
import { reactive, computed } from '@nuxtjs/composition-api'
import type { NuxtAxiosInstance } from '@nuxtjs/axios'
import { useToast } from '~/composables/useCommon'
import { useAuthStore } from '~/composables/useAuthStore'

interface VenmoState {
  username: string | null
  qrCode: File | null
  loading: {
    save: boolean

    remove: boolean
    verify: boolean
  }
  preview: {
    amount: string
    note: string
    qrUrl: string | null
    showDialog: boolean
  }
  showVendorQRPreviewDialog: boolean
  showEditDialog: boolean
}

export const useVenmoStore = defineStore('venmo', () => {
  const state = reactive<VenmoState>({
    username: null,
    qrCode: null,
    loading: {
      save: false,
      remove: false,
      verify: false,
    },
    preview: {
      amount: '10',
      note: 'Cleaning fee',
      qrUrl: null,
      showDialog: false,
    },
    showVendorQRPreviewDialog: false,
    showEditDialog: false,
  })

  // Get current user and toast
  const toast = useToast()
  const authStore = useAuthStore()
  const currentUser = computed(() => authStore.currentUser)
  // Computed properties
  const isConnected = computed(() => !!currentUser.value?.venmo_username)

  const displayUsername = computed(() => {
    if (!currentUser.value?.venmo_username) return null
    const username = currentUser.value.venmo_username.toString()
    return `@${username.replace('@', '')}`
  })

  const verifyUsername = async (username: string) => {
    if (!username) {
      throw new Error('Please enter a Venmo username')
    }

    state.loading.verify = true
    try {
      // Simulate verification - replace with actual API call if available
      await new Promise(resolve => setTimeout(resolve, 500))
      return true
    } catch (error) {
      console.error('Error verifying Venmo username:', error)
      throw error
    } finally {
      state.loading.verify = false
    }
  }

  const saveUsername = async (api: NuxtAxiosInstance) => {
    if (!state.username) {
      toast.error('Please enter a Venmo username').goAway(3000)
      return
    }

    const cleanUsername = state.username.toString().replace('@', '')
    if (!/^[a-zA-Z0-9._-]+$/.test(cleanUsername)) {
      toast.error('Invalid Venmo username format').goAway(3000)
      return
    }

    try {
      state.loading.save = true
      await verifyUsername(cleanUsername)

      // Update user data
      await api.post('user/update_venmo', {
        venmo_username: cleanUsername,
      })

      // Use the new auth store to refetch user
      await authStore.updateUser({
        ...currentUser.value,
        venmo_username: cleanUsername,
      })

      state.showEditDialog = false
      toast.success('Venmo username successfully updated').goAway(3000)
    } catch (error) {
      console.error('Error saving Venmo details:', error)
      toast.error('Failed to update Venmo username').goAway(3000)
      throw error
    } finally {
      state.loading.save = false
    }
  }

  const removeUsername = async (api: NuxtAxiosInstance) => {
    if (!currentUser.value?.venmo_username) return

    try {
      state.loading.remove = true

      // Update user data
      await api.post('user/update_venmo', {
        venmo_username: null,
      })

      // Use the new auth store to refetch user
      await authStore.updateUser({
        ...currentUser.value,
        venmo_username: null,
      })

      toast.success('Venmo username successfully removed').goAway(3000)
    } catch (error) {
      console.error('Error removing Venmo details:', error)
      toast.error('Failed to remove Venmo username').goAway(3000)
      throw error
    } finally {
      state.loading.remove = false
    }
  }

  const generateQrPreview = () => {
    if (!currentUser.value?.venmo_username) {
      toast.error('No Venmo username connected').goAway(3000)
      return
    }

    const username = currentUser.value.venmo_username.toString().replace('@', '')
    const note = encodeURIComponent(state.preview.note)
    state.preview.qrUrl = `https://venmo.com/${username}?txn=pay&amount=${state.preview.amount}&note=${note}`
  }

  const generateVendorQrPreview = () => {
    if (!currentUser.value?.venmo_username) {
      toast.error('No Venmo username connected').goAway(3000)
      return
    }

    state.showVendorQRPreviewDialog = true
    const username = currentUser.value.venmo_username.toString().replace('@', '')

    state.preview.qrUrl = `https://venmo.com/code?user_id=${username}`
  }

  const resetPreview = () => {
    state.preview.amount = '10'
    state.preview.note = 'Cleaning fee'
    state.preview.qrUrl = null
    state.preview.showDialog = false
  }

  const closeEditDialog = () => {
    state.showEditDialog = false
    state.username = null
    resetPreview()
  }

  // Add the missing fetchVenmoStatus method
  const fetchVenmoStatus = async (api: NuxtAxiosInstance) => {
    try {
      // No need for an actual API call since we already have the venmo username in the user object
      // This is just to match the expected interface in the profile page
      console.log('Venmo status checked from user data')
      return true
    } catch (error) {
      console.error('Error fetching Venmo status:', error)
      return false
    }
  }

  return {
    // State
    username: computed({
      get: () => state.username,
      set: value => (state.username = value),
    }),
    loading: computed(() => state.loading),
    preview: computed(() => state.preview),
    showEditDialog: computed({
      get: () => state.showEditDialog,
      set: value => (state.showEditDialog = value),
    }),

    // Getters
    isConnected,
    displayUsername,
    showVendorQRPreviewDialog: computed({
      get: () => state.showVendorQRPreviewDialog,
      set: value => (state.showVendorQRPreviewDialog = value),
    }),

    // Actions
    saveUsername,
    removeUsername,
    verifyUsername,
    generateQrPreview,
    generateVendorQrPreview,
    resetPreview,
    closeEditDialog,
    fetchVenmoStatus,
  }
})
