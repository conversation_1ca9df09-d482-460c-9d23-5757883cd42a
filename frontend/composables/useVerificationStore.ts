import { defineStore } from 'pinia'
import { reactive, computed } from '@nuxtjs/composition-api'
import type { NuxtAxiosInstance } from '@nuxtjs/axios'

export interface VerificationState {
  isVerified: boolean
  verificationSessionUrl: string | null
  loading: boolean
  showVerificationModal: boolean
  verificationSessionId: string | null
  currentHomeId: number | null
  // Global flag to prevent duplicate toast notifications
  hasShownSuccessToast: boolean
}

export const useVerificationStore = defineStore('verification', () => {
  const state = reactive<VerificationState>({
    isVerified: false,
    verificationSessionUrl: null,
    loading: false,
    showVerificationModal: false,
    verificationSessionId: null,
    currentHomeId: null,
    hasShownSuccessToast: false,
  })

  // Computed properties
  const isUserVerified = computed(() => state.isVerified)

  // Actions
  const checkVerificationStatus = async (api: NuxtAxiosInstance) => {
    try {
      state.loading = true
      console.log('Checking verification status...')
      const { data } = await api.get('/identity/check-verification')
      console.log('Verification status response:', data)
      state.isVerified = data.verified
      return data.verified
    } catch (error) {
      console.error('Failed to check verification status:', error)
      throw error
    } finally {
      state.loading = false
    }
  }

  const createVerificationSession = async (api: NuxtAxiosInstance) => {
    try {
      state.loading = true
      console.log('Creating verification session...')

      // Log the request path and params for debugging
      console.log('Requesting verification session from:', '/identity/create-verification')

      const { data } = await api.post('/identity/create-verification')
      console.log('Create verification session response:', data)

      // More detailed logging to help debug
      if (data && data.verification_url) {
        console.log('Verification URL received:', data.verification_url.substring(0, 50) + '...')
        console.log('Session ID received:', data.session_id)
      } else {
        console.error('No verification URL or session ID in response:', data)
      }

      state.verificationSessionUrl = data.verification_url
      state.verificationSessionId = data.session_id || null

      return data
    } catch (error: any) {
      console.error('Failed to create verification session:', error)
      // Log additional error details if available
      if (error.response) {
        console.error('Error response:', error.response.status, error.response.data)
      }
      throw error
    } finally {
      state.loading = false
    }
  }

  const checkVerificationStatusAndOpenVerification = async (
    api: NuxtAxiosInstance,
    homeId: number | null = null
  ): Promise<boolean> => {
    try {
      await checkVerificationStatus(api)

      if (!state.isVerified) {
        if (homeId !== null) {
          console.log('Setting current home ID:', homeId)
          state.currentHomeId = homeId
        }

        await createVerificationSession(api)
        state.showVerificationModal = true
        return true
      }

      return false
    } catch (error) {
      console.error('Error in checkVerificationStatusAndOpenVerification:', error)
      return false
    }
  }

  const verificationCompleted = async (
    api: NuxtAxiosInstance
  ): Promise<{ verified: boolean; status?: string }> => {
    console.log('Verification store: Checking verification completion')
    try {
      // If no session ID is available, we can't check
      if (!state.verificationSessionId) {
        console.log('Verification store: No session ID available for checking status')
        return { verified: false, status: 'unknown' }
      }

      // First try the direct API endpoint
      try {
        console.log('Verification store: Checking via identity API endpoint')
        const { data } = await api.get('/identity/check-verification')

        if (data && data.verified) {
          console.log('Verification store: User is verified via identity API')
          state.isVerified = true
          return { verified: true, status: 'verified' }
        }
      } catch (identityError) {
        console.error('Verification store: Error with identity API, falling back to session check:', identityError)
      }

      // Fall back to session-specific check if needed
      console.log('Verification store: Checking via session-specific endpoint')
      const { data } = await api.get('/identity/check-verification', {
        params: {
          session_id: state.verificationSessionId,
        },
      })

      // Update local verification status if the check was successful
      if (data && data.status) {
        console.log(`Verification store: Status check result: ${data.status}`)

        if (data.verified) {
          state.isVerified = true
          return { verified: true, status: 'verified' }
        } else {
          // For non-verified statuses, return the specific status for handling
          return { verified: false, status: data.status }
        }
      }

      // Default case if data is missing
      return { verified: false, status: 'unknown' }
    } catch (error) {
      console.error('Verification store: Error checking verification status:', error)
      return { verified: false, status: 'error' }
    }
  }

  // New separate function for activating a home after verification
  const activateHomeAfterVerification = async (api: NuxtAxiosInstance, homeId: number) => {
    try {
      console.log('Activating home after verification:', homeId)
      const { data } = await api.patch(`user/homes/${homeId}`, {
        status: 'active',
      })
      return data
    } catch (error) {
      console.error('Failed to activate home after verification:', error)
      throw error
    }
  }

  const showVerification = (homeId: number | null = null) => {
    if (homeId !== null) {
      state.currentHomeId = homeId
    }
    state.showVerificationModal = true
  }

  const hideVerification = () => {
    console.log('Verification store: Hiding verification modal')
    state.showVerificationModal = false

    // Clean up session data when hiding the modal
    state.verificationSessionUrl = null
    state.verificationSessionId = null

    // DO NOT reset the toast flag here - it should persist until component unmount
    // This prevents multiple toasts when verification completes

    // Log cleanup for debugging
    console.log('Verification store: Cleaned up after hiding modal')
  }

  const resetVerificationState = () => {
    // Reset everything except isVerified
    state.verificationSessionUrl = null
    state.verificationSessionId = null
    state.showVerificationModal = false
    state.currentHomeId = null
    console.log('Verification store: Reset verification state')
  }

  const resetToastFlag = () => {
    state.hasShownSuccessToast = false
    console.log('Verification store: Reset toast flag')
  }

  return {
    // State
    loading: computed(() => state.loading),
    isVerified: computed(() => state.isVerified),
    verificationSessionUrl: computed(() => state.verificationSessionUrl),
    showVerificationModal: computed({
      get: () => state.showVerificationModal,
      set: (value: boolean) => {
        console.log(`Verification store: Setting showVerificationModal to ${value}`)
        state.showVerificationModal = value
      },
    }),
    currentHomeId: computed(() => state.currentHomeId),
    hasShownSuccessToast: computed({
      get: () => state.hasShownSuccessToast,
      set: (value: boolean) => {
        state.hasShownSuccessToast = value
      },
    }),

    // Computed
    isUserVerified,

    // Actions
    checkVerificationStatus,
    createVerificationSession,
    checkVerificationStatusAndOpenVerification,
    verificationCompleted,
    activateHomeAfterVerification,
    showVerification,
    hideVerification,
    resetVerificationState,
    resetToastFlag,
  }
})
