import { defineStore } from 'pinia'
import type { NuxtAxiosInstance } from '@nuxtjs/axios'

export interface CreateHomeImageFile {
  id: string
  file?: File
  url: string
  uploaded: boolean
  media_id?: number
  // Additional props to support ImagesUploader component
  name?: string
  size?: number
  type?: string
  progress?: number
  // For remote images (like from Airbnb)
  remote_url?: string
  // For tracking processing status
  processing?: boolean
  from_airbnb?: boolean
}

export const useCreateHomeImageStore = defineStore('createHomeImageStore', {
  state: () => ({
    images: [] as CreateHomeImageFile[],
    isUploading: false,
    isDirty: false,
    lastError: null as string | null,
    processingStatus: null as string | null,
    isFromAirbnb: false,
  }),

  actions: {
    // Add new files to store with preview URLs
    addImages(files: File[]) {
      if (!files || files.length === 0) return

      console.log(`Adding ${files.length} files to image store`)

      try {
        const newImages = files.map(file => ({
          id: Math.random().toString(36).substring(2, 9),
          file,
          url: URL.createObjectURL(file),
          uploaded: false,
          name: file.name,
          size: file.size,
          type: file.type,
          progress: 100, // Set to 100% since we're just storing locally for now
        }))

        // Append to existing images
        this.images = [...this.images, ...newImages]
        this.isDirty = true

        console.log(`Successfully added ${newImages.length} images, total: ${this.images.length}`)
      } catch (error) {
        console.error('Error adding images:', error)
        this.lastError = error.message || 'Unknown error'
        throw error
      }
    },

    // Delete an image
    deleteImage(id: string) {
      try {
        const imageToDelete = this.images.find(img => img.id === id)

        if (imageToDelete && imageToDelete.url.startsWith('blob:')) {
          // Revoke object URL to prevent memory leaks
          URL.revokeObjectURL(imageToDelete.url)
        }

        // Filter out the deleted image
        this.images = this.images.filter(img => img.id !== id)
        this.isDirty = true

        console.log(`Deleted image, remaining: ${this.images.length}`)
      } catch (error) {
        console.error('Error deleting image:', error)
        this.lastError = error.message || 'Unknown error'
        throw error
      }
    },

    // Reorder images based on dragging
    reorderImages(newOrder: CreateHomeImageFile[]) {
      try {
        // Validate the new order to ensure it contains all images
        if (newOrder.length !== this.images.length) {
          console.warn('New order length does not match current images length')
          return
        }

        // Update the store with the new order
        this.images = [...newOrder]
        this.isDirty = true

        console.log('Images reordered successfully')
      } catch (error) {
        console.error('Error reordering images:', error)
        this.lastError = error.message || 'Unknown error'
        throw error
      }
    },

    // Move an image from one index to another
    moveImage(fromIndex: number, toIndex: number) {
      try {
        if (
          fromIndex < 0 ||
          fromIndex >= this.images.length ||
          toIndex < 0 ||
          toIndex >= this.images.length
        ) {
          console.warn('Invalid source or target index for image reordering')
          return
        }

        // Create a copy of the current images array
        const newOrder = [...this.images]

        // Remove the image from the source index
        const [movedImage] = newOrder.splice(fromIndex, 1)

        // Insert it at the target index
        newOrder.splice(toIndex, 0, movedImage)

        // Update the store with the new order
        this.images = newOrder
        this.isDirty = true

        console.log(`Moved image from index ${fromIndex} to ${toIndex}`)
      } catch (error) {
        console.error('Error moving image:', error)
        this.lastError = error.message || 'Unknown error'
        throw error
      }
    },

    // Clear all images
    clearImages() {
      // Revoke any object URLs to prevent memory leaks
      this.images.forEach(img => {
        if (img.file && img.url.startsWith('blob:')) {
          URL.revokeObjectURL(img.url)
        }
      })

      this.images = []
      this.isDirty = false
      this.lastError = null
    },

    // Save images to the server after home is created
    async saveImages(api: NuxtAxiosInstance, homeId: number | string) {
      // Debug: log initial state when saveImages is called
      console.log(`saveImages called for home ${homeId} with ${this.images.length} images`, this.images)
      if (!homeId || this.images.length === 0) {
        console.log('saveImages aborted: no homeId or no images to process')
        return false
      }

      this.isUploading = true
      console.log(`Starting upload of ${this.images.length} images to home ${homeId}`)

      try {
        // First, handle remote images (like from Airbnb)
        const remoteImages = this.images.filter(image => image.remote_url)
        console.log(`remoteImages count: ${remoteImages.length}`, remoteImages)
        if (remoteImages.length > 0) {
          console.log(`Found ${remoteImages.length} remote images to process`)

          // Send remote URLs to backend
          const remoteUrls = remoteImages.map(img => img.remote_url)
          const response = await api.post(`user/homes/${homeId}/remote-photos`, {
            urls: remoteUrls,
            is_from_airbnb: this.isFromAirbnb
          })

          // Update processing status
          this.processingStatus = response.data.processing_status || 'queued'

          console.log('Remote images queued for processing')
        }

        // Now handle local file uploads
        // Prepare formData for upload
        const formData = new FormData()

        // Add files to upload
        let fileCount = 0
        const localFiles = [] as File[]
        this.images.forEach((image, index) => {
          if (image.file) {
            console.log(`local file ${index}:`, image.file.name)
            localFiles.push(image.file)
            formData.append(`files[]`, image.file)
            fileCount++
          }
        })
        console.log(`fileCount: ${fileCount}`, localFiles)

        if (fileCount === 0) {
          console.log('No local files to upload')
          return true
        }

        // Mark all images as processing before sending the request
        this.images = this.images.map(img => ({
          ...img,
          processing: true
        }))

        // Send the upload request
        const response = await api.post(`user/homes/${homeId}/photos`, formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
            Accept: 'application/json',
          },
        })

        console.log('Upload response:', response.data)

        // Don't clear images if they're being processed in the background
        // Just mark them as being processed
        if (this.isFromAirbnb) {
          this.images = this.images.map(img => ({
            ...img,
            processing: true
          }))
        } else {
          this.clearImages()
        }

        return true
      } catch (error) {
        console.error('Error saving images:', error)
        this.lastError = error.message || 'Unknown error'
        throw error
      } finally {
        this.isUploading = false
      }
    },
  },
})
