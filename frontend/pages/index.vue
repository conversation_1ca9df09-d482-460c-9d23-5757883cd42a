<script lang="ts">
// @ts-nocheck
import { defineComponent, useRouter } from '@nuxtjs/composition-api'

import ImageContent from '~/components/ImageContent.vue'
import WhyTwimo from '~/components/WhyTwimo.vue'
import Plan from '~/components/Plan.vue'
import BookingFeatures from '~/components/BookingFeatures.vue'
import AnchorLinkStyleOne from '~/components/AnchorLinkStyleOne.vue'
import NewHomeCard from '~/components/NewHomeCard.vue'
import NewImageBox from '~/components/NewImageBox.vue'
import GoodButton from '~/components/GoodButton.vue'
import FaqItemV2 from '~/components/FaqItemV2.vue'
import ResponsiveBookingFeatures from '~/components/ResponsiveBookingFeatures.vue'
import iconTrue from '~/assets/icon-true.png';
import iconFalse from '~/assets/icon-false.png';

export default defineComponent({
  name: 'LatestHomePage',

  components: {
    ImageContent,
    BookingFeatures,
    Plan,
    AnchorLinkStyleOne,
    WhyTwimo,
    GoodButton,
    FaqItemV2,
    ResponsiveBookingFeatures,
  },

  mixins: [],

  layout: 'static',

  props: {},

  setup(props) {
    const router = useRouter()
  },

  data() {
    return {
      feature_list: [
        'Private, password protected booking site',
        'Venmo payment options',
        'Adjustable Pricing per Booking',
        'Home Swaps ',
        'Vendor Management',
        'Public rental option',
      ],
      twimo_features: [
        `<span><img src="${iconTrue}" width="40" /></span>`,
        `<span><img src="${iconTrue}" width="40" /></span>`,
        `<span><img src="${iconTrue}" width="40" /></span>`,
        `<span><img src="${iconTrue}" width="40" /></span>`,
        `<span><img src="${iconTrue}" width="40" /></span>`,
        '<span class="tw-font-extrabold tw-flex tw-text-center">ZERO<br class="tw-block md:tw-hidden"> Service Fees</span>',
      ],
      other_features: [
        `<span><img src="${iconFalse}" width="35" /></span>`,
        `<span><img src="${iconFalse}" width="35" /></span>`,
        `<span><img src="${iconFalse}" width="35" /></span>`,
        `<span><img src="${iconFalse}" width="35" /></span>`,
        `<span><img src="${iconFalse}" width="35" /></span>`,
        '<span class="tw-font-extrabold">12-15%<br class="tw-block md:tw-hidden"> Service Fees</span>',
      ],
      plan_features: [
        'Private Bookings with No Fees',
        'Rental Price Adjustments',
        'Home Swaps',
        'Public Bookings with No Fees',
        'Vendor Management',
        'Calendar Sync',
        'Guest Verification',
        'Expense Tracking',
      ],
      faqData: [
        {
          title: 'How do I make sure my booking type is private?',
          content:
            'All Twimo Users automatically have the Family & Friends Booking Type turned ON. You will have the ability to share a private, password-protected link with your friends & family.',
        },
        {
          title: 'Can I have both private and public booking turned on?',
          content: 'Yes, you can have both booking types turned on',
        },
        {
          title: 'Can I schedule recurring cleanings through MyCrew?',
          content:
            'Yes, you have the ability to schedule recurring cleanings within your calendar, which will connect directly with your vendor in MyCrew',
        },
        {
          title: 'How does the airbnb sync work?',
          content:
            'Yes, when adding your home you will have the ability to sync your airbnb listing. Our system will pull your data to build your home listing. You can also sync your airbnb calendar to Twimo, and vice a versa.',
        },
      ],
      testimonials_first_row: [
        {
          content:
            '“Knowing each guest goes through verification, makes me feel more confident with the renters staying in my home.”',
          name: 'Sanjay B. ',
          image: require('~/assets/testimonial-1.png'),
          bgImage: require('~/assets/testimonials/bg-1.jpg'),
        },
        {
          content:
            '“The one-time link is so easy. I send my extended family one link to our home and now they can book directly and see our calendar availability.”',
          name: 'Keith and Kathy D.',
          image: require('~/assets/testimonial-2.png'),
          bgImage: require('~/assets/testimonials/bg-2.jpg'),
        },
        {
          content:
            '“Integrating my airbnb was super easy. I simply added the link and all my info was there. Also cool that the calendars can sync.”',
          name: 'Chase R.',
          image: require('~/assets/testimonial-chase.png'),
          bgImage: require('~/assets/testimonials/bg-3.jpg'),
        },
        {
          content:
            '“My wife and I have been enjoying the home swap exchange, allows us to travel to other places not just our vacation home.”',
          name: '',
          image: require('~/assets/testimonial-roger.png'),
          bgImage: require('~/assets/testimonials/bg-4.jpg'),
        },
        {
          content:
            '“Being able to share my home privately and rent it publicly is such a unique feature. I love being able to share it with whom I chose.”',
          name: '',
          image: require('~/assets/testimonial-3.png'),
          bgImage: require('~/assets/testimonials/bg-5.jpg'),
        },
        {
          content:
            '“The calendar is awesome, nice to have bookings and MyCrew jobs all in one place and easy to categorize.”',
          name: '',
          image: require('~/assets/testimonial-4.png'),
          bgImage: require('~/assets/testimonials/bg-6.jpg'),
        },
        {
          content:
            '“My vacation home is 4 hours away so it’s nice to coordinate with my vendors over Twimo, track progress and pay directly on the platform.”',
          name: '',
          image: require('~/assets/testimonial-carmen-f.png'),
          bgImage: require('~/assets/testimonials/bg-7.jpg'),
        },
        {
          content:
            '“We wanted a simple way to allow our family members to book our home without all the back and forth.”',
          name: '',
          image: require('~/assets/testimonial-tom.png'),
          bgImage: require('~/assets/testimonials/bg-8.jpg'),
        },
      ],

      email_signup: '',
      errors: [],
      invalid: false,
      valid: true,
      success: false,
      video: null, 
      videoState: false,
    }
  },
  computed: {
    form(): any {
      return this.$refs.email_subscriber
    },
  },

  mounted() {
    window.addEventListener('scroll', this.handleScroll)
    this.animateOnScroll()
    this.video = this.$refs.videoElement as HTMLVideoElement;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            this.video.play();
            if(!this.video.paused) {
              this.video.controls = true;
              this.videoState = true;
            }
          } else {
            this.video.pause();
            this.video.controls = false;
            this.videoState = false;
          }
        });
      },
      { threshold: 0.5 }
    );

    observer.observe(this.video);

  },
  methods: {
    handleScroll() {},

    // Function to observe elements and trigger animation
    animateOnScroll() {
      const elements = document.querySelectorAll('.animate')

      const observer = new IntersectionObserver(
        (entries, observer) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              entry.target.classList.add('visible')
              observer.unobserve(entry.target) // Stop observing after it's visible
            }
          })
        },
        { threshold: 0.5 }
      )

      elements.forEach(element => {
        observer.observe(element)
      })
    },
    handleSignupFormSubmit() {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/

      if (this.email_signup !== '' && !emailRegex.test(this.email_signup)) {
        return
      }

      this.$router.push('/signup'+((this.email_signup !== '')? '?email=' + this.email_signup : ''))
    },
    toggleVideoPlay() {
      if (this.video.paused) {
        this.video.play();
        this.video.controls = true;
        this.videoState = true;
      } else {
        this.video.pause();
        this.video.controls = false;
        this.videoState = false;
      }
    },
  },
})
</script>
<template>
  <main class="home-page tw-bg-white">
    <section
      :style="{
        backgroundImage: `url(${require('~/assets/home-banner-imaeg-min.jpeg')})`,
      }"
      class="about__page__banner tw-pt-[2rem] md:tw-pt-[6rem] home__page--banner tw-min-h-[75vh] md:tw-min-h-[100vh]"
    >
      <div class="about__page__banner--inner tw-container tw-mx-auto">
        <v-row class="tw-mx-auto animate tw-w-full">
          <v-col class="tw-flex tw-items-end md:tw-items-center tw-p-0">
            <div class="tw-text-left tw-pb-5 md:tw-pb-0">
              <h1
                class="about__page__banner--title tw-mb-5 md:tw-mb-10 tw-text-[16px] md:tw-text-3xl tw-font-[Poppins] tw-font-light"
              >
                The only tool for vacation homeowners to
                <br class="tw-hidden md:tw-block" /><strong
                  class="tw-text-[28px] md:tw-text-5xl tw-leading-[40px] md:tw-leading-[70px]  tw-font-[Poppins] tw-font-bold"
                  >Share Privately. Rent Discreetly.<br class="tw-hidden md:tw-block" />
                  Swap Globally.</strong
                >
              </h1>

              <AnchorLinkStyleOne
                text="Sign up now"
                link="/signup"
                font_size="tw-text-base tw-inline md:tw-hidden"
              />

              <v-form
                ref="signup_form"
                v-model="valid"
                class="tw-hidden md:tw-flex tw-flex-wrap tw-flex-col tw-mx-auto md:tw-mx-0 tw-items-center md:tw-flex-nowrap md:tw-flex-row tw-gap-3 tw-align-top animate tw-max-w-[700px]"
                @submit.prevent="handleSignupFormSubmit"
              >
                <div
                  class="tw-flex tw-flex-col tw-w-4/6 tw-justify-center tw-items-top tw-gap-4 tw-min-w-72 lg:tw-w-9/12"
                >
                  <v-text-field
                    id="email_signup"
                    v-model="email_signup"
                    :rules="[v => !!v || 'Email is required']"
                    type="email"
                    hide-details
                    outlined
                    label="Email Address"
                    class="tw-bg-white tw-rounded-full tw-w-full tw-shadow-custom footer-email"
                    @focus="invalid = false"
                    @keyup.enter.prevent="handleSignupFormSubmit"
                  />
                </div>

                <div
                  class="tw-flex tw-flex-col tw-w-3/6 md:tw-w-2/6 tw-justify-center tw-items-top tw-gap-4 tw-max-w-[12rem]"
                >
                  <good-button
                    class="tw-text-base tw-w-full tw-font-bold tw-py-[1.7rem]"
                    @click.prevent="handleSignupFormSubmit"
                  >
                    Sign up today
                  </good-button>
                </div>
              </v-form>
            </div>
          </v-col>
        </v-row>
      </div>

      <div class="integrated__platforms__section tw-z-[1]">
        <div class="integrated__platforms__section--inner tw-container tw-mx-auto">
          <a id="features" href="#"></a>
          <h2 class="integrated__platforms__section--title animate tw-font-[Poppins] tw-font-medium">
            We’ve partnered with your favorite platforms, keeping efficiency and seamless management
            top of mind
          </h2>
          <div class="logos-container animate">
            <div class="integrated__platforms__section--slider tw-flex">
              <img src="~/assets/partners/superhog.png" alt="Superhog Logo" />
              <img src="~/assets/partners/airbnb.png" alt="Airbnb Logo" />
              <img src="~/assets/partners/stripe.png" alt="Stripe Logo" />
              <img src="~/assets/partners/icalendar.png" alt="Icalender Logo" />
              <img src="~/assets/partners/venmo.png" alt="venmo Logo" />
              <img src="~/assets/partners/gmail.png" alt="Gmail Logo" />
              <img src="~/assets/partners/stripe-identity.png" alt="Identity Logo" />
              <img src="~/assets/partners/stripe-radar.png" alt="Radar Logo" />

              <img src="~/assets/partners/superhog.png" alt="Superhog Logo" />
              <img src="~/assets/partners/airbnb.png" alt="Airbnb Logo" />
              <img src="~/assets/partners/stripe.png" alt="Stripe Logo" />
              <img src="~/assets/partners/icalendar.png" alt="Icalender Logo" />
              <img src="~/assets/partners/venmo.png" alt="venmo Logo" />
              <img src="~/assets/partners/gmail.png" alt="Gmail Logo" />
              <img src="~/assets/partners/stripe-identity.png" alt="Identity Logo" />
              <img src="~/assets/partners/stripe-radar.png" alt="Radar Logo" />
            </div>
          </div>
        </div>
      </div>

    </section>

    <section class="tw-bg-[#F5F5F5] tw-py-10 quick_info_container tw-pt-[4rem]">
      <div class="tw-container tw-m-auto tw-px-5 tw-mx-auto tw-max-w-[1100px]">
        <h2 class="tw-text-center tw-text-xl md:tw-text-[2rem] tw-text-[#2C005B] tw-mb-5 animate tw-font-[Poppins] tw-font-medium">A smarter way to mange your vacation home</h2>
        <p
          class="tw-text-center tw-font-normal tw-text-base md:tw-text-[1.2rem] tw-text-[#2C005B] animate tw-font-[Poppins] tw-font-light tw-mx-auto tw-max-w-[900px] tw-leading-[1.8rem] tw-mb-10"
        >
          The only service for vacation homeowners to privately share their home with friends and family, swap homes around the world, and discreetly rent their vacation home
        </p>
        <div
          class="tw-grid tw-grid-cols-1 md:tw-grid-cols-3 tw-gap-3 lg:tw-gap-[2rem] tw-mt-[2rem] tw-pl-0 md:tw-pl-[5%] tw-pr-0 md:tw-pr-[5%]"
        >
          <NewHomeCard
            title="Share your home <br>privately or publicly"
            description="Share your private booking link with family & friends or integrate your existing Airbnb listing, and start renting your vacation home tactfully to your chosen network."
            :image="require('~/assets/share-your-home-privately2.jpg')"
            animate_delay="1"
          />
          <NewHomeCard
            title="Swap Homes with other <br>Twimo Hosts"
            description="Gain access to thousands of homes around the world within our Twimo Host Network. Swap Homes for free and unlock access to adventure all around the globe."
            :image="require('~/assets/swap-homes-with-other-hosts.jpg')"
            animate_delay="2"
          />
          <NewHomeCard
            title="Manage Vendors, Expenses & <br>Sync Calendars"
            description="Add vendors to Twimo with ease. Seamless vendor management and payments, with an all-in-one calendar, and a complete view of your homes utilization."
            :image="require('~/assets/manage-vendors.jpg')"
            animate_delay="3"
          />
        </div>
      </div>
    </section>

    <section class="tw-bg-white tw-py-10 quick_info_container tw-pt-[4rem]">
      <div class="tw-container tw-m-auto tw-px-5 tw-mx-auto tw-max-w-[1000px]">
        <h2 class="tw-text-center tw-text-2xl md:tw-text-[2rem] tw-text-[#2C005B] tw-mb-5 animate tw-font-[Poppins] tw-font-medium">A <strong>private</strong> network of vacation homeowners</h2>
        <p
          class="tw-text-center tw-font-normal tw-text-base md:tw-text-[1.2rem] tw-text-[#2C005B] animate tw-font-[Poppins] tw-font-light tw-leading-[1.8rem] tw-max-w-[550px] tw-mr-auto tw-ml-auto tw-mb-0"
        >
           Discover why Twimo is redefining privacy in the vacation rental market, giving you complete control over who stays in your home and how you share it.
        </p>
        <div
          class="tw-text-center tw-mx-auto tw-max-w-[420px] md:tw-max-w-[740px] tw-mt-[2rem] tw-relative"
        >          
          <div  class="tw-w-full tw-mx-auto tw-h-[180px] md:tw-h-[400px]">
            <video
              ref="videoElement"
              class="tw-w-[100%] tw-h-[180px] md:tw-h-[395px] tw-relative tw-object-contain"
              loop
              playsinline
              :poster="require('~/assets/video-cover.jpg')">
              <source src="~/assets/home-video-v2.mp4" type="video/mp4" />
              Your browser does not support the video tag.
            </video>
            <button
              v-if="!videoState"
              class="tw-absolute tw-top-1/2 tw-left-1/2 tw-transform tw--translate-x-1/2 tw--translate-y-1/2 tw-bg-white tw-rounded-full tw-p-4 tw-shadow-lg tw-z-20"
              @click="toggleVideoPlay"
            >
              <img src="~/assets/play-icon.png" alt="Play Button" class="tw-w-8 tw-h-8" />
            </button>
          </div>   
        </div>
      </div>
    </section>


    <section
      class="tw-pt-20 tw-bg-[#F5F5F599] features-container tw-z-[1] tw-rounded-[50px] tw-mb-14 tw-pb-12"
    >
      <div
        class="tw-container tw-mx-auto tw-max-w-[800px] tw-px-5 tw-mt-0 tw-z-10 tw-relative animate"
      >
        <h4
          class="tw-text-[#2C005B] tw-text-sm tw-text-left md:tw-text-center tw-uppercase tw-font-bold tw-mb-0"
        >
          Features
        </h4>
        <p class="tw-text-[#2C005B] tw-text-3xl tw-text-left md:tw-text-center tw-font-medium tw-font-[Poppins]">
          Providing vacation homeowners the services they need, with <strong class="tw-font-bold">zero</strong> added fees
        </p>
      </div>

      <div class="tw-container tw-mx-auto tw-max-w-[1000px] tw-px-5 tw-mt-16 animate">
        <ImageContent
          :src="require('~/assets/newhome/private-booking.png')"
          sub_heading="PRIVATE BOOKING"
          heading="Let your friends & family<br class='tw-hidden md:tw-block'> book your home privately"
          content="<p>Sharing your home with friends & family can be time consuming with back and forth communication, and checking your calendar.
                    <br><br>With Twimo, you can share a one-time link that grants your friends & family private access to your booking page. Your booking page is password protected, where your guests can access your calendar availability, and request to book.
                </p>"
          order="DESC"
          button-name="Sign up today"
          button-link="/signup"
          bg-color="bg-purple"
        />
      </div>
      <div
        class="tw-container tw-mx-auto tw-max-w-[1000px] tw-px-5 tw-mt-20 tw-pb-10 md:mt-52 animate"
      >
        <ImageContent
          :src="require('~/assets/newhome/booking-flexibility.png')"
          sub_heading="BOOKING FLEXIBILITY"
          heading="Rent your vacation home<br class='tw-hidden md:tw-block'> publicly or privately"
          content="<p>Whether you want to integrate a current Airbnb listing or start renting your vacation home for the first time, you have options.
						<br><br>
					If you want to rent your home within our public Twimo network - you can!
						<br><br>
					If you want to rent your home privately, sharing a unique link within your chosen network, for example your HOA - you can!
					</p>"
          button-name="Join Now"
          button-link="/signup"
          bg-color="bg-purple"
        />
      </div>
      <div
        class="tw-container tw-mx-auto tw-max-w-[1000px] tw-px-5 tw-mt-10 tw-mt-20 tw-pb-10 md:mt-52 animate"
      >
        <ImageContent
          :src="require('~/assets/home-swap.png')"
          sub_heading="HOME SWAP"
          heading="Swap Homes with other<br class='tw-hidden md:tw-block'> Twimo Host"
          content="<p>As a Twimo Host you gain access to our exclusive Home Swap network. Twimo Host can explore other vacation homes across the globe, and request to Swap!
                    <br><br>Unlike other swap networks where you must build credits or points, Swaps are completely free for Twimo Host.
                </p>"
          order="DESC"
          button-name="Explore Homes"
          button-link="/explore"
          bg-color="bg-purple"
        />
      </div>
      <a id="why_twimo" href="#"></a>
    </section>

    <section class="tw-pt-12 tw-pb-24">
      <div
        :style="{
          backgroundImage: `url(${require('~/assets/faqs-bg.jpg')})`,
        }"
        class="tw-container tw-mx-auto tw-px-2 md:tw-px-5 tw-gap-5 md:tw-gap-10 lg:tw-max-w-[1000px] tw-w-[88%] md:tw-w-full tw-mr-5 tw-ml-5 tw-pb-8 tw-pt-5 md:tw-pt-8 md:tw-mr-auto md:tw-ml-auto tw-bg-slate-400 tw-rounded-[10px] tw-bg-center tw-bg-cover tw-bg-no-repeat"
      >
        <div
          class="md:tw-w-12/12 tw-py-0 tw-px-2 md:tw-px-0 tw-pt-5 md:tw-pl-[1.6rem] animate tw-flex tw-flex-col tw-items-center md:tw-items-start tw-text-center md:tw-text-left tw-w-full tw-justify-center tw-bg-[#ffffff77] md:tw-bg-[#ffffff66] tw-rounded-[10px]"
        >
          <h2 class="tw-text-3xl md:tw-text-4xl tw-text-[#2C005B] tw-mb-3 tw-font-semibold">
            FAQs
          </h2>
          
          <div class="tw-mt-0 tw-mdspace-y-10">
            <!-- <FaqItem :item="faqData" /> -->
            <FaqItemV2 :items="faqData" />
          </div>
          <div class="tw-mt-2 tw-mb-10 tw-text-center md:tw-text-right tw-pt-5 tw-w-[100%] tw-pr-5">
            <AnchorLinkStyleOne text="Visit FAQ Page" link="/faqs" font_size="tw-text-base" />
          </div>
        </div>
      </div>
    </section>
    <section class="tw-py-10 main_content_area tw-bg-[#F5F5F566] tw-rounded-t-[50px]">
      <div class="tw-container tw-mx-auto tw-max-w-[1000px] tw-px-5 tw-mt-10 tw-mb-10">
        <p class="tw-text-[#2C005B] tw-text-2xl md:tw-text-3xl tw-text-left  tw-font-[Poppins] animate">
          Providing you with the tools you need to 
          manage your home <br class="tw-hidden md:tw-block" /> while keeping
          <strong class="tw-font-bold"
            >safety and 
            security</strong
          >
          top of mind
        </p>

        <div
          class="md:tw-grid tw-grid-cols-1 md:tw-grid-cols-3 tw-gap-5 tw-mt-[4rem] tw-justify-items-center"
        >
          <NewImageBox
            :logo="require('~/assets/icon-flip-venmo.png')"
            heading="Safe transactions, zero fees"
            description="We’ve integrated with Venmo to provide seamless and safe payments throughout the platform. Use Venmo to collect payment from private guest to Host, or to pay your vendors directly."
            animate_delay="1"
            alt="Venmo Logo"
          />
          <NewImageBox
            :logo="require('~/assets/icon-flip-stripe.png')"
            heading="Secure transactions"
            description="We’ve integrated with Stripe Connect to provide seamless and safe payments from guest to Host, as well as subscription payments. We’ve also added Stripe Radar, which detects and blocks fraud using machine learning."
            animate_delay="2"
            alt="Stripe Logo"
          />
          <NewImageBox
            :logo="require('~/assets/icon-flip-airbnb.png')"
            heading="Upload your airbnb listing"
            description="When adding a home you can simply upload your airbnb listing and our system will pull the data from your Airbnb. Host the listing on Twimo’s platform, and synchronize your Airbnb and Twimo bookings via iCal on one calendar."
            animate_delay="3"
            alt="Airbnb Logo"
          />

          <div class="tw-col-span-3 tw-w-full tw-mb-10 tw-hidden md:tw-block">
            <div
              :style="{ boxShadow: '0 3px 3px #00000040' }"
              class="tw-border-[1px] tw-border-[#F5F5F566]">
            </div>
          </div>

          <NewImageBox
            :logo="require('~/assets/Truvi.png')"
            heading="Rent to verified guests"
            description="We’ve integrated with Truvi Know Your Guest to provide industry-leading guests verification for our Host looking to reduce risk and feel confident renting their home to verified guests. "
            animate_delay="1"
            alt="Truvi Logo"
          />
          <NewImageBox
            :logo="require('~/assets/identity-full.png')"
            heading="Stripe identity verification"
            description="We’ve integrated with Stripe Identity, Stripe Identity confirms the identity of global users so you can prevent attacks from fraudsters while minimizing friction for real users."
            animate_delay="2"
            alt="Identity Logo"
          />
          <NewImageBox
            :logo="require('~/assets/twimo-full.png')"
            heading="Share your home privately"
            description="Every Twimo Host has access to private bookings. Don’t worry about your prying neighbors eyes, seeing your home on public listing sites. Our password protected booking sites keep your home private."
            animate_delay="3"
            alt="Twimo Logo"
          />
        </div>
      </div>

      <a id="testimonials" href=""></a>
    </section>

    <section
      id="testimonials"
      class="tw-pt-10 tw-pb-10 tw-mb-20 testimonial_container tw-bg-center tw-bg-no-repeat tw-bg-cover"
    >
      <div class="tw-container lg:tw-mx-auto tw-px-5 animate lg:tw-max-w-[1000px]">
        <h4
          class="tw-text-[#2C005B] tw-text-sm tw-uppercase tw-font-semibold tw-font-[Poppins] tw-mb-1 tw-text-left animate"
        >
          TESTIMONIALS
        </h4>
        <p class="tw-text-[#2C005B] tw-text-3xl tw-mb-5 tw-text-left tw-font-[Poppins]">
          What our hosts are saying about <strong>twimo</strong>
        </p>
      </div>

      <div class="tw-mb-0 tw-mt-5 animate">
        <TestimonialsV3 :data="testimonials_first_row"  />
      </div>
    </section>

    <section id="why_twimo_v2" class="tw-pb-24 tw-pt-0 main_content_area">
      <div class="tw-container tw-mx-auto tw-max-w-[1000px] tw-px-5 tw-mt-10">
        <h4
          class="tw-text-[#2C005B] tw-text-sm tw-text-left tw-uppercase tw-font-bold tw-mb-1 animate"
        >
          Why Twimo
        </h4>
        <p class="tw-text-[#2C005B] tw-font-[Poppins] tw-text-3xl tw-text-left  tw-font-medium animate">
          Looking for additional management features?
          <br class="hidden lg:block" />
          You’ve come to the right place
        </p>

        <div class="tw-flex tw-flex-col md:tw-flex-row tw-gap-5 lg:tw-gap-5 tw-mt-[3rem]">
          <div class="tw-w-[100%] md:tw-w-[38%]">
            <img
              src="@/assets/calendar-v3.png"
              class="tw-mx-auto tw-mb-5" />
          </div>
          <div class="tw-grid tw-grid-cols-1 md:tw-grid-cols-1 tw-gap-2 lg:tw-gap-2 tw-mt-[1rem] tw-w-[100%] md:tw-w-[62%]">
              <whyTwimo
              :animate_delay="1"
              :image="require('~/assets/icon-manage.png')"
              title="Manage vendor jobs with ease"
              description="<p>Add your vendors to Twimo by simply sending them a one-time email. From there you can schedule recurring cleanings, pay vendors directly and track job progress. Vendors add their Venmo information upon sign up, making it super simple to send direct payments through the platform with no added fees!</p>"
              alt="Manage vendor jobs with ease"
            />
            <whyTwimo
              :animate_delay="2"
              :image="require('~/assets/icon-twimo-calendar.png')"
              title="Sync your Twimo Calendar with iCal, Gmail, and Airbnb"
              description="<p>Unlike other booking platforms, your calendar will show you more then just bookings. With the ability to customize you’re calendar you will have an over-arching view of your availability. Swaps, Bookings, Personal Use, Cleanings - all in one place! You can also sync with outside calendars, iCal, GMail, Airbnb - seeing everything in one place.</p>"
              alt="Sync your Twimo Calendar with iCal, Gmail, and Airbnb"
            />
            <whyTwimo
              :animate_delay="3"
              :image="require('~/assets/icon-keep-track.png')"
              title="Keep Track of Expenses"
              description="<p>Trying to keep track of your home utilization? Whether you are trying to reach a certain amount of rental days, generate more income, or track expenses with vendor management, Twimo helps you see a holistic view of your home utilization.</p>"
              alt="Keep Track of Expenses"
            />
          </div>
        </div>
      </div>
    </section>

    <section class="tw-bg-[#fff] tw-relative tw-z-[1] tw-pb-20">
      <div
        class="tw-container animate tw-px-5 tw-py-[1rem] tw-items-center md:tw-flex tw-mx-auto tw-max-w-[1000px]"
      >
        <div class="tw-w-full tw-block md:tw-hidden">
          <h3
            class="tw-text-3xl tw-mb-5 tw-text-[#2C005B] tw-font-light tw-text-center md:tw-text-left tw-pb-4 tw-pl-0"
          >
            See how Twimo <span class="tw-font-bold">compares</span>
          </h3>

          <ResponsiveBookingFeatures
            :features="feature_list"
            :twimo_features="twimo_features"
            :other_features="other_features"
          />
        </div>

        <div
          class="animate tw-w-full tw-gap-1 tw-mx-auto tw-min-w-[580px] lg:tw-min-w-[440px] md:tw-gap-1 tw-mt-0 tw-origin-top-left tw-max-h-[400px] md:tw-max-h-full tw-scale-[.6] md:tw-scale-100 tw-items-start tw-translate-y-1/3 md:tw-translate-y-0 tw-hidden md:tw-flex"
        >
          <div class="tw-w-1/2">
            <div
              class="tw-min-h-[78px] tw-bg-transparent tw-flex tw-items-end tw-justify-start tw-rounded-t-[40px]"
            >
              <h3
                class="tw-text-2xl md:tw-text-3xl tw-mb-0 tw-text-[#2C005B] tw-font-light tw-text-center tw-pb-4 tw-pl-0"
              >
                See how Twimo <span class="tw-font-bold">compares</span>
              </h3>
            </div>
            <div class="space-y-2 features-list-1">
              <BookingFeatures :features="feature_list" />
            </div>
          </div>
          <div class="tw-w-1/4">
            <div
              class="tw-h-[4.85rem] tw-bg-[#2C005BCC] tw-flex tw-items-center tw-justify-center tw-rounded-t-[40px] tw-border-b-[5px] tw-border-[#360877CC]"
            >
              <img src="@/assets/twimo-white.png" class="tw-max-w-[90px] lg:tw-max-w-[110px]" />
            </div>
            <div class="space-y-2 features-list-2">
              <BookingFeatures :features="twimo_features" />
            </div>
          </div>
          <div class="tw-w-1/4">
            <div
              class="tw-min-h-15 tw-bg-[#2C005B80] tw-flex tw-items-center tw-justify-center tw-rounded-t-[40px] tw-border-b-[5px] tw-border-[#36087759] tw-pt-[14px] tw-pb-[10px]"
            >
              <h3
                class="tw-text-sm md:tw-text-base tw-mb-0 tw-text-white tw-font-bold tw-text-center tw-pl-4 tw-pr-4"
              >
                Airbnb, VRBO, Property Management Companies
              </h3>
            </div>
            <div class="tw-space-y-2 features-list-3">
              <BookingFeatures :features="other_features" />
            </div>
          </div>
        </div>
      </div>
    </section>

    <section
      id="pricing"
      class="tw-pb-20 tw-pt-10 pricing_bg tw-bg-[#F5F5F566] tw-rounded-t-[50px]"
    >
      <div class="tw-container tw-mx-auto tw-px-5 tw-max-w-[1000px]">
        <h4
          class="tw-text-[#2C005B] tw-text-sm tw-text-left md:tw-text-left tw-uppercase tw-font-bold tw-mb-0 animate"
        >
          PRICING
        </h4>
        <p
          class="tw-text-[#2C005B] tw-text-3xl tw-text-left md:tw-text-left tw-font-medium animate"
        >
          Add Your Home for <strong class="tw-font-bold">Free</strong>
        </p>

        <div
          class="tw-flex tw-gap-8 tw-mt-7 animate tw-flex-col md:tw-flex-row tw-justify-center"
        >
          <div class="tw-mr-0 md:tw-mr-10">
            <Plan title="Twimo Host Account" price="" :features="plan_features" />
          </div>
          <div class="tw-flex-1 animate tw-mt-2">
            <h4
              class="tw-text-[#2C005B] tw-text-left tw-text-2xl md:tw-text-3xl tw-font-[500] tw-mb-3 md:tw-mb-5 tw-mt-2"
            >
            Join the Private Network Today
            </h4>
            <h3
              class="tw-text-[#2C005B] tw-text-left tw-text-2xl md:tw-text-3xl tw-font-[500] tw-mb-5 md:tw-mb-5 tw-mt-2"
            >
            $14/month or $144/annually
            </h3>
            <h3
              class="tw-text-[#2C005B] tw-hidden tw-text-left tw-text-2xl md:tw-text-3xl tw-font-[700] tw-mb-10 md:tw-mb-5 tw-mt-2"
            >
              Sign up today, 1 year for only $99!
            </h3>

            <p
              class="tw-text-xl tw-left-6 md:tw-text-lg tw-text-[#2C005B] tw-max-w-[500px] tw-mr-0 tw-ml-0 tw-mb-12 tw-font-light"
            >
              All Twimo Hosts will be able to sign up for free! We want you to be able to explore
              the program and add your first home before collecting payment. Cancel at anytime.
            </p>
            <div class="tw-text-center md:tw-text-left">
              <AnchorLinkStyleOne text="Sign up now" link="/signup" font_size="tw-text-base" />
            </div>
          </div>
        </div>
        <!-- <div
					class="tw-flex tw-flex-wrap tw-mx-auto tw-max-w-[600px] md:tw-flex-nowrap tw-gap-9 tw-justify-center animate"
				> -->
        <!-- </div> -->
      </div>
    </section>

    <section
      :style="{
        background: `linear-gradient(to right, #C2C0D1, #ffffff)`,
      }"
      class="tw-py-16 tw-pt-[4rem] tw-bg-[#F2F2F2] tw-bg-center tw-bg-no-repeat tw-bg-cover"
    >
      <div class="tw-container tw-mx-auto tw-px-5 animate">
        <p
          class="tw-text-center tw-text-xl tw-text-[Poppins] tw-left-6 md:tw-text-2xl tw-text-[#2C005B] tw-mr-auto tw-ml-auto tw-mb-5 tw-font-medium"
        >
          Want to look for Twimo homes to book? You can explore
          <br class="tw-hidden md:tw-block" />
          homes and sign up for a traveler account at no cost.
        </p>

        <div
          class="tw-flex tw-flex-wrap tw-mx-auto tw-max-w-[600px] md:tw-flex-nowrap tw-gap-9 tw-justify-center"
        >
          <AnchorLinkStyleOne text="Explore Homes" link="/explore" font_size="tw-text-base" />
        </div>
      </div>
    </section>
  </main>
</template>
<style scoped>
.home__page--banner .v-input,
.home__page--banner button {
  border-radius: 15px !important;
}
.about__page__banner {
  /* background-image: url('~/assets/about-banner-image.jpg'); */
  background-position: center center;
  background-size: 2200px;
  background-repeat: no-repeat;
  position: relative;
}
.about__page__banner:before{
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.1);
  width: 100%;
  height: 100%;
}
.about__page__banner--inner {
  min-height: 600px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: start;
  gap: 20px;
  padding-top: 25vh;
  position: relative;
  z-index: 2;
}
.about__page__banner--title {
  font-size: 1.8rem;
  color: white;
  font-weight: 500;
  line-height: normal;
  /*text-shadow: 0 2px 3px #0000008f;*/
}
.about__page__banner--title strong {
  font-weight: 700;
  font-size: 3rem;
  display: block;
  margin-top: 10px;
}
.about__page__banner--subtitle {
  font-size: 18px;
  line-height: 20px;
  color: white;
}
.about__page__banner--button,
.contact__form--button {
  background-color: #7c0cb1;
  border-radius: 50px;
  padding: 12px 35px;
  font-weight: 800;
  color: white;
  transition: all 300ms ease;
  display: inline-block;
  cursor: pointer;
}
.about__page__banner--button:hover,
.contact__form--button:hover {
  background-color: white;
  color: #7c0cb1;
}
.about__twimo__section {
  padding: 100px 0;
}

.home__page--banner {
  /* background: url('~/assets/home/<USER>'); */
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
  padding: 50px 0 0px;
}
.home__page__banner--inner {
  display: flex;
  flex-direction: column;
  align-items: start;
  min-height: 600px;
  justify-content: end;
  gap: 30px;
}
.home__banner--title {
  color: white;
  font-size: 55px;
  line-height: 55px;
  font-weight: bold;
}
.banner__button {
  background-color: #7c0cb1;
  border-radius: 50px;
  padding: 12px 35px;
  font-weight: 800;
  /* border: 2px solid #7C0CB1; */
  color: white;
  transition: all 300ms ease;
  display: inline-block;
}
.banner__button:hover {
  background-color: white;
  color: #7c0cb1;
}
.home__content__section--inner {
  padding-top: 60px;
  padding-bottom: 60px;
  max-width: 900px;
}
.never__double--book {
  font-size: 30px;
  font-weight: 800;
  margin-bottom: 10px;
  text-align: center;
  margin-left: auto;
  margin-right: auto;
  line-height: normal;
}
.never__double--content {
  color: #7b7b7b;
  font-size: 18px;
  text-align: center;
  line-height: 24px;
  width: 60%;
  margin: auto;
  font-weight: 300;
}
.content__section1,
.content__section2 {
  padding: 70px 0;
}
.swap__section_home {
  background: linear-gradient(to right, #480a83, #5d0d82);
  padding: 40px 0 0 0;
}
.swap__section--inner {
  display: flex;
  gap: 20px;
}
.swap__section__inner--col1 {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 15px;
}
.swap__section__inner--col1 h2 {
  font-weight: 800;
  font-size: 40px;
  color: white;
  line-height: 35px;
  margin: 0;
}
.swap__section__inner--col1 input {
  padding: 10px 15px;
  outline: none;
  border-radius: 10px;
  border: none;
  font-size: 20px;
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
}
.swap__section__inner--col1 {
  width: 40%;
}
.swap__section__inner--col2 {
  width: 30%;
}
.swap__section__inner--col3 {
  width: 30%;
}
.swap__section__inner--col2 img {
  display: block;
  min-height: 500px;
  object-fit: cover;
  object-position: bottom;
}
.swap__section__inner--col3 {
  display: flex;
  align-items: end;
  padding-bottom: 10px;
  color: white;
}
.swap__section__inner--col3 p {
  margin-bottom: 0;
}
.essential__tools__section {
  padding: 100px 0;
}
.essential__tools__section--inner,
.why__twimo__section--inner {
  max-width: 900px;
}
.essential__tools__section--inner .image__content--container:nth-of-type(2) {
  margin: 100px 0 50px;
}
.title {
  margin-bottom: 30px;
  color: #7b7b7b;
  font-weight: 800;
}
.integrated__platforms__section {
  /* 		background: linear-gradient(to right, #480A83 ,#5D0D82);
 */ /* background: url('~/assets/platforms-logo-bg.png'); */
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.4)), rgba(44, 0, 91, 0.5);
  background-size: cover;
  padding: 20px 0 0 0;
  position: relative;
  background-position: center bottom;
  background-size: cover;
  margin-top: 50px;
}

.integrated__platforms__section--title {
  font-size: 1.1rem;
  line-height: 40px;
  color: white;
  font-weight: 500;
  text-align: center;
  width: 75%;
  margin: auto;
}
.integrated__platforms__section--slider {
  margin-top: 10px;
  display: flex;
  flex-wrap: nowrap;
  max-width: 1200px;
  min-width: 1200px;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 20px;
  gap: 15px;
}
.integrated__platforms__section--slider img {
  /* aspect-ratio:3 / 2; */
  max-width: 140px;
  max-height: 150px;
  object-fit: contain;
}
.home__testimonil__section {
  padding: 80px 0 100px;
}
.home__testimonil__section--title {
  font-weight: 800;
  color: #7b7b7b;
  margin-bottom: 30px;
  font-size: 40px;
}
.testimonials_container {
  display: flex;
  gap: 30px;
  flex-wrap: wrap;
}
.why__twimo__section {
  background:
    linear-gradient(to left, #dfdfdfcc, #fbfbfbcc),
    url('~/assets/home/<USER>');
  padding: 100px 0;
  background-position: bottom right;
  background-size: contain;
  background-repeat: no-repeat;
}
.why__twimo__section--content {
  color: #434343;
  font-weight: 400;
  width: 60%;
  margin-bottom: 20px;
}
.why__twimo--button {
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
}
.host__pricing__section {
  padding: 90px 0 100px;
}
.host__pricing__section--inner {
  display: flex;
  gap: 30px;
  justify-content: space-between;
}
.host__pricing__section--left,
.host__pricing__section--right {
  width: 50%;
}
.host__pricing__section--right {
  padding: 40px;
  background: linear-gradient(to right, #480a83, #5d0d82);
  border-radius: 25px;
  color: #fff;
}
.host__pricing__section--title {
  font-weight: 300;
  font-size: 35px;
  font-weight: 800;
  margin-bottom: 0;
  background-image: linear-gradient(to right, #5d0d82cc, #32076e);
  color: transparent;
  background-clip: text;
}
.host__pricing__section--subtitle {
  color: #7b7b7b;
  font-weight: 300;
  font-size: 30px;
  margin-bottom: 30px;
}
.host__pricing__section--left p {
  font-size: 18px;
  line-height: 22px;
  color: #7b7b7b;
  padding-right: 15%;
}
.pricing_title {
  font-size: 35px;
  margin-bottom: 50px;
  line-height: 40px;
  font-weight: 300;
}
.pricing_title strong {
  font-weight: 700;
}
.pricing_subtitle {
  font-size: 25px;
  font-weight: 300;
  margin-bottom: 15px;
}
.pricing_includes {
  padding: 0;
  margin: 0;
  columns: 2;
  line-height: 22px;
  list-style-position: inside;
}
.pricing_includes li {
  margin-bottom: 10px;
  background: url(~/assets/home/<USER>
  list-style-type: none;
  padding: 0px 0px 10px 30px;
  font-weight: 300;
}
.pricing__button {
  margin-top: 40px;
  border: none;
  background-color: white;
  color: #7c0cb1;
}
.pricing__button:hover {
  background-color: #7c0cb1;
  color: #fff;
}

.sticky_content_container {
  display: flex;
  gap: 50px;
  justify-content: space-between;
  align-items: flex-start;
  padding-top: 5rem;
  padding-bottom: 5rem;
}
.sticky_content_container .image__content__container--image,
.sticky_content_container .image__content__container--content {
  width: 50%;
}

.sticky_content_container .content_left {
  position: sticky;
  top: 8rem;
}

.sticky_content_container .content_left .image {
  transition: all 0.3s;
}

.sticky_content_container .content_left .image.scrolled {
  transform: scale(1.1);
  opacity: 0;
}

.sticky_content_container .content_left .image:first-child {
  width: 100%;
  object-fit: cover;
  border-radius: 20px;
  position: absolute;
  margin-top: 15px;
  margin-left: -15px;
}
.sticky_content_container .image__content__container--content {
  padding: 0 20px;
  color: #7b7b7b;
}
.sticky_content_container .image__content__container--content > div {
  margin-top: 5rem;
  padding-bottom: 8rem;
}
.sticky_content_container .image__content__container--content > div:last-child {
  padding-bottom: 4rem;
}

.about__page__banner video {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100vw;
  height: 100%;
  object-fit: cover;
  transform: translate(-50%, -50%);
  z-index: 1;
}

.logos-container {
  position: relative;
  overflow: hidden;
  width: 100%;
}

.integrated__platforms__section--slider {
  white-space: nowrap;
  animation: slide 35s linear infinite;
}

.integrated__platforms__section--slider img {
  min-height: 50px;
}

@keyframes slide {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-100%);
  }
}

.features-column > div:first-child {
  display: none;
}

>>> .features-list-1 > div > div {
  margin-bottom: 8px;
  box-shadow: 0px 4px 4px #2c005b42;
  /* background: #5d0d8257; */
  background-color: #2c005b;
  justify-content: start !important;
}

>>> .features-list-1 > div > div {
  border-top-left-radius: 0.75rem;
  border-bottom-left-radius: 0.75rem;
}

>>> .features-list-2 > div > div {
  margin-bottom: 8px;
  box-shadow: 0px 2px 2px rgba(54, 8, 119, 0.26);
  background: #2c005bcc;
}

>>> .features-list-3 > div > div {
  margin-bottom: 8px;
  box-shadow: 0px 2px 2px rgba(54, 8, 119, 0.26);
  background: #2c005b66;
}

.quick_info_container {
  /* margin-top: -3rem; */
  padding-bottom: 4rem !important;
  position: relative;
}

/* .quick_info_container:after {
		background: #f6f6f6;
		transform: skewY(4deg);
		position: absolute;
		left: 0px;
		bottom: -70px;
		content: ' ';
		width: 100%;
		height: 150px;
		z-index: 1;
	} */

/* .main_content_area {
		background: rgb(121,121,121);
    background: linear-gradient(129deg, rgba(121,121,121,0) 66%, rgba(76,9,127,0.15732230392156865) 100%);
		background: rgb(76, 9, 127);
		background: linear-gradient(
			180deg,
			rgba(76, 9, 127, 0.03547356442577032) 22%,
			rgba(76, 11, 127, 0.04667804621848737) 50%,
			rgba(77, 13, 127, 0.17) 74%,
			rgba(255, 255, 255, 1) 100%
		);
		padding-bottom: 10rem !important;
	} */

#why_twimo_v2 {
  /* background: linear-gradient(
			345deg,
			rgba(255, 255, 255, 1) 17%,
			rgb(76 9 127 / 9%) 48%,
			rgba(255, 255, 255, 1) 72%
		); */
  position: relative;
}

/* #why_twimo_v2::after {
		background: #f2f2f2;
		transform: skewY(4deg);
		position: absolute;
		left: 0px;
		bottom: -45px;
		content: ' ';
		width: 100%;
		height: 12%;
		z-index: 1;
	} */

.features-container,
.testimonial_container {
  position: relative;
}

/* .testimonial_container:before {
		background: #f2f2f2;
		transform: skewY(3deg);
		position: absolute;
		left: 0px;
		top: -45px;
		content: ' ';
		width: 100%;
		height: 113%;
		z-index: 1;
	} */

.testimonial_container > div {
  position: relative;
  z-index: 2;
}

.pricing_bg {
  /* background: rgb(121, 121, 121);
		background: radial-gradient(
			circle,
			rgba(121, 121, 121, 0) 57%,
			rgba(76, 9, 127, 0.15732230392156865) 100%
		); */
  position: relative;
}

/* .pricing_bg::after {
		background: #f2f2f2;
		transform: skewY(3deg);
		position: absolute;
		left: 0px;
		top: -45px;
		content: ' ';
		width: 100%;
		height: 10%;
		z-index: 1;
	} */

/* .pricing_bg::before {
		background: #f2f2f2;
		transform: skewY(3deg);
		position: absolute;
		left: 0px;
		bottom: -5%;
		content: ' ';
		width: 100%;
		height: 8%;
		z-index: 1;
	} */

.animate {
  opacity: 0;
  transform: translateY(50px);
  transition:
    opacity 0.6s ease,
    transform 0.6s ease;
}

.animate.visible {
  opacity: 1;
  transform: translateY(0);
}

@media screen and (max-width: 600px) {
  .about__page__banner--title {
    font-size: 26px;
    margin-top: 40px;
  }

  .about__page__banner:before {
    position: absolute;
    top: 0px;
    left: 0px;
    width: 100%;
    height: 100%;
    background: #00000050;
  }

  .about__page__banner--title br {
    display: none;
  }

  .about__page__banner--title strong {
    font-size: 2.5rem;
  }

  .integrated__platforms__section--title {
    line-height: 28px;
  }

  .about__page__banner--inner img {
    margin-top: 2rem;
    max-width: 180px;
  }

  .sticky_content_container {
    display: block;
  }

  .sticky_content_container .image__content__container--image,
  .sticky_content_container .image__content__container--content {
    width: 100%;
  }

  .sticky_content_container .content_left {
    position: static;
  }

  .swap__section--inner {
    max-width: 80%;
  }

  .essential__tools__section {
    max-width: 90%;
    margin-left: auto;
    margin-right: auto;
    padding: 50px 0px;
  }

  .integrated__platforms__section--slider {
    justify-content: space-around !important;
    animation: slide 25s linear infinite;
  }

  .integrated__platforms__section--title {
    font-size: 15px;
    line-height: 22px;
    padding-left: 15px;
    padding-right: 15px;
  }
  .tw-container {
    padding: 0px 20px;
  }
  .sticky_content_container {
    padding-bottom: 0px;
  }
  .sticky_content_container .image {
    position: static !important;
    margin: auto !important;
    width: 90% !important;
  }
  .sticky_content_container .content_left #image_2 {
    display: none;
  }

  #why_twimo_v2::after {
    bottom: -5%;
    height: 8%;
  }
}

@media screen and (min-width: 768px) {
  .essential__tools__section--inner .image__content--container:nth-of-type(2) {
    flex-direction: row-reverse;
  }
  .why__twimo__section--content {
    width: 60%;
  }
}
@media screen and (max-width: 1024px) {
  .swap__section__inner--col2 img {
    min-height: 380px;
    max-width: 500px;
    display: block;
    margin: auto;
  }
  .home__banner--title {
    color: white;
    font-size: 45px;
    line-height: 45px;
  }
}
@media screen and (max-width: 767px) {
  .pricing_includes {
    columns: 1;
  }
  .host__pricing__section--inner {
    flex-direction: column;
  }
  .host__pricing__section--inner > div {
    width: 100%;
  }
  .host__pricing__section--left,
  .host__pricing__section--right {
    width: 50%;
  }
  .integrated__platforms__section--title,
  .why__twimo__section--content {
    width: 100%;
  }
  .content__section1,
  .content__section2 {
    padding: 40px 0;
  }
  .never__double--content {
    width: 100%;
  }
  .swap__section--inner {
    flex-direction: column;
  }
  .swap__section--inner > div {
    width: 100%;
  }
  .swap__section_home {
    padding: 50px 0;
  }
}
</style>
