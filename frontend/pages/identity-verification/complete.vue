<template>
  <div class="tw-flex tw-flex-col tw-items-center tw-justify-center tw-min-h-screen tw-py-12 tw-px-4">
    <div class="tw-max-w-md tw-w-full tw-bg-white tw-shadow-lg tw-rounded-lg tw-p-8 tw-text-center">
      <div v-if="verificationStatus === 'loading'">
        <div class="tw-flex tw-justify-center tw-mb-6">
          <div class="tw-relative">
            <v-progress-circular indeterminate color="primary" size="64" />
            <div class="tw-absolute tw-inset-0 tw-flex tw-items-center tw-justify-center">
              <v-icon color="primary" size="24">mdi-shield-check</v-icon>
            </div>
          </div>
        </div>
        <h1 class="tw-text-2xl tw-font-bold tw-text-primary tw-mb-4">Verifying Your Identity</h1>
        <p class="tw-text-zinc-600 tw-mb-4">
          Please wait while we confirm your verification status...
        </p>
        <div class="tw-bg-blue-50 tw-border tw-border-blue-200 tw-rounded-lg tw-p-4 tw-text-left tw-mb-4">
          <div class="tw-flex tw-items-start">
            <v-icon color="info" class="tw-mr-3 tw-mt-0.5">mdi-information-outline</v-icon>
            <p class="tw-text-zinc-700 tw-text-sm">
              This page will automatically update when verification is complete.
              <span class="tw-block tw-mt-1">Please do not close this page.</span>
            </p>
          </div>
        </div>
        <div class="tw-flex tw-justify-center tw-items-center tw-gap-2 tw-text-zinc-500 tw-text-sm">
          <v-progress-circular indeterminate color="primary" size="16" />
          <span>Checking verification status...</span>
        </div>
      </div>

      <div v-else-if="verificationStatus === 'verified'">
        <div class="tw-bg-green-50 tw-rounded-full tw-h-24 tw-w-24 tw-flex tw-items-center tw-justify-center tw-mx-auto tw-mb-6">
          <v-icon color="success" size="64">mdi-check-circle</v-icon>
        </div>
        <h1 class="tw-text-2xl tw-font-bold tw-text-green-600 tw-mb-4">Verification Complete!</h1>
        <p class="tw-text-zinc-600 tw-mb-6">
          Thank you for verifying your identity. Your account has been updated.
        </p>
        <GoodButton class="tw-bg-primary tw-px-8 tw-py-3" @click="redirectToOriginalPage">
          Continue to Twimo
        </GoodButton>
      </div>

      <div v-else-if="verificationStatus === 'failed'">
        <div class="tw-bg-red-50 tw-rounded-full tw-h-24 tw-w-24 tw-flex tw-items-center tw-justify-center tw-mx-auto tw-mb-6">
          <v-icon color="error" size="64">mdi-alert-circle</v-icon>
        </div>
        <h1 class="tw-text-2xl tw-font-bold tw-text-red-600 tw-mb-4">Verification Failed</h1>
        <p class="tw-text-zinc-600 tw-mb-6">
          We couldn't verify your identity. This could be due to image quality or information mismatch.
        </p>
        <div class="tw-flex tw-flex-col sm:tw-flex-row tw-gap-4 tw-justify-center">
          <GoodButton class="tw-bg-primary tw-px-8" @click="retryVerification">
            Try Again
          </GoodButton>
          <GoodButtonReverted @click="redirectToOriginalPage">
            Go Back
          </GoodButtonReverted>
        </div>
      </div>

      <div v-else>
        <div class="tw-bg-amber-50 tw-rounded-full tw-h-24 tw-w-24 tw-flex tw-items-center tw-justify-center tw-mx-auto tw-mb-6">
          <v-icon color="warning" size="64">mdi-help-circle</v-icon>
        </div>
        <h1 class="tw-text-2xl tw-font-bold tw-text-amber-600 tw-mb-4">Verification Status Unknown</h1>
        <p class="tw-text-zinc-600 tw-mb-6">
          We couldn't determine the status of your verification. This might be a temporary issue.
        </p>
        <div class="tw-flex tw-flex-col sm:tw-flex-row tw-gap-4 tw-justify-center">
          <GoodButton class="tw-bg-primary tw-px-8" @click="checkVerificationStatus">
            Check Status Again
          </GoodButton>
          <GoodButtonReverted @click="redirectToOriginalPage">
            Go Back
          </GoodButtonReverted>
        </div>
      </div>
    </div>

    <div class="tw-mt-8 tw-text-center tw-text-zinc-500">
      <p>Need help? Contact support at <a href="mailto:<EMAIL>" class="tw-text-primary"><EMAIL></a></p>
    </div>
  </div>
</template>

<script>
import { defineComponent, ref, onMounted, onBeforeUnmount } from '@nuxtjs/composition-api'

import { useVerificationStore } from '~/composables/useVerificationStore'
import { useToast, useApi } from '~/composables/useCommon'
import GoodButton from '~/components/GoodButton.vue'
import GoodButtonReverted from '~/components/GoodButtonReverted.vue'

export default defineComponent({
  name: 'IdentityVerificationComplete',

  components: {
    GoodButton,
    GoodButtonReverted
  },

  setup() {
    const verificationStore = useVerificationStore()
    const api = useApi()
    const toast = useToast()

    const verificationStatus = ref('loading')
    const pollInterval = ref(null)
    const pollCount = ref(0)
    const maxPolls = 10 // Maximum number of polling attempts
    const pollDelay = 2000 // 2 seconds between polls

    // Log for debugging
    const logEvent = (message, data = {}) => {
      console.log(`[IdentityVerification] ${message}`, data)
    }

    // Start polling for verification status
    const startPolling = () => {
      logEvent('Starting verification status polling')

      if (pollInterval.value) {
        clearInterval(pollInterval.value)
      }

      pollCount.value = 0

      // First check immediately
      checkVerificationStatus(false)

      // Then set up interval for repeated checks
      pollInterval.value = setInterval(async () => {
        pollCount.value++
        logEvent(`Polling attempt ${pollCount.value}/${maxPolls}`)

        // Check verification status (silent = true to avoid showing errors for routine checks)
        await checkVerificationStatus(true)

        // If we've reached max polls and still not verified, show unknown status
        if (pollCount.value >= maxPolls && verificationStatus.value !== 'verified') {
          logEvent('Max polling attempts reached without verification')
          clearInterval(pollInterval.value)

          // Only change to unknown if we're still in loading state
          if (verificationStatus.value === 'loading') {
            verificationStatus.value = 'unknown'
          }
        }
      }, pollDelay)
    }

    // Check verification status
    const checkVerificationStatus = async (silent = false) => {
      if (!silent) {
        logEvent('Manually checking verification status')
        verificationStatus.value = 'loading'
      } else {
        logEvent('Silently checking verification status')
      }

      try {
        // First try the direct API check
        const isVerified = await verificationStore.checkVerificationStatus(api)

        if (isVerified) {
          logEvent('Verification confirmed!')
          verificationStatus.value = 'verified'

          // Clear polling interval
          if (pollInterval.value) {
            clearInterval(pollInterval.value)
            pollInterval.value = null
          }

          if (!silent) {
            toast.success('Identity verification confirmed!').goAway(3000)
          }

          // Auto-redirect after a short delay
          setTimeout(() => {
            redirectToOriginalPage()
          }, 3000)

          return true
        }

        // If we're doing a manual check and not verified, show unknown status
        if (!silent && verificationStatus.value === 'loading') {
          verificationStatus.value = 'unknown'
        }

        return false
      } catch (error) {
        logEvent('Error checking verification status', { error: error.message })

        // Only update UI state and show error for manual checks
        if (!silent) {
          verificationStatus.value = 'failed'
          toast.error('Failed to check verification status').goAway(3000)
        }

        return false
      }
    }

    // Retry verification
    const retryVerification = async () => {
      logEvent('Retrying verification')

      try {
        // Create a new verification session
        await verificationStore.createVerificationSession(api)

        // If we have a URL, redirect to it
        if (verificationStore.verificationSessionUrl) {
          window.location.href = verificationStore.verificationSessionUrl
        } else {
          toast.error('Could not create a new verification session').goAway(3000)
        }
      } catch (error) {
        logEvent('Error retrying verification', { error: error.message })
        toast.error('Failed to start a new verification session').goAway(3000)
      }
    }

    // Redirect to the original page
    const redirectToOriginalPage = () => {
      logEvent('Redirecting to original page')

      // Try to get the home ID from the store
      const homeId = verificationStore.currentHomeId

      if (homeId) {
        logEvent('Redirecting to home page with ID', { homeId })
        window.$nuxt.$router.push(`/home/<USER>
      } else {
        // Default fallback - go to profile or home page
        logEvent('No specific redirect target, using default')
        window.$nuxt.$router.push('/profile')
      }
    }

    // Lifecycle hooks
    onMounted(() => {
      logEvent('Completion page mounted')

      // Start polling for verification status
      startPolling()

      // Also check immediately
      checkVerificationStatus()
    })

    onBeforeUnmount(() => {
      logEvent('Cleaning up')
      if (pollInterval.value) {
        clearInterval(pollInterval.value)
      }
    })

    return {
      verificationStatus,
      checkVerificationStatus,
      retryVerification,
      redirectToOriginalPage
    }
  }
})
</script>
