<script lang="ts">
// @ts-nocheck
import { defineComponent, ref, computed } from '@nuxtjs/composition-api'

import alreadyLoggedIn from '~/middleware/alreadyLoggedIn'
import AuthSignupForm from '~/components/AuthSignupForm.vue'
import { useAuthStore } from '~/composables/useAuthStore'

export default defineComponent({
  name: 'Signup',

  components: { AuthSignupForm },

  middleware: [alreadyLoggedIn],

  setup() {
    const authStore = useAuthStore()
    const userType = ref('')
    const isUserTypeSet = ref(false)

    // Check if we're coming from a protected page
    const isFromProtectedPage = computed(() => {
      const redirectPath = authStore.getRedirectPath()
      return redirectPath?.includes('/protected')
    })

    // If from protected page, auto-set to traveler
    if (isFromProtectedPage.value) {
      isUserTypeSet.value = true
    }

    const setUserType = type => {
      userType.value = type
      isUserTypeSet.value = true
    }

    return {
      userType,
      isUserTypeSet,
      setUserType,
      isFromProtectedPage,
    }
  },
})
</script>

<template>
  <v-row
    no-gutters
    class="tw-bg-[url('~/assets/signup_background_v2.webp')] tw-bg-cover tw-bg-center tw-min-h-[100vh]"
    style="background: rgba(0, 0, 0, 0.4); background-blend-mode: multiply"
  >


    <v-col
      cols="12"
      md="6"
      class="tw-flex tw-flex-col tw-justify-start md:tw-justify-end tw-items-left tw-p-8 sm:tw-gap-5 tw-gap-1 sm:tw-pt-[calc(12.5%+90px)]"
    >
      <h1 class="tw-font-bold tw-text-[2rem] md:tw-text-5xl tw-text-white">Welcome to Twimo!</h1>
      <template v-if="!isFromProtectedPage">
        <h2 class="tw-font-light tw-text-xl md:tw-text-2xl tw-text-white tw-mt-0 tw-pt-0">
          To Sign Up, Please Select Your Account Type
        </h2>

        <v-row
          class="tw-max-w-[600px] tw-block md:tw-flex tw-pb-[1rem] md:tw-pb-[10rem] tw-pt-[1rem]"
        >
          <v-col cols="12" md="7">
            <div
              class="tw-bg-[#7C0CB1AA] hover:tw-bg-[#7C0CB1] signup-opt-btn tw-text-white tw-rounded-[5rem] tw-px-8 tw-py-3 tw-cursor-pointer tw-text-[16px]"
              :class="userType == 'host' || userType.length === 0 ? 'active' : 'inactive'"
              @click="setUserType(`host`)"
            >
              <strong class="tw-font-bold tw-text-[18px]">I'm a Vacation Home Owner</strong>
              <br />
              Host Account
            </div>
          </v-col>
          <v-col cols="12" md="5">
            <div
              class="tw-bg-[#7C0CB1AA] hover:tw-bg-[#7C0CB1] signup-opt-btn tw-text-white tw-rounded-[5rem] tw-px-10 tw-py-3 tw-cursor-pointer tw-text-[16px]"
              :class="userType == 'traveler' || userType.length === 0 ? 'active' : 'inactive'"
              @click="setUserType(`traveler`)"
            >
              <strong class="tw-font-bold tw-text-[18px]">I'm a Traveler </strong>
              <br />
              Guest Account
            </div>
          </v-col>
        </v-row>
      </template>
      <template v-else>
        <h2 class="tw-font-light sm:tw-text-2xl tw-text-2xl tw-text-white tw-mt-0 tw-pt-0">
          Create Your Guest Account to Access the Private Home
        </h2>
      </template>
    </v-col>

    <!-- Form -->
    <v-col
      v-if="isUserTypeSet || isFromProtectedPage"
      cols="12"
      md="6"
      class="tw-flex tw-flex-col tw-justify-start tw-items-center tw-gap-6 sm:tw-pt-[8%] sm:tw-bg-[#787777]/60 tw-px-6 sm:tw-rounded-lg tw-pb-6"
    >
      <div class="tw-w-full tw-flex tw-justify-center">
        <div class="sm:tw-w-[80%] tw-w-full">
          <div class="tw-text-center tw-text-white tw-text-2xl">
            <strong>{{
              isFromProtectedPage ? 'Guest' : userType == 'host' ? 'Host' : 'Guest'
            }}</strong>
            Sign Up
          </div>
          <div class="tw-flex tw-justify-center tw-pb-2">
            <img :src="require('~/assets/plain_logo.webp')" width="100" height="60" alt="twimo" />
          </div>

          <AuthSignupForm
            :user-type="isFromProtectedPage ? 'traveler' : userType"
            :is-signup-page="true"
          />
        </div>
      </div>
    </v-col>
  </v-row>
</template>

<style scoped>
.signup-opt-btn {
  background: conic-gradient(
    from 90deg at 50% 50%,
    rgba(56, 11, 115, 0.9) -124.46deg,
    #7c0cb1 73.63deg,
    rgba(56, 11, 115, 0.9) 235.54deg,
    #7c0cb1 433.63deg
  );
  border-radius: 50px;
}

.signup-opt-btn:hover {
  background: conic-gradient(
    from 90deg at 50% 50%,
    #7c0cb1 -62.13deg,
    rgba(56, 11, 115, 0.9) 63.38deg,
    #7c0cb1 297.87deg,
    rgba(56, 11, 115, 0.9) 423.38deg
  );
}

.signup-opt-btn.inactive {
  opacity: 0.4;
}

:deep(.v-text-field label) {
  color: rgba(0, 0, 0, 0.6) !important;
}

:deep(.v-text-field__placeholder) {
  color: rgba(0, 0, 0, 0.6) !important;
}
</style>
