<script>
// @ts-nocheck

import { defineComponent } from '@nuxtjs/composition-api'

import hasWhiteLabelMixins from '~/mixins/hasWhiteLabel.mixins'
import UserPublicDetailComponent from '~/components/UserPublicDetailComponent.vue'

export default defineComponent({
  components: {
    UserPublicDetailComponent,
  },

  mixins: [hasWhiteLabelMixins],

  async asyncData({ $axios, params, error }) {
    try {
      const { data } = await $axios.get(`users/shared-url/${params.slug}`)

      const userInfo = {
        firstName: data.first_name,
        about: data.about,
        sharedUrl: data.shared_url,
        canonicalUrl: data.canonical_url,
        avatar: data.avatar,
        profileCompletion: data.profile_completion,
      }

      const userHomes = data.homes

      return { userPublicDetailModel: userInfo, userHomes }
    } catch (e) {
      return error({ statusCode: 404 })
    }
  },
  data() {
    return {
      userPublicDetailModel: {
        firstName: '',
        about: '',
        sharedUrl: '',
        canonicalUrl: '',
        avatar: '',
        profileCompletion: 0,
      },
      userHomes: [],
    }
  },
  head() {
    return {
      title: `Book vacation rentals with ${this.userPublicDetailModel.firstName}`,
      meta: [
        {
          hid: 'keywords',
          name: 'keywords',
          content:
            'vacation rental points, direct booking sites, book vacation homes with points, vacation rentals without all the fees, best home exchange website, marriott villas points, airbnb house swap, airbnb home swap, free home exchange sites, swap homes, free homeswap sites, vacation rental swap, airbnb host swap, use points on airbnb, home exchange, how to save money on airbnb, how to book vacation rentals with points, how to set up a direct booking site, can i award points on vacation rentals',
        },
        {
          hid: 'description',
          name: 'description',
          content: `About ${this.userPublicDetailModel.firstName}: ${this.userPublicDetailModel.about}`,
        },
        {
          hid: 'og:url',
          property: 'og:url',
          content: `https://twimo.com/u/${this.userPublicDetailModel.sharedUrl}`,
        },
        {
          hid: 'og:title',
          property: 'og:title',
          content: `Book vacation rentals with ${this.userPublicDetailModel.firstName}`,
        },
        {
          hid: 'og:description',
          property: 'og:description',
          content: `About ${this.userPublicDetailModel.firstName}: ${this.userPublicDetailModel.about}`,
        },
        {
          hid: 'og:type',
          property: 'og:type',
          content: 'website',
        },
        {
          hid: 'og:image',
          property: 'og:image',
          content: this.userPublicDetailModel.avatar,
        },
        {
          hid: 'og:locale',
          property: 'og:locale',
          content: 'en_US',
        },
        {
          property: 'og:image:width',
          content: '740',
        },
        {
          property: 'og:image:height',
          content: '300',
        },
        {
          name: 'twitter:site',
          content: '@twimo',
        },
        {
          hid: 'twitter:url',
          name: 'twitter:url',
          content: `https://twimo.com/u/${this.userPublicDetailModel.sharedUrl}`,
        },
        {
          hid: 'twitter:title',
          name: 'twitter:title',
          content: `Book vacation rentals with ${this.userPublicDetailModel.firstName}`,
        },
        {
          hid: 'twitter:description',
          name: 'twitter:description',
          content: `About ${this.userPublicDetailModel.firstName}: ${this.userPublicDetailModel.about}`,
        },
        {
          hid: 'twitter:image',
          name: 'twitter:image',
          content: this.userPublicDetailModel.avatar,
        },
      ],
      link: [
        {
          rel: 'canonical',
          href: `https://twimo.com/u/${this.userPublicDetailModel.canonicalUrl}`,
        },
      ],
    }
  },
})
</script>

<template>
  <UserPublicDetailComponent :user-info="userPublicDetailModel" :user-homes="userHomes" />
</template>
