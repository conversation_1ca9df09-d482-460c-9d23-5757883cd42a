<script lang="ts">
// @ts-nocheck

import {
  computed,
  defineComponent,
  onMounted,
  ref,
  useStore,
  useRoute,
  useRouter,
  watch,
  nextTick,
} from '@nuxtjs/composition-api'
import { ref as vueRef } from 'vue'
import { storeToRefs } from 'pinia'


import BeastImage from '~/components/BeastImage.vue'
import GoodCard2 from '~/components/GoodCard2.vue'
import GoodButton from '~/components/GoodButton.vue'
import MyHomePageTitle from '~/components/MyHomePageTitle.vue'
import BookingTypeToggle from '~/components/BookingTypeToggle.vue'
import { useApi, useToast } from '~/composables/useCommon'
import { TOAST_DURATION } from '~/constants'
import { Booking } from '~/types'
import GoodButtonReverted from '~/components/GoodButtonReverted.vue'
import NewMessageModal from '~/components/NewMessageModal.vue'
import BookingCard from '~/components/BookingCard.vue'
import StripeIdentityModal from '~/components/StripeIdentityModal.vue'
import CancellationPolicyDialog from '~/components/CancellationPolicyDialog.vue'
import { useStripeConnectStore } from '~/composables/useStripeConnectStore'
import { useVenmoStore } from '~/composables/useVenmoStore'
import { useVerificationStore } from '~/composables/useVerificationStore'
import { useAuthStore } from '~/composables/useAuthStore'

import { formatDateRange } from '../../helpers'

// Separate composable for booking state management
const useBookingState = () => {
  const bookingRequests = ref<Booking[]>([])
  const upcomingHostBookings = ref<Booking[]>([])
  const pastHostBookings = ref<Booking[]>([])
  const cancelledBookings = ref<Booking[]>([])
  const selectedBooking = ref<Booking | null>(null)
  const isLoading = ref(true)

  return {
    bookingRequests,
    upcomingHostBookings,
    pastHostBookings,
    cancelledBookings,
    selectedBooking,
    isLoading,
  }
}

// Separate composable for modal state management
const useModalState = () => {
  const isMessageModalOpen = ref(false)
  const showApprovalDialog = vueRef(false)
  const showDenialDialog = vueRef(false)
  const showIndefiniteApprovalDialog = ref(false)
  const showCancelModal = ref(false)
  const showCancelSuccessModal = ref(false)
  const showCancellationPolicyDialog = ref(false)
  const cancellationPolicyMessage = ref('')
  const selectedBookingId = vueRef(null)
  const guestMessage = vueRef('')
  const hostCleaningFee = ref(false)
  const showStripeConnectDialog = ref(false)
  // Identity verification is now handled in the home activation flow, not in booking approval
  // This is kept for compatibility
  const showIdentityVerificationDialog = ref(false)

  return {
    isMessageModalOpen,
    showApprovalDialog,
    showDenialDialog,
    showIndefiniteApprovalDialog,
    showCancelModal,
    showCancelSuccessModal,
    showCancellationPolicyDialog,
    cancellationPolicyMessage,
    selectedBookingId,
    guestMessage,
    hostCleaningFee,
    showStripeConnectDialog,
    showIdentityVerificationDialog,
  }
}

// Separate composable for booking actions
const useBookingActions = (
  bookingState: ReturnType<typeof useBookingState>,
  modalState: ReturnType<typeof useModalState>
) => {
  const api = useApi()
  const toast = useToast()
  const store = useStore()
  const route = useRoute()
  const currentUser = computed(() => store.getters['auth/getuser'])
  const verificationStore = useVerificationStore()

  const bookingType = computed(() => {
    return route.value.query.booking_type === 'swap' ? 'SWAP' : 'RENT'
  })

  // Helper functions for booking display
  const isHost = (booking: Booking) => booking.toHome?.user_id === currentUser.value?.id

  const getBookingRole = (booking: Booking) => {
    return isHost(booking) ? 'Host' : 'Guest'
  }

  const getHomeTitle = (booking: Booking) => {
    if (bookingType.value === 'SWAP') {
      return isHost(booking) ? 'Your Home' : "Host's Home"
    }
    return isHost(booking) ? 'Your Home' : "Home You're Booking"
  }

  const getOtherHomeTitle = (booking: Booking) => {
    if (bookingType.value === 'SWAP') {
      return isHost(booking) ? "Swap Partner's Home" : 'Your Home'
    }
    return ''
  }

  // Booking fetch and filter logic
  const fetchBookings = async () => {
    try {
      const { data: bookings } = await api.get('/bookings')
      const filteredBookings = bookings
        .filter((booking: Booking) => booking.booking_type === bookingType.value)
        .sort((a: Booking, b: Booking) => b.id - a.id)

      // Filter requests
      bookingState.bookingRequests.value = filteredBookings.filter(
        (booking: Booking) =>
          ['requested', 'accepted'].includes(booking.status) &&
          (booking.toHome?.user_id === currentUser.value?.id ||
            booking.user_id === currentUser.value?.id)
      )

      // Set initial switch states
      bookingState.bookingRequests.value.forEach(booking => {
        booking.approved = booking.status === 'accepted'
        booking.denied = false
      })

      const currentDate = new Date()

      // Filter upcoming bookings
      bookingState.upcomingHostBookings.value = filteredBookings.filter(
        (booking: Booking) =>
          booking.status === 'completed' &&
          (booking.toHome?.user_id === currentUser.value?.id ||
            booking.user_id === currentUser.value?.id) &&
          new Date(booking.end_at) >= currentDate
      )

      // Filter past bookings
      bookingState.pastHostBookings.value = filteredBookings.filter(
        (booking: Booking) =>
          booking.status === 'completed' &&
          (booking.toHome?.user_id === currentUser.value?.id ||
            booking.user_id === currentUser.value?.id) &&
          new Date(booking.end_at) < currentDate
      )

      // Filter cancelled bookings
      bookingState.cancelledBookings.value = filteredBookings.filter(
        (booking: Booking) =>
          (booking.status === 'canceled' || booking.status === 'rejected') &&
          (booking.toHome?.user_id === currentUser.value?.id ||
            booking.user_id === currentUser.value?.id)
      )
    } catch (e) {
      console.error(e)
      toast.error('Failed to fetch bookings').goAway(TOAST_DURATION)
    } finally {
      bookingState.isLoading.value = false
    }
  }

  // Booking approval actions
  const approveBookingWithMessage = async () => {
    if (modalState.selectedBookingId.value) {
      try {
        // Store the booking ID before making the API call
        const bookingId = modalState.selectedBookingId.value

        // Get the current booking to check if it's a swap
        const selectedBooking = bookingState.selectedBooking.value
        const isSwap = selectedBooking && selectedBooking.booking_type === 'SWAP'

        // Get the guest cleaning fee status
        const guestCleaningFeeEnabled = selectedBooking ? selectedBooking.guest_cleaning_fee_enabled : false

        // Make the API call and get the response
        const { data: updatedBooking } = await api.put(`/bookings/${bookingId}/approve`, {
          message: modalState.guestMessage.value,
          host_cleaning_fee_enabled: modalState.hostCleaningFee.value,
        })

        // Close the dialog and reset form values
        modalState.showApprovalDialog.value = false
        modalState.guestMessage.value = ''
        modalState.hostCleaningFee.value = false

        // Check if this is a swap booking that was automatically completed
        // This happens when both hosts opt out of cleaning fees
        if (isSwap && updatedBooking.status === 'completed') {
          console.log('Swap booking was automatically completed because both hosts opted out of cleaning fees')

          // Show a more specific success message
          toast.success('Swap approved and completed without cleaning fees').goAway(TOAST_DURATION)

          // Refresh the bookings data
          await fetchBookings()

          // Return the booking data for the component to handle redirection
          return {
            bookingId,
            status: 'completed',
            endDate: updatedBooking.end_at
          }
        } else {
          // Regular success message for normal approvals
          const hasMessage = modalState.guestMessage.value.trim() !== ''
          toast.success(`Booking approved${hasMessage ? ' and message sent' : ''}`).goAway(TOAST_DURATION)

          // Just refresh the bookings without redirecting
          fetchBookings()
          return null
        }
      } catch (e) {
        console.error(e)
        toast.error('Failed to approve booking').goAway(TOAST_DURATION)
        return null
      }
    }
    return null
  }

  const approveBookingIndefinitely = async () => {
    if (modalState.selectedBookingId.value) {
      try {
        // Store the booking ID before making the API call
        const bookingId = modalState.selectedBookingId.value

        // Get the current booking to check if it's a swap
        const selectedBooking = bookingState.selectedBooking.value
        const isSwap = selectedBooking && selectedBooking.booking_type === 'SWAP'

        // Get the guest cleaning fee status
        const guestCleaningFeeEnabled = selectedBooking ? selectedBooking.guest_cleaning_fee_enabled : false

        // Make the API call and get the response
        const { data: updatedBooking } = await api.put(`/bookings/${bookingId}/approve-indefinitely`, {
          message: modalState.guestMessage.value,
          host_cleaning_fee_enabled: modalState.hostCleaningFee.value,
        })

        // Close the dialog and reset form values
        modalState.showIndefiniteApprovalDialog.value = false
        modalState.guestMessage.value = ''
        modalState.hostCleaningFee.value = false

        // Check if this is a swap booking that was automatically completed
        // This happens when both hosts opt out of cleaning fees
        if (isSwap && updatedBooking.status === 'completed') {
          console.log('Swap booking was automatically completed because both hosts opted out of cleaning fees')

          // Show a more specific success message
          toast.success('Swap approved indefinitely and completed without cleaning fees').goAway(TOAST_DURATION)

          // Refresh the bookings data
          await fetchBookings()

          // Return the booking data for the component to handle redirection
          return {
            bookingId,
            status: 'completed',
            endDate: updatedBooking.end_at
          }
        } else {
          // Regular success message for normal approvals
          const hasMessage = modalState.guestMessage.value.trim() !== ''
          toast.success(`Booking approved indefinitely${hasMessage ? ' and message sent' : ''}`).goAway(TOAST_DURATION)

          // Just refresh the bookings without redirecting
          fetchBookings()
          return null
        }
      } catch (e) {
        console.error(e)
        toast.error('Failed to approve booking indefinitely').goAway(TOAST_DURATION)
        return null
      }
    }
    return null
  }

  const rejectBookingWithMessage = async () => {
    if (modalState.selectedBookingId.value) {
      try {
        await api.put(`/bookings/${modalState.selectedBookingId.value}/reject`, {
          message: modalState.guestMessage.value,
        })
        const hasMessage = modalState.guestMessage.value.trim() !== ''
        toast.success(`Booking rejected${hasMessage ? ' and message sent' : ''}`).goAway(TOAST_DURATION)
        modalState.showDenialDialog.value = false
        modalState.guestMessage.value = ''
        fetchBookings()
      } catch (e) {
        console.error(e)
        toast.error('Failed to reject booking').goAway(TOAST_DURATION)
      }
    }
  }

  // Identity verification is now handled in the home activation flow, not in booking approval
  const checkIdentityVerification = async (selectedBooking: Booking) => {
    console.log('Identity verification is now handled in home activation flow, skipping check')
    return true
  }

  // Dialog open handlers
  const openApprovalDialog = async (selectedBooking: Booking) => {
    // Always set the selected booking first so we have it available for the payment method dialog
    bookingState.selectedBooking.value = selectedBooking

    // Check if payment methods are set up for payment processing
    const paymentStatus = await checkPaymentMethods(selectedBooking)
    if (!paymentStatus.hasRequiredPaymentMethods) {
      modalState.showStripeConnectDialog.value = true
      return
    }

    // Initialize the host cleaning fee value based on the selected booking
    modalState.hostCleaningFee.value = selectedBooking.host_cleaning_fee_enabled || false

    modalState.selectedBookingId.value = selectedBooking.id
    modalState.showApprovalDialog.value = true
  }

  const openDenialDialog = (id: number) => {
    modalState.selectedBookingId.value = id
    modalState.showDenialDialog.value = true
  }

  const openIndefiniteApprovalDialog = async (selectedBooking: Booking) => {
    // Ensure Stripe and Venmo stores are loaded
    try {
      await stripeStore.fetchStatus(api)
      await venmoStore.fetchVenmoStatus(api)
    } catch (error) {
      console.error('Error fetching payment method status:', error)
    }

    // Check if payment methods are set up for payment processing
    const paymentStatus = await checkPaymentMethods(selectedBooking)
    if (!paymentStatus.hasRequiredPaymentMethods) {
      bookingState.selectedBooking.value = selectedBooking
      modalState.showStripeConnectDialog.value = true
      return
    }

    // Set the selected booking
    bookingState.selectedBooking.value = selectedBooking

    // Initialize the host cleaning fee value based on the selected booking
    modalState.hostCleaningFee.value = selectedBooking.host_cleaning_fee_enabled || false

    modalState.selectedBookingId.value = selectedBooking.id
    modalState.showIndefiniteApprovalDialog.value = true
  }

  // This function is kept for compatibility but is no longer needed for booking approval
  // Identity verification is now handled in the home activation flow
  const handleVerificationComplete = async () => {
    // Close the modal in both places
    modalState.showIdentityVerificationDialog = false
    verificationStore.showVerificationModal = false

    console.log('Verification modal closed')

    // If a booking was selected, continue with the approval process
    if (bookingState.selectedBooking.value) {
      const selectedBooking = bookingState.selectedBooking.value

      // Ensure Stripe and Venmo stores are loaded
      try {
        await stripeStore.fetchStatus(api)
        await venmoStore.fetchVenmoStatus(api)
      } catch (error) {
        console.error('Error fetching payment method status:', error)
      }

      // Check if payment methods are set up before proceeding
      const paymentStatus = await checkPaymentMethods(selectedBooking)
      if (!paymentStatus.hasRequiredPaymentMethods) {
        modalState.showStripeConnectDialog.value = true
        return
      }

      // Show the approval dialog
      modalState.selectedBookingId.value = selectedBooking.id
      modalState.showApprovalDialog.value = true
    }
  }

  // Message handling
  const messageGuest = (booking: Booking) => {
    bookingState.selectedBooking.value = booking
    modalState.isMessageModalOpen.value = true
  }

  const handleMessageSent = () => {
    modalState.isMessageModalOpen.value = false
    bookingState.selectedBooking.value = null
    toast.success('Message sent successfully').goAway(TOAST_DURATION)
  }

  // Cancellation handling
  const cancelStay = (booking: Booking) => {
    bookingState.selectedBooking.value = booking
    modalState.showCancelModal.value = true
  }

  const confirmCancelStay = async () => {
    if (!bookingState.selectedBooking.value) return

    try {
      // Store the booking ID before making the API call
      const bookingId = bookingState.selectedBooking.value.id

      // Cancel the booking
      await api.put(`/bookings/${bookingId}/cancel`)

      // Close the cancel modal and show success modal
      modalState.showCancelModal.value = false
      modalState.showCancelSuccessModal.value = true

      // Clear the selected booking
      bookingState.selectedBooking.value = null

      // Refresh the bookings data
      await fetchBookings()

      // After cancellation, the booking will be in the "Cancelled" tab (index 3)
      const cancelledTabIndex = 3

      // First switch to the correct tab
      await switchToTab(cancelledTabIndex, { keepBookingId: false })

      // Then after a short delay to allow the tab to fully render, navigate to the specific booking
      setTimeout(() => {
        navigateToBooking(bookingId)
      }, 500)
    } catch (error) {
      console.error('Error cancelling booking:', error)

      // Check if this is a cancellation policy violation
      if (error.response?.status === 403 && error.response?.data?.error === 'CANCELLATION_POLICY_VIOLATION') {
        // Close the cancel modal
        modalState.showCancelModal.value = false

        // Show the cancellation policy dialog
        modalState.showCancellationPolicyDialog.value = true
        modalState.cancellationPolicyMessage.value = error.response.data.message
      } else {
        toast.error('Failed to cancel booking. Please try again.').goAway(TOAST_DURATION)
      }
    }
  }

  // Utility functions
  const getCleaningFeeCount = (booking: Booking) => {
    let count = 0
    if (booking.guest_cleaning_fee_enabled) count++
    if (booking.host_cleaning_fee_enabled) count++
    return count
  }

  const stripeStore = useStripeConnectStore()
  const venmoStore = useVenmoStore()

  // Enhanced payment method check that returns detailed status
  const checkPaymentMethods = async (booking: Booking) => {
    try {
      await stripeStore.fetchStatus(api)
      await venmoStore.fetchVenmoStatus(api)

      const isStripeConnected = stripeStore.status.is_connected && stripeStore.status.charges_enabled
      const isVenmoConnected = venmoStore.isConnected

      const isRentBooking = booking.booking_type === 'RENT'
      const isFromSharableLink = !!booking.from_sharable_link
      const isPublicBooking = isRentBooking && !isFromSharableLink
      const isPrivateBooking = isRentBooking && isFromSharableLink
      const isSwapBooking = !isRentBooking

      // For public bookings and swaps: require Stripe Connect
      // For private bookings: require EITHER Stripe Connect OR Venmo
      const hasRequiredPaymentMethods = isPrivateBooking
        ? (isStripeConnected || isVenmoConnected) // Private booking: either payment method is acceptable
        : isStripeConnected // Public booking or swap: only Stripe Connect is acceptable

      return {
        isStripeConnected,
        isVenmoConnected,
        isPrivateBooking,
        isPublicBooking,
        isSwapBooking,
        hasRequiredPaymentMethods,
        // Add specific missing requirements for better UI feedback
        missingRequirements: {
          needsStripeConnect: isPublicBooking || isSwapBooking ? !isStripeConnected : false,
          needsAnyPaymentMethod: isPrivateBooking && !isStripeConnected && !isVenmoConnected
        }
      }
    } catch (error) {
      console.error(error)
      toast.error('Something went wrong. Please try again.').goAway(TOAST_DURATION)
      return {
        isStripeConnected: false,
        isVenmoConnected: false,
        isPrivateBooking: false,
        isPublicBooking: false,
        isSwapBooking: false,
        hasRequiredPaymentMethods: false
      }
    }
  }

  // Backward compatibility function - now just calls the enhanced version
  const checkStripeSetup = async (booking: Booking) => {
    const status = await checkPaymentMethods(booking)
    return status.hasRequiredPaymentMethods
  }

  const confirmVenmoPayment = async (booking: Booking) => {
    try {
      // Store the booking ID before making the API call
      const bookingId = booking.id

      // Complete the booking
      await api.put(`/bookings/${bookingId}/complete`, {
        payment_method: 'venmo',
        venmo_payment_confirmed: true,
        venmo_payment_amount: booking.extra_info?.venmo_payment?.amount,
      })

      toast.success('Payment confirmed and booking completed').goAway(TOAST_DURATION)

      // Refresh the bookings data
      await fetchBookings()

      // Determine if the booking should go to upcoming or past tab based on end date
      const endDate = new Date(booking.end_at)
      const now = new Date()
      const isUpcoming = endDate >= now
      const tabIndex = isUpcoming ? 1 : 2 // 1 = upcoming, 2 = past

      // Navigate to the correct tab and highlight the booking
      console.log(`Redirecting to tab ${tabIndex} (${isUpcoming ? 'upcoming' : 'past'}) for booking ${bookingId}`)

      // First switch to the correct tab
      await switchToTab(tabIndex, { keepBookingId: false })

      // Then after a short delay, navigate to the specific booking to highlight it
      setTimeout(() => {
        navigateToBooking(bookingId)
      }, 500)
    } catch (error) {
      console.error('Error confirming payment:', error)
      toast.error('Failed to confirm payment. Please try again.').goAway(TOAST_DURATION)
    }
  }

  return {
    currentUser,
    bookingType,
    isHost,
    getBookingRole,
    getHomeTitle,
    getOtherHomeTitle,
    fetchBookings,
    approveBookingWithMessage,
    approveBookingIndefinitely,
    rejectBookingWithMessage,
    openApprovalDialog,
    openDenialDialog,
    openIndefiniteApprovalDialog,
    messageGuest,
    handleMessageSent,
    cancelStay,
    confirmCancelStay,
    getCleaningFeeCount,
    checkStripeSetup,
    confirmVenmoPayment,
    handleVerificationComplete,
    verificationStore,
  }
}

const useBookingTabs = (bookingState: ReturnType<typeof useBookingState>) => {
  const tabs = computed(() => [
    {
      id: 'requests',
      label: 'requests',
      getBookings: () => bookingState.bookingRequests.value,
      emptyMessage: 'No pending booking requests.',
    },
    {
      id: 'upcoming',
      label: 'upcoming',
      getBookings: () => bookingState.upcomingHostBookings.value,
      emptyMessage: 'No upcoming bookings.',
    },
    {
      id: 'past',
      label: 'past',
      getBookings: () => bookingState.pastHostBookings.value,
      emptyMessage: 'No past bookings.',
    },
    {
      id: 'cancelled',
      label: 'cancelled',
      getBookings: () => bookingState.cancelledBookings.value,
      emptyMessage: 'No cancelled bookings.',
    },
  ])

  const route = useRoute()
  // Initialize activeTab from URL query parameter if available
  const activeTab = ref(route.value.query.tab ? parseInt(route.value.query.tab as string) : 0)

  return {
    tabs,
    activeTab,
  }
}

export default defineComponent({
  components: {
    BeastImage,
    MyHomePageTitle,
    GoodCard2,
    GoodButton,
    GoodButtonReverted,
    NewMessageModal,
    BookingCard,
    StripeIdentityModal,
    BookingTypeToggle,
    CancellationPolicyDialog,
  },

  // We'll remove the navigation guard for now as it's causing issues
  middleware: ['auth'],
  setup() {
    const bookingState = useBookingState()
    const modalState = useModalState()
    const bookingActions = useBookingActions(bookingState, modalState)
    const authStore = useAuthStore()
    const { isTravelerType } = storeToRefs(authStore)
    const router = useRouter()
    const route = useRoute()
    const api = useApi()
    const toast = useToast()

    const { tabs, activeTab } = useBookingTabs(bookingState)

    // Create a reactive property for the booking type toggle
    const selectedBookingType = ref(bookingActions.bookingType.value)

    // Watch for changes to the selected booking type
    watch(selectedBookingType, (newType) => {
      // Only update if the value actually changed to avoid unnecessary navigation
      const currentBookingType = route.value.query.booking_type
      const newBookingTypeParam = newType === 'SWAP' ? 'swap' : 'rent'

      if (currentBookingType !== newBookingTypeParam) {
        // Prepare the query parameters
        const query = { ...route.value.query }

        // Set the booking type parameter
        query.booking_type = newBookingTypeParam

        // Remove the booking_id parameter when changing booking types
        // This prevents the system from auto-scrolling to a booking when the user is trying to switch views
        if (query.booking_id) {
          console.log(`Removing booking_id ${query.booking_id} from URL when changing booking type`)
          delete query.booking_id
        }

        // Update the URL query parameter
        router.push({ query })
      }
    })

    // Watch for changes to the booking type from URL
    watch(bookingActions.bookingType, (newType) => {
      // Update the selected booking type to match URL
      selectedBookingType.value = newType
      // Fetch bookings with the new type
      bookingActions.fetchBookings()
    })

    // Watch for changes to the active tab and update the URL
    watch(activeTab, (newTabIndex) => {
      // Prepare the query parameters
      const query = { ...route.value.query }

      // Set the tab parameter
      query.tab = newTabIndex.toString()

      // Remove the booking_id parameter when changing tabs
      // This prevents the system from auto-scrolling to a booking when the user is trying to switch tabs
      if (query.booking_id) {
        console.log(`Removing booking_id ${query.booking_id} from URL when changing tab`)
        delete query.booking_id
      }

      // Update the URL query parameter without reloading the page
      router.push({ query })
    })

    // Function to navigate to a specific booking
    // This can be called from anywhere, including templates
    const navigateToBooking = (bookingId) => {
      if (!bookingId) return

      console.log(`Navigating to booking ${bookingId}`)

      // Simply update the URL with the booking_id parameter
      // The route watcher will handle the rest
      router.push({
        query: { booking_id: bookingId }
      })
    }

    // Function to fetch a specific booking by ID and determine the correct tab and booking type
    const fetchBookingAndSetContext = async (bookingId) => {
      if (!bookingId) return

      try {
        console.log(`Fetching booking ${bookingId} and setting context`)

        // Show loading state
        bookingState.isLoading.value = true

        // Fetch the specific booking details
        const { data: booking } = await api.get(`/bookings/${bookingId}`)

        if (!booking) {
          console.error(`Booking with ID ${bookingId} not found`)
          toast.error('Booking not found').goAway(TOAST_DURATION)
          return
        }

        console.log('Fetched booking details:', booking)

        // Determine the correct booking type based on the booking
        const isSwap = booking.booking_type === 'SWAP'

        // Set the booking type - this will trigger a re-fetch of bookings
        console.log(`Setting booking type to ${isSwap ? 'SWAP' : 'RENT'}`)
        selectedBookingType.value = isSwap ? 'SWAP' : 'RENT'

        // Wait for bookings to be fetched
        console.log('Fetching bookings...')
        await bookingActions.fetchBookings()

        // Check if bookings were loaded
        const hasBookings = tabs.value.some(tab => tab.getBookings().length > 0)
        console.log(`Bookings loaded: ${hasBookings}`)

        if (!hasBookings) {
          console.log('No bookings loaded, trying to fetch bookings again...')
          // Try fetching bookings again
          await bookingActions.fetchBookings()
        }

        // Determine which tab this booking should appear in based on its status
        // 0 = requests, 1 = upcoming, 2 = past, 3 = cancelled
        let tabIndex = 0 // Default to requests tab

        console.log(`Determining tab for booking ${bookingId} with status ${booking.status}`)

        // Convert status to uppercase for case-insensitive comparison
        const status = booking.status.toUpperCase()
        console.log(`Normalized status: ${status} (original: ${booking.status})`)

        if (status === 'REQUESTED' || status === 'ACCEPTED') {
          console.log(`Booking ${bookingId} is in REQUESTED or ACCEPTED status, setting tab to 0 (Requests)`)
          tabIndex = 0 // Requests tab
        } else if (status === 'COMPLETED') {
          // If end date is in the future, it's upcoming, otherwise it's past
          const endDate = new Date(booking.end_at)
          const now = new Date()
          const isUpcoming = endDate >= now
          tabIndex = isUpcoming ? 1 : 2
          console.log(`Booking ${bookingId} is in COMPLETED status with end date ${booking.end_at}`)
          console.log(`Current date: ${now.toISOString()}, End date: ${endDate.toISOString()}`)
          console.log(`End date is ${isUpcoming ? 'in the future' : 'in the past'}, setting tab to ${tabIndex} (${isUpcoming ? 'Upcoming' : 'Past'})`)
        } else if (status === 'REJECTED' || status === 'CANCELED') {
          console.log(`Booking ${bookingId} is in REJECTED or CANCELED status, setting tab to 3 (Cancelled)`)
          tabIndex = 3 // Cancelled tab
        } else {
          console.log(`Booking ${bookingId} has unknown status ${booking.status}, defaulting to tab 0 (Requests)`)
        }

        console.log(`Setting active tab to ${tabIndex} for booking ${bookingId}`)

        // Force the tab to update
        console.log(`Forcing tab ${tabIndex} for booking ${bookingId}`)

        // Use the switchToTab function with keepBookingId=true
        await switchToTab(tabIndex, { keepBookingId: true, fromAutoNavigation: true })

        // Wait for the DOM to update
        await nextTick()

        // Double-check that the tab was set correctly
        console.log(`After tab update: activeTab.value = ${activeTab.value}`)

        // Check if the booking is visible in the current tab
        const checkBookingVisibility = () => {
          // Check if any bookings are loaded
          const hasBookings = tabs.value.some(tab => tab.getBookings().length > 0)

          if (!hasBookings) {
            console.log('No bookings loaded yet, waiting for bookings to load...')

            // Try again after a delay
            setTimeout(checkBookingVisibility, 1000)
            return
          }

          // Get the current tab's bookings
          const currentTabBookings = tabs.value[activeTab.value].getBookings()
          console.log(`Current tab (${activeTab.value}) has ${currentTabBookings.length} bookings`)

          // Check if the booking is in the current tab
          const bookingInCurrentTab = currentTabBookings.some(b => b.id.toString() === bookingId.toString())
          console.log(`Booking ${bookingId} is ${bookingInCurrentTab ? '' : 'NOT '} in the current tab (${activeTab.value})`)

          if (bookingInCurrentTab) {
            // If the booking is in the current tab, scroll to it
            console.log(`Scrolling to booking ${bookingId}`)
            scrollToBooking(bookingId)
          } else {
            // If the booking is not in the current tab, check all tabs
            console.log(`Booking ${bookingId} is not in the current tab (${activeTab.value}), checking all tabs...`)

            // Check all tabs for the booking
            let foundInTab = -1
            tabs.value.forEach((tab, index) => {
              const tabBookings = tab.getBookings()
              console.log(`Tab ${index} (${tab.id}) has ${tabBookings.length} bookings`)

              // Log all bookings in this tab
              tabBookings.forEach(b => console.log(`- Booking ${b.id} (${b.status})`))

              // Check if the booking is in this tab
              if (tabBookings.some(b => b.id.toString() === bookingId.toString())) {
                foundInTab = index
                console.log(`Found booking ${bookingId} in tab ${index} (${tab.id})`)
              }
            })

            if (foundInTab >= 0) {
              // If the booking is found in another tab, switch to that tab
              console.log(`Switching to tab ${foundInTab} where booking ${bookingId} was found`)
              switchToTab(foundInTab, { keepBookingId: true, fromAutoNavigation: true })

              // Try again after a delay to allow the tab to switch
              setTimeout(() => {
                console.log(`Checking visibility again after switching to tab ${foundInTab}`)
                checkBookingVisibility()
              }, 1000)
            } else {
              console.error(`Booking ${bookingId} not found in any tab, cannot scroll to it`)
            }
          }
        }

        // Scroll to the specific booking with a delay to ensure everything is rendered
        setTimeout(() => {
          checkBookingVisibility()
        }, 1000) // Longer delay to ensure everything is loaded
      } catch (error) {
        console.error('Error fetching booking details:', error)
        toast.error('Failed to load booking details').goAway(TOAST_DURATION)
      } finally {
        bookingState.isLoading.value = false
      }
    }

    // We'll remove the onBeforeRouteUpdate hook for now as it's causing issues
    // We'll rely on the watch for route.value.query instead

    // Watch for route changes
    watch(() => route.value.fullPath, async () => {
      console.log('Route changed:', route.value.fullPath)
      console.log('Route query parameters:', route.value.query)

      // Get the booking ID from the URL
      const bookingId = route.value.query.booking_id

      if (bookingId) {
        console.log(`Found booking_id ${bookingId} in URL`)
        try {
          // Process the booking ID
          // This will fetch the booking, determine the correct tab, and scroll to the booking
          await fetchBookingAndSetContext(bookingId)
        } catch (error) {
          console.error('Error processing booking ID:', error)
          toast.error('Failed to load booking details').goAway(TOAST_DURATION)
        }
      } else {
        // If there's a tab parameter but no booking_id, just switch to the tab
        const tabParam = route.value.query.tab
        if (tabParam) {
          const tabIndex = parseInt(tabParam as string)
          if (!isNaN(tabIndex) && tabIndex >= 0 && tabIndex < 4) { // 4 tabs total
            console.log(`Found tab ${tabIndex} in URL, switching to it`)
            // Just set the active tab directly, don't call switchToTab to avoid an infinite loop
            activeTab.value = tabIndex
          }
        }
      }
    }, { immediate: true })

    // Reference to store booking cards
    const bookingCards = ref([])

    // Track which booking is currently highlighted
    const highlightedBookingId = ref(null)

    // Function to switch to a specific tab
    const switchToTab = async (tabIndex, options = {}) => {
      if (tabIndex === undefined || tabIndex === null) return

      // Default options
      const { keepBookingId = false, fromAutoNavigation = false } = options

      console.log(`Switching to tab ${tabIndex} (keepBookingId: ${keepBookingId}, fromAutoNavigation: ${fromAutoNavigation})`)

      try {
        // Prepare the query parameters
        const query = { ...route.value.query }

        // Set the tab parameter
        query.tab = tabIndex.toString()

        // Remove the booking_id parameter if not keeping it
        // We only keep the booking_id when auto-navigating to a specific booking
        if (!keepBookingId && query.booking_id) {
          console.log(`Removing booking_id ${query.booking_id} from URL`)
          delete query.booking_id
        }

        // Update the URL to reflect the tab change
        // This ensures the URL is updated before the UI changes
        await router.push({ query })

        // Then set the active tab
        // This ensures the UI is updated after the URL change
        console.log(`Setting activeTab.value to ${tabIndex}`)
        activeTab.value = tabIndex

        // Force a DOM update
        await nextTick()

        console.log(`Tab switch to ${tabIndex} complete`)
      } catch (error) {
        console.error(`Error switching to tab ${tabIndex}:`, error)
      }
    }

    // Function to scroll to a specific booking
    const scrollToBooking = (bookingId) => {
      if (!bookingId) return

      console.log(`Scrolling to booking ${bookingId}`)

      try {
        // Find the container element by ID
        const containerElement = document.getElementById(`booking-container-${bookingId}`)

        if (containerElement) {
          console.log(`Found booking container for ${bookingId}, scrolling to it`)

          // Scroll the booking into view
          containerElement.scrollIntoView({ behavior: 'smooth', block: 'center' })

          // Set the highlighted booking ID
          highlightedBookingId.value = bookingId

          // Remove the highlight after 5 seconds
          setTimeout(() => {
            highlightedBookingId.value = null
          }, 5000)

           // Success
        } else {
          console.log(`Booking container for ${bookingId} not found, will try again`)

          // Try again after a delay
          setTimeout(() => scrollToBooking(bookingId), 1000)
        }
      } catch (error) {
        console.error('Error scrolling to booking:', error)
      }
    }

    // Watch for the Stripe Connect dialog to ensure stores are loaded
    watch(
      () => modalState.showStripeConnectDialog.value,
      async (isShown) => {
        if (isShown) {
          // Ensure Stripe and Venmo stores are loaded when dialog is shown
          try {
            console.log('Dialog shown, fetching payment method status')
            await stripeStore.fetchStatus(api)
            await venmoStore.fetchVenmoStatus(api)
          } catch (error) {
            console.error('Error fetching payment method status:', error)
          }
        }
      }
    )

    onMounted(async () => {
      console.log('Bookings page mounted')

      // The route watcher with immediate: true will handle the initial booking ID check
      // We just need to fetch bookings if there's no booking ID
      if (!route.value.query.booking_id) {
        try {
          console.log('No booking_id in URL, fetching bookings normally')
          await bookingActions.fetchBookings()
        } catch (error) {
          console.error('Error fetching bookings:', error)
          toast.error('Failed to load bookings. Please try again.').goAway(TOAST_DURATION)
        }
      }
    })

    // Handler for approving a booking
    const handleApproveBooking = async () => {
      try {
        console.log('Handling approve booking')
        const result = await bookingActions.approveBookingWithMessage()

        if (result && result.status === 'completed') {
          console.log('Booking was automatically completed, redirecting...', result)

          // Determine if the booking should go to upcoming or past tab based on end date
          const endDate = new Date(result.endDate)
          const now = new Date()
          const isUpcoming = endDate >= now
          const tabIndex = isUpcoming ? 1 : 2 // 1 = upcoming, 2 = past

          // Navigate to the correct tab and highlight the booking
          console.log(`Redirecting to tab ${tabIndex} (${isUpcoming ? 'upcoming' : 'past'}) for booking ${result.bookingId}`)

          // First switch to the correct tab
          await switchToTab(tabIndex, { keepBookingId: false })

          // Then after a short delay, navigate to the specific booking to highlight it
          setTimeout(() => {
            navigateToBooking(result.bookingId)
          }, 500)
        }
      } catch (error) {
        console.error('Error in handleApproveBooking:', error)
        toast.error('Failed to approve booking. Please try again.').goAway(TOAST_DURATION)
      }
    }

    // Handler for approving a booking indefinitely
    const handleApproveBookingIndefinitely = async () => {
      try {
        console.log('Handling approve booking indefinitely')
        const result = await bookingActions.approveBookingIndefinitely()

        if (result && result.status === 'completed') {
          console.log('Booking was automatically completed, redirecting...', result)

          // Determine if the booking should go to upcoming or past tab based on end date
          const endDate = new Date(result.endDate)
          const now = new Date()
          const isUpcoming = endDate >= now
          const tabIndex = isUpcoming ? 1 : 2 // 1 = upcoming, 2 = past

          // Navigate to the correct tab and highlight the booking
          console.log(`Redirecting to tab ${tabIndex} (${isUpcoming ? 'upcoming' : 'past'}) for booking ${result.bookingId}`)

          // First switch to the correct tab
          await switchToTab(tabIndex, { keepBookingId: false })

          // Then after a short delay, navigate to the specific booking to highlight it
          setTimeout(() => {
            navigateToBooking(result.bookingId)
          }, 500)
        }
      } catch (error) {
        console.error('Error in handleApproveBookingIndefinitely:', error)
        toast.error('Failed to approve booking indefinitely. Please try again.').goAway(TOAST_DURATION)
      }
    }

    // Create a custom implementation of confirmVenmoPayment that uses the local switchToTab and navigateToBooking functions
    const onConfirmVenmoPayment = async (booking: Booking) => {
      try {
        // Store the booking ID before making the API call
        const bookingId = booking.id

        // Complete the booking
        await api.put(`/bookings/${bookingId}/complete`, {
          payment_method: 'venmo',
          venmo_payment_confirmed: true,
          venmo_payment_amount: booking.extra_info?.venmo_payment?.amount,
        })

        toast.success('Payment confirmed and booking completed').goAway(TOAST_DURATION)

        // Refresh the bookings data
        await bookingActions.fetchBookings()

        // Determine if the booking should go to upcoming or past tab based on end date
        const endDate = new Date(booking.end_at)
        const now = new Date()
        const isUpcoming = endDate >= now
        const tabIndex = isUpcoming ? 1 : 2 // 1 = upcoming, 2 = past

        // Navigate to the correct tab and highlight the booking
        console.log(`Redirecting to tab ${tabIndex} (${isUpcoming ? 'upcoming' : 'past'}) for booking ${bookingId}`)

        // First switch to the correct tab
        await switchToTab(tabIndex, { keepBookingId: false })

        // Then after a short delay, navigate to the specific booking to highlight it
        setTimeout(() => {
          navigateToBooking(bookingId)
        }, 500)
      } catch (error) {
        console.error('Error confirming payment:', error)
        toast.error('Failed to confirm payment. Please try again.').goAway(TOAST_DURATION)
      }
    }

    // Create a custom implementation of confirmCancelStay that uses the local switchToTab and navigateToBooking functions
    const onConfirmCancelStay = async () => {
      if (!bookingState.selectedBooking.value) return

      try {
        // Store the booking ID before making the API call
        const bookingId = bookingState.selectedBooking.value.id

        // Cancel the booking
        await api.put(`/bookings/${bookingId}/cancel`)

        // Close the cancel modal and show success modal
        modalState.showCancelModal.value = false
        modalState.showCancelSuccessModal.value = true

        // Clear the selected booking
        bookingState.selectedBooking.value = null

        // Refresh the bookings data
        await bookingActions.fetchBookings()

        // After cancellation, the booking will be in the "Cancelled" tab (index 3)
        const cancelledTabIndex = 3

        // First switch to the correct tab
        await switchToTab(cancelledTabIndex, { keepBookingId: false })

        // Then after a short delay to allow the tab to fully render, navigate to the specific booking
        setTimeout(() => {
          navigateToBooking(bookingId)
        }, 500)
      } catch (error) {
        console.error('Error cancelling booking:', error)
        console.log('Error response:', error.response)
        console.log('Error status:', error.response?.status)
        console.log('Error data:', error.response?.data)

        // Check if this is a cancellation policy violation
        if (error.response?.status === 403 && error.response?.data?.error === 'CANCELLATION_POLICY_VIOLATION') {
          console.log('Cancellation policy violation detected, showing dialog')

          // Close the cancel modal
          modalState.showCancelModal.value = false

          // Show the cancellation policy dialog
          modalState.showCancellationPolicyDialog.value = true
          modalState.cancellationPolicyMessage.value = error.response.data.message

          console.log('Dialog state set:', {
            showDialog: modalState.showCancellationPolicyDialog.value,
            message: modalState.cancellationPolicyMessage.value
          })

          // Also show a toast as fallback
          toast.error(error.response.data.message).goAway(TOAST_DURATION * 2)
        } else {
          toast.error('Failed to cancel booking. Please try again.').goAway(TOAST_DURATION)
        }
      }
    }

    // Handle cancellation policy dialog message action
    const handleCancellationPolicyMessage = () => {
      // Close the policy dialog
      modalState.showCancellationPolicyDialog.value = false

      // Open the message modal with the selected booking
      if (bookingState.selectedBooking.value) {
        modalState.isMessageModalOpen.value = true
      }
    }

    return {
      ...bookingState,
      ...modalState,
      ...bookingActions,
      tabs,
      activeTab,
      formatDateRange,
      selectedBookingType,
      onConfirmVenmoPayment, // Use our local implementation instead of bookingActions.confirmVenmoPayment
      onConfirmCancelStay, // Use our local implementation instead of bookingActions.confirmCancelStay
      showIdentityVerificationDialog: modalState.showIdentityVerificationDialog,
      handleVerificationComplete: bookingActions.handleVerificationComplete,
      bookingCards,
      highlightedBookingId,
      scrollToBooking,
      fetchBookingAndSetContext,
      navigateToBooking,
      switchToTab,
      handleApproveBooking,
      handleApproveBookingIndefinitely,
      handleCancellationPolicyMessage,
      isTravelerType, // Add isTravelerType to the return object
    }
  },
})
</script>

<template>
  <v-row>
    <v-col cols="12" class="tw-px-4">
      <div class="tw-flex tw-flex-wrap tw-justify-between tw-items-center tw-w-full">
        <MyHomePageTitle
          :title="bookingType === 'SWAP' ? 'Swaps' : isTravelerType ? 'Stays' : 'Bookings'"
          class="tw-mb-0"
        />
      </div>
    </v-col>

    <v-col cols="12">
      <BookingTypeToggle v-model="selectedBookingType" />
    </v-col>

    <v-col v-if="isLoading" cols="12" class="tw-px-4 tw-flex tw-justify-center">
      <v-progress-circular indeterminate color="primary"></v-progress-circular>
    </v-col>

    <v-col v-else-if="bookingRequests.length === 0 && upcomingHostBookings.length === 0 && pastHostBookings.length === 0 && cancelledBookings.length === 0" cols="12" class="tw-px-4">
      <div class="tw-text-center tw-py-12">
        <p class="tw-text-xl tw-text-gray-500 tw-mb-4">
          You don't have any {{ bookingType === 'SWAP' ? 'swaps' : 'bookings' }} yet.
        </p>
      </div>
    </v-col>

    <v-col v-else cols="12" class="tw-px-4">
      <v-tabs :key="'tabs-' + activeTab" v-model="activeTab" @change="index => switchToTab(index, { keepBookingId: false })">
        <v-tab
          v-for="tab in tabs"
          :key="tab.id"
          class="tw-font-medium tw-text-lg tw-text-zinc-500 tw-normal-case"
        >
          {{ tab.label }}
        </v-tab>

        <v-tab-item v-for="tab in tabs" :key="tab.id">
          <div v-if="tab.getBookings().length === 0" class="tw-text-center tw-py-8">
            <p class="tw-text-xl tw-text-gray-500 tw-mb-4">{{ tab.emptyMessage }}</p>
          </div>
          <template v-else>
            <div v-for="booking in tab.getBookings()" :key="booking.id" class="tw-mt-6">
              <div :id="`booking-container-${booking.id}`" :key="booking.id">
                <BookingCard
                  ref="bookingCards"
                  :booking="booking"
                  :booking-type="bookingType"
                  :is-host="isHost"
                  :get-booking-role="getBookingRole"
                  :get-home-title="getHomeTitle"
                  :get-other-home-title="getOtherHomeTitle"
                  :format-date-range="formatDateRange"
                  :on-message-click="messageGuest"
                  :on-cancel-click="cancelStay"
                  :on-approve-click="booking => openApprovalDialog(booking)"
                  :on-approve-indefinitely-click="booking => openIndefiniteApprovalDialog(booking)"
                  :on-deny-click="booking => openDenialDialog(booking.id)"
                  :on-confirm-venmo-payment="onConfirmVenmoPayment"
                  :class="{
                    'tw-transition-all tw-duration-500': true,
                    'tw-rounded-lg': true,
                    'hover:tw-shadow-md': true,
                    'tw-border-2 tw-border-primary tw-bg-primary-50': highlightedBookingId === booking.id
                  }"
                />

                <!-- Debug buttons - hidden in production -->
                <div v-if="false" class="tw-mt-2 tw-flex tw-justify-end tw-gap-2">
                  <button
                    class="tw-bg-gray-200 tw-text-gray-700 tw-px-3 tw-py-1 tw-rounded tw-text-sm"
                    @click="navigateToBooking(booking.id)"
                  >
                    Navigate to this booking
                  </button>
                  <button
                    class="tw-bg-blue-200 tw-text-blue-700 tw-px-3 tw-py-1 tw-rounded tw-text-sm"
                    @click="switchToTab(0, { keepBookingId: false })"
                  >
                    Tab 0
                  </button>
                  <button
                    class="tw-bg-green-200 tw-text-green-700 tw-px-3 tw-py-1 tw-rounded tw-text-sm"
                    @click="switchToTab(1, { keepBookingId: false })"
                  >
                    Tab 1
                  </button>
                  <button
                    class="tw-bg-yellow-200 tw-text-yellow-700 tw-px-3 tw-py-1 tw-rounded tw-text-sm"
                    @click="switchToTab(2, { keepBookingId: false })"
                  >
                    Tab 2
                  </button>
                  <button
                    class="tw-bg-red-200 tw-text-red-700 tw-px-3 tw-py-1 tw-rounded tw-text-sm"
                    @click="switchToTab(3, { keepBookingId: false })"
                  >
                    Tab 3
                  </button>
                </div>
              </div>
            </div>
          </template>
        </v-tab-item>
      </v-tabs>
    </v-col>

    <!-- Approval Dialog -->
    <v-dialog v-model="showApprovalDialog" max-width="500px">
      <v-card>
        <v-card-title class="tw-text-2xl tw-font-semibold tw-text-primary">
          {{ bookingType === 'SWAP' ? 'Approve Swap Request' : 'Approve Booking Request' }}
        </v-card-title>
        <v-card-text class="tw-flex tw-flex-col tw-gap-4">
          <div class="tw-text-lg">
            Your {{ bookingType === 'SWAP' ? 'swap partner' : 'guest' }} will be notified and {{ bookingType !== 'SWAP' || (bookingType === 'SWAP' && selectedBooking?.toHome?.cleaning_fee) ? 'requested to make payment' : 'informed of the approval' }}.
          </div>
          <div class="tw-text-lg">
            You will be able to see your {{ bookingType === 'SWAP' ? 'swap' : 'booking' }} in your Upcoming {{ bookingType === 'SWAP' ? 'Swaps' : 'Bookings' }}, and your {{ bookingType === 'SWAP' ? 'swap partner' : 'guest' }}
            information in Your {{ bookingType === 'SWAP' ? 'Swap Partners' : 'Guest' }} List
          </div>

          <template v-if="bookingType === 'SWAP'">
            <div class="tw-flex tw-flex-col tw-gap-2">
              <div class="tw-text-lg tw-font-semibold tw-text-primary">Cleaning Fee Details</div>
              <div class="tw-text-gray-600">
                <span class="tw-font-medium">Guest's Home:</span>
                {{
                  selectedBooking?.fromHome?.cleaning_fee
                    ? `$${selectedBooking.fromHome.cleaning_fee}`
                    : 'Not set'
                }}
                {{
                  selectedBooking?.guest_cleaning_fee_enabled ? '(Requested)' : '(Not requested)'
                }}
              </div>
              <div class="tw-text-gray-600">
                <span class="tw-font-medium">Your Home:</span>
                {{
                  selectedBooking?.toHome?.cleaning_fee
                    ? `$${selectedBooking.toHome.cleaning_fee}`
                    : 'Not set'
                }}
                {{ hostCleaningFee.value ? '(Requested)' : '(Not requested)' }}
              </div>

              <v-switch
                v-model="hostCleaningFee"
                label="I want to collect a cleaning fee from the guest"
                class="tw-mb-4"
                color="primary"
              ></v-switch>
            </div>
          </template>

          <v-textarea
            v-model="guestMessage"
            :label="`Send Your ${bookingType === 'SWAP' ? 'Swap Partner' : 'Guest'} a Message!`"
            outlined
            color="primary"
          ></v-textarea>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <GoodButton @click="handleApproveBooking">Send</GoodButton>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Denial Dialog -->
    <v-dialog v-model="showDenialDialog" max-width="500px">
      <v-card>
        <v-card-title class="tw-text-2xl tw-font-semibold tw-text-gray-700">
          Deny {{ bookingType === 'SWAP' ? 'Swap' : 'Booking' }} Request
        </v-card-title>
        <v-card-text>
          <div class="tw-mb-4 tw-text-lg">You are about to deny this {{ bookingType === 'SWAP' ? 'swap' : 'booking' }} request.</div>
          <div class="tw-mb-4 tw-text-lg">
            Your {{ bookingType === 'SWAP' ? 'swap partner' : 'guest' }} will be notified that their request has been denied.
          </div>
          <v-textarea
            v-model="guestMessage"
            :label="`Send Your ${bookingType === 'SWAP' ? 'Swap Partner' : 'Guest'} a Message (Optional)`"
            outlined
            color="primary"
          ></v-textarea>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <GoodButton @click="rejectBookingWithMessage">Deny and Send</GoodButton>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Indefinite Approval Dialog -->
    <v-dialog v-model="showIndefiniteApprovalDialog" max-width="500px">
      <v-card>
        <v-card-title class="tw-text-2xl tw-font-semibold tw-text-primary">
          Approve {{ bookingType === 'SWAP' ? 'Swap' : 'Booking' }} Request Indefinitely
        </v-card-title>
        <v-card-text class="tw-flex tw-flex-col tw-gap-4">
          <div class="tw-text-lg">
            You are about to approve this {{ bookingType === 'SWAP' ? 'swap' : 'booking' }} request and all future requests from this {{ bookingType === 'SWAP' ? 'swap partner' : 'guest' }}
            for this home.
          </div>
          <div class="tw-text-lg">
            Your {{ bookingType === 'SWAP' ? 'swap partner' : 'guest' }} will be notified and {{ bookingType !== 'SWAP' || (bookingType === 'SWAP' && selectedBooking?.toHome?.cleaning_fee) ? 'requested to make payment' : 'informed of the approval' }}.
          </div>
          <div class="tw-text-lg">
            You will be able to see your {{ bookingType === 'SWAP' ? 'swap' : 'booking' }} in your Upcoming {{ bookingType === 'SWAP' ? 'Swaps' : 'Bookings' }}, and your {{ bookingType === 'SWAP' ? 'swap partner' : 'guest' }}
            information in Your {{ bookingType === 'SWAP' ? 'Swap Partners' : 'Guest' }} List
          </div>

          <template v-if="bookingType === 'SWAP'">
            <div class="tw-flex tw-flex-col tw-gap-2">
              <div class="tw-text-lg tw-font-semibold tw-text-primary">Cleaning Fee Details</div>
              <div class="tw-text-gray-600">
                <span class="tw-font-medium">Guest's Home:</span>
                {{
                  selectedBooking?.fromHome?.cleaning_fee
                    ? `$${selectedBooking.fromHome.cleaning_fee}`
                    : 'Not set'
                }}
                {{
                  selectedBooking?.guest_cleaning_fee_enabled ? '(Requested)' : '(Not requested)'
                }}
              </div>
              <div class="tw-text-gray-600">
                <span class="tw-font-medium">Your Home:</span>
                {{
                  selectedBooking?.toHome?.cleaning_fee
                    ? `$${selectedBooking.toHome.cleaning_fee}`
                    : 'Not set'
                }}
                {{ hostCleaningFee.value ? '(Requested)' : '(Not requested)' }}
              </div>

              <v-switch
                v-model="hostCleaningFee"
                label="I want to collect a cleaning fee from the guest"
                class="tw-mb-4"
                color="primary"
              ></v-switch>
            </div>
          </template>

          <v-textarea
            v-model="guestMessage"
            :label="`Send Your ${bookingType === 'SWAP' ? 'Swap Partner' : 'Guest'} a Message!`"
            outlined
            color="primary"
          ></v-textarea>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <GoodButton @click="handleApproveBookingIndefinitely">Approve Indefinitely</GoodButton>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <NewMessageModal
      :is-open="isMessageModalOpen"
      :pre-selected-receiver-id="selectedBooking?.user.id"
      :booking="selectedBooking"
      @message-sent="handleMessageSent"
      @update:is-open="isMessageModalOpen = $event"
    />

    <v-dialog v-model="showCancelModal" max-width="800">
      <GoodCard2 class="tw-flex tw-flex-col tw-gap-4 tw-p-6">
        <div class="tw-text-2xl tw-font-semibold tw-text-primary">Cancel {{ bookingType === 'SWAP' ? 'Home Swap' : 'Guest Booking' }}?</div>
        <div class="tw-text-lg tw-text-zinc-500">
          You are about to cancel this {{ bookingType === 'SWAP' ? 'home swap' : 'guest\'s booking' }}. We will notify the {{ bookingType === 'SWAP' ? 'swap partner' : 'guest' }} of the
          cancellation{{ bookingType !== 'SWAP' ? ', and their payment will be refunded according to your cancellation policy' : '' }}.
        </div>
        <div v-if="selectedBooking" class="tw-flex tw-flex-col tw-gap-4">
          <div class="tw-flex tw-items-center tw-gap-4 tw-w-full">
            <BeastImage
              :src="selectedBooking.toHome.photos[0]?.src || ''"
              :alt="selectedBooking.toHome.title"
              class="tw-max-w-32 tw-aspect-square tw-rounded-full tw-object-cover"
            />
            <div>
              <div class="tw-text-xl tw-font-semibold tw-text-primary">
                {{ selectedBooking.toHome.title }}
              </div>
              <div class="tw-text-lg tw-text-zinc-500">
                {{ selectedBooking.toHome.address }}
              </div>
              <div class="tw-text-lg tw-text-zinc-500">
                {{ formatDateRange(selectedBooking.start_at, selectedBooking.end_at) }}
              </div>
            </div>
          </div>
          <div v-if="bookingType !== 'SWAP' || (bookingType === 'SWAP' && selectedBooking.toHome.cleaning_fee)" class="tw-flex tw-flex-col tw-gap-2">
            <div class="tw-text-xl tw-font-semibold tw-text-primary">{{ bookingType === 'SWAP' ? 'Cleaning Fee' : 'Payment' }} Completed</div>
            <div class="tw-text-lg tw-text-zinc-500">
              {{ bookingType === 'SWAP' ? 'Cleaning Fee' : 'Payment' }} Complete ${{
                bookingType === 'SWAP'
                  ? (selectedBooking.extra_info.priceInfo?.getCleaningFee || selectedBooking.toHome.cleaning_fee || 0)
                  : (selectedBooking.extra_info.priceInfo?.hostPaymentAmount || selectedBooking.extra_info.priceInfo?.getTotalMoney)
              }}
            </div>
          </div>
          <!-- Refund Information -->
          <div v-if="selectedBooking.status === 'CANCELED'" class="tw-flex tw-flex-col tw-gap-2">
            <div class="tw-text-xl tw-font-semibold tw-text-primary">
              {{ bookingType === 'SWAP' ? 'Cleaning Fee Refund' : 'Payment Refund' }}
            </div>
            <div class="tw-text-lg tw-text-zinc-500">
              <template v-if="bookingType === 'SWAP'">
                <div v-if="selectedBooking.extra_info.refund_info?.guest_refund_amount">
                  Guest Cleaning Fee Refund: ${{ selectedBooking.extra_info.refund_info.guest_refund_amount }}
                </div>
                <div v-if="selectedBooking.extra_info.refund_info?.host_refund_amount">
                  Host Cleaning Fee Refund: ${{ selectedBooking.extra_info.refund_info.host_refund_amount }}
                </div>
                <div v-if="!selectedBooking.extra_info.refund_info?.guest_refund_amount && !selectedBooking.extra_info.refund_info?.host_refund_amount">
                  No cleaning fees to refund
                </div>
              </template>
              <template v-else>
                Payment Refund Amount ${{
                  selectedBooking.extra_info.refund_info?.guest_refund_amount ||
                  selectedBooking.extra_info.priceInfo?.hostPaymentAmount ||
                  selectedBooking.extra_info.priceInfo?.getTotalMoney
                }}
              </template>
            </div>
          </div>
          <!-- Payment Information for Active Bookings -->
          <div v-else-if="bookingType !== 'SWAP'" class="tw-flex tw-flex-col tw-gap-2">
            <div class="tw-text-xl tw-font-semibold tw-text-primary">Payment Refund</div>
            <div class="tw-text-lg tw-text-zinc-500">
              Payment Refund Amount ${{
                selectedBooking.extra_info.priceInfo?.hostPaymentAmount ||
                selectedBooking.extra_info.priceInfo?.getTotalMoney
              }}
            </div>
          </div>
        </div>
        <div class="tw-flex tw-justify-end tw-gap-4 tw-mt-4">
          <GoodButtonReverted @click="showCancelModal = false">Exit</GoodButtonReverted>
          <GoodButton @click="onConfirmCancelStay">Confirm Cancellation</GoodButton>
        </div>
      </GoodCard2>
    </v-dialog>
    <v-dialog v-model="showCancelSuccessModal" max-width="600">
      <GoodCard2 class="tw-flex tw-flex-col tw-gap-4 tw-p-6">
        <div class="tw-text-3xl tw-font-bold tw-text-primary">{{ bookingType === 'SWAP' ? 'Home Swap' : 'Booking' }} Has Been Cancelled</div>
        <div class="tw-text-lg tw-text-zinc-500">
          The {{ bookingType === 'SWAP' ? 'swap partner' : 'guest' }} has been notified of the cancellation.
          {{ bookingType !== 'SWAP' ? 'Their refund will be processed according to your cancellation policy.' : '' }}
        </div>
        <div class="tw-flex tw-justify-end tw-mt-4">
          <GoodButton @click="showCancelSuccessModal = false">Close</GoodButton>
        </div>
      </GoodCard2>
    </v-dialog>

    <!-- Cancellation Policy Dialog -->
    <CancellationPolicyDialog
      v-model="showCancellationPolicyDialog"
      :message="cancellationPolicyMessage"
      :is-host="selectedBooking ? isHost(selectedBooking) : false"
      @message="handleCancellationPolicyMessage"
    />

    <!-- Payment Method Required Dialog -->
    <v-dialog v-model="showStripeConnectDialog" max-width="550">
      <v-card class="tw-p-8 tw-rounded-xl">
        <div class="tw-flex tw-flex-col tw-gap-6">
          <!-- Header with close button -->
          <div class="tw-flex tw-justify-between tw-items-center">
            <h2 class="tw-text-2xl tw-font-bold tw-text-primary">
              Payment Method Required
            </h2>
            <v-btn icon @click="showStripeConnectDialog = false">
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </div>

          <!-- Public Booking - Only Stripe Connect -->
          <template v-if="selectedBooking && !selectedBooking.from_sharable_link">
            <!-- Specific description for public bookings -->
            <p class="tw-text-gray-600 tw-text-lg">
              To approve this public booking, you need to set up Stripe Connect:
            </p>

            <div class="tw-bg-gray-50 tw-p-5 tw-rounded-lg tw-border tw-border-gray-200">
              <!-- Stripe Connect Option -->
              <div class="tw-flex tw-items-center tw-justify-between">
                <div class="tw-flex tw-items-center">
                  <img src="~/assets/stripe.png" alt="Stripe" class="tw-h-8 tw-object-contain tw-mr-3" />
                  <span class="tw-font-medium tw-text-lg">Stripe Connect</span>
                </div>
              </div>
            </div>

            <!-- Action buttons for public bookings -->
            <div class="tw-flex tw-flex-col tw-gap-3 tw-mt-4">
              <GoodButton
                class="tw-w-full"
                @click="
                  $router.push({ path: '/profile', query: { section: 'stripe' } })
                  showStripeConnectDialog = false
                "
              >
                <v-icon left>mdi-credit-card</v-icon>
                Set Up Stripe Connect
              </GoodButton>

              <GoodButton
                class="tw-w-full"
                @click="
                  $router.push({ path: '/profile', query: { section: 'payment' } })
                  showStripeConnectDialog = false
                "
              >
                <v-icon left>mdi-cog</v-icon>
                Go to Payment Settings
              </GoodButton>

              <GoodButtonReverted
                class="tw-w-full"
                @click="showStripeConnectDialog = false"
              >
                Cancel
              </GoodButtonReverted>
            </div>
          </template>

          <!-- Private Booking - Either payment method is acceptable -->
          <template v-else>
            <!-- Specific description for private bookings -->
            <p class="tw-text-gray-600 tw-text-lg">
              To approve this private booking, you need to set up at least one payment method:
            </p>

            <div class="tw-bg-gray-50 tw-p-5 tw-rounded-lg tw-border tw-border-gray-200">
              <!-- Stripe Connect Option -->
              <div class="tw-flex tw-items-center tw-justify-between tw-mb-4 tw-pb-4 tw-border-b tw-border-gray-200">
                <div class="tw-flex tw-items-center">
                  <img src="~/assets/stripe.png" alt="Stripe" class="tw-h-8 tw-object-contain tw-mr-3" />
                  <span class="tw-font-medium tw-text-lg">Stripe Connect</span>
                </div>
                <GoodButton
                  size="small"
                  @click="
                    $router.push({ path: '/profile', query: { section: 'stripe' } })
                    showStripeConnectDialog = false
                  "
                >
                  Set Up
                </GoodButton>
              </div>

              <!-- Venmo Option -->
              <div class="tw-flex tw-items-center tw-justify-between">
                <div class="tw-flex tw-items-center">
                  <img src="~/assets/venmo.png" alt="Venmo" class="tw-h-6 tw-object-contain tw-mr-3" />
                  <span class="tw-font-medium tw-text-lg">Venmo</span>
                </div>
                <GoodButton
                  size="small"
                  @click="
                    $router.push({ path: '/profile', query: { section: 'venmo' } })
                    showStripeConnectDialog = false
                  "
                >
                  Set Up
                </GoodButton>
              </div>
            </div>

            <!-- Action buttons for private bookings -->
            <div class="tw-flex tw-flex-col tw-gap-3 tw-mt-4">
              <GoodButton
                class="tw-w-full"
                @click="
                  $router.push({ path: '/profile', query: { section: 'stripe' } })
                  showStripeConnectDialog = false
                "
              >
                <v-icon left>mdi-credit-card</v-icon>
                Set Up Stripe Connect
              </GoodButton>

              <GoodButton
                class="tw-w-full"
                @click="
                  $router.push({ path: '/profile', query: { section: 'venmo' } })
                  showStripeConnectDialog = false
                "
              >
                <v-icon left>mdi-cash</v-icon>
                Set Up Venmo
              </GoodButton>

              <GoodButton
                class="tw-w-full"
                @click="
                  $router.push({ path: '/profile', query: { section: 'payment' } })
                  showStripeConnectDialog = false
                "
              >
                <v-icon left>mdi-cog</v-icon>
                Go to Payment Settings
              </GoodButton>

              <GoodButtonReverted
                class="tw-w-full"
                @click="showStripeConnectDialog = false"
              >
                Cancel
              </GoodButtonReverted>
            </div>
          </template>
        </div>
      </v-card>
    </v-dialog>

    <!-- Stripe Identity Verification Modal -->
    <!-- This modal is kept for compatibility but is no longer required for booking approval -->
    <!-- Identity verification is now handled in the home activation flow -->
    <StripeIdentityModal
      v-model="showIdentityVerificationDialog"
      :home-id="selectedBooking ? selectedBooking.toHome?.id : null"
      @verification-complete="handleVerificationComplete"
    />
  </v-row>
</template>
