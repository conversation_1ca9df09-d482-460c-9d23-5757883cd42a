<script lang="ts">
// @ts-nocheck

import {
  computed,
  defineComponent,
  onMounted,
  ref,
  useRoute,
  useRouter,
  useStore,
  reactive,
  watch,
  nextTick,
} from '@nuxtjs/composition-api'

import { useApi, useToast } from '~/composables/useCommon'
import { Booking } from '~/types'
import GoodCard2 from '~/components/GoodCard2.vue'
import { formatDateRange, formatNumberToDisplay } from '~/helpers'
import BeastImage from '~/components/BeastImage.vue'
import PriceInfoItem from '~/components/PriceInfoItem.vue'
import BoldText from '~/components/BoldText.vue'
import GoodButton from '~/components/GoodButton.vue'
import GoodButtonReverted from '~/components/GoodButtonReverted.vue'
import BoldPurpleText from '~/components/BoldPurpleText.vue'
import AppDateRangePicker from '~/components/AppDateRangePicker.vue'
import GoodButtonIncreaseNumber from '~/components/GoodButtonIncreaseNumber.vue'
import useAddressFormatter from '~/composables/useAddressFormatter'
import {
  petFriendlyOptions,
  petFriendlyTypes,
  petFriendlyNumbers,
  TOAST_DURATION,
  CANCELLATION_POLICY_TYPE,
} from '~/constants'
import { useHostHomesStore } from '~/composables/useStore'
import AuthLoginForm from '~/components/AuthLoginForm.vue'
import AuthSignupForm from '~/components/AuthSignupForm.vue'
import LiabilityPayModal from '~/components/LiabilityPayModal.vue'
import { useStripeConnectStore } from '~/composables/useStripeConnectStore'

export default defineComponent({
  components: {
    BoldPurpleText,
    GoodButton,
    GoodButtonReverted,
    BoldText,
    PriceInfoItem,
    BeastImage,
    GoodCard2,
    AppDateRangePicker,
    GoodButtonIncreaseNumber,
    AuthLoginForm,
    AuthSignupForm,
    LiabilityPayModal
  },

  scrollToTop: true,

  setup() {
    const route = useRoute()
    const router = useRouter()
    const api = useApi()
    const booking = ref<Booking | null>(null)
    const startDateSelected = ref(null)
    const endDateSelected = ref(null)
    const isTravelInsuranceChecked = ref(false)
    const isLoading = ref(true)
    const toast = useToast()
    const store = useStore()
    const currentUser = computed(() => store.getters['auth/getuser'])
    const isDepositCoverage = ref(false)
    const isTravelInsuranceCoverage = ref(false)
    const hostHomesStore = useHostHomesStore()
    const selectedHomeForSwap = ref(null)
    const cleaningFeeOption = ref('Yes')
    const showPayModal = ref(false)
    const reservationId = ref(null)
    const showStripeConnectDialog = ref(false)
    const stripeStore = useStripeConnectStore()

    const isSwapBooking = computed(() => {
      return booking.value?.booking_type === 'SWAP'
    })

    const isPrivateBooking = computed(() => {
      return booking.value?.from_sharable_link !== null || booking.value?.toHome?.is_public === false
    })

    const bookingId = route.value.params.id

    if (!bookingId) {
      router.push('/bookings')
    }

    const bookingHomePolicies = computed(() => {
      return booking.value?.toHome?.extra_info?.cancellationPolicies
    })

    const cancellationPolicy = computed(() => {
      return isPrivateBooking.value
        ? 'PRIVATE'
        : bookingHomePolicies.value?.publicRental
          ? 'PUBLIC'
          : bookingHomePolicies.value?.flexiblePublicRental
            ? 'FLEXIBLE'
            : bookingHomePolicies.value?.longTermStay
              ? 'LONG_TERM'
              : 'NON_REFUNDABLE'
    })

    const cancellationPolicyType = computed(() => {
      return CANCELLATION_POLICY_TYPE[cancellationPolicy.value]
    })

    const displayedPets = computed((): string => {
      if (!booking.value) return 'No pets allowed'

      // Use the otherUserProperty computed property
      // This will always be a valid object with a valid extra_info.pets property
      const pets = otherUserProperty.value.extra_info.pets

      if (pets.enabled === 'yes') {
        return 'Pets allowed'
      }

      if (pets.enabled === 'service animal only') {
        return 'Service animals only'
      }

      return 'No pets allowed'
    })

    // Computed property to determine if pet fee should be displayed
    const shouldShowPetFee = computed((): boolean => {
      if (!booking.value) return false

      // Check if property allows pets
      const propertyAllowsPets = booking.value.toHome?.extra_info?.pets?.enabled === 'yes' ||
                                booking.value.toHome?.extra_info?.pets?.enabled === 'service animal only'

      // Check if traveler has indicated they are bringing pets
      const travelerHasPets = booking.value.extra_info?.pets?.enabled === 'yes'

      // Only show pet fee if both conditions are met
      return propertyAllowsPets && travelerHasPets
    })

    const isEditingDates = ref(false)
    const isEditingGuests = ref(false)
    const isEditingPets = ref(false)

    const editAmountDialogData = reactive({
      guests: booking.value?.extra_info?.guests || 1,
      pets: booking.value?.extra_info?.pets || {
        enabled: 'no',
        type: '',
        number: 0,
      },
    })

    const tempStartDate = ref(null)
    const tempEndDate = ref(null)

    const toggleEditDates = () => {
      isEditingDates.value = !isEditingDates.value
      if (isEditingDates.value) {
        tempStartDate.value = new Date(booking.value.start_at)
        tempEndDate.value = new Date(booking.value.end_at)
      } else {
        // Reset to original values if editing is cancelled
        tempStartDate.value = null
        tempEndDate.value = null
      }
    }

    const toggleEditGuests = () => {
      isEditingGuests.value = !isEditingGuests.value
      if (isEditingGuests.value) {
        editAmountDialogData.guests = booking.value.extra_info?.guests || 1
      } else {
        // Reset to original value if editing is cancelled
        editAmountDialogData.guests = booking.value.extra_info?.guests || 1
      }
    }

    const toggleEditPets = () => {
      isEditingPets.value = !isEditingPets.value
      if (isEditingPets.value) {
        editAmountDialogData.pets = { ...booking.value.extra_info?.pets } || {
          enabled: 'no',
          type: '',
          number: 0,
        }
      } else {
        // Reset to original values if editing is cancelled
        editAmountDialogData.pets = { ...booking.value.extra_info?.pets } || {
          enabled: 'no',
          type: '',
          number: 0,
        }
      }
    }

    const updateBookingInfo = async () => {
      try {
        const updatedData: any = {}

        if (isEditingDates.value) {
          updatedData.start_at = startDateSelected.value
          updatedData.end_at = endDateSelected.value
          // updatedData.extra_info = {
          // 	...updatedData.extra_info,
          // 	priceInfo: {
          // 		nightly_rate: nightly_rate.value,
          // 		getDiffsInDays: getDiffsInDays.value,
          // 		getRawTotalMoney: getRawTotalMoney.value,
          // 		getCleaningFee: getCleaningFee.value,
          // 		getTaxRate: getTaxRate.value,
          // 		getTax: getTax.value,
          // 		getServiceFee: getServiceFee.value,
          // 		getTotalMoney: getTotalMoney.value,
          // 		averageNightlyRateOnTotal: averageNightlyRateOnTotal.value,
          // 	},
          // }
        }

        if (isEditingGuests.value) {
          updatedData.extra_info = {
            ...updatedData.extra_info,
            guests: editAmountDialogData.guests,
          }
        }

        if (isEditingPets.value) {
          updatedData.extra_info = {
            ...updatedData.extra_info,
            pets: editAmountDialogData.pets,
          }
        }

        if (Object.keys(updatedData).length > 0) {
          const { data } = await api.patch(`/bookings/${bookingId}`, updatedData)
          booking.value = data
          isEditingDates.value = false
          isEditingGuests.value = false
          isEditingPets.value = false
          toast.success('Booking information updated successfully').goAway(TOAST_DURATION)
        } else {
          toast.info('No changes to update').goAway(TOAST_DURATION)
        }
      } catch (error) {
        console.error('Error updating booking:', error)
        toast
          .error('Failed to update booking information. Please try again.')
          .goAway(TOAST_DURATION)
      }
    }

    const loginFormRef = ref(null)

    const handleRequestToBook = async () => {
      if (!isPrivateBooking.value && !isSwapBooking.value) {
        showPayModal.value = true
        reservationId.value = bookingId
      } else {
        await requestToBook()
      }
    }

    const requestToBook = async () => {
      if (!isUserLoggedIn.value) {
        await nextTick()
        if (loginFormRef.value) {
          const yOffset = -200
          const y =
            loginFormRef.value.$el.getBoundingClientRect().top + window.pageYOffset + yOffset
          window.scrollTo({ top: y, behavior: 'smooth' })
        }
        return
      }

      try {
        // First, check if the booking has already been auto-approved
        // This can happen with pre-approved travelers after Liability Protection payment
        const { data: currentBooking } = await api.get(`/bookings/${bookingId}`)

        // If the booking is already in ACCEPTED or COMPLETED status, it means it was auto-approved
        if (currentBooking.status === 'accepted' || currentBooking.status === 'completed') {
          // Update the local booking data
          booking.value = currentBooking

          // Show a success message
          toast.success(
            isSwapBooking.value
              ? 'Your home swap request was automatically approved!'
              : 'Your booking request was automatically approved!'
          ).goAway(TOAST_DURATION)

          // If the booking is accepted and requires payment, redirect to payment page
          if (currentBooking.status === 'accepted') {
            const needsPayment = isSwapBooking.value
              ? (currentBooking.host_cleaning_fee_enabled || currentBooking.guest_cleaning_fee_enabled)
              : true

            if (needsPayment) {
              // Redirect to payment page
              router.push(`/bookings/${bookingId}/payment`)
            }
          }

          return
        }

        // If the booking is still in CREATED status, proceed with the request
        const requestData: any = {
          message: booking.value.comment,
          is_deposit_coverage: isDepositCoverage.value,
          is_travel_insurance_coverage: isTravelInsuranceCoverage.value,
        }

        if (isSwapBooking.value) {
          if (!selectedHomeForSwap.value) {
            toast.error('Please select your home for the swap').goAway(TOAST_DURATION)
            return
          }

          requestData.user_home = selectedHomeForSwap.value
          // Default to Yes for cleaning fee
          if (cleaningFeeOption.value === '') {
            cleaningFeeOption.value = 'Yes'
          }

          // Check if guest wants to charge a cleaning fee
          const wantsCleaningFee = cleaningFeeOption.value === 'Yes'
          requestData.guest_cleaning_fee_enabled = wantsCleaningFee

          // If guest wants to charge a cleaning fee, check if they have Stripe Connect set up
          if (wantsCleaningFee) {
            const hasStripeConnect = await checkGuestStripeConnect()
            if (!hasStripeConnect) {
              // Show the Stripe Connect dialog if the guest doesn't have Stripe Connect set up
              showStripeConnectDialog.value = true
              return
            }
          }
        }

        const { data } = await api.put(`/bookings/${bookingId}/request`, requestData)

        booking.value = data
        toast
          .success(
            isSwapBooking.value
              ? 'Home swap request sent successfully'
              : 'Booking request sent successfully'
          )
          .goAway(TOAST_DURATION)
      } catch (error) {
        // Check if this is a 403 error with "Booking is not in the correct status" message
        // This can happen if the booking was auto-approved between our initial check and the request
        if (error.response && error.response.status === 403 &&
            error.response.data && error.response.data.message === 'Booking is not in the correct status') {

          // Fetch the current booking state
          try {
            const { data: currentBooking } = await api.get(`/bookings/${bookingId}`)

            // Update the local booking data
            booking.value = currentBooking

            // Show appropriate message based on the current status
            if (currentBooking.status === 'accepted' || currentBooking.status === 'completed') {
              toast.success(
                isSwapBooking.value
                  ? 'Your home swap request was automatically approved!'
                  : 'Your booking request was automatically approved!'
              ).goAway(TOAST_DURATION)

              // If the booking is accepted and requires payment, redirect to payment page
              if (currentBooking.status === 'accepted') {
                const needsPayment = isSwapBooking.value
                  ? (currentBooking.host_cleaning_fee_enabled || currentBooking.guest_cleaning_fee_enabled)
                  : true

                if (needsPayment) {
                  // Redirect to payment page
                  router.push(`/bookings/${bookingId}/payment`)
                }
              }
            } else {
              // For other statuses, show a generic message
              toast.info(`Booking status is now: ${currentBooking.status}`).goAway(TOAST_DURATION)
            }
          } catch (fetchError) {
            console.error('Error fetching updated booking:', fetchError)
            toast.error('An error occurred. Please refresh the page.').goAway(TOAST_DURATION)
          }
        } else {
          // For other errors, show the standard error message
          console.error('Error requesting booking:', error)
          toast.error(`Failed to request ${isSwapBooking.value ? 'home swap' : 'booking'}. Please try again.`).goAway(TOAST_DURATION)
        }
      }
    }

    const handleDateSelection = dates => {
      tempStartDate.value = dates.isStartSelected ? dates.startDate : null
      tempEndDate.value = dates.isFinalSelected ? dates.endDate : null
      if (tempStartDate.value) startDateRed.value = false
      if (tempEndDate.value) endDateRed.value = false
    }

    const saveDates = async () => {
      if (tempStartDate.value && tempEndDate.value) {
        startDateSelected.value = tempStartDate.value
        endDateSelected.value = tempEndDate.value
        await updateBookingInfo()
        isEditingDates.value = false
      } else {
        toast.error('Please select both start and end dates').goAway(TOAST_DURATION)
      }
    }

    // Update isHost check to match the logic from bookings/index.vue
    const isHost = computed(() => {
      return booking.value?.toHome?.user_id === currentUser.value?.id
    })

    // Create a default property object to use when a property is undefined
    const defaultProperty = {
      title: 'Property',
      address: '',
      city_long: '',
      state_long: '',
      country_long: '',
      beds: 0,
      baths: 0,
      guests: 0,
      photos: [],
      user: {
        first_name: 'Host',
        last_name: ''
      },
      cleaning_fee: 0,
      extra_info: {
        pets: { enabled: 'no' }
      }
    }

    // Helper function to ensure a property object is valid
    const ensureValidProperty = (property) => {
      if (!property) return { ...defaultProperty }

      // Create a new object with all the default properties
      const validProperty = { ...defaultProperty }

      // Copy over any existing properties from the input property
      if (property.title) validProperty.title = property.title
      if (property.address) validProperty.address = property.address
      if (property.city_long) validProperty.city_long = property.city_long
      if (property.state_long) validProperty.state_long = property.state_long
      if (property.country_long) validProperty.country_long = property.country_long
      if (property.beds !== undefined) validProperty.beds = property.beds
      if (property.baths !== undefined) validProperty.baths = property.baths
      if (property.guests !== undefined) validProperty.guests = property.guests
      if (property.cleaning_fee !== undefined) validProperty.cleaning_fee = property.cleaning_fee

      // Ensure photos array exists
      validProperty.photos = property.photos || []

      // Ensure user object exists
      validProperty.user = {
        first_name: property.user?.first_name || 'Host',
        last_name: property.user?.last_name || ''
      }

      // Ensure extra_info object exists
      validProperty.extra_info = {
        pets: { enabled: property.extra_info?.pets?.enabled || 'no' }
      }

      return validProperty
    }

    // Determine which property to show for the current user in a swap booking
    const currentUserProperty = computed(() => {
      if (!booking.value) {
        return { ...defaultProperty }
      }

      if (booking.value.booking_type !== 'SWAP') {
        return ensureValidProperty(booking.value.toHome)
      }

      // For swap bookings, each user should see their own property
      // The host (second host) owns the toHome property
      // The guest (first host/requester) owns the fromHome property
      return isHost.value
        ? ensureValidProperty(booking.value.toHome)
        : ensureValidProperty(booking.value.fromHome)
    })

    // Determine which property to show for the other user in a swap booking
    const otherUserProperty = computed(() => {
      if (!booking.value) {
        return { ...defaultProperty }
      }

      if (booking.value.booking_type !== 'SWAP') {
        return ensureValidProperty(booking.value.toHome)
      }

      // For swap bookings, each user should see the other user's property
      // The host (second host) should see the guest's property (fromHome)
      // The guest (first host/requester) should see the host's property (toHome)
      return isHost.value
        ? ensureValidProperty(booking.value.fromHome)
        : ensureValidProperty(booking.value.toHome)
    })

    // Use address formatter
    const { formatAddress } = useAddressFormatter()

    // Format address based on booking status and payment status
    const formattedAddress = computed(() => {
      if (!booking.value) return 'Address not available'

      // Use the otherUserProperty computed property
      // This will always be a valid object with all required properties
      const home = otherUserProperty.value

      return formatAddress(
        home.address,
        home.city_long,
        home.state_long,
        home.country_long,
        booking.value.status || '',
        booking.value.payment_status || ''
      )
    })

    // Format address for the current user's property
    const formattedCurrentUserAddress = computed(() => {
      if (!booking.value) return 'Address not available'

      // Use the currentUserProperty computed property
      // This will always be a valid object with all required properties
      const home = currentUserProperty.value

      return formatAddress(
        home.address,
        home.city_long,
        home.state_long,
        home.country_long,
        booking.value.status || '',
        booking.value.payment_status || ''
      )
    })

    // Determine if there are any price items to display for swap bookings
    const hasSwapPriceItems = computed(() => {
      if (!isSwapBooking.value) return true; // Always show for non-swap bookings

      // For swap bookings, only show if there's a cleaning fee
      return booking.value?.extra_info?.priceInfo?.getCleaningFee > 0 ||
             booking.value?.toHome?.cleaning_fee > 0;
    })

    onMounted(async () => {
      try {
        isLoading.value = true

        // Fetch booking details first
        const { data } = await api.get(`/bookings/${bookingId}`)

        if (!data) {
          router.push('/bookings')
          return
        }

        // Remove the host redirect since we now want to allow hosts to view
        // Only block access to 'created' status for hosts
        if (data.toHome.user_id === currentUser.value?.id && data.status === 'created') {
          router.push('/bookings')
          return
        }

        // Check if the home is archived (deleted)
        if (data.toHome?.status === 'archived') {
          toast.error('This home has been removed from Twimo. Explore other homes.').goAway(TOAST_DURATION)
          router.push('/bookings')
          return
        }

        // For swap bookings, also check if the fromHome is archived
        if (data.booking_type === 'SWAP' && data.fromHome?.status === 'archived') {
          toast.error('One of the homes in this swap has been removed from Twimo. Explore other homes.').goAway(TOAST_DURATION)
          router.push('/bookings')
          return
        }

        booking.value = data

        // If it's a swap booking, fetch user's homes (only active ones)
        if (isSwapBooking.value && currentUser.value) {
          await hostHomesStore.getHostHomes('', api, true) // Pass true to filter out draft homes
        }

        // First, handle booking completion if payment_intent is present
        if (route.value.query.payment_intent) {
          try {
            // Show loading toast while processing
            const loadingToast = toast.info('Processing payment...').goAway(0)

            // Process the payment completion
            const { data: updatedBooking } = await api.put(`/bookings/${bookingId}/complete`)

            // Determine if this is a swap booking
            const isSwap = updatedBooking.booking_type === 'SWAP'

            // Determine the appropriate toast message based on booking type and status
            if (isSwap) {
              const hostPaymentCompleted = updatedBooking.extra_info?.priceInfo?.hostPaymentIntentCompleted || false
              const guestPaymentCompleted = updatedBooking.extra_info?.priceInfo?.guestPaymentIntentCompleted || false

              if (updatedBooking.status === 'completed') {
                // Booking is completed (all required payments are done)
                toast.success('All required payments completed! Your swap is now confirmed.').goAway(TOAST_DURATION)
              } else {
                // Only one payment completed, but another is still required
                const isHost = updatedBooking.toHome?.user_id === currentUser.value?.id
                const hostRequired = updatedBooking.host_cleaning_fee_enabled;
                const guestRequired = updatedBooking.guest_cleaning_fee_enabled;

                if (isHost && hostPaymentCompleted) {
                  if (guestRequired) {
                    toast.success('Your payment for the guest home cleaning fee has been processed. Waiting for guest to complete their payment.').goAway(TOAST_DURATION)
                  } else {
                    toast.success('Your payment has been processed. The booking is now confirmed!').goAway(TOAST_DURATION)
                  }
                } else if (!isHost && guestPaymentCompleted) {
                  if (hostRequired) {
                    toast.success('Your payment for the host home cleaning fee has been processed. Waiting for host to complete their payment.').goAway(TOAST_DURATION)
                  } else {
                    toast.success('Your payment has been processed. The booking is now confirmed!').goAway(TOAST_DURATION)
                  }
                } else {
                  toast.success('Payment processed successfully.').goAway(TOAST_DURATION)
                }
              }
            } else {
              // Regular booking
              toast.success('Payment completed! Your booking is now confirmed.').goAway(TOAST_DURATION)
            }

            // Remove the query parameters from the URL
            const newUrl = window.location.pathname
            window.history.replaceState({}, document.title, newUrl)

            // Reload the page to get fresh data and avoid undefined errors
            window.location.reload()

          } catch (error) {
            toast
              .error(error?.response?.data?.message || 'Error completing booking')
              .goAway(TOAST_DURATION)
            console.error('Error completing booking:', error)
          }
        }

        isTravelInsuranceChecked.value = data.extra_info?.travelInsurance || false

        // Initialize startDateSelected and endDateSelected, but don't set isEditingDates to true
        startDateSelected.value = new Date(booking.value.start_at)
        endDateSelected.value = new Date(booking.value.end_at)
      } catch (error) {
        console.error('Error in booking process:', error)
        router.push('/bookings')
      } finally {
        isLoading.value = false
      }
    })

    const pickerRef = ref(null)
    const blockedDates = ref([])
    const datesHashMap = ref({})
    const startDateRed = ref(false)
    const endDateRed = ref(false)

    // Add a watcher for booking.status
    watch(
      () => booking.value?.status,
      (newStatus, oldStatus) => {
        if (newStatus !== oldStatus) {
          // Scroll to top when status changes
          window.scrollTo(0, 0)
        }
      }
    )

    const isUserLoggedIn = computed(() => store.getters['auth/isLoggedIn'])
    const showLoginForm = ref(false)

    const toggleForm = () => {
      showLoginForm.value = !showLoginForm.value
    }

    // Check if the guest has Stripe Connect set up when they opt-in for cleaning fee collection
    const checkGuestStripeConnect = async () => {
      try {
        await stripeStore.fetchStatus(api)
        const isStripeConnected = stripeStore.status.is_connected && stripeStore.status.charges_enabled
        return isStripeConnected
      } catch (error) {
        console.error('Error checking Stripe Connect status:', error)
        return false
      }
    }

    const handleLoginSuccess = () => {
      toast.success('Logged in successfully').goAway(TOAST_DURATION)
    }

    const handleSignupSuccess = () => {
      toast.success('Sign up successful').goAway(TOAST_DURATION)
    }

    // Update the My Stays link to use dynamic routing
    const getBookingsLink = computed(() => {
      const bookingType = isSwapBooking.value ? 'swap' : 'booking'
      return `/bookings?booking_type=${bookingType}`
    })

    return {
      booking,
      isLoading,
      displayedPets,
      shouldShowPetFee,
      currentUser,
      isTravelInsuranceChecked,
      isDepositCoverage,
      isTravelInsuranceCoverage,
      showPayModal,
      reservationId,
      pickerRef,
      startDateSelected,
      endDateSelected,
      blockedDates,
      datesHashMap,
      startDateRed,
      endDateRed,
      isEditingDates,
      isEditingGuests,
      isEditingPets,
      editAmountDialogData,
      tempStartDate,
      tempEndDate,
      toggleEditDates,
      toggleEditGuests,
      toggleEditPets,
      updateBookingInfo,
      requestToBook,
      handleRequestToBook,
      handleDateSelection,
      saveDates,
      petFriendlyOptions,
      petFriendlyTypes,
      petFriendlyNumbers,
      isPrivateBooking,
      cancellationPolicyType,
      isUserLoggedIn,
      showLoginForm,
      toggleForm,
      loginFormRef,
      isSwapBooking,
      selectedHomeForSwap,
      cleaningFeeOption,
      hostHomes: computed(() => hostHomesStore.hostHomes),
      isHost,
      getBookingsLink,
      formatDateRange,
      formatNumberToDisplay,
      formattedAddress,
      formattedCurrentUserAddress,
      currentUserProperty,
      otherUserProperty,
      handleLoginSuccess,
      handleSignupSuccess,
      showStripeConnectDialog,
      hasSwapPriceItems,
    }
  },
})
</script>

<template>
  <div class="tw-container tw-mx-auto tw-py-10 tw-px-4 md:tw-px-6 lg:tw-px-8">
    <v-row>
      <v-col v-if="isLoading || !booking || !booking.toHome" cols="12" class="tw-flex tw-justify-center tw-py-16">
        <v-progress-circular indeterminate color="primary" size="64" width="5"></v-progress-circular>
      </v-col>
      <template v-else>
      <template v-if="booking.status === 'created'">
        <!-- Booking Type Badge -->
        <v-col cols="12" class="tw-mb-2">
          <div class="tw-flex tw-justify-between tw-items-center">
            <div class="tw-flex tw-items-center tw-gap-2">
              <v-btn
                icon
                outlined
                small
                :to="isPrivateBooking && booking.from_sharable_link ?
                  `/${booking.toHome.slug || ''}?sharable_link=${booking.from_sharable_link}` :
                  `/${booking.toHome.slug || ''}`"
              >
                <v-icon>mdi-arrow-left</v-icon>
              </v-btn>
              <div v-if="isSwapBooking" class="tw-bg-purple-100 tw-text-purple-800 tw-px-4 tw-py-1 tw-rounded-full tw-text-sm tw-font-medium tw-flex tw-items-center">
                <v-icon size="16" class="tw-mr-1">mdi-swap-horizontal</v-icon>
                Home Swap
              </div>
              <div v-else class="tw-bg-blue-100 tw-text-blue-800 tw-px-4 tw-py-1 tw-rounded-full tw-text-sm tw-font-medium tw-flex tw-items-center">
                <v-icon size="16" class="tw-mr-1">mdi-home</v-icon>
                Regular Booking
              </div>
            </div>
          </div>
        </v-col>

        <v-col cols="12" class="tw-flex tw-flex-col tw-gap-8">
          <!-- Status Header Card -->
          <div class="tw-bg-white tw-rounded-xl tw-shadow-sm tw-p-6 tw-border tw-border-gray-100">
            <div class="tw-text-primary tw-text-3xl tw-font-bold tw-mb-4">
              {{ isSwapBooking ? 'Swap Your Home' : 'Confirm and Book Your Stay' }}
            </div>
            <div class="tw-text-zinc-700 tw-text-xl tw-font-medium tw-mb-2">
              {{ isSwapBooking ? 'Get Ready for Your Swap' : 'Get Ready for Your Vacation' }}
            </div>
            <div class="tw-text-zinc-600 tw-text-lg">
              Review your booking details below and submit your request to the host.
            </div>
          </div>
        </v-col>

        <!-- Two-column layout for booking details -->
        <v-col cols="12" sm="6" class="tw-flex tw-flex-col tw-gap-4">
          <!-- Dates Card -->
          <GoodCard2 class="tw-flex tw-flex-col tw-gap-4">
            <div class="tw-flex tw-justify-between tw-items-center">
              <div class="tw-text-lg tw-font-semibold tw-text-zinc-700">
                <div class="tw-flex tw-items-center tw-gap-2">
                  <v-icon color="primary" size="22">mdi-calendar-range</v-icon>
                  Dates
                </div>
              </div>
              <v-icon class="tw-cursor-pointer" @click="toggleEditDates">
                {{ isEditingDates ? 'mdi-close' : 'mdi-pencil' }}
              </v-icon>
            </div>
            <div v-if="!isEditingDates" class="tw-font-medium tw-text-zinc-600">
              {{ formatDateRange(booking.start_at, booking.end_at) }}
            </div>
            <div v-else class="tw-flex tw-flex-col tw-gap-2">
              <app-date-range-picker
                ref="pickerRef"
                width="100%"
                :blocked-dates="blockedDates"
                :dates-hash-map="datesHashMap"
                :start-date-red="startDateRed"
                :end-date-red="endDateRed"
                class="tw-mt-3"
                @selected-dates="handleDateSelection"
              ></app-date-range-picker>
              <GoodButton class="tw-mt-2 tw-w-full" @click="saveDates">Save Dates</GoodButton>
            </div>
          </GoodCard2>

          <!-- Guests Card -->
          <GoodCard2 class="tw-flex tw-flex-col tw-gap-4">
            <div class="tw-flex tw-justify-between tw-items-center">
              <div class="tw-text-lg tw-font-semibold tw-text-zinc-700">
                <div class="tw-flex tw-items-center tw-gap-2">
                  <v-icon color="primary" size="22">mdi-account-group</v-icon>
                  Guests (max: {{ booking.toHome.guests }})
                </div>
              </div>
              <v-icon class="tw-cursor-pointer" @click="toggleEditGuests">
                {{ isEditingGuests ? 'mdi-close' : 'mdi-pencil' }}
              </v-icon>
            </div>
            <div v-if="!isEditingGuests" class="tw-font-medium tw-text-zinc-600">
              {{ booking.extra_info.guests }}
            </div>
            <div v-else class="tw-flex tw-flex-col tw-gap-2">
              <GoodButtonIncreaseNumber
                style="border-color: gray !important"
                class="tw-w-full tw-text-lg tw-py-[28px]"
                :value="editAmountDialogData.guests"
                @decrease="editAmountDialogData.guests > 1 ? editAmountDialogData.guests-- : 1"
                @increase="
                  editAmountDialogData.guests < booking.toHome.guests
                    ? editAmountDialogData.guests++
                    : booking.toHome.guests
                "
              ></GoodButtonIncreaseNumber>
              <GoodButton class="tw-mt-2 tw-w-full" @click="updateBookingInfo"
                >Save Guests</GoodButton
              >
            </div>
          </GoodCard2>

          <!-- Pets Card -->
          <GoodCard2 class="tw-flex tw-flex-col tw-gap-4">
            <div class="tw-flex tw-justify-between tw-items-center">
              <div class="tw-text-lg tw-font-semibold tw-text-zinc-700">
                <div class="tw-flex tw-items-center tw-gap-2">
                  <v-icon color="primary" size="22">mdi-paw</v-icon>
                  Pets
                </div>
              </div>
              <v-icon class="tw-cursor-pointer" @click="toggleEditPets">
                {{ isEditingPets ? 'mdi-close' : 'mdi-pencil' }}
              </v-icon>
            </div>
            <div v-if="!isEditingPets" class="tw-font-medium tw-text-zinc-600">
              {{ displayedPets }}
            </div>
            <div v-else class="tw-flex tw-flex-col tw-gap-2">
              <v-select
                v-model="editAmountDialogData.pets.enabled"
                :items="[
                  { text: 'Yes', value: 'yes' },
                  { text: 'No', value: 'no' },
                ]"
                label="Y / N"
                outlined
                dense
                hide-details
                class="tw-rounded-xl tw-border tw-mb-2"
              ></v-select>
              <GoodButton class="tw-mt-2 tw-w-full" @click="updateBookingInfo"
                >Save Pets</GoodButton
              >
            </div>
          </GoodCard2>

          <!-- Message Card -->
          <GoodCard2 class="tw-flex tw-flex-col tw-gap-4">
            <div class="tw-text-lg tw-font-semibold tw-text-zinc-700">
              <div class="tw-flex tw-items-center tw-gap-2">
                <v-icon color="primary" size="22">mdi-message-text</v-icon>
                Message the Host
              </div>
            </div>
            <v-textarea
              v-model="booking.comment"
              outlined
              placeholder="Anything you want to ask the Host or share about your trip? "
              dense
              rows="5"
              counter="500"
            ></v-textarea>
          </GoodCard2>
        </v-col>

        <!-- Second column -->
        <v-col cols="12" sm="6" class="tw-flex tw-flex-col tw-gap-4">
          <!-- Property Details Card -->
          <GoodCard2 class="tw-flex tw-flex-col tw-gap-6 tw-p-6">
            <div class="tw-text-zinc-800 tw-text-xl tw-font-bold">Property Details</div>
            <v-row>
              <v-col cols="12" sm="7" class="tw-flex tw-flex-col tw-gap-4">
                <div
                  class="tw-text-zinc-800 tw-text-2xl tw-font-bold tw-cursor-pointer hover:tw-underline"
                  @click="$router.push(isHost ?
                    `/${booking.toHome.slug}/edit` :
                    (isPrivateBooking && booking.from_sharable_link ?
                      `/${booking.toHome.slug}?sharable_link=${booking.from_sharable_link}` :
                      `/${booking.toHome.slug}`))"
                >
                  {{ booking.toHome.title }}
                </div>

                <div class="tw-text-zinc-600 tw-text-lg tw-font-medium">
                  {{ formattedAddress }}
                </div>

                <div class="tw-flex tw-flex-row tw-gap-6 tw-mt-2">
                  <div class="tw-flex tw-items-center tw-gap-2">
                    <v-icon color="primary" size="22">mdi-bed</v-icon>
                    <span class="tw-text-zinc-700 tw-text-base tw-font-medium">
                      {{ booking.toHome.beds || 0 }} Beds
                    </span>
                  </div>

                  <div class="tw-flex tw-items-center tw-gap-2">
                    <v-icon color="primary" size="22">mdi-shower</v-icon>
                    <span class="tw-text-zinc-700 tw-text-base tw-font-medium">
                      {{ booking.toHome.baths || 0 }} Bathrooms
                    </span>
                  </div>
                </div>
              </v-col>
              <v-col cols="12" sm="5">
                <BeastImage
                  :src="booking?.toHome?.photos[0]?.src"
                  :alt="booking?.toHome?.title"
                  class="tw-aspect-video tw-rounded-lg tw-shadow-sm tw-border tw-border-gray-100"
                  @click="$router.push(isHost ?
                    `/${booking.toHome.slug}/edit` :
                    (isPrivateBooking && booking.from_sharable_link ?
                      `/${booking.toHome.slug}?sharable_link=${booking.from_sharable_link}` :
                      `/${booking.toHome.slug}`))"
                />
              </v-col>
            </v-row>

            <v-divider class="tw-my-2" />

            <!-- Pricing Section -->
            <div v-if="!isSwapBooking || (isSwapBooking && hasSwapPriceItems)" class="tw-flex tw-flex-col tw-gap-4">
              <div class="tw-text-zinc-800 tw-text-xl tw-font-bold">
                {{ isSwapBooking ? 'Cleaning Fee' : 'Price Breakdown' }}
              </div>

              <div v-if="!isSwapBooking" class="tw-flex tw-flex-col tw-gap-4">
                <PriceInfoItem
                  :title="`$${booking.extra_info.priceInfo.nightly_rate} x ${booking.extra_info.priceInfo.getDiffsInDays} nights`"
                  :value="`$${formatNumberToDisplay(booking.extra_info.priceInfo.getRawTotalMoney)}`"
                />
                <PriceInfoItem
                  title="Cleaning Fee"
                  :value="`$${formatNumberToDisplay(booking.extra_info.priceInfo.getCleaningFee || booking.toHome.cleaning_fee || 0)}`"
                />
                <PriceInfoItem
                  v-if="shouldShowPetFee"
                  title="Pet Fee"
                  :value="`$${formatNumberToDisplay(booking.toHome.pet_fee)}`"
                />
                <PriceInfoItem
                  title="Taxes"
                  :value="`$${formatNumberToDisplay(booking.extra_info.priceInfo.getTax)}`"
                />
                <PriceInfoItem
                  v-if="booking.extra_info.priceInfo.deposit_coverage"
                  title="Deposit Coverage"
                  :value="`$${formatNumberToDisplay(booking.extra_info.priceInfo.deposit_coverage_fee)}`"
                />
                <PriceInfoItem
                  v-if="booking.extra_info.priceInfo.travel_insurance_coverage"
                  title="Travel Insurance"
                  :value="`$${formatNumberToDisplay(booking.extra_info.priceInfo.travel_insurance_fee)}`"
                />
                <PriceInfoItem
                  v-if="!isPrivateBooking"
                  title="Liability Protection"
                  :value="`$${formatNumberToDisplay(booking.extra_info.priceInfo.getLiabilityProtection)}`"
                />
              </div>

              <div v-else class="tw-flex tw-flex-col tw-gap-4">
                <PriceInfoItem
                  title="Cleaning Fee"
                  :value="`$${formatNumberToDisplay(booking.extra_info.priceInfo.getCleaningFee || booking.toHome.cleaning_fee || 0)}`"
                />
              </div>

              <div class="tw-bg-blue-50 tw-rounded-lg tw-p-4 tw-flex tw-items-center tw-gap-2 tw-mt-2">
                <v-icon color="primary" size="22">mdi-cash</v-icon>
                <span class="tw-text-zinc-700 tw-text-base tw-font-medium">
                  Total Due: ${{
                    isSwapBooking
                      ? formatNumberToDisplay(booking.extra_info.priceInfo.getCleaningFee || booking.toHome.cleaning_fee || 0)
                      : formatNumberToDisplay(booking.extra_info.priceInfo.getTotalMoney)
                  }}
                  <span v-if="!isSwapBooking && !isPrivateBooking" class="tw-text-xs tw-font-normal">(Excluding Liability Protection)</span>
                </span>
              </div>
            </div>
          </GoodCard2>

          <!-- Additional Information Cards -->
          <!-- Cancellation Policies -->
          <GoodCard2 v-if="!isSwapBooking" class="tw-flex tw-flex-col tw-gap-4 tw-mt-4">
            <div class="tw-text-zinc-800 tw-text-xl tw-font-bold">
              {{ isPrivateBooking ? 'If You Need to Cancel' : 'Cancellation Policy' }}
            </div>
            <div class="tw-flex tw-flex-col tw-gap-2">
              <div class="tw-text-xl tw-text-zinc-700 tw-font-semibold">
                {{ cancellationPolicyType.TITLE }}
              </div>
              <div class="tw-text-zinc-600 tw-text-sm">
                Guests have 24 hours to cancel reservations in California for a full refund
              </div>
              <div class="tw-font-medium tw-text-base tw-text-zinc-600">
                {{ cancellationPolicyType.GUEST_DESCRIPTION }}
              </div>
            </div>
          </GoodCard2>

          <!-- House Rules & Check-in/out -->
          <GoodCard2 v-if="!isSwapBooking" class="tw-flex tw-flex-col tw-gap-4 tw-mt-4">
            <div class="tw-text-zinc-800 tw-text-xl tw-font-bold">House Rules</div>
            <!-- Check if either checkInOut or houseRules exist with a value -->
            <div
              v-if="
                booking.toHome.extra_info &&
                ((booking.toHome.extra_info.houseRules &&
                  (booking.toHome.extra_info.houseRules.noEvents ||
                    booking.toHome.extra_info.houseRules.noSmoking ||
                    booking.toHome.extra_info.houseRules.quietHours ||
                    booking.toHome.extra_info.houseRules.additionalRules)) ||
                  (booking.toHome.extra_info.checkInOut &&
                    (booking.toHome.extra_info.checkInOut.checkinTime ||
                      booking.toHome.extra_info.checkInOut.checkoutTime)))
              "
              class="tw-flex tw-flex-col tw-gap-4"
            >
              <!-- Check-in/Check-out Rules -->
              <div
                v-if="
                  booking.toHome.extra_info.checkInOut &&
                  (booking.toHome.extra_info.checkInOut.checkinTime ||
                    booking.toHome.extra_info.checkInOut.checkoutTime)
                "
                class="tw-flex tw-items-center tw-gap-2 tw-bg-gray-50 tw-p-3 tw-rounded-lg"
              >
                <v-icon color="primary" size="22">mdi-clock-outline</v-icon>
                <div class="tw-text-zinc-700 tw-text-base tw-font-medium">
                  Check in:
                  {{ booking.toHome.extra_info.checkInOut.checkinTime || 'N/A' }}
                  &nbsp;&amp;&nbsp; Check out:
                  {{ booking.toHome.extra_info.checkInOut.checkoutTime || 'N/A' }}
                </div>
              </div>

              <!-- House Rules -->
              <div
                v-if="
                  booking.toHome.extra_info.houseRules &&
                  (booking.toHome.extra_info.houseRules.noEvents ||
                    booking.toHome.extra_info.houseRules.noSmoking ||
                    booking.toHome.extra_info.houseRules.quietHours ||
                    booking.toHome.extra_info.houseRules.additionalRules)
                "
                class="tw-flex tw-flex-col tw-gap-4"
              >
                <div class="tw-flex tw-flex-wrap tw-gap-4">
                  <div
                    v-if="booking.toHome.extra_info.houseRules.noEvents"
                    class="tw-flex tw-items-center tw-gap-2 tw-bg-gray-50 tw-px-3 tw-py-2 tw-rounded-lg"
                  >
                    <v-icon color="error" size="18">mdi-party-popper</v-icon>
                    <span class="tw-text-zinc-700 tw-text-sm tw-font-medium">No events</span>
                  </div>
                  <div
                    v-if="booking.toHome.extra_info.houseRules.noSmoking"
                    class="tw-flex tw-items-center tw-gap-2 tw-bg-gray-50 tw-px-3 tw-py-2 tw-rounded-lg"
                  >
                    <v-icon color="error" size="18">mdi-smoking-off</v-icon>
                    <span class="tw-text-zinc-700 tw-text-sm tw-font-medium">No smoking</span>
                  </div>
                  <div
                    v-if="booking.toHome.extra_info.houseRules.quietHours"
                    class="tw-flex tw-items-center tw-gap-2 tw-bg-gray-50 tw-px-3 tw-py-2 tw-rounded-lg"
                  >
                    <v-icon color="primary" size="18">mdi-volume-off</v-icon>
                    <span class="tw-text-zinc-700 tw-text-sm tw-font-medium">
                      Quiet hours: {{
                        booking.toHome.extra_info.houseRules.quietHoursTime
                          ? booking.toHome.extra_info.houseRules.quietHoursTime.startTime +
                            ' - ' +
                            booking.toHome.extra_info.houseRules.quietHoursTime.endTime
                          : 'Enforced'
                      }}
                    </span>
                  </div>
                </div>

                <div
                  v-if="booking.toHome.extra_info.houseRules.additionalRules"
                  class="tw-bg-gray-50 tw-p-3 tw-rounded-lg"
                >
                  <div class="tw-text-zinc-700 tw-text-sm tw-font-medium">Additional rules:</div>
                  <div class="tw-text-zinc-600 tw-text-sm tw-mt-1">
                    {{ booking.toHome.extra_info.houseRules.additionalRules }}
                  </div>
                </div>
              </div>
            </div>
            <div v-else class="tw-text-zinc-600 tw-text-base">
              No specific rules provided
            </div>
          </GoodCard2>

          <!-- Travel and Deposit Insurance -->
          <GoodCard2 v-if="!isSwapBooking && !isPrivateBooking" class="tw-flex tw-flex-col tw-gap-4 tw-mt-4">
            <div class="tw-text-zinc-800 tw-text-xl tw-font-bold">
              Guest Verification &amp; Liability Protection
            </div>
            <div class="tw-bg-blue-50 tw-rounded-lg tw-p-4 tw-text-zinc-700">
              <p class="tw-text-base tw-mb-2">
                To ensure a safe and smooth stay for all guests, we require a quick
                verification process. You will verify instantaneously via Truvi, based on the
                information you've provided. Your host will receive confirmation once you're
                verified.
              </p>

              <p class="tw-text-base tw-mb-2">
                Instead of a large upfront security deposit, you'll pay just $7 per night to
                protect yourself from any accidental damages that might occur, so you can
                enjoy your stay worry-free.
              </p>

              <p class="tw-text-base">
                By proceeding with your booking, you acknowledge and agree to complete the
                Truvi verification process and pay the associated protection fee. Failure to
                do so may result in the cancellation of your reservation.
              </p>
            </div>
          </GoodCard2>

          <!-- Home Swap Box -->
          <GoodCard2 v-if="isSwapBooking" class="tw-flex tw-flex-col tw-gap-4 tw-mt-4">
            <div class="tw-text-zinc-800 tw-text-xl tw-font-bold">
              Your Home Swap Details
            </div>
            <div class="tw-flex tw-flex-col tw-gap-4">
              <div class="tw-text-zinc-700 tw-text-base tw-font-medium">Select Your Home</div>
              <v-select
                v-model="selectedHomeForSwap"
                :items="hostHomes"
                item-text="title"
                item-value="id"
                outlined
                dense
                hide-details
                class="tw-rounded-xl tw-border"
              ></v-select>

              <div class="tw-text-zinc-700 tw-text-base tw-font-medium tw-mt-2">
                Would you like to charge a cleaning fee?
              </div>
              <v-select
                v-model="cleaningFeeOption"
                :items="['Yes', 'No']"
                outlined
                dense
                hide-details
                class="tw-rounded-xl tw-border"
              ></v-select>
              <div v-if="cleaningFeeOption === 'Yes'" class="tw-flex tw-items-center tw-gap-2 tw-text-sm tw-text-zinc-600 tw-mt-1">
                <v-icon small color="info">mdi-information-outline</v-icon>
                Stripe Connect is required to collect cleaning fees
              </div>
            </div>
          </GoodCard2>

          <!-- Payment Information Card -->
          <GoodCard2 class="tw-flex tw-flex-col tw-gap-4 tw-mt-4">
            <div class="tw-text-zinc-800 tw-text-xl tw-font-bold">
              {{ isSwapBooking ? 'Finalize' : 'Payment' }} Information
            </div>
            <div class="tw-bg-blue-50 tw-rounded-lg tw-p-4 tw-text-zinc-700">
              <div class="tw-flex tw-items-center tw-gap-2 tw-mb-2">
                <v-icon color="info" size="22">mdi-information-outline</v-icon>
                <span class="tw-font-medium">Next Steps:</span>
              </div>
              <p class="tw-text-base tw-mb-2">
                Once you
                <span class="tw-font-medium">{{ isSwapBooking ? 'Request to Swap' : 'Request to Book' }}</span>
                the Host will need to Approve the
                {{ isSwapBooking ? 'Swap' : 'Booking' }}.
              </p>

              <template v-if="isSwapBooking">
                <p class="tw-text-base">
                  Once the swap is accepted, you will be asked to input payment information if
                  the Host wants to charge a cleaning fee.
                </p>
              </template>
              <template v-else>
                <p class="tw-text-base">
                  Once the booking is accepted, you will be asked to input payment information.
                </p>
              </template>
            </div>
          </GoodCard2>

          <!-- Action Button -->
          <div class="tw-mt-6">
            <GoodButton class="tw-w-full tw-text-lg tw-py-4" @click="handleRequestToBook">
              {{ isSwapBooking ? 'Request Home Swap' : 'Request to Book' }}
            </GoodButton>
          </div>
        </v-col>

        <LiabilityPayModal
          :model-value="showPayModal"
          :b-id="reservationId"
          :liability-fee="booking?.extra_info?.priceInfo?.getLiabilityProtection"
          :user-email="currentUser?.email"
          @update-model-value="showPayModal = $event"
          @payment-success="requestToBook"
        />

        <!-- Stripe Connect Dialog for Guests -->
        <v-dialog v-model="showStripeConnectDialog" max-width="550">
          <v-card class="tw-p-8 tw-rounded-xl">
            <div class="tw-flex tw-flex-col tw-gap-6">
              <!-- Header with close button -->
              <div class="tw-flex tw-justify-between tw-items-center">
                <h2 class="tw-text-2xl tw-font-bold tw-text-primary">
                  Stripe Connect Required
                </h2>
                <v-btn icon @click="showStripeConnectDialog = false">
                  <v-icon>mdi-close</v-icon>
                </v-btn>
              </div>

              <!-- Description for swap bookings with cleaning fee -->
              <p class="tw-text-gray-600 tw-text-lg">
                To request a home swap with cleaning fee collection, you need to set up Stripe Connect:
              </p>

              <div class="tw-bg-gray-50 tw-p-5 tw-rounded-lg tw-border tw-border-gray-200">
                <!-- Stripe Connect Option -->
                <div class="tw-flex tw-items-center tw-justify-between">
                  <div class="tw-flex tw-items-center">
                    <img src="~/assets/stripe.png" alt="Stripe" class="tw-h-8 tw-object-contain tw-mr-3" />
                    <span class="tw-font-medium tw-text-lg">Stripe Connect</span>
                  </div>
                </div>
              </div>

              <!-- Action buttons -->
              <div class="tw-flex tw-flex-col tw-gap-3 tw-mt-4">
                <GoodButton
                  class="tw-w-full"
                  @click="
                    $router.push({ path: '/profile', query: { section: 'stripe' } })
                    showStripeConnectDialog = false
                  "
                >
                  <v-icon left>mdi-credit-card</v-icon>
                  Set Up Stripe Connect
                </GoodButton>

                <GoodButton
                  class="tw-w-full"
                  @click="
                    $router.push({ path: '/profile', query: { section: 'payment' } })
                    showStripeConnectDialog = false
                  "
                >
                  <v-icon left>mdi-cog</v-icon>
                  Go to Payment Settings
                </GoodButton>

                <GoodButtonReverted
                  class="tw-w-full"
                  @click="showStripeConnectDialog = false"
                >
                  Cancel
                </GoodButtonReverted>
              </div>
            </div>
          </v-card>
        </v-dialog>

        <template v-if="!isUserLoggedIn">
          <v-col cols="12" class="tw-mt-8">
            <v-divider></v-divider>
          </v-col>
          <v-col cols="12" md="6" class="tw-flex tw-items-center tw-justify-center">
            <div class="tw-text-xl tw-font-medium tw-text-zinc-500">
              {{ showLoginForm ? 'New to Twimo?' : 'Already have an account?' }}
              <a href="#" class="tw-text-primary tw-font-semibold" @click.prevent="toggleForm">
                {{ showLoginForm ? 'Sign up' : 'Log in' }}
              </a>
            </div>
          </v-col>
          <v-col cols="12" md="6" class="tw-flex tw-flex-col tw-gap-4">
            <h2 class="tw-text-2xl tw-font-semibold tw-text-zinc-600">
              {{ showLoginForm ? 'Log in' : 'Sign Up to Complete Booking' }}
            </h2>

            <template v-if="showLoginForm">
              <AuthLoginForm
                ref="loginFormRef"
                :show-remember-me="false"
                :show-forgot-password="false"
              />
            </template>

            <template v-else>
              <AuthSignupForm ref="loginFormRef" :dark-mode="false" />
            </template>
          </v-col>
        </template>
      </template>

      <template v-else-if="booking.status === 'requested'">
        <!-- Booking Type Badge -->
        <v-col cols="12" class="tw-mb-2">
          <div class="tw-flex tw-justify-start">
            <div v-if="isSwapBooking" class="tw-bg-purple-100 tw-text-purple-800 tw-px-4 tw-py-1 tw-rounded-full tw-text-sm tw-font-medium tw-flex tw-items-center">
              <v-icon size="16" class="tw-mr-1">mdi-swap-horizontal</v-icon>
              Home Swap
            </div>
            <div v-else class="tw-bg-blue-100 tw-text-blue-800 tw-px-4 tw-py-1 tw-rounded-full tw-text-sm tw-font-medium tw-flex tw-items-center">
              <v-icon size="16" class="tw-mr-1">mdi-home</v-icon>
              Regular Booking
            </div>
          </div>
        </v-col>

        <v-col cols="12" class="tw-flex tw-flex-col tw-gap-8">
          <!-- Host View -->
          <template v-if="isHost">
            <div class="tw-bg-white tw-rounded-xl tw-shadow-sm tw-p-6 tw-border tw-border-gray-100">
              <div class="tw-text-primary tw-text-3xl tw-font-bold tw-mb-4">
                {{ isSwapBooking ? 'New Swap Request Received!' : 'New Booking Request Received!' }}
              </div>
              <div class="tw-text-zinc-700 tw-text-xl tw-font-medium tw-mb-2">
                You have received a new {{ isSwapBooking ? 'home swap' : 'booking' }} request from
                {{ booking.user.first_name }} {{ booking.user.last_name }}.
              </div>
              <div class="tw-text-zinc-600 tw-text-lg">
                Please review the {{ isSwapBooking ? 'swap' : 'booking' }} details below and take action. The {{ isSwapBooking ? 'swap partner' : 'guest' }} is waiting for your
                response.
              </div>
            </div>
          </template>

          <!-- Guest View -->
          <template v-else>
            <div class="tw-bg-white tw-rounded-xl tw-shadow-sm tw-p-6 tw-border tw-border-gray-100">
              <div class="tw-text-primary tw-text-3xl tw-font-bold tw-mb-4">
                Your {{ isSwapBooking ? 'Swap' : 'Booking' }} Request has been Sent!
              </div>
              <div class="tw-text-zinc-700 tw-text-xl tw-font-medium tw-mb-2">
                Your request has been sent to
                {{ booking.toHome?.user?.first_name || 'the host' }}. You will be notified shortly regarding approval.
              </div>
              <div class="tw-text-zinc-600 tw-text-lg">
                You will be notified on your Twimo account and via email. Once approved, you will be
                able to finalize your {{ isSwapBooking ? 'swap' : 'trip' }} {{ !isSwapBooking ? 'by completing your payment!' : (booking.toHome?.cleaning_fee ? 'by completing your cleaning fee payment!' : '!') }}
              </div>
            </div>
          </template>

          <!-- Property Details Card -->
          <GoodCard2 class="tw-flex tw-flex-col tw-gap-6 sm:tw-w-[80%] lg:tw-w-[70%] tw-p-6">
            <v-row>
              <v-col cols="12" sm="7" class="tw-flex tw-flex-col tw-gap-4">
                <div class="tw-text-zinc-800 tw-text-2xl tw-font-bold">
                  <template v-if="booking.booking_type === 'SWAP'">
                    {{ otherUserProperty.title }}
                  </template>
                  <template v-else>
                    {{ booking.toHome.title }}
                  </template>
                </div>

                <div class="tw-text-zinc-600 tw-text-lg tw-font-medium">
                  {{ formattedAddress }}
                </div>

                <div class="tw-flex tw-flex-row tw-gap-6 tw-mt-2">
                  <div class="tw-flex tw-items-center tw-gap-2">
                    <v-icon color="primary" size="22">mdi-bed</v-icon>
                    <span class="tw-text-zinc-700 tw-text-base tw-font-medium">
                      <template v-if="booking.booking_type === 'SWAP'">
                        {{ otherUserProperty.beds || 0 }} Beds
                      </template>
                      <template v-else>
                        {{ booking.toHome.beds || 0 }} Beds
                      </template>
                    </span>
                  </div>

                  <div class="tw-flex tw-items-center tw-gap-2">
                    <v-icon color="primary" size="22">mdi-shower</v-icon>
                    <span class="tw-text-zinc-700 tw-text-base tw-font-medium">
                      <template v-if="booking.booking_type === 'SWAP'">
                        {{ otherUserProperty.baths || 0 }} Bathrooms
                      </template>
                      <template v-else>
                        {{ booking.toHome.baths || 0 }} Bathrooms
                      </template>
                    </span>
                  </div>
                </div>
              </v-col>
              <v-col cols="12" sm="5">
                <BeastImage
                  :src="booking.booking_type === 'SWAP' ?
                    (otherUserProperty.photos && otherUserProperty.photos.length > 0 ? otherUserProperty.photos[0].src : '') :
                    (booking.toHome.photos && booking.toHome.photos.length > 0 ? booking.toHome.photos[0].src : '')"
                  :alt="booking.booking_type === 'SWAP' ? otherUserProperty.title : booking.toHome.title"
                  class="tw-aspect-video tw-rounded-lg tw-shadow-sm tw-border tw-border-gray-100"
                />
              </v-col>
              <v-col cols="12" class="tw-mt-2">
                <div class="tw-flex tw-items-center tw-gap-2 tw-text-zinc-700 tw-text-lg tw-font-medium">
                  <v-icon color="primary" size="22">mdi-calendar-range</v-icon>
                  {{ formatDateRange(booking.start_at, booking.end_at) }}
                </div>
              </v-col>
            </v-row>

            <v-divider class="tw-my-2" />

            <!-- Guests Section -->
            <div class="tw-flex tw-flex-col tw-gap-4">
              <div class="tw-text-zinc-800 tw-text-xl tw-font-bold">Guests</div>

              <div class="tw-flex tw-flex-col sm:tw-flex-row tw-gap-6">
                <div class="tw-flex tw-items-center tw-gap-2">
                  <v-icon color="primary" size="22" class="tw-min-w-[22px]">mdi-account-group</v-icon>
                  <span class="tw-text-zinc-700 tw-text-base tw-font-medium">
                    <template v-if="booking.booking_type === 'SWAP'">
                      {{ otherUserProperty.guests || 0 }} People
                    </template>
                    <template v-else>
                      {{ booking.toHome.guests || 0 }} People
                    </template>
                  </span>
                </div>

                <div class="tw-flex tw-items-center tw-gap-2">
                  <v-icon color="primary" size="22" class="tw-min-w-[22px]">mdi-paw</v-icon>
                  <span class="tw-text-zinc-700 tw-text-base tw-font-medium">{{ displayedPets }}</span>
                </div>
              </div>
            </div>

            <v-divider class="tw-my-2" />

            <!-- Payment Section -->
            <div class="tw-flex tw-flex-col tw-gap-4">
              <div class="tw-text-zinc-800 tw-text-xl tw-font-bold">
                {{ isSwapBooking ? 'Cleaning Fee' : 'Payment Amount' }}
              </div>

              <div class="tw-bg-blue-50 tw-rounded-lg tw-p-4 tw-flex tw-items-center tw-gap-2">
                <v-icon color="primary" size="22">mdi-cash</v-icon>
                <span class="tw-text-zinc-700 tw-text-base tw-font-medium">
                  Total Due: ${{
                    isSwapBooking
                      ? formatNumberToDisplay(booking.extra_info.priceInfo.getCleaningFee)
                      : formatNumberToDisplay(booking.extra_info.priceInfo.getTotalMoney)
                  }}
                </span>
              </div>
            </div>

            <v-divider class="tw-my-2" />

            <!-- Reference Code Section -->
            <div class="tw-flex tw-flex-col tw-gap-3">
              <div class="tw-text-zinc-800 tw-text-xl tw-font-bold">Reference Code</div>
              <div class="tw-bg-gray-50 tw-rounded-lg tw-p-3 tw-text-zinc-700 tw-text-base tw-font-mono">
                {{ booking.code }}
              </div>
            </div>
          </GoodCard2>

          <!-- Host Actions -->
          <template v-if="isHost">
            <div class="tw-bg-gray-50 tw-rounded-lg tw-p-4 tw-border tw-border-gray-200 tw-mt-2">
              <div class="tw-text-zinc-700 tw-text-lg tw-font-medium tw-mb-4">
                Review the request and take action from the bookings page:
              </div>
              <div class="tw-flex tw-flex-wrap tw-gap-4">
                <GoodButton
                  class="tw-font-medium tw-px-6 tw-py-3 tw-text-base"
                  color="primary"
                  @click="$router.push({
                    path: '/bookings',
                    query: {
                      booking_type: isSwapBooking ? 'swap' : 'booking',
                      booking_id: booking.id
                    }
                  })"
                >
                  <v-icon left size="20" class="tw-mr-2">mdi-view-list</v-icon>
                  Go to Bookings
                </GoodButton>
                <GoodButton
                  class="tw-font-medium tw-px-6 tw-py-3 tw-text-base"
                  @click="$router.push(`/messages`)"
                >
                  <v-icon left size="20" class="tw-mr-2">mdi-message</v-icon>
                  Message Guest
                </GoodButton>
              </div>
            </div>
          </template>

          <!-- Guest Actions -->
          <template v-else>
            <div class="tw-bg-gray-50 tw-rounded-lg tw-p-4 tw-border tw-border-gray-200 tw-mt-2">
              <div class="tw-text-zinc-700 tw-text-lg tw-font-medium tw-mb-4">
                While you wait, we recommend you finish your Twimo profile:
              </div>
              <div class="tw-flex tw-flex-wrap tw-gap-4">
                <GoodButton
                  class="tw-font-medium tw-px-6 tw-py-3 tw-text-base"
                  @click="$router.push('/profile')"
                >
                  <v-icon left size="20" class="tw-mr-2">mdi-account-edit</v-icon>
                  Complete Your Profile
                </GoodButton>
                <GoodButton
                  class="tw-font-medium tw-px-6 tw-py-3 tw-text-base"
                  @click="$router.push(`/messages`)"
                >
                  <v-icon left size="20" class="tw-mr-2">mdi-message</v-icon>
                  Message Host
                </GoodButton>
              </div>
            </div>
          </template>
        </v-col>
      </template>

      <template v-else-if="booking.status === 'accepted'">
        <!-- Booking Type Badge -->
        <v-col cols="12" class="tw-mb-2">
          <div class="tw-flex tw-justify-start">
            <div v-if="isSwapBooking" class="tw-bg-purple-100 tw-text-purple-800 tw-px-4 tw-py-1 tw-rounded-full tw-text-sm tw-font-medium tw-flex tw-items-center">
              <v-icon size="16" class="tw-mr-1">mdi-swap-horizontal</v-icon>
              Home Swap
            </div>
            <div v-else class="tw-bg-blue-100 tw-text-blue-800 tw-px-4 tw-py-1 tw-rounded-full tw-text-sm tw-font-medium tw-flex tw-items-center">
              <v-icon size="16" class="tw-mr-1">mdi-home</v-icon>
              Regular Booking
            </div>
          </div>
        </v-col>

        <v-col cols="12" class="tw-flex tw-flex-col tw-gap-8">
          <!-- Host View -->
          <template v-if="isHost">
            <div class="tw-bg-white tw-rounded-xl tw-shadow-sm tw-p-6 tw-border tw-border-gray-100">
              <div class="tw-flex tw-items-center tw-gap-2 tw-mb-4">
                <v-icon color="success" size="28">mdi-check-circle</v-icon>
                <div class="tw-text-primary tw-text-3xl tw-font-bold">
                  {{ isSwapBooking ? 'Swap Accepted' : 'Booking Accepted' }}{{ !isSwapBooking || (isSwapBooking && (booking.host_cleaning_fee_enabled || booking.guest_cleaning_fee_enabled)) ? ' - Awaiting Payment' : '' }}
                </div>
              </div>

              <div class="tw-text-zinc-700 tw-text-xl tw-font-medium tw-mb-3">
                You have approved {{ booking.user.first_name }}'s {{ isSwapBooking ? 'swap' : 'booking' }} request for
                {{ booking.booking_type === 'SWAP' ? (currentUserProperty?.title || 'your property') : (booking.toHome?.title || 'the property') }}!
              </div>

              <div
v-if="!isSwapBooking || (isSwapBooking && (booking.host_cleaning_fee_enabled || booking.guest_cleaning_fee_enabled))"
                   class="tw-bg-blue-50 tw-rounded-lg tw-p-4 tw-text-zinc-700 tw-text-lg">
                <!-- For regular bookings or Venmo payments -->
                <template v-if="booking.extra_info?.venmo_payment">
                  <div class="tw-flex tw-items-center tw-gap-2">
                    <v-icon color="info" size="22">mdi-information-outline</v-icon>
                    {{ `${isSwapBooking ? 'Swap partner' : 'Guest'} has marked Venmo payment of $${formatNumberToDisplay(booking.extra_info.venmo_payment.amount)} as complete.` }}
                  </div>
                </template>

                <!-- For swap bookings with Stripe payments -->
                <template v-else-if="isSwapBooking">
                  <!-- Check if guest has completed their payment -->
                  <template v-if="booking.extra_info?.priceInfo?.guestPaymentIntentCompleted">
                    <!-- If host needs to pay and hasn't yet -->
                    <template v-if="booking.guest_cleaning_fee_enabled && !booking.extra_info?.priceInfo?.hostPaymentIntentCompleted">
                      <div class="tw-flex tw-items-start tw-gap-2">
                        <v-icon color="warning" size="22" class="tw-mt-0.5">mdi-alert-circle</v-icon>
                        <div>
                          <span class="tw-font-medium">Action Required:</span> {{ booking.user.first_name }} has completed their payment. Please complete your payment for the guest home cleaning fee to finalize the swap.
                        </div>
                      </div>
                    </template>
                    <!-- If host doesn't need to pay or has already paid -->
                    <template v-else>
                      <div class="tw-flex tw-items-center tw-gap-2">
                        <v-icon color="success" size="22">mdi-check-circle</v-icon>
                        {{ `${booking.user.first_name} has completed their payment. The swap is now confirmed!` }}
                      </div>
                    </template>
                  </template>
                  <!-- If guest hasn't completed their payment -->
                  <template v-else>
                    <div class="tw-flex tw-items-center tw-gap-2">
                      <v-icon color="info" size="22">mdi-clock-outline</v-icon>
                      {{ `Waiting for ${booking.user.first_name} to complete payment.` }}
                    </div>
                  </template>
                </template>

                <!-- For regular bookings -->
                <template v-else>
                  <div class="tw-flex tw-items-center tw-gap-2">
                    <v-icon color="info" size="22">mdi-clock-outline</v-icon>
                    {{ `Waiting for guest to complete payment.` }}
                  </div>
                </template>
              </div>

              <div
v-else-if="isSwapBooking && !booking.host_cleaning_fee_enabled && !booking.guest_cleaning_fee_enabled"
                   class="tw-bg-green-50 tw-rounded-lg tw-p-4 tw-text-green-800 tw-text-lg tw-flex tw-items-center tw-gap-2">
                <v-icon color="success" size="22">mdi-check-circle</v-icon>
                No cleaning fees are required for this swap. The swap is confirmed!
              </div>
            </div>
          </template>

          <!-- Guest View -->
          <template v-else>
            <div class="tw-bg-white tw-rounded-xl tw-shadow-sm tw-p-6 tw-border tw-border-gray-100">
              <div class="tw-flex tw-items-center tw-gap-2 tw-mb-4">
                <v-icon color="success" size="28">mdi-check-circle</v-icon>
                <div class="tw-text-primary tw-text-3xl tw-font-bold">
                  {{ booking.toHome?.user?.first_name || 'The host' }} has Approved Your {{ isSwapBooking ? 'Swap' : 'Booking' }} Request!
                </div>
              </div>

              <div class="tw-text-zinc-700 tw-text-xl tw-font-medium tw-mb-3">
                Great news! Your request for {{ booking.booking_type === 'SWAP' ? (otherUserProperty?.title || 'the property') : (booking.toHome?.title || 'the property') }} has been approved.
              </div>

              <div
v-if="!isSwapBooking || (isSwapBooking && (booking.host_cleaning_fee_enabled || booking.guest_cleaning_fee_enabled))"
                   class="tw-bg-blue-50 tw-rounded-lg tw-p-4 tw-text-zinc-700 tw-text-lg">
                <!-- For regular bookings or Venmo payments -->
                <template v-if="booking.extra_info?.venmo_payment">
                  <div class="tw-flex tw-items-center tw-gap-2">
                    <v-icon color="info" size="22">mdi-information-outline</v-icon>
                    {{ 'Your Venmo payment has been marked as sent. Waiting for host verification.' }}
                  </div>
                </template>

                <!-- For swap bookings with Stripe payments -->
                <template v-else-if="isSwapBooking">
                  <!-- Check if guest (first host) has completed their payment -->
                  <template v-if="booking.extra_info?.priceInfo?.guestPaymentIntentCompleted">
                    <!-- If host has completed their payment -->
                    <template v-if="booking.extra_info?.priceInfo?.hostPaymentIntentCompleted">
                      <div class="tw-flex tw-items-center tw-gap-2">
                        <v-icon color="success" size="22">mdi-check-circle</v-icon>
                        {{ 'Both cleaning fee payments have been completed. Your swap is confirmed!' }}
                      </div>
                    </template>
                    <!-- If host hasn't completed their payment yet -->
                    <template v-else-if="booking.guest_cleaning_fee_enabled">
                      <div class="tw-flex tw-items-center tw-gap-2">
                        <v-icon color="info" size="22">mdi-clock-outline</v-icon>
                        {{ `Your payment has been completed. Waiting for ${otherUserProperty.user.first_name} to complete their payment.` }}
                      </div>
                    </template>
                    <!-- If host doesn't need to pay -->
                    <template v-else>
                      <div class="tw-flex tw-items-center tw-gap-2">
                        <v-icon color="success" size="22">mdi-check-circle</v-icon>
                        {{ 'Your payment has been completed. The swap is confirmed!' }}
                      </div>
                    </template>
                  </template>
                  <!-- If guest hasn't completed their payment -->
                  <template v-else>
                    <div class="tw-flex tw-items-start tw-gap-2">
                      <v-icon color="warning" size="22" class="tw-mt-0.5">mdi-alert-circle</v-icon>
                      <div>
                        <span class="tw-font-medium">Action Required:</span> Please complete your {{ booking.host_cleaning_fee_enabled ? 'cleaning fee ' : '' }}payment to finalize your swap.
                      </div>
                    </div>
                  </template>
                </template>

                <!-- For regular bookings -->
                <template v-else>
                  <div class="tw-flex tw-items-start tw-gap-2">
                    <v-icon color="warning" size="22" class="tw-mt-0.5">mdi-alert-circle</v-icon>
                    <div>
                      <span class="tw-font-medium">Action Required:</span> Please complete your payment to finalize your booking.
                    </div>
                  </div>
                </template>
              </div>

              <div
v-else-if="isSwapBooking && !booking.host_cleaning_fee_enabled && !booking.guest_cleaning_fee_enabled"
                   class="tw-bg-green-50 tw-rounded-lg tw-p-4 tw-text-green-800 tw-text-lg tw-flex tw-items-center tw-gap-2">
                <v-icon color="success" size="22">mdi-check-circle</v-icon>
                No cleaning fees are required for this swap. The swap is confirmed!
              </div>
            </div>
          </template>

          <!-- Property Details Card -->
          <GoodCard2 class="tw-flex tw-flex-col tw-gap-6 sm:tw-w-[80%] lg:tw-w-[70%] tw-p-6">
            <v-row>
              <v-col cols="12" sm="7" class="tw-flex tw-flex-col tw-gap-4">
                <div class="tw-text-zinc-800 tw-text-2xl tw-font-bold">
                  <template v-if="booking.booking_type === 'SWAP'">
                    {{ otherUserProperty.title }}
                  </template>
                  <template v-else>
                    {{ booking.toHome.title }}
                  </template>
                </div>

                <div class="tw-text-zinc-600 tw-text-lg tw-font-medium">
                  {{ formattedAddress }}
                </div>

                <div class="tw-flex tw-flex-row tw-gap-6 tw-mt-2">
                  <div class="tw-flex tw-items-center tw-gap-2">
                    <v-icon color="primary" size="22">mdi-bed</v-icon>
                    <span class="tw-text-zinc-700 tw-text-base tw-font-medium">
                      <template v-if="booking.booking_type === 'SWAP'">
                        {{ otherUserProperty.beds || 0 }} Beds
                      </template>
                      <template v-else>
                        {{ booking.toHome.beds || 0 }} Beds
                      </template>
                    </span>
                  </div>

                  <div class="tw-flex tw-items-center tw-gap-2">
                    <v-icon color="primary" size="22">mdi-shower</v-icon>
                    <span class="tw-text-zinc-700 tw-text-base tw-font-medium">
                      <template v-if="booking.booking_type === 'SWAP'">
                        {{ otherUserProperty.baths || 0 }} Bathrooms
                      </template>
                      <template v-else>
                        {{ booking.toHome.baths || 0 }} Bathrooms
                      </template>
                    </span>
                  </div>
                </div>
              </v-col>
              <v-col cols="12" sm="5">
                <BeastImage
                  :src="booking.booking_type === 'SWAP' ?
                    (otherUserProperty.photos && otherUserProperty.photos.length > 0 ? otherUserProperty.photos[0].src : '') :
                    (booking.toHome.photos && booking.toHome.photos.length > 0 ? booking.toHome.photos[0].src : '')"
                  :alt="booking.booking_type === 'SWAP' ? otherUserProperty.title : booking.toHome.title"
                  class="tw-aspect-video tw-rounded-lg tw-shadow-sm tw-border tw-border-gray-100"
                />
              </v-col>
              <v-col cols="12" class="tw-mt-2">
                <div class="tw-flex tw-items-center tw-gap-2 tw-text-zinc-700 tw-text-lg tw-font-medium">
                  <v-icon color="primary" size="22">mdi-calendar-range</v-icon>
                  {{ formatDateRange(booking.start_at, booking.end_at) }}
                </div>
              </v-col>
            </v-row>

            <v-divider class="tw-my-2" />

            <!-- Guests Section -->
            <div class="tw-flex tw-flex-col tw-gap-4">
              <div class="tw-text-zinc-800 tw-text-xl tw-font-bold">Guests</div>

              <div class="tw-flex tw-flex-col sm:tw-flex-row tw-gap-6">
                <div class="tw-flex tw-items-center tw-gap-2">
                  <v-icon color="primary" size="22" class="tw-min-w-[22px]">mdi-account-group</v-icon>
                  <span class="tw-text-zinc-700 tw-text-base tw-font-medium">
                    <template v-if="booking.booking_type === 'SWAP'">
                      {{ otherUserProperty.guests || 0 }} People
                    </template>
                    <template v-else>
                      {{ booking.toHome.guests || 0 }} People
                    </template>
                  </span>
                </div>

                <div class="tw-flex tw-items-center tw-gap-2">
                  <v-icon color="primary" size="22" class="tw-min-w-[22px]">mdi-paw</v-icon>
                  <span class="tw-text-zinc-700 tw-text-base tw-font-medium">{{ displayedPets }}</span>
                </div>
              </div>
            </div>

            <v-divider class="tw-my-2" />
            <!-- Payment Section -->
            <div class="tw-flex tw-flex-col tw-gap-4">
              <div class="tw-text-zinc-800 tw-text-xl tw-font-bold">
                {{ isSwapBooking ? 'Swap Cleaning Fees' : 'Payment Amount' }}
              </div>

              <!-- Regular booking payment display -->
              <div v-if="!isSwapBooking" class="tw-bg-blue-50 tw-rounded-lg tw-p-4 tw-flex tw-items-center tw-gap-2">
                <v-icon color="primary" size="22">mdi-cash</v-icon>
                <span class="tw-text-zinc-700 tw-text-base tw-font-medium">
                  Total Due: ${{ formatNumberToDisplay(booking.extra_info.priceInfo.getTotalMoney) }}
                </span>
              </div>

              <!-- Swap booking payment display -->
              <div v-else class="tw-flex tw-flex-col tw-gap-4">
                <!-- Host Home Cleaning Fee (paid by guest) -->
                <div class="tw-bg-purple-50 tw-rounded-lg tw-p-4 tw-flex tw-flex-col tw-gap-2">
                  <div class="tw-flex tw-items-center tw-justify-between tw-flex-wrap">
                    <div class="tw-flex tw-items-center tw-gap-2">
                      <v-icon color="primary" size="22">mdi-home-outline</v-icon>
                      <span class="tw-font-semibold tw-text-zinc-800">Host Home Cleaning Fee:</span>
                    </div>
                    <div>
                      <span v-if="booking.host_cleaning_fee_enabled" class="tw-text-base">
                        <template v-if="booking.extra_info.priceInfo?.guestPaymentIntentCompleted">
                          <span class="tw-bg-green-100 tw-text-green-800 tw-px-3 tw-py-1 tw-rounded-full tw-text-sm tw-font-medium tw-flex tw-items-center">
                            <v-icon size="16" class="tw-mr-1">mdi-check-circle</v-icon>
                            Paid (${{ formatNumberToDisplay(booking.extra_info.priceInfo?.guestPaymentAmount || booking.toHome?.cleaning_fee || 0) }})
                          </span>
                        </template>
                        <template v-else-if="booking.extra_info.priceInfo?.guestPaymentIntent">
                          <span class="tw-bg-orange-100 tw-text-orange-800 tw-px-3 tw-py-1 tw-rounded-full tw-text-sm tw-font-medium tw-flex tw-items-center">
                            <v-icon size="16" class="tw-mr-1">mdi-clock-outline</v-icon>
                            Pending (${{ formatNumberToDisplay(booking.extra_info.priceInfo?.guestPaymentAmount || booking.toHome?.cleaning_fee || 0) }})
                          </span>
                        </template>
                        <template v-else>
                          <span class="tw-bg-purple-100 tw-text-purple-800 tw-px-3 tw-py-1 tw-rounded-full tw-text-sm tw-font-medium">
                            Required (${{ formatNumberToDisplay(booking.toHome?.cleaning_fee || 0) }})
                          </span>
                        </template>
                      </span>
                      <span v-else class="tw-bg-gray-100 tw-text-gray-600 tw-px-3 tw-py-1 tw-rounded-full tw-text-sm tw-font-medium">
                        Not Required
                      </span>
                    </div>
                  </div>

                  <div v-if="!isHost && booking.host_cleaning_fee_enabled && booking.extra_info.priceInfo?.guestPaymentIntent && !booking.extra_info.priceInfo?.guestPaymentIntentCompleted" class="tw-mt-2">
                    <GoodButton
                      class="tw-py-2 tw-text-sm tw-h-auto"
                      @click="$router.push(`/bookings/${booking.id}/payment`)"
                    >
                      <v-icon left size="18" class="tw-mr-1">mdi-credit-card</v-icon>
                      Complete Payment
                    </GoodButton>
                  </div>
                </div>

                <!-- Guest Home Cleaning Fee (paid by host) -->
                <div class="tw-bg-purple-50 tw-rounded-lg tw-p-4 tw-flex tw-flex-col tw-gap-2">
                  <div class="tw-flex tw-items-center tw-justify-between tw-flex-wrap">
                    <div class="tw-flex tw-items-center tw-gap-2">
                      <v-icon color="primary" size="22">mdi-home-outline</v-icon>
                      <span class="tw-font-semibold tw-text-zinc-800">Guest Home Cleaning Fee:</span>
                    </div>
                    <div>
                      <span v-if="booking.guest_cleaning_fee_enabled" class="tw-text-base">
                        <template v-if="booking.extra_info.priceInfo?.hostPaymentIntentCompleted">
                          <span class="tw-bg-green-100 tw-text-green-800 tw-px-3 tw-py-1 tw-rounded-full tw-text-sm tw-font-medium tw-flex tw-items-center">
                            <v-icon size="16" class="tw-mr-1">mdi-check-circle</v-icon>
                            Paid (${{ formatNumberToDisplay(booking.extra_info.priceInfo?.hostPaymentAmount || booking.fromHome?.cleaning_fee || 0) }})
                          </span>
                        </template>
                        <template v-else-if="booking.extra_info.priceInfo?.hostPaymentIntent">
                          <span class="tw-bg-orange-100 tw-text-orange-800 tw-px-3 tw-py-1 tw-rounded-full tw-text-sm tw-font-medium tw-flex tw-items-center">
                            <v-icon size="16" class="tw-mr-1">mdi-clock-outline</v-icon>
                            Pending (${{ formatNumberToDisplay(booking.extra_info.priceInfo?.hostPaymentAmount || booking.fromHome?.cleaning_fee || 0) }})
                          </span>
                        </template>
                        <template v-else>
                          <span class="tw-bg-purple-100 tw-text-purple-800 tw-px-3 tw-py-1 tw-rounded-full tw-text-sm tw-font-medium">
                            Required (${{ formatNumberToDisplay(booking.fromHome?.cleaning_fee || 0) }})
                          </span>
                        </template>
                      </span>
                      <span v-else class="tw-bg-gray-100 tw-text-gray-600 tw-px-3 tw-py-1 tw-rounded-full tw-text-sm tw-font-medium">
                        Not Required
                      </span>
                    </div>
                  </div>

                  <div v-if="isHost && booking.guest_cleaning_fee_enabled && booking.extra_info.priceInfo?.hostPaymentIntent && !booking.extra_info.priceInfo?.hostPaymentIntentCompleted" class="tw-mt-2">
                    <GoodButton
                      class="tw-py-2 tw-text-sm tw-h-auto"
                      @click="$router.push(`/bookings/${booking.id}/payment`)"
                    >
                      <v-icon left size="18" class="tw-mr-1">mdi-credit-card</v-icon>
                      Complete Payment
                    </GoodButton>
                  </div>
                </div>
              </div>
            </div>
            <v-divider />
            <!-- Reference Code Section -->
            <div class="tw-flex tw-flex-col tw-gap-3">
              <div class="tw-text-zinc-800 tw-text-xl tw-font-bold">Reference Code</div>
              <div class="tw-bg-gray-50 tw-rounded-lg tw-p-3 tw-text-zinc-700 tw-text-base tw-font-mono">
                {{ booking.code }}
              </div>
            </div>

            <v-divider class="tw-my-2" />

            <!-- Cancellation Policy Section -->
            <div class="tw-flex tw-flex-col tw-gap-3">
              <div class="tw-text-zinc-800 tw-text-xl tw-font-bold">If You Need to Cancel</div>
              <div class="tw-bg-amber-50 tw-rounded-lg tw-p-3 tw-text-amber-800 tw-text-sm tw-font-medium">
                Guests have 24 hours to cancel reservations in California for a full refund
              </div>
              <div class="tw-text-zinc-600 tw-text-base">
                As a Friends & Family guest, please be considerate of your Host if you need to
                cancel. In the event you need to reschedule or cancel your booking, please let your
                Host know ASAP, so they can ensure they are able to avoid cleaning cost or any
                additional fees.
              </div>
            </div>

            <v-divider class="tw-my-2" />

            <!-- My Stays Section -->
            <div class="tw-flex tw-flex-col tw-gap-3">
              <div class="tw-text-zinc-800 tw-text-xl tw-font-bold">My Stays</div>
              <div class="tw-text-zinc-600 tw-text-base">
                Your Host created a Private Booking Link, tailored to you. This booking link is
                specific to you, with your pricing. You can find this link and rebook this Home by
                going to
                <BoldPurpleText class="tw-cursor-pointer tw-font-semibold" @click="$router.push(getBookingsLink)">
                  My Stays
                </BoldPurpleText>
                or by clicking
                <BoldPurpleText
                  class="tw-cursor-pointer tw-font-semibold"
                  @click="$router.push(isHost ?
                    `/${booking.toHome.slug}/edit` :
                    (isPrivateBooking && booking.from_sharable_link ?
                      `/${booking.toHome.slug}?sharable_link=${booking.from_sharable_link}` :
                      `/${booking.toHome.slug}`))"
                >
                  here
                </BoldPurpleText>
              </div>
            </div>
          </GoodCard2>

          <!-- Action Buttons -->
          <div class="tw-bg-gray-50 tw-rounded-lg tw-p-4 tw-border tw-border-gray-200 tw-mt-4 tw-mb-8">
            <!-- Host Actions -->
            <template v-if="isHost">
              <div class="tw-flex tw-flex-wrap tw-gap-4">
                <GoodButton
                  class="tw-font-medium tw-px-6 tw-py-3 tw-text-base"
                  color="primary"
                  @click="$router.push({
                    path: '/bookings',
                    query: {
                      booking_type: isSwapBooking ? 'swap' : 'booking',
                      booking_id: booking.id
                    }
                  })"
                >
                  <v-icon left size="20" class="tw-mr-2">mdi-view-list</v-icon>
                  Go to Bookings
                </GoodButton>
                <GoodButton
                  class="tw-font-medium tw-px-6 tw-py-3 tw-text-base"
                  @click="$router.push(`/messages`)"
                >
                  <v-icon left size="20" class="tw-mr-2">mdi-message</v-icon>
                  Message Guest
                </GoodButton>
              </div>
            </template>

            <!-- Guest Actions -->
            <template v-else>
              <div class="tw-flex tw-flex-wrap tw-gap-4">
                <GoodButton
                  v-if="!isSwapBooking || (isSwapBooking && (booking.host_cleaning_fee_enabled || booking.guest_cleaning_fee_enabled))"
                  class="tw-font-medium tw-px-6 tw-py-3 tw-text-base"
                  color="success"
                  @click="$router.push(`/bookings/${booking.id}/payment`)"
                >
                  <v-icon left size="20" class="tw-mr-2">mdi-credit-card</v-icon>
                  <template v-if="booking.extra_info?.venmo_payment">
                    View Payment Status
                  </template>
                  <template v-else-if="isSwapBooking">
                    <template v-if="isHost">
                      <!-- Second host (receiver) -->
                      <template v-if="booking.guest_cleaning_fee_enabled && !booking.extra_info.priceInfo?.hostPaymentIntentCompleted">
                        Pay Guest Home Cleaning Fee
                      </template>
                      <template v-else-if="booking.extra_info.priceInfo?.guestPaymentIntentCompleted && booking.extra_info.priceInfo?.hostPaymentIntentCompleted">
                        View Swap Details
                      </template>
                      <template v-else-if="booking.extra_info.priceInfo?.guestPaymentIntentCompleted">
                        View Payment Status
                      </template>
                      <template v-else>
                        Waiting for Guest Payment
                      </template>
                    </template>
                    <template v-else>
                      <!-- First host (requester) -->
                      <template v-if="booking.host_cleaning_fee_enabled && !booking.extra_info.priceInfo?.guestPaymentIntentCompleted">
                        Pay Host Home Cleaning Fee
                      </template>
                      <template v-else-if="booking.extra_info.priceInfo?.guestPaymentIntentCompleted && booking.extra_info.priceInfo?.hostPaymentIntentCompleted">
                        View Swap Details
                      </template>
                      <template v-else-if="booking.extra_info.priceInfo?.guestPaymentIntentCompleted">
                        Payment Complete - View Status
                      </template>
                      <template v-else>
                        View Payment Status
                      </template>
                    </template>
                  </template>
                  <template v-else>
                    Make Payment
                  </template>
                </GoodButton>
                <GoodButton
                  class="tw-font-medium tw-px-6 tw-py-3 tw-text-base"
                  @click="$router.push(`/messages`)"
                >
                  <v-icon left size="20" class="tw-mr-2">mdi-message</v-icon>
                  Message Host
                </GoodButton>
              </div>
            </template>
          </div>
        </v-col>
      </template>

      <template v-else-if="booking.status === 'completed'">
        <!-- Booking Type Badge -->
        <v-col cols="12" class="tw-mb-2">
          <div class="tw-flex tw-justify-start">
            <div v-if="isSwapBooking" class="tw-bg-purple-100 tw-text-purple-800 tw-px-4 tw-py-1 tw-rounded-full tw-text-sm tw-font-medium tw-flex tw-items-center">
              <v-icon size="16" class="tw-mr-1">mdi-swap-horizontal</v-icon>
              Home Swap
            </div>
            <div v-else class="tw-bg-blue-100 tw-text-blue-800 tw-px-4 tw-py-1 tw-rounded-full tw-text-sm tw-font-medium tw-flex tw-items-center">
              <v-icon size="16" class="tw-mr-1">mdi-home</v-icon>
              Regular Booking
            </div>
          </div>
        </v-col>

        <v-col cols="12" class="tw-flex tw-flex-col tw-gap-8">
          <!-- Host View -->
          <template v-if="isHost">
            <div class="tw-bg-white tw-rounded-xl tw-shadow-sm tw-p-6 tw-border tw-border-gray-100">
              <div class="tw-flex tw-items-center tw-gap-2 tw-mb-4">
                <v-icon color="success" size="28">mdi-check-circle</v-icon>
                <div class="tw-text-primary tw-text-3xl tw-font-bold">
                  {{ isSwapBooking ? 'Swap Confirmed: Prepare for Home Exchange' : 'Guest Payment Successful: Prepare for Their Arrival' }}
                </div>
              </div>

              <div class="tw-text-zinc-700 tw-text-xl tw-font-medium tw-mb-4">
                {{ isSwapBooking ? `Your home swap with ${booking.user.first_name} is confirmed.` : `${booking.user.first_name}'s payment has been confirmed.` }} Here's what you need to
                do:
              </div>

              <ul class="tw-text-zinc-600 tw-text-lg tw-list-disc tw-ml-6 tw-space-y-3">
                <li>
                  Prepare your property for {{ isSwapBooking ? 'your swap partner\'s' : 'the guest\'s' }} arrival on
                  {{ formatDateRange(booking.start_at, booking.end_at) }}
                </li>
                <li>
                  Double-check that all amenities mentioned in your listing are available and in good
                  condition
                </li>
                <li>Ensure the property is thoroughly cleaned before {{ isSwapBooking ? 'your swap partner\'s' : 'the guest\'s' }} check-in</li>
                <li>Review any special requests or communications from {{ isSwapBooking ? 'your swap partner' : 'the guest' }}</li>
                <li>Update your calendar to reflect this confirmed {{ isSwapBooking ? 'swap' : 'booking' }}</li>
              </ul>
            </div>
          </template>

          <!-- Guest View -->
          <template v-else>
            <div class="tw-bg-white tw-rounded-xl tw-shadow-sm tw-p-6 tw-border tw-border-gray-100">
              <div class="tw-flex tw-items-center tw-gap-2 tw-mb-4">
                <v-icon color="success" size="28">mdi-check-circle</v-icon>
                <div class="tw-text-primary tw-text-3xl tw-font-bold">
                  {{ isSwapBooking ? 'Start Packing! Your Home Swap is Confirmed' : 'Start Your Packing List! Your Trip is Booked' }}
                </div>
              </div>

              <div class="tw-text-zinc-700 tw-text-xl tw-font-medium tw-mb-2">
                {{ isSwapBooking && otherUserProperty?.cleaning_fee ? 'Your cleaning fee payment has been confirmed and your' : isSwapBooking ? 'Your' : 'Your payment has been confirmed and your' }} {{ isSwapBooking ? 'home swap at' : 'stay at' }}
                {{ otherUserProperty?.title || 'the property' }} is {{ isSwapBooking ? 'confirmed' : 'booked' }}!
              </div>

              <div class="tw-text-zinc-600 tw-text-lg tw-bg-green-50 tw-rounded-lg tw-p-4 tw-flex tw-items-center tw-gap-2">
                <v-icon color="success" size="22">mdi-calendar-check</v-icon>
                {{ otherUserProperty?.user?.first_name || 'The host' }} is preparing for your arrival on
                {{ formatDateRange(booking.start_at, booking.end_at) }}.
              </div>
            </div>
          </template>

          <!-- Property Details Card -->
          <GoodCard2 class="tw-flex tw-flex-col tw-gap-6 sm:tw-w-[80%] lg:tw-w-[70%] tw-p-6">
            <v-row>
              <v-col cols="12" sm="7" class="tw-flex tw-flex-col tw-gap-4">
                <div class="tw-text-zinc-800 tw-text-2xl tw-font-bold">
                  {{ booking.booking_type === 'SWAP' ? otherUserProperty.title : (booking.toHome?.title || 'Property') }}
                </div>

                <div class="tw-text-zinc-600 tw-text-lg tw-font-medium">
                  {{ formattedAddress }}
                </div>

                <div class="tw-flex tw-flex-row tw-gap-6 tw-mt-2">
                  <div class="tw-flex tw-items-center tw-gap-2">
                    <v-icon color="primary" size="22">mdi-bed</v-icon>
                    <span class="tw-text-zinc-700 tw-text-base tw-font-medium">
                      {{ booking.booking_type === 'SWAP' ? otherUserProperty.beds : (booking.toHome?.beds || 0) }} Beds
                    </span>
                  </div>

                  <div class="tw-flex tw-items-center tw-gap-2">
                    <v-icon color="primary" size="22">mdi-shower</v-icon>
                    <span class="tw-text-zinc-700 tw-text-base tw-font-medium">
                      {{ booking.booking_type === 'SWAP' ? otherUserProperty.baths : (booking.toHome?.baths || 0) }} Bathrooms
                    </span>
                  </div>
                </div>
              </v-col>
              <v-col cols="12" sm="5">
                <BeastImage
                  :src="booking.booking_type === 'SWAP' ?
                    (otherUserProperty.photos && otherUserProperty.photos.length > 0 ? otherUserProperty.photos[0].src : '') :
                    (booking.toHome?.photos && booking.toHome.photos.length > 0 ? booking.toHome.photos[0].src : '')"
                  :alt="booking.booking_type === 'SWAP' ? otherUserProperty.title : (booking.toHome?.title || 'Property')"
                  class="tw-aspect-video tw-rounded-lg tw-shadow-sm tw-border tw-border-gray-100"
                />
              </v-col>
              <v-col cols="12" class="tw-mt-2">
                <div class="tw-flex tw-items-center tw-gap-2 tw-text-zinc-700 tw-text-lg tw-font-medium">
                  <v-icon color="primary" size="22">mdi-calendar-range</v-icon>
                  {{ formatDateRange(booking.start_at, booking.end_at) }}
                </div>
              </v-col>
            </v-row>

            <v-divider class="tw-my-2" />

            <!-- Guests Section -->
            <div class="tw-flex tw-flex-col tw-gap-4">
              <div class="tw-text-zinc-800 tw-text-xl tw-font-bold">Guests</div>

              <div class="tw-flex tw-flex-col sm:tw-flex-row tw-gap-6">
                <div class="tw-flex tw-items-center tw-gap-2">
                  <v-icon color="primary" size="22" class="tw-min-w-[22px]">mdi-account-group</v-icon>
                  <span class="tw-text-zinc-700 tw-text-base tw-font-medium">
                    {{ booking.booking_type === 'SWAP' ? otherUserProperty.guests : (booking.toHome?.guests || 0) }} People
                  </span>
                </div>

                <div class="tw-flex tw-items-center tw-gap-2">
                  <v-icon color="primary" size="22" class="tw-min-w-[22px]">mdi-paw</v-icon>
                  <span class="tw-text-zinc-700 tw-text-base tw-font-medium">{{ displayedPets }}</span>
                </div>
              </div>
            </div>

            <v-divider class="tw-my-2" />
            <!-- Payment Section -->
            <div class="tw-flex tw-flex-col tw-gap-4">
              <div class="tw-text-zinc-800 tw-text-xl tw-font-bold">
                {{ isSwapBooking
                  ? (booking.host_cleaning_fee_enabled || booking.guest_cleaning_fee_enabled
                    ? 'Cleaning Fee Completed'
                    : 'Swap Confirmed')
                  : 'Payment Completed'
                }}
              </div>

              <!-- No cleaning fees required -->
              <div
v-if="isSwapBooking && !booking.host_cleaning_fee_enabled && !booking.guest_cleaning_fee_enabled"
                   class="tw-bg-green-50 tw-rounded-lg tw-p-4 tw-flex tw-items-center tw-gap-2">
                <v-icon color="success" size="22">mdi-check-circle</v-icon>
                <span class="tw-text-green-800 tw-text-base tw-font-medium">
                  No cleaning fees required for this swap
                </span>
              </div>

              <!-- Only host cleaning fee required (paid by guest) -->
              <div
v-else-if="isSwapBooking && booking.host_cleaning_fee_enabled && !booking.guest_cleaning_fee_enabled"
                   class="tw-bg-green-50 tw-rounded-lg tw-p-4 tw-flex tw-items-center tw-gap-2">
                <v-icon color="success" size="22">mdi-check-circle</v-icon>
                <span class="tw-text-green-800 tw-text-base tw-font-medium">
                  Host Home Cleaning Fee Paid: ${{ formatNumberToDisplay(booking.extra_info?.priceInfo?.guestPaymentAmount || booking.toHome?.cleaning_fee || 0) }}
                </span>
              </div>

              <!-- Only guest cleaning fee required (paid by host) -->
              <div
v-else-if="isSwapBooking && !booking.host_cleaning_fee_enabled && booking.guest_cleaning_fee_enabled"
                   class="tw-bg-green-50 tw-rounded-lg tw-p-4 tw-flex tw-items-center tw-gap-2">
                <v-icon color="success" size="22">mdi-check-circle</v-icon>
                <span class="tw-text-green-800 tw-text-base tw-font-medium">
                  Guest Home Cleaning Fee Paid: ${{ formatNumberToDisplay(booking.extra_info?.priceInfo?.hostPaymentAmount || booking.fromHome?.cleaning_fee || 0) }}
                </span>
              </div>

              <!-- Both cleaning fees required -->
              <div
v-else-if="isSwapBooking && booking.host_cleaning_fee_enabled && booking.guest_cleaning_fee_enabled"
                   class="tw-bg-green-50 tw-rounded-lg tw-p-4 tw-flex tw-flex-col tw-gap-2">
                <div class="tw-flex tw-items-center tw-gap-2">
                  <v-icon color="success" size="22">mdi-check-circle</v-icon>
                  <span class="tw-text-green-800 tw-text-base tw-font-medium">
                    Host Home Cleaning Fee Paid: ${{ formatNumberToDisplay(booking.extra_info?.priceInfo?.guestPaymentAmount || booking.toHome?.cleaning_fee || 0) }}
                  </span>
                </div>
                <div class="tw-flex tw-items-center tw-gap-2 tw-ml-7">
                  <span class="tw-text-green-800 tw-text-base tw-font-medium">
                    Guest Home Cleaning Fee Paid: ${{ formatNumberToDisplay(booking.extra_info?.priceInfo?.hostPaymentAmount || booking.fromHome?.cleaning_fee || 0) }}
                  </span>
                </div>
              </div>

              <!-- Regular booking -->
              <div v-else class="tw-bg-green-50 tw-rounded-lg tw-p-4 tw-flex tw-items-center tw-gap-2">
                <v-icon color="success" size="22">mdi-check-circle</v-icon>
                <span class="tw-text-green-800 tw-text-base tw-font-medium">
                  Total Paid: ${{ formatNumberToDisplay(booking.extra_info?.priceInfo?.getTotalMoney || 0) }}
                </span>
              </div>
            </div>
            <v-divider class="tw-my-2" />

            <!-- Reference Code Section -->
            <div class="tw-flex tw-flex-col tw-gap-3">
              <div class="tw-text-zinc-800 tw-text-xl tw-font-bold">Reference Code</div>
              <div class="tw-bg-gray-50 tw-rounded-lg tw-p-3 tw-text-zinc-700 tw-text-base tw-font-mono">
                {{ booking.code }}
              </div>
            </div>

            <v-divider class="tw-my-2" />

            <!-- Cancellation Policy Section -->
            <div class="tw-flex tw-flex-col tw-gap-3">
              <div class="tw-text-zinc-800 tw-text-xl tw-font-bold">If You Need to Cancel</div>
              <div class="tw-bg-amber-50 tw-rounded-lg tw-p-3 tw-text-amber-800 tw-text-sm tw-font-medium">
                Guests have 24 hours to cancel reservations in California for a full refund
              </div>
              <div class="tw-text-zinc-600 tw-text-base">
                As a Friends & Family guest, please be considerate of your Host if you need to
                cancel. In the event you need to reschedule or cancel your booking, please let your
                Host know ASAP, so they can ensure they are able to avoid cleaning cost or any
                additional fees.
              </div>
            </div>

            <v-divider class="tw-my-2" />

            <!-- My Stays Section -->
            <div v-if="currentUser.id === booking.user_id" class="tw-flex tw-flex-col tw-gap-3">
              <div class="tw-text-zinc-800 tw-text-xl tw-font-bold">My Stays</div>
              <div class="tw-text-zinc-600 tw-text-base">
                Your Host created a Private Booking Link, tailored to you. This booking link is
                specific to you, with your pricing. You can find this link and rebook this Home by
                going to
                <BoldPurpleText class="tw-cursor-pointer tw-font-semibold" @click="$router.push(getBookingsLink)">
                  My Stays
                </BoldPurpleText>
              </div>
            </div>
          </GoodCard2>

          <!-- Action Buttons -->
          <div class="tw-bg-gray-50 tw-rounded-lg tw-p-4 tw-border tw-border-gray-200 tw-mt-4 tw-mb-8">
            <div class="tw-flex tw-flex-wrap tw-gap-4">
              <!-- Host Actions -->
              <template v-if="isHost">
                <GoodButton
                  class="tw-font-medium tw-px-6 tw-py-3 tw-text-base"
                  color="primary"
                  @click="$router.push({
                    path: '/bookings',
                    query: {
                      booking_type: isSwapBooking ? 'swap' : 'booking',
                      booking_id: booking.id
                    }
                  })"
                >
                  <v-icon left size="20" class="tw-mr-2">mdi-view-list</v-icon>
                  Go to Bookings
                </GoodButton>
                <GoodButton
                  class="tw-font-medium tw-px-6 tw-py-3 tw-text-base"
                  @click="$router.push(`/messages`)"
                >
                  <v-icon left size="20" class="tw-mr-2">mdi-message</v-icon>
                  Message Guest
                </GoodButton>
              </template>

              <!-- Guest Actions -->
              <template v-else>
                <GoodButton
                  class="tw-font-medium tw-px-6 tw-py-3 tw-text-base"
                  color="primary"
                  @click="$router.push({
                    path: '/bookings',
                    query: {
                      booking_type: isSwapBooking ? 'swap' : 'booking',
                      booking_id: booking.id
                    }
                  })"
                >
                  <v-icon left size="20" class="tw-mr-2">mdi-view-list</v-icon>
                  Go to Bookings
                </GoodButton>
                <GoodButton
                  class="tw-font-medium tw-px-6 tw-py-3 tw-text-base"
                  @click="$router.push(`/messages`)"
                >
                  <v-icon left size="20" class="tw-mr-2">mdi-message</v-icon>
                  Message Host
                </GoodButton>
              </template>
            </div>
          </div>
        </v-col>
      </template>

      <template v-else-if="booking.status === 'rejected'">
        <v-col cols="12" class="tw-flex tw-flex-col tw-gap-6">
          <!-- Host View -->
          <template v-if="isHost">
            <div class="tw-bg-white tw-rounded-xl tw-shadow-sm tw-p-6 tw-border tw-border-gray-100">
              <div class="tw-flex tw-items-center tw-gap-2 tw-mb-4">
                <v-icon color="error" size="28">mdi-close-circle</v-icon>
                <div class="tw-text-primary tw-text-3xl tw-font-bold">Booking Request Declined</div>
              </div>
              <div class="tw-text-zinc-700 tw-text-xl tw-font-medium">
                You have declined {{ booking.user.first_name }}'s request for
                {{ booking.toHome.title }}.
              </div>
            </div>

            <!-- Action Buttons -->
            <div class="tw-bg-gray-50 tw-rounded-lg tw-p-4 tw-border tw-border-gray-200 tw-mt-4 tw-mb-8">
              <div class="tw-flex tw-flex-wrap tw-gap-4">
                <GoodButton
                  class="tw-font-medium tw-px-6 tw-py-3 tw-text-base"
                  color="primary"
                  @click="$router.push({
                    path: '/bookings',
                    query: {
                      booking_type: isSwapBooking ? 'swap' : 'booking',
                      booking_id: booking.id
                    }
                  })"
                >
                  <v-icon left size="20" class="tw-mr-2">mdi-view-list</v-icon>
                  Go to Bookings
                </GoodButton>
              </div>
            </div>
          </template>

          <!-- Guest View -->
          <template v-else>
            <div class="tw-bg-white tw-rounded-xl tw-shadow-sm tw-p-6 tw-border tw-border-gray-100">
              <div class="tw-flex tw-items-center tw-gap-2 tw-mb-4">
                <v-icon color="error" size="28">mdi-close-circle</v-icon>
                <div class="tw-text-primary tw-text-3xl tw-font-bold">
                  Booking Request Not Approved
                </div>
              </div>
              <div class="tw-text-zinc-700 tw-text-xl tw-font-medium tw-mb-2">
                Unfortunately, {{ booking.toHome.user.first_name }} was unable to accommodate your
                request for {{ booking.toHome.title }}.
              </div>
              <div class="tw-text-zinc-600 tw-text-lg">
                Please contact the host if you would like to discuss alternative dates or
                arrangements.
              </div>
            </div>

            <!-- Action Buttons -->
            <div class="tw-bg-gray-50 tw-rounded-lg tw-p-4 tw-border tw-border-gray-200 tw-mt-4 tw-mb-8">
              <div class="tw-flex tw-flex-wrap tw-gap-4">
                <GoodButton
                  class="tw-font-medium tw-px-6 tw-py-3 tw-text-base"
                  color="primary"
                  @click="$router.push({
                    path: '/bookings',
                    query: {
                      booking_type: isSwapBooking ? 'swap' : 'booking',
                      booking_id: booking.id
                    }
                  })"
                >
                  <v-icon left size="20" class="tw-mr-2">mdi-view-list</v-icon>
                  Go to Bookings
                </GoodButton>
                <GoodButton
                  class="tw-font-medium tw-px-6 tw-py-3 tw-text-base"
                  @click="$router.push(`/messages`)"
                >
                  <v-icon left size="20" class="tw-mr-2">mdi-message</v-icon>
                  Message Host
                </GoodButton>
              </div>
            </div>
          </template>
        </v-col>
      </template>

      <template v-else-if="booking.status === 'canceled'">
        <!-- Booking Type Badge -->
        <v-col cols="12" class="tw-mb-2">
          <div class="tw-flex tw-justify-start">
            <div v-if="isSwapBooking" class="tw-bg-purple-100 tw-text-purple-800 tw-px-4 tw-py-1 tw-rounded-full tw-text-sm tw-font-medium tw-flex tw-items-center">
              <v-icon size="16" class="tw-mr-1">mdi-swap-horizontal</v-icon>
              Home Swap
            </div>
            <div v-else class="tw-bg-blue-100 tw-text-blue-800 tw-px-4 tw-py-1 tw-rounded-full tw-text-sm tw-font-medium tw-flex tw-items-center">
              <v-icon size="16" class="tw-mr-1">mdi-home</v-icon>
              Regular Booking
            </div>
          </div>
        </v-col>

        <v-col cols="12" class="tw-flex tw-flex-col tw-gap-8">
          <!-- Host View -->
          <template v-if="isHost">
            <div class="tw-bg-white tw-rounded-xl tw-shadow-sm tw-p-6 tw-border tw-border-gray-100">
              <div class="tw-flex tw-items-center tw-gap-2 tw-mb-4">
                <v-icon color="error" size="28">mdi-cancel</v-icon>
                <div class="tw-text-primary tw-text-3xl tw-font-bold">
                  {{ isSwapBooking ? 'Home Swap Cancelled' : 'Booking Cancelled' }}
                </div>
              </div>

              <div class="tw-text-zinc-700 tw-text-xl tw-font-medium tw-mb-3">
                This {{ isSwapBooking ? 'home swap' : 'booking' }} with {{ booking.user.first_name }} for
                {{ booking.booking_type === 'SWAP' ? (currentUserProperty?.title || 'your property') : (booking.toHome?.title || 'the property') }}
                has been cancelled.
              </div>

              <div class="tw-bg-gray-50 tw-rounded-lg tw-p-4 tw-text-zinc-700 tw-text-lg">
                <div class="tw-flex tw-items-start tw-gap-2">
                  <v-icon color="info" size="22" class="tw-mt-0.5">mdi-information-outline</v-icon>
                  <div>
                    <span class="tw-font-medium">Cancellation Details:</span>
                    <div class="tw-mt-1">
                      <template v-if="booking.extra_info?.venmo_payment">
                        This booking was paid via Venmo. Any refunds should be processed manually outside the Twimo platform.
                      </template>
                      <template v-else>
                        Any applicable refunds have been processed according to the cancellation policy.
                      </template>
                    </div>
                    <div class="tw-mt-2">
                      Cancelled on: {{ new Date(booking.extra_info?.cancelled_at).toLocaleDateString() }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </template>

          <!-- Guest View -->
          <template v-else>
            <div class="tw-bg-white tw-rounded-xl tw-shadow-sm tw-p-6 tw-border tw-border-gray-100">
              <div class="tw-flex tw-items-center tw-gap-2 tw-mb-4">
                <v-icon color="error" size="28">mdi-cancel</v-icon>
                <div class="tw-text-primary tw-text-3xl tw-font-bold">
                  {{ isSwapBooking ? 'Home Swap Cancelled' : 'Booking Cancelled' }}
                </div>
              </div>

              <div class="tw-text-zinc-700 tw-text-xl tw-font-medium tw-mb-3">
                Your {{ isSwapBooking ? 'home swap' : 'booking' }} at
                {{ booking.booking_type === 'SWAP' ? otherUserProperty.title : (booking.toHome?.title || 'the property') }}
                has been cancelled.
              </div>

              <div class="tw-bg-gray-50 tw-rounded-lg tw-p-4 tw-text-zinc-700 tw-text-lg">
                <div class="tw-flex tw-items-start tw-gap-2">
                  <v-icon color="info" size="22" class="tw-mt-0.5">mdi-information-outline</v-icon>
                  <div>
                    <span class="tw-font-medium">Cancellation Details:</span>
                    <div class="tw-mt-1">
                      <template v-if="booking.extra_info?.venmo_payment">
                        This booking was paid via Venmo. Any refunds should be processed manually outside the Twimo platform.
                      </template>
                      <template v-else>
                        Any applicable refunds have been processed according to the cancellation policy.
                      </template>
                    </div>
                    <div class="tw-mt-2">
                      Cancelled on: {{ new Date(booking.extra_info?.cancelled_at).toLocaleDateString() }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </template>

          <!-- Property Details Card -->
          <GoodCard2 class="tw-flex tw-flex-col tw-gap-6 sm:tw-w-[80%] lg:tw-w-[70%] tw-p-6">
            <v-row>
              <v-col cols="12" sm="7" class="tw-flex tw-flex-col tw-gap-4">
                <div class="tw-text-zinc-800 tw-text-2xl tw-font-bold">
                  {{ booking.booking_type === 'SWAP' ? otherUserProperty.title : (booking.toHome?.title || 'Property') }}
                </div>

                <div class="tw-text-zinc-600 tw-text-lg tw-font-medium">
                  {{ formattedAddress }}
                </div>

                <div class="tw-flex tw-flex-row tw-gap-6 tw-mt-2">
                  <div class="tw-flex tw-items-center tw-gap-2">
                    <v-icon color="primary" size="22">mdi-bed</v-icon>
                    <span class="tw-text-zinc-700 tw-text-base tw-font-medium">
                      {{ booking.booking_type === 'SWAP' ? otherUserProperty.beds : (booking.toHome?.beds || 0) }} Beds
                    </span>
                  </div>

                  <div class="tw-flex tw-items-center tw-gap-2">
                    <v-icon color="primary" size="22">mdi-shower</v-icon>
                    <span class="tw-text-zinc-700 tw-text-base tw-font-medium">
                      {{ booking.booking_type === 'SWAP' ? otherUserProperty.baths : (booking.toHome?.baths || 0) }} Bathrooms
                    </span>
                  </div>
                </div>
              </v-col>
              <v-col cols="12" sm="5">
                <BeastImage
                  :src="booking.booking_type === 'SWAP' ?
                    (otherUserProperty.photos && otherUserProperty.photos.length > 0 ? otherUserProperty.photos[0].src : '') :
                    (booking.toHome?.photos && booking.toHome.photos.length > 0 ? booking.toHome.photos[0].src : '')"
                  :alt="booking.booking_type === 'SWAP' ? otherUserProperty.title : (booking.toHome?.title || 'Property')"
                  class="tw-aspect-video tw-rounded-lg tw-shadow-sm tw-border tw-border-gray-100"
                />
              </v-col>
              <v-col cols="12" class="tw-mt-2">
                <div class="tw-flex tw-items-center tw-gap-2 tw-text-zinc-700 tw-text-lg tw-font-medium">
                  <v-icon color="primary" size="22">mdi-calendar-range</v-icon>
                  {{ formatDateRange(booking.start_at, booking.end_at) }}
                </div>
              </v-col>
            </v-row>

            <v-divider class="tw-my-2" />

            <!-- Reference Code Section -->
            <div class="tw-flex tw-flex-col tw-gap-3">
              <div class="tw-text-zinc-800 tw-text-xl tw-font-bold">Reference Code</div>
              <div class="tw-bg-gray-50 tw-rounded-lg tw-p-3 tw-text-zinc-700 tw-text-base tw-font-mono">
                {{ booking.code }}
              </div>
            </div>
          </GoodCard2>

          <!-- Action Buttons -->
          <div class="tw-bg-gray-50 tw-rounded-lg tw-p-4 tw-border tw-border-gray-200 tw-mt-4 tw-mb-8">
            <div class="tw-flex tw-flex-wrap tw-gap-4">
              <GoodButton
                class="tw-font-medium tw-px-6 tw-py-3 tw-text-base"
                color="primary"
                @click="$router.push({
                  path: '/bookings',
                  query: {
                    booking_type: isSwapBooking ? 'swap' : 'booking',
                    booking_id: booking.id
                  }
                })"
              >
                <v-icon left size="20" class="tw-mr-2">mdi-view-list</v-icon>
                Go to Bookings
              </GoodButton>
              <GoodButton
                class="tw-font-medium tw-px-6 tw-py-3 tw-text-base"
                @click="$router.push(`/messages`)"
              >
                <v-icon left size="20" class="tw-mr-2">mdi-message</v-icon>
                {{ isHost ? 'Message Guest' : 'Message Host' }}
              </GoodButton>
            </div>
          </div>
        </v-col>
      </template>
    </template>
    </v-row>
  </div>
</template>
