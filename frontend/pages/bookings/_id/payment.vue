<script lang="ts">
// @ts-nocheck

import {
  computed,
  defineComponent,
  onMounted,
  ref,
  useContext,
  useRoute,
  useRouter,
  watch,
  onUnmounted,
  nextTick,
} from '@nuxtjs/composition-api'
import QrcodeVue from 'qrcode.vue'

import GoodCard2 from '~/components/GoodCard2.vue'
import { useApi, useCurrentUser, useToast } from '~/composables/useCommon'
import { TOAST_DURATION } from '~/constants'
import { formatDateRange, formatNumberToDisplay } from '~/helpers'
import { Booking } from '~/types'
import GoodButton from '~/components/GoodButton.vue'
import { useHostPaymentMethods } from '~/composables/useHostPaymentMethods'
import useAddressFormatter from '~/composables/useAddressFormatter'

export default defineComponent({
  components: {
    GoodCard2,
    GoodButton,
    QrcodeVue,
  },

  middleware: ['auth'],

  setup() {
    const route = useRoute()
    const router = useRouter()
    const api = useApi()
    const booking = ref<Booking | null>(null)
    const isTravelInsuranceChecked = ref(false)
    const isLoading = ref(true)
    const toast = useToast()
    const isLoadingConfirmation = ref(false)
    const currentUser = useCurrentUser()
    const hostPaymentMethods = useHostPaymentMethods()
    const selectedPaymentMethod = ref('stripe')
    const stripeCard = ref(null)

    const bookingId = route.value.params.id

    if (!bookingId) {
      router.push('/bookings')
    }

    const elements = ref(null)

    const { $stripe } = useContext()

    const displayedPets = computed(() => {
      if (!booking.value) return 'No pets allowed'

      let pets
      if (booking.value.booking_type === 'SWAP' && isHost.value) {
        // Host is paying for guest's home cleaning fee, so show guest's home pets policy
        pets = booking.value.fromHome?.extra_info?.pets || { enabled: 'no' }
      } else {
        // Guest is paying for host's home cleaning fee, so show host's home pets policy
        // Or for regular bookings, always show the host's home pets policy
        pets = booking.value.toHome?.extra_info?.pets || { enabled: 'no' }
      }

      switch (pets.enabled) {
        case 'yes':
          return 'Pets allowed'
        case 'service animal only':
          return 'Service animals only'
        case 'no':
        default:
          return 'No pets allowed'
      }
    })

    const nightly_rate = ref(0)
    const getDiffsInDays = ref(0)
    const getRawTotalMoney = ref(0)
    const getCleaningFee = ref(0)
    const getTaxRate = ref(0)
    const getTax = ref(0)
    const getServiceFee = ref(0)
    const getTotalMoney = ref(0)
    const averageNightlyRateOnTotal = ref(0)

    const confirmPayment = () => {
      try {
        isLoadingConfirmation.value = true

        // Show loading toast
        toast.info('Processing payment...').goAway(0)

        // Add a timestamp parameter to force page reload and avoid caching issues
        const timestamp = new Date().getTime()
        const returnUrl = `${window.location.origin}/bookings/${bookingId}?timestamp=${timestamp}`

        $stripe
          .confirmPayment({
            elements: elements.value,
            confirmParams: {
              return_url: returnUrl,
            },
          })
          .then(function (result: { error: { message: any }; paymentIntent: { status: string } }) {
            if (result.error) {
              toast.error(result.error.message).goAway(TOAST_DURATION)
            }
          })
      } catch (error) {
        console.error('Error confirming payment:', error)
        toast.error('Error confirming payment').goAway(TOAST_DURATION)
      } finally {
        isLoadingConfirmation.value = false
      }
    }

    const isHost = computed(() => {
      return booking.value?.toHome?.user_id === currentUser.value?.id
    })

    // Use address formatter
    const { formatAddress } = useAddressFormatter()

    // Format address based on booking status and payment status
    const formattedAddress = computed(() => {
      if (!booking.value) return 'Address not available'

      // For swap bookings, show the appropriate home based on user role
      let home
      if (booking.value.booking_type === 'SWAP') {
        if (isHost.value) {
          // Host is paying for guest's home cleaning fee, so show guest's home
          home = booking.value.fromHome
        } else {
          // Guest is paying for host's home cleaning fee, so show host's home
          home = booking.value.toHome
        }
      } else {
        // For regular bookings, always show the host's home
        home = booking.value.toHome
      }

      if (!home) return 'Address not available'

      return formatAddress(
        home.address,
        home.city_long,
        home.state_long,
        home.country_long,
        booking.value.status,
        booking.value.payment_status
      )
    })

    const paymentAmount = computed(() => {
      if (!booking.value?.extra_info?.priceInfo) return 0

      // For swap bookings
      if (booking.value.booking_type === 'SWAP') {
        if (isHost.value) {
          // For host in swap: show guest home cleaning fee
          return booking.value.extra_info.priceInfo.hostPaymentAmount || booking.value.fromHome?.cleaning_fee || 0
        } else {
          // For guest in swap: show host home cleaning fee
          return booking.value.extra_info.priceInfo.guestPaymentAmount || booking.value.toHome?.cleaning_fee || 0
        }
      } else {
        // For regular bookings
        return booking.value.extra_info.priceInfo.getTotalMoney || 0
      }
    })

    const mountStripeElement = async () => {
      if (!$stripe || !booking.value || !allowedPaymentMethods.value.includes('stripe')) return

      // Wait for next tick to ensure DOM is updated
      await nextTick()

      // Cleanup previous element if it exists
      if (stripeCard.value) {
        stripeCard.value.destroy()
        stripeCard.value = null
      }

      const element = document.getElementById('card-element')
      if (!element) {
        console.error('Card element not found')
        return
      }

      const priceInfo = booking.value.extra_info?.priceInfo || {}
      let clientSecret = null

      // For swap bookings, use the appropriate payment intent based on user role
      if (booking.value.booking_type === 'SWAP') {
        if (isHost.value) {
          // Host pays the guest home cleaning fee using hostPaymentIntent
          // Only if guest cleaning fee is enabled
          if (booking.value.guest_cleaning_fee_enabled) {
            clientSecret = priceInfo.hostPaymentIntent
          } else {
            router.push(`/bookings/${bookingId}`)
            toast.info('No cleaning fee payment required').goAway(TOAST_DURATION)
            return
          }
        } else {
          // Guest pays the host home cleaning fee using guestPaymentIntent
          // Only if host cleaning fee is enabled
          if (booking.value.host_cleaning_fee_enabled) {
            clientSecret = priceInfo.guestPaymentIntent
          } else {
            router.push(`/bookings/${bookingId}`)
            toast.info('No cleaning fee payment required').goAway(TOAST_DURATION)
            return
          }
        }
      } else {
        // For regular bookings, guest always pays using paymentIntent
        clientSecret = priceInfo.paymentIntent
      }

      if (!clientSecret) {
        router.push(`/bookings/${bookingId}`)
        toast.error('Payment intent not found').goAway(TOAST_DURATION)
        return
      }

      elements.value = $stripe.elements({
        clientSecret,
      })

      // Store reference to card element
      stripeCard.value = elements.value.create('payment')
      stripeCard.value.mount('#card-element')
    }

    // Add cleanup on component unmount
    onUnmounted(() => {
      if (stripeCard.value) {
        stripeCard.value.destroy()
      }
    })

    const allowedPaymentMethods = computed(() => {
      if (!booking.value) return []

      const methods = []

      // For public bookings and swaps, only allow Stripe Connect
      if (!booking.value.from_sharable_link || booking.value.booking_type === 'SWAP') {
        if (
          hostPaymentMethods.stripe_connect.value.enabled &&
          hostPaymentMethods.stripe_connect.value.charges_enabled
        ) {
          methods.push('stripe')
        }
      } else {
        // For private bookings, allow both Stripe Connect and Venmo
        if (
          hostPaymentMethods.stripe_connect.value.enabled &&
          hostPaymentMethods.stripe_connect.value.charges_enabled
        ) {
          methods.push('stripe')
        }
        if (hostPaymentMethods.venmo.value.enabled) {
          methods.push('venmo')
        }
      }

      return methods
    })

    const canProcessPayment = computed(() => {
      return allowedPaymentMethods.value.length > 0
    })

    const venmoQrUrl = computed(() => {
      if (!booking.value || !hostPaymentMethods.venmo.value.username) return ''

      const username = hostPaymentMethods.venmo.value.username.replace('@', '')
      const amount = paymentAmount.value
      const note = encodeURIComponent(`Booking: ${booking.value.code}`)

      return `https://venmo.com/${username}?txn=pay&amount=${amount}&note=${note}`
    })

    onMounted(async () => {
      try {
        const { data } = await api.get(`/bookings/${bookingId}`)

        if (!data) {
          router.push('/bookings')
          return
        }

        // Check if the home is archived (deleted)
        if (data.toHome?.status === 'archived') {
          toast.error('This home has been removed from Twimo. Explore other homes.').goAway(TOAST_DURATION)
          router.push('/bookings')
          return
        }

        // For swap bookings, also check if the fromHome is archived
        if (data.booking_type === 'SWAP' && data.fromHome?.status === 'archived') {
          toast.error('One of the homes in this swap has been removed from Twimo. Explore other homes.').goAway(TOAST_DURATION)
          router.push('/bookings')
          return
        }

        // For swap bookings, hosts need to be able to pay the guest home cleaning fee
        // Only redirect hosts for non-swap bookings
        if (data.toHome.user_id === currentUser.value?.id && data.booking_type !== 'SWAP') {
          router.push(`/bookings/${bookingId}`)
          return
        }

        // For swap bookings, check if the host needs to make a payment
        if (data.toHome.user_id === currentUser.value?.id && data.booking_type === 'SWAP') {
          // If guest cleaning fee is not enabled or already paid, redirect back
          if (!data.guest_cleaning_fee_enabled ||
              (data.extra_info?.priceInfo?.hostPaymentIntentCompleted === true)) {
            router.push(`/bookings/${bookingId}`)

            // Show appropriate message
            if (data.extra_info?.priceInfo?.hostPaymentIntentCompleted === true) {
              toast.info('You have already completed your payment').goAway(TOAST_DURATION)
            } else {
              toast.info('No cleaning fee payment required from you').goAway(TOAST_DURATION)
            }
            return
          }
        }

        // For swap bookings, check if the guest needs to make a payment
        if (data.user_id === currentUser.value?.id && data.booking_type === 'SWAP') {
          // If host cleaning fee is not enabled or already paid, redirect back
          if (!data.host_cleaning_fee_enabled ||
              (data.extra_info?.priceInfo?.guestPaymentIntentCompleted === true)) {
            router.push(`/bookings/${bookingId}`)

            // Show appropriate message
            if (data.extra_info?.priceInfo?.guestPaymentIntentCompleted === true) {
              toast.info('You have already completed your payment').goAway(TOAST_DURATION)
            } else {
              toast.info('No cleaning fee payment required from you').goAway(TOAST_DURATION)
            }
            return
          }
        }

        // If booking is already completed, redirect back
        if (data.status === 'completed') {
          router.push(`/bookings/${bookingId}`)
          toast.info('This booking is already completed').goAway(TOAST_DURATION)
          return
        }

        booking.value = data

        // Fetch host payment methods
        await hostPaymentMethods.fetchHostPaymentMethods(api, booking.value.toHome.user_id)

        if (!canProcessPayment.value) {
          toast.error('Host has not enabled any payment methods').goAway(TOAST_DURATION)
          router.push(`/bookings/${bookingId}`)
          return
        }

        // Check booking status in accepted so can proceed with payment
        if (data.status !== 'accepted') {
          router.push(`/bookings/${bookingId}`)
          toast.error('Invalid booking status').goAway(TOAST_DURATION)
          return
        }

        isTravelInsuranceChecked.value = booking.value.extra_info?.travelInsurance || false
        const priceInfo = booking.value.extra_info?.priceInfo || {}

        if (!$stripe) {
          console.error('Stripe.js has not loaded')

          toast.error('Stripe.js has not loaded').goAway(TOAST_DURATION)

          return
        }

        isLoading.value = false

        // Set default payment method based on what's available
        if (allowedPaymentMethods.value.includes('stripe')) {
          selectedPaymentMethod.value = 'stripe'
        } else if (allowedPaymentMethods.value.includes('venmo')) {
          selectedPaymentMethod.value = 'venmo'
        }

        // Only mount Stripe if it's an allowed payment method
        if (allowedPaymentMethods.value.includes('stripe')) {
          mountStripeElement()
        }

        nightly_rate.value = priceInfo.nightly_rate || 0
        getDiffsInDays.value = priceInfo.getDiffsInDays || 0
        getRawTotalMoney.value = priceInfo.getRawTotalMoney || 0
        getCleaningFee.value = priceInfo.getCleaningFee || 0
        getTaxRate.value = priceInfo.getTaxRate || 0
        getTax.value = priceInfo.getTax || 0
        // Service fee is no longer used
        getServiceFee.value = 0
        getTotalMoney.value = priceInfo.getTotalMoney || 0
        averageNightlyRateOnTotal.value = priceInfo.averageNightlyRateOnTotal || 0
      } catch (error) {
        console.error('Error fetching booking:', error)
        router.push('/bookings')
      } finally {
        isLoading.value = false
      }
    })

    watch(selectedPaymentMethod, async newValue => {
      if (newValue === 'stripe') {
        await mountStripeElement()
      }
    })

    // Add new method for Venmo payment
    const markVenmoAsPaid = async () => {
      try {
        isLoadingConfirmation.value = true
        await api.put(`/bookings/${bookingId}/mark-venmo-paid`)
        toast
          .success('Payment marked as pending. Host has been notified to verify the payment.')
          .goAway(TOAST_DURATION)

        // Fetch updated booking data to reflect latest status
        const { data } = await api.get(`/bookings/${bookingId}`)
        booking.value = data

        router.push(`/bookings/${bookingId}/payment`)
      } catch (error) {
        console.error('Error marking Venmo payment:', error)
        toast
          .error(error?.response?.data?.message || 'Error marking payment as paid')
          .goAway(TOAST_DURATION)
      } finally {
        isLoadingConfirmation.value = false
      }
    }

    const formatDate = isoDate => {
      return new Date(isoDate).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      })
    }

    return {
      booking,
      isLoading,
      displayedPets,
      nightly_rate,
      getDiffsInDays,
      getRawTotalMoney,
      getCleaningFee,
      getTaxRate,
      getTax,
      getServiceFee,
      getTotalMoney,
      averageNightlyRateOnTotal,
      isTravelInsuranceChecked,
      formatNumberToDisplay,
      formatDateRange,
      confirmPayment,
      isLoadingConfirmation,
      mountStripeElement,
      isHost,
      paymentAmount,
      allowedPaymentMethods,
      selectedPaymentMethod,
      hostPaymentMethods,
      venmoQrUrl,
      markVenmoAsPaid,
      formatDate,
      formattedAddress,
    }
  },
})
</script>

<template>
  <v-row>
    <v-col cols="12" class="tw-text-3xl tw-font-semibold tw-text-zinc-600">
      <template v-if="booking && booking.booking_type === 'SWAP'">
        <template v-if="isHost">
          Make Your Swap Cleaning Fee Payment
        </template>
        <template v-else>
          Make Your Swap Cleaning Fee Payment
        </template>
      </template>
      <template v-else>
        Make Your Payment to Finalize your Booking
      </template>
    </v-col>
    <v-col cols="12" sm="4">

      <!-- Payment Method Selection -->
      <GoodCard2 v-if="allowedPaymentMethods.length > 1">
        <div class="tw-text-xl tw-font-semibold tw-mb-4">Select Payment Method</div>
        <v-radio-group v-model="selectedPaymentMethod">
          <v-radio
            v-if="allowedPaymentMethods.includes('stripe')"
            label="Credit Card"
            value="stripe"
          ></v-radio>
          <v-radio
            v-if="allowedPaymentMethods.includes('venmo')"
            label="Venmo"
            value="venmo"
          ></v-radio>
        </v-radio-group>
      </GoodCard2>

      <!-- Stripe Payment Form -->
      <div v-show="selectedPaymentMethod === 'stripe'">
        <GoodCard2 class="tw-mt-4">
          <div id="card-element"></div>
        </GoodCard2>
        <GoodButton
          :disabled="isLoadingConfirmation"
          class="tw-mx-auto tw-w-full tw-mt-4"
          @click="confirmPayment"
        >
          Confirm and Pay
        </GoodButton>
      </div>

      <!-- Venmo Payment Form -->
      <div v-show="selectedPaymentMethod === 'venmo'">
        <GoodCard2 class="tw-flex tw-flex-col tw-gap-4">
          <div class="tw-text-xl tw-font-semibold">Pay with Venmo</div>

          <div class="tw-flex tw-flex-col tw-items-center tw-gap-4">
            <qrcode-vue :value="venmoQrUrl" :size="200" level="H" render-as="svg" />
            <div class="tw-text-sm tw-text-zinc-500 tw-text-center">
              Scan this QR code with your Venmo app to complete the payment
            </div>
          </div>

          <div class="tw-flex tw-flex-col tw-items-center tw-gap-2">
            <div class="tw-text-sm tw-text-zinc-500">Or click below to open Venmo:</div>
            <a
              :href="venmoQrUrl"
              target="_blank"
              rel="noopener noreferrer"
              class="tw-text-primary hover:tw-text-primary-dark tw-underline"
            >
              Open in Venmo
            </a>
          </div>

          <!-- Show payment marked message if already paid -->
          <template v-if="booking?.extra_info?.venmo_payment">
            <div class="tw-bg-green-50 tw-p-4 tw-rounded-lg tw-mt-4">
              <div class="tw-text-green-700 tw-font-semibold tw-mb-2">
                Payment Marked as Completed
              </div>
              <div class="tw-text-green-600 tw-text-sm">
                You marked this payment as completed via Venmo on
                {{ formatDate(booking.extra_info.venmo_payment.marked_paid_at) }}. The host has been
                notified and will verify the payment.
              </div>
            </div>
          </template>

          <!-- Only show Mark as Paid button if not already paid -->
          <template v-else>
            <div class="tw-text-sm tw-text-zinc-500 tw-mt-4">
              After sending payment through Venmo, click the button below to mark it as paid:
            </div>
            <GoodButton
              class="tw-mx-auto tw-w-full"
              :loading="isLoadingConfirmation"
              @click="markVenmoAsPaid"
            >
              Mark as Paid
            </GoodButton>
          </template>
        </GoodCard2>
      </div>
    </v-col>
    <v-col v-if="isLoading || !booking || !booking.toHome" cols="12">
      <v-progress-circular indeterminate color="primary"></v-progress-circular>
    </v-col>
    <template v-else>
      <v-col cols="12" sm="8" class="tw-flex justify-center tw-flex-col">
        <GoodCard2 class="tw-flex tw-flex-col tw-gap-4">
          <v-row>
            <v-col cols="12" sm="7" class="tw-flex tw-flex-col tw-gap-4">
              <div class="tw-text-zinc-500 tw-text-2xl tw-font-semibold">
                <template v-if="booking.booking_type === 'SWAP'">
                  <template v-if="isHost">
                    {{ booking.fromHome.title }}
                  </template>
                  <template v-else>
                    {{ booking.toHome.title }}
                  </template>
                </template>
                <template v-else>
                  {{ booking.toHome.title }}
                </template>
              </div>

              <div class="tw-text-zinc-500 tw-text-xl tw-font-medium">
                {{ formattedAddress }}
              </div>

              <div class="tw-flex tw-flex-row tw-gap-4">
                <div class="tw-flex tw-items-center tw-gap-1">
                  <span class="tw-text-zinc-500 tw-text-lg">
                    <template v-if="booking.booking_type === 'SWAP' && isHost">
                      {{ booking.fromHome.beds || 0 }} Beds
                    </template>
                    <template v-else>
                      {{ booking.toHome.beds || 0 }} Beds
                    </template>
                  </span>
                  <v-icon color="primary" size="24" dark>mdi-bed</v-icon>
                </div>

                <div class="tw-flex tw-items-center tw-gap-1">
                  <span class="tw-text-zinc-500 tw-text-lg">
                    <template v-if="booking.booking_type === 'SWAP' && isHost">
                      {{ booking.fromHome.baths || 0 }} Bathrooms
                    </template>
                    <template v-else>
                      {{ booking.toHome.baths || 0 }} Bathrooms
                    </template>
                  </span>
                  <v-icon color="primary" size="24" dark>mdi-shower</v-icon>
                </div>
              </div>
            </v-col>
            <v-col cols="12" sm="5">
              <BeastImage
                :src="booking.booking_type === 'SWAP' && isHost ?
                  (booking.fromHome.photos && booking.fromHome.photos.length > 0 ? booking.fromHome.photos[0].src : '') :
                  (booking.toHome.photos && booking.toHome.photos.length > 0 ? booking.toHome.photos[0].src : '')"
                :alt="booking.booking_type === 'SWAP' && isHost ? booking.fromHome.title : booking.toHome.title"
                class="tw-aspect-video tw-rounded-lg"
              />
            </v-col>
            <v-col cols="12" class="tw-text-zinc-500 tw-text-xl tw-font-medium">
              {{ formatDateRange(booking.start_at, booking.end_at) }}
            </v-col>
          </v-row>
          <v-divider />
          <div class="tw-flex tw-flex-col tw-gap-4 tw-flex-wrap">
            <div class="tw-text-zinc-500 tw-text-xl tw-font-medium">Guests</div>

            <div class="tw-flex tw-flex-col sm:tw-flex-row tw-gap-4 tw-flex-wrap">
              <div class="tw-flex tw-gap-1 tw-items-center">
                <span class="tw-text-zinc-500 tw-text-lg">
                  <template v-if="booking.booking_type === 'SWAP' && isHost">
                    {{ booking.fromHome.guests || 0 }} People
                  </template>
                  <template v-else>
                    {{ booking.toHome.guests || 0 }} People
                  </template>
                </span>
                <v-icon color="primary" size="24" dark>mdi-account</v-icon>
              </div>

              <div class="tw-flex tw-items-center tw-gap-1">
                <span class="tw-text-zinc-500 tw-text-lg">{{ displayedPets }}</span>
                <v-icon color="primary" size="24" dark>mdi-paw</v-icon>
              </div>
            </div>
          </div>
          <v-divider />
          <div class="tw-flex tw-flex-col tw-gap-4">
            <div class="tw-text-zinc-500 tw-text-xl tw-font-medium">Payment Amount</div>
            <template v-if="booking && booking.booking_type === 'SWAP'">
              <div class="tw-text-zinc-500 tw-text-lg">
                <template v-if="isHost">
                  <div class="tw-flex tw-flex-col tw-gap-2">
                    <div>
                      <span class="tw-font-medium">Guest Home Cleaning Fee:</span> ${{ formatNumberToDisplay(booking.extra_info.priceInfo?.hostPaymentAmount || paymentAmount) }}
                    </div>
                    <div class="tw-text-sm tw-text-gray-500">
                      This is the cleaning fee for your swap partner's home.
                    </div>
                  </div>
                </template>
                <template v-else>
                  <div class="tw-flex tw-flex-col tw-gap-2">
                    <div>
                      <span class="tw-font-medium">Host Home Cleaning Fee:</span> ${{ formatNumberToDisplay(booking.extra_info.priceInfo?.guestPaymentAmount || paymentAmount) }}
                    </div>
                    <div class="tw-text-sm tw-text-gray-500">
                      This is the cleaning fee for the home you'll be staying at.
                    </div>
                  </div>
                </template>
              </div>
            </template>
            <template v-else>
              <div class="tw-text-zinc-500 tw-text-lg">
                Total Due ${{ formatNumberToDisplay(paymentAmount) }}
              </div>
            </template>
          </div>
          <v-divider />
          <div class="tw-flex tw-flex-col tw-gap-4">
            <div class="tw-text-zinc-500 tw-text-xl tw-font-medium">Reference Code</div>
            <div class="tw-text-zinc-500 tw-text-lg">
              {{ booking.code }}
            </div>
          </div>
          <v-divider />
          <div class="tw-flex tw-flex-col tw-gap-4">
            <div class="tw-text-zinc-500 tw-text-xl tw-font-medium">If You Need to Cancel</div>
            <div class="tw-text-sm tw-text-zinc-500">
              Guests have 24 hours to cancel reservations in California for a full refund
            </div>
            <div class="tw-text-zinc-500 tw-text-lg">
              As a Friends & Family guests, please be considerate of your Host if you need to
              cancel. In the event you need to reschedule or cancel your booking please let your
              Host know ASAP, so they can ensure they are able to avoid cleaning cost, or any
              additional fees.
            </div>
          </div>
          <v-divider />
        </GoodCard2>
      </v-col>
    </template>
  </v-row>
</template>
