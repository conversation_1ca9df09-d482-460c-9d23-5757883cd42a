<script lang="ts">
// @ts-nocheck
import { defineComponent, onMounted, toRefs } from '@nuxtjs/composition-api'
import { mapMutations, mapState } from 'vuex'

import HostHomeTableRow from '~/components/HostHomeTableRow.vue'
import MyHomePageTitle from '~/components/MyHomePageTitle.vue'
import { capitalizeFirstLetter, deepClone, humanize } from '~/helpers'
import generalMixin from '~/mixins/general'
import screenCheckerMixins from '~/mixins/screenChecker.mixins'
import { HomeTo } from '~/types'
import GoodButton from '~/components/GoodButton.vue'
import BeastImage from '~/components/BeastImage.vue'
import { useUserHomesStore } from '~/composables/useUserHomesStore'

export default defineComponent({
  name: 'MyHomes',

  components: {
    MyHomePageTitle,
    HostHomeTableRow,
    GoodButton,
    BeastImage,
  },

  mixins: [generalMixin, screenCheckerMixins],

  middleware: ['auth', 'isHost'],

  setup() {
    const userHomesStore = useUserHomesStore()

    const { isLoading, archivedHomes, notArchivedHomes } = toRefs(userHomesStore)
    const { getUserHomes } = userHomesStore

    onMounted(async () => {
      await getUserHomes()
    })

    return {
      loading: isLoading,
      archivedHomes,
      notArchivedHomes,
    }
  },

  data() {
    return {
      host_landing_page: null,
      tab: 0,
      logo: require('~/assets/logo.png'),
    }
  },

  computed: {
    ...mapState('auth', ['user']),
  },

  watch: {
    'user.host_landing_page': {
      handler: function (val) {
        this.host_landing_page = val
      },
      immediate: true,
    },
  },

  mounted() {
    this.getHouses()
  },

  methods: {
    handleArchive(home: HomeTo) {
      const status = home.status === 'archived' ? 'active' : 'archived'

      this.updateHomeSingleField(home, 'status', status)
    },

    getStatusColor(status: string) {
      const map = {
        draft: 'tw-bg-orange-500',
        active: 'tw-bg-green-500',
      }

      return map[status]
    },

    capitalizeFirstLetter,

    ...mapMutations('auth', {
      updateUser: 'updateUser',
    }),

    async saveHostLandingPage() {
      try {
        const { data: user } = await this.$axios.post('user/update', {
          host_landing_page: this.host_landing_page,
        })

        this.updateUserData(user)
      } catch (error) {
        console.log(error)

        if (error?.response?.data?.errors?.host_landing_page[0]) {
          this.$toast.error(error?.response?.data?.errors?.host_landing_page[0]).goAway(3000)
        }
      }
    },

    async hostLogoChanged(e: { target: { files: any[] } }) {
      try {
        const image = e.target.files[0]

        if (!image) {
          this.$toast.error('Please select an image').goAway(3000)
        }

        const formData = new FormData()

        formData.append('host_logo', image)

        const { data: user } = await this.$axios.post('user/update', formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        })

        this.updateUserData(user)
      } catch (error) {
        console.log(error)

        if (error?.response?.data?.errors?.host_logo[0]) {
          this.$toast.error(error?.response?.data?.errors?.host_logo[0]).goAway(3000)
        }
      }
    },

    updateUserData(user: any) {
      this.updateUser(user)

      this.$toast.success('Host logo successfully updated').goAway(3000)
    },

    triggerHostLogoChanged() {
      this.$refs.hostLogoInput.click()
    },

    handleEdit(item: HomeTo) {
      this.$router.push({
        path: `/${item.slug}/edit`,
      })
    },

    getImageUrl(item: { photos: string | any[] }) {
      if (item.photos.length) {
        return item.photos[0].src
      }
    },

    async getHouses() {
      this.loading = true

      try {
        await this.$store.dispatch('houses/getHouses')

        const houses = this.$store.getters['houses/getHousesData']

        this.getHousesData = deepClone(houses)
      } finally {
        this.loading = false
      }
    },

    humanize,

    async updateHomeSingleField(house: { id: any }, fieldName: string | number, fieldValue: any) {
      try {
        await this.$axios.patch(`user/homes/${house.id}`, {
          [fieldName]: fieldValue,
        })

        await this.getHouses()

        const humanizedFieldName = humanize(fieldName)

        this.$toast.success(`${humanizedFieldName} updated`).goAway(3000)
      } catch (err) {
        if (err?.response?.data?.errors[fieldName]) {
          this.$toast.error(err?.response?.data?.errors[fieldName][0]).goAway(3000)
        } else {
          this.$toast.error('Failed to update').goAway(3000)
        }
      }
    },
  },
})
</script>

<template>
  <v-row no-gutters>
    <!--    PAGE TITLE-->
    <MyHomePageTitle>
      <GoodButton @click="$router.push('/create')">
        <v-icon small>mdi-plus</v-icon>

        <span class="tw-font-bold tw-mr-1">add home</span>
      </GoodButton>
    </MyHomePageTitle>

    <!--    TABS TITLE-->
    <v-col cols="12" class="tw-mb-4">
      <v-tabs v-model="tab" center-active show-arrows>
        <v-tab class="tw-normal-case sm:tw-text-lg">homes</v-tab>
        <v-tab class="tw-normal-case sm:tw-text-lg">your logo</v-tab>
        <v-tab class="tw-normal-case sm:tw-text-lg">archived</v-tab>
      </v-tabs>
    </v-col>

    <!--    TABS CONTENT-->
    <v-col v-if="!loading" cols="12">
      <v-tabs-items v-model="tab">
        <!--        LIST HOMES-->
        <v-tab-item>
          <v-row no-gutters>
            <!-- Grid of Homes -->
            <v-col cols="12">
              <v-row>
                <!-- Empty State -->
                <v-col
                  v-if="!notArchivedHomes.length"
                  cols="12"
                  sm="6"
                  md="4"
                  class="tw-px-2 tw-mb-4"
                >
                  <v-card
                    class="tw-h-full tw-cursor-pointer border-left-top border-right-top transition-swing"
                    elevation="0"
                    @click="$router.push('/create')"
                  >
                    <v-card-text class="pa-0">
                      <!-- Image Container - Square aspect ratio -->
                      <div class="tw-relative tw-aspect-square">
                        <BeastImage
                          class="border-left-top border-right-top tw-rounded-b-lg tw-rounded-t-lg explore-image-container tw-object-cover tw-w-full tw-h-full"
                          :src="null"
                        />

                        <!-- Centered Add Home Button -->
                        <div
                          class="tw-absolute tw-inset-0 tw-flex tw-flex-col tw-items-center tw-justify-center tw-bg-black/30"
                        >
                          <GoodButton color="purple darken-2" dark rounded class="tw-px-8">
                            <v-icon left small>mdi-plus</v-icon>
                            add home
                          </GoodButton>
                        </div>
                      </div>

                      <!-- Content Section -->
                      <div class="tw-mt-1 pa-4">
                        <h3 class="tw-text-xl tw-font-medium tw-text-gray-800">
                          Let’s add your home!
                        </h3>
                      </div>
                    </v-card-text>
                  </v-card>
                </v-col>

                <!-- Existing Homes Grid -->
                <v-col
                  v-for="(item, index) in notArchivedHomes"
                  :key="index"
                  cols="12"
                  sm="6"
                  md="4"
                  class="tw-px-2 tw-mb-4"
                >
                  <HostHomeTableRow
                    :item="item"
                    :get-image-url="getImageUrl"
                    :get-status-color="getStatusColor"
                    :capitalize-first-letter="capitalizeFirstLetter"
                    :update-home-single-field="updateHomeSingleField"
                    :handle-edit="handleEdit"
                    :handle-archive="handleArchive"
                    class="tw-h-full"
                    @click.native="handleEdit(item)"
                  />
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-tab-item>

        <!--        WHITELABEL-->
        <v-tab-item>
          <div class="tw-flex tw-flex-col tw-mb-8">
            <div class="tw-font-semibold tw-text-xl">Your Logo</div>

            <div class="tw-mt-4 tw-text-slate-500">
              Have your own company logo or website? Upload your logo and it will appear in the
              header of your booking page.
            </div>
          </div>

          <v-row>
            <v-col cols="12">
              <v-card class="pa-5" elevation="10" style="background-color: #f5f5f7">
                <v-row no-gutters>
                  <v-col cols="12">
                    <v-row no-gutters align="center" justify="center">
                      <v-col cols="12" md="6">
                        <v-row class="mb-5" justify="center">
                          <div class="tw-w-1/2">
                            <div class="d-flex justify-center align-center">
                              <v-avatar
                                size="100%"
                                object-fit="cover"
                                max-height="190"
                                class="elevation-2"
                                style="border-radius: 15px"
                              >
                                <img
                                  :src="user.host_logo || logo"
                                  alt="Host Logo"
                                  class="rounded-lg"
                                />
                              </v-avatar>
                            </div>
                            <div class="text-center mt-3">
                              <GoodButton @click="triggerHostLogoChanged">
                                Upload Image
                              </GoodButton>
                              <input
                                ref="hostLogoInput"
                                type="file"
                                :hidden="true"
                                @change="hostLogoChanged"
                              />
                            </div>
                          </div>
                        </v-row>
                        <v-divider></v-divider>
                        <v-row no-gutters class="mt-5">
                          <v-col cols="12">
                            <div class="text-h6 font-weight-bold mb-1">Homepage Link</div>
                            <div class="text-body-2 mb-3 text--disabled">
                              If you have a link to a personal website you can link below. This will
                              take guests to your homepage when they click your company logo.
                            </div>
                          </v-col>
                          <v-col cols="12" class="mt-5">
                            <v-form class="d-flex justify-space-between tw-gap-4">
                              <v-text-field
                                v-model="host_landing_page"
                                label="Homepage Link"
                                dense
                                outlined
                              ></v-text-field>
                              <GoodButton @click="saveHostLandingPage"> Save </GoodButton>
                            </v-form>
                          </v-col>
                        </v-row>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
              </v-card>
            </v-col>
          </v-row>
        </v-tab-item>

        <!--        ARCHIVED HOMES-->
        <v-tab-item>
          <v-row no-gutters>
            <v-col v-if="!loading" cols="12">
              <!-- Empty State -->
              <v-row v-if="!archivedHomes.length" class="tw-mb-8 tw-mt-8">
                <v-col cols="12" class="tw-text-center">
                  <p class="tw-text-gray-500">No archived homes available.</p>
                </v-col>
              </v-row>

              <!-- Grid Layout for Archived Homes -->
              <v-row v-else>
                <v-col
                  v-for="(item, index) in archivedHomes"
                  :key="index"
                  cols="12"
                  sm="6"
                  md="4"
                  class="tw-px-2 tw-mb-4"
                >
                  <HostHomeTableRow
                    :item="item"
                    :get-image-url="getImageUrl"
                    :get-status-color="getStatusColor"
                    :capitalize-first-letter="capitalizeFirstLetter"
                    :update-home-single-field="updateHomeSingleField"
                    :handle-edit="handleEdit"
                    :handle-archive="handleArchive"
                    class="tw-h-full"
                    @click.native="handleEdit(item)"
                  />
                </v-col>
              </v-row>
            </v-col>

            <!-- Loading State -->
            <v-col v-else cols="12" class="tw-flex tw-justify-center tw-items-center">
              <v-progress-circular indeterminate color="primary" size="64" />
            </v-col>
          </v-row>
        </v-tab-item>
      </v-tabs-items>
    </v-col>

    <v-col v-else cols="12" class="tw-flex tw-justify-center tw-items-center">
      <v-progress-circular indeterminate color="primary" size="64" />
    </v-col>
  </v-row>
</template>

<style scoped></style>
