<script lang="ts">
// @ts-nocheck
import {
  computed,
  defineComponent,
  onMounted,
  reactive,
  toRefs,
  useContext,
  useRoute,
  useCookie
} from '@nuxtjs/composition-api'

import { TOAST_DURATION } from '~/constants'
import alreadyLoggedIn from '~/middleware/alreadyLoggedIn'
import GoodButton from '~/components/GoodButton.vue'
import PasswordRequirements from '~/components/PasswordRequirements.vue'
import { useAuthStore } from '~/composables/useAuthStore'
import { useToast, useApi } from '~/composables/useCommon'

export default defineComponent({
  name: 'Signup',

  components: { GoodButton, PasswordRequirements },

  middleware: [alreadyLoggedIn],

  props: {
    showLogo: {
      type: Boolean,
      default: () => true,
    },

    denseForm: {
      type: Boolean,
      default: () => false,
    },
  },

  setup(props, { emit }) {
    const { app } = useContext()
    const { login, authStore }= useAuthStore()
    const toast = useToast()
    const route = useRoute();

    const state = reactive({
      formRef: null as any,
      first_name: '',
      last_name: '',
      email: '',
      password: '',
      countryCode: '+1', // Fixed to US country code
      phoneNumber: '',
      userType: 'vendor',
      inviteCode: route.value.query.vi,
      optedInToEmail: false,
      optedInToSms: false,
      agreedToTerms: false,
      valid: true,
      darkMode: true,
      errors: [] as string[],
      isSignupPage: true,
    })

    onMounted(async () => {

        // Ensure that the reCAPTCHA client is fully initialized
        try {
          await app.$recaptcha.init()
          console.log('reCAPTCHA initialized', app.$recaptcha)
        } catch (error) {
          console.error('reCAPTCHA initialization error:', error)
        }

        if (state.inviteCode) {
          const cookieOptions = {
            path: '/',
            maxAge: 24 * 60 * 60, // 1 day in seconds
          };

          app.$cookies.set('invite_code', state.inviteCode, cookieOptions);
        }

    })

    const executeRecaptcha = async () => {
      try {
        // Try to execute reCAPTCHA
        const token = await app.$recaptcha.execute('register')
        if (!token) {
          throw new Error('No reCAPTCHA token received')
        }
        return token
      } catch (error) {
        console.error('reCAPTCHA execution error:', error)
      }
    }


    const handleSignup = async() => {
      try {
        let tokenCaptcha: string | undefined

        if (app.$recaptcha) {
          tokenCaptcha = await executeRecaptcha()
        }

        const { data } = await app.$axios.post('auth/register', {
                                                      first_name: state.first_name,
                                                      last_name: state.last_name,
                                                      email: state.email.trim(),
                                                      phone_number: state.countryCode + state.phoneNumber.replace(/\D/g, ''),
                                                      password: state.password,
                                                      user_type: state.userType,
                                                      invite_code: app.$cookies.get('invite_code') ?? undefined,
                                                      opted_in_to_email: state.optedInToEmail,
                                                      opted_in_to_sms: state.optedInToSms,
                                                      agreed_to_terms: state.agreedToTerms,
                                                      recaptcha_response: tokenCaptcha,
                                                    })

            try {
              const result = await login({
                email: state.email.trim(),
                password: state.password,
              })

              if (!result.success) {
                errors.value = [
                  result.error?.response?.data?.error || 'Login failed. Please check your credentials.',
                ]
              }
            } catch (error) {
              errors.value = ['An unexpected error occurred']
            }

          return { success: true, data }

      } catch (error) {
        console.log('Error during signup:', error)
        toast
          .error(error?.response?.data?.message || 'Invalid input, please check again.')
          .goAway(TOAST_DURATION)
      }
    },

    const handleSignIn = () => {
      if (this.$route.path === '/signup') {
        this.$router.push({ path: '/login' })
      } else {
        this.$emit('show-signin')
      }
    }

    const getInvite = () => {
      this.$axios
        .get('invitations/get?hash=' + this.$route.params.hash)
        .then(response => {
          if (!response.data.invite) {
            // this.$router.push('/404');
          } else {
            this.invite = response.data.invite
          }
        })
        .catch(err => console.error(err))
    }

    const getRequestObject = () => {
      if (app.$cookies.get('invite_code') !== undefined) {
        return {
          first_name: state.first_name,
          last_name: state.last_name,
          email: state.email.trim(),
          phone_number: state.countryCode + state.phoneNumber.replace(/\D/g, ''),
          password: state.password,
          user_type: state.userType,
          invite_code: app.$cookies.get('invite_code'),
          rewardPointUuid: state.rewardPointId,
          opted_in_to_email: state.optedInToEmail,
          opted_in_to_sms: state.optedInToSms,
        }
      }

      return {
        first_name: state.first_name,
        last_name: state.last_name,
        email: state.email.trim(),
        phone_number: state.countryCode + state.phoneNumber.replace(/\D/g, ''),
        password: state.password,
        user_type: state.userType,
        rewardPointUuid: state.rewardPointId,
        opted_in_to_email: state.optedInToEmail,
        opted_in_to_sms: state.optedInToSms,
      }
    }

    const signup = async() => {

      try {
        const {
          data: { token, user },
        } = await this.$axios.post('auth/register', this.getRequestObject())

        if (!token) {
          this.$toast.error('Something went wrong').goAway(3000)
          return
        }

        const cookieOptions = {
          path: '/',
          maxAge: 7 * 24 * 60 * 60,
        }

        app.$cookies.set('token', token, cookieOptions)

        const { id, role } = user

        app.$cookies.set('user', { id, role }, cookieOptions)

        this.$toast.success('Account Created Successfully').goAway(3000)

        setTimeout(() => {
          this.navigateTo()
        }, 300)
      } catch (error) {
        this.$toast
          .error(error?.response?.data?.message || 'Invalid input, please check again.')
          .goAway(TOAST_DURATION)
      }
    }

    const navigateTo = () => {
      window.location.href = '/vendor'
    }

    const setDataInLocal = (_: any, value: any) => {
      app.$cookies.set(value, {
        path: '/',
        maxAge: 3650 * 24 * 60 * 60,
      })
    },

    const formatPhoneNumber = () => {
      // Clean the phone number to contain only digits
      const phone = state.phoneNumber.replace(/\D/g, '')
      // Limit to exactly 10 digits
      state.phoneNumber = phone.slice(0, 10)
    }

    const canSubmit = computed(() => {
      return (
        state.valid &&
        state.optedInToEmail &&
        state.optedInToSms &&
        state.first_name &&
        state.last_name &&
        state.email &&
        state.password &&
        state.countryCode === '+1' && state.phoneNumber.replace(/\D/g, '').length === 10
      )
    })

    return {
      ...toRefs(state),
      handleSignup,
      handleSignIn,
      navigateTo,
      setDataInLocal,
      formatPhoneNumber,
      canSubmit,
    }

  },

  computed: {},

  watch: {},

  /* mounted() {

    // Vendor Invitation
    if (this.$route.query.vi !== undefined) {
      const cookieOptions = {
        path: '/',
        maxAge: 24 * 60 * 60,
      }

      this.$cookies.set('invite_code', this.$route.query.vi, cookieOptions)
      this.isInvitedUser = true
    }
  }, */
})
</script>

<template>
  <v-row
    no-gutters
    class="tw-bg-[url('~/assets/signup_background_v2.webp')] tw-bg-cover tw-bg-center"
    style="background: rgba(0, 0, 0, 0.4); background-blend-mode: multiply"
  >
    <!--		Text-->
    <v-col
      cols="12"
      md="6"
      class="tw-flex tw-flex-col sm:tw-justify-start tw-justify-end tw-items-left  tw-p-8 sm:tw-gap-8 tw-gap-2 sm:tw-pt-[calc(12.5%+90px)]"
    >
      <h1 class="tw-font-bold sm:tw-text-6xl tw-text-4xl tw-text-white">Welcome to Twimo</h1>
      <h2 class="sm:tw-text-3xl tw-text-2xl tw-text-white">
        To Get Started Create<br />Your
        <span class="tw-font-bold">Vendor Account</span>
      </h2>
    </v-col>
    <!-- Form -->
    <v-col
      cols="12"
      md="6"
      class="tw-flex tw-flex-col tw-justify-start tw-items-center tw-gap-8 sm:tw-pt-[3.5%] sm:tw-bg-[#787777]/60 tw-px-8 sm:tw-rounded-lg"
    >
      <div class="tw-w-full tw-flex tw-justify-center">
        <div class="sm:tw-w-[80%] tw-w-full">
          <div class="tw-flex tw-justify-center tw-mb-5">
            <a href="https://twimo.com" target="_blank">
              <img
                :src="require('~/assets/plain_logo.webp')"
                width="100"
                height="60"
                alt="Twimo"
              />
            </a>
          </div>
          <v-form ref="form" v-model="valid" lazy-validation class="tw-flex tw-flex-col tw-gap-5">
            <v-text-field
              v-model="first_name"
              :rules="[v => !!v || 'First Name is required']"
              outlined
              hide-details
              label="First Name"
              class="tw-bg-white tw-rounded-xl tw-shadow-md"
              @keyup.enter.prevent="validate"
            ></v-text-field>

            <v-text-field
              v-model="last_name"
              :rules="[v => !!v || 'Last Name is required']"
              outlined
              label="Last Name"
              hide-details
              class="tw-bg-white tw-rounded-xl tw-shadow-md"
              @keyup.enter.prevent="validate"
            ></v-text-field>

            <div class="tw-flex tw-gap-2">
              <v-text-field
                v-model="countryCode"
                :rules="[
                      v => !!v || 'Country code is required',
                      v => v.startsWith('+') || 'Country code must start with +',
                      v => /^\+[0-9]{1,4}$/.test(v) || 'Invalid country code format',
                    ]"
                outlined
                label="Country Code"
                placeholder="+1"
                hide-details
                disabled
                class="tw-bg-white tw-rounded-xl tw-shadow-md tw-max-w-[120px]"
                :dense="dense"
              />
              <v-text-field
                v-model="phoneNumber"
                :rules="[
                          v => !!v || 'Phone number is required',
                          v => v.replace(/\D/g, '').length === 10 || 'Phone number must be exactly 10 digits',
                          v => /^[0-9]+$/.test(v.replace(/\D/g, '')) || 'Phone number should contain only digits',
                        ]"
                outlined
                label="Phone Number"
                placeholder="1234567890"
                hide-details
                class="tw-bg-white tw-rounded-xl tw-shadow-md"
                :dense="dense"
                @input="formatPhoneNumber"
              />
            </div>

            <v-text-field
              v-model="email"
              :rules="[v => !!v || 'Email is required']"
              outlined
              label="Email"
              hide-details
              class="tw-bg-white tw-rounded-xl tw-shadow-md"
              @keyup.enter.prevent="validate"
            ></v-text-field>

            <div class="tw-w-full">
              <v-text-field
                v-model="password"
                :rules="[
                  v => !!v || 'Password is required',
                  v => v.length >= 8 || 'Password must be at least 8 characters',
                  v => v.length <= 24 || 'Password must not exceed 24 characters',
                ]"
                outlined
                hide-details
                type="password"
                label="Password"
                class="tw-bg-white tw-rounded-xl tw-shadow-md"
                @keyup.enter.prevent="validate"
              ></v-text-field>

              <!-- Password Requirements Component -->
              <PasswordRequirements
                :password="password"
                :dark-mode="darkMode"
                class="tw-mt-2"
              />
            </div>

            <div :class="`tw-text-${darkMode ? 'white' : 'black'} tw-mt-3`">
              <h3 :class="`tw-font-medium tw-text-${darkMode ? 'white' : 'black'} tw-text-base`">
                Sign up for SMS:
              </h3>
              <v-checkbox
                v-model="optedInToSms"
                :rules="[v => v || 'You must agree to receive SMS']"
                :class="[`tw-text-${darkMode ? 'white' : 'black'}`, { 'white-checkbox': isSignupPage }, 'checkbox-fix']"
                hide-details="auto"
                required
                :color="isSignupPage ? 'white' : 'primary'"
              >
                <template #label>
                  <div :class="`tw-text-${darkMode ? 'white' : 'black'} tw-text-xs`">
                    I agree to receive notification text messages from Twimo. By subscribing, I consent to
                    receive automated and non-automated messages, and updates. Message and data rates may
                    apply. Reply STOP to unsubscribe anytime. Message frequency may vary. Standard Message
                    and Data Rates may apply. Reply STOP to opt out. Reply Help for help. Your mobile
                    information will not be sold or shared with third parties for promotional or marketing
                    purposes (<a
                      href="/privacy-policy"
                      target="_blank"
                      :class="`tw-text-${darkMode ? 'white' : 'black'} tw-underline hover:tw-text-gray-200`"
                      @click.stop
                      >Privacy Policy</a
                    >
                    &
                    <a
                      href="/terms-and-service"
                      target="_blank"
                      :class="`tw-text-${darkMode ? 'white' : 'black'} tw-underline hover:tw-text-gray-200`"
                      @click.stop
                      >Terms of Service</a
                    >
                    apply.)
                  </div>
                </template>
              </v-checkbox>
            </div>

            <div :class="`tw-text-${darkMode ? 'white' : 'black'} tw-mt-3`">
              <h3 :class="`tw-font-medium tw-text-${darkMode ? 'white' : 'black'} tw-text-base`">
                Sign up for Email:
              </h3>
              <v-checkbox
                v-model="optedInToEmail"
                :rules="[v => v || 'You must agree to receive emails']"
                :class="[`tw-text-${darkMode ? 'white' : 'black'}`, { 'white-checkbox': isSignupPage }, 'checkbox-fix']"
                hide-details="auto"
                required
                :color="isSignupPage ? 'white' : 'primary'"
              >
                <template #label>
                  <div :class="`tw-text-${darkMode ? 'white' : 'black'} tw-text-xs`">
                    I consent to receive marketing emails from Twimo about new features, promotions, and
                    company updates. I acknowledge that my information will be handled according to the
                    Privacy Policy and that I can unsubscribe at any time. (<a
                      href="/privacy-policy"
                      target="_blank"
                      :class="`tw-text-${darkMode ? 'white' : 'black'} tw-underline hover:tw-text-gray-200`"
                      @click.stop
                      >Privacy Policy</a
                    >
                    &
                    <a
                      href="/terms-and-service"
                      target="_blank"
                      :class="`tw-text-${darkMode ? 'white' : 'black'} tw-underline hover:tw-text-gray-200`"
                      @click.stop
                      >Terms of Service</a
                    >
                    apply.)
                  </div>
                </template>
              </v-checkbox>
            </div>

            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center tw-gap-4">
              <good-button
                class="tw-text-xl tw-w-1/2"
                :disabled="!canSubmit"
                @click.prevent="handleSignup">
                Sign up
              </good-button>

              <div class="tw-text-white tw-text-lg tw-pb-10">
                Already a member?
                <a class="tw-font-semibold tw-text-white" @click="handleSignIn">Log In</a>
              </div>
            </div>
          </v-form>
        </div>
      </div>
    </v-col>
  </v-row>
</template>
<style scoped>
.white-checkbox :deep(.v-icon) {
  color: white !important;
}

.white-checkbox :deep(.v-input--selection-controls__ripple:before) {
  background-color: white !important;
}

.white-checkbox :deep(.v-input--selection-controls__input .mdi-checkbox-marked) {
  color: white !important;
}

.white-checkbox :deep(.v-input--selection-controls__input .mdi-checkbox-blank-outline) {
  color: white !important;
}
</style>