<script lang="ts">
// @ts-nocheck
import { defineComponent } from '@nuxtjs/composition-api'

import { cookieOptions } from '~/constants'
import HomeFilter from '~/components/HomeFilter.vue'
import HotHomeCard from '~/components/HotHomeCard.vue'
import { HomeTo, User } from '~/types'
import GoodButton from '~/components/GoodButton.vue'

export default defineComponent({
  name: 'Explore',

  components: {
    GoodButton,
    HomeFilter,
    HotHomeCard,
  },

  async asyncData({ $axios, query, error }) {
    const {
      page = 1,
      per_page = 64,
      country,
      state,
      city,
      start,
      end,
      allow_booking,
      allow_swaps,
      allow_points,
      featured,
      recommended,
      ski_lease,
      seasonal_lease,
      short_term_rentals,
      long_term_rentals,

      guests,
      pets,
      type_of_space,
      min_price,
      max_price,
      bedrooms,
      beds,
      bathrooms,
      petFriendly,
    } = query

    let homesEndpoint = `/home/<USER>/hot?page=${page}&per_page=${per_page}`

    let homeIdsEndpoint = `/home/<USER>/hotIds?`

    let fullLocation = ''

    const queryParameters = {
      country,
      state,
      city,
      featured,
      recommended,
      ski_lease,
      seasonal_lease,
      start_date: start,
      end_date: end,
      // eslint-disable-next-line eqeqeq
      allow_booking: allow_booking == 1 ? 1 : undefined,
      // eslint-disable-next-line eqeqeq
      allow_swaps:  ((allow_swaps === undefined)? 1 :  (allow_swaps == 1 ? 1 : 0))  ,
      allow_points: allow_points ? 1 : undefined,
      short_term_rentals,
      long_term_rentals,

      guests,
      pets,
      type_of_space,
      min_price,
      max_price,
      bedrooms,
      beds,
      bathrooms,
      petFriendly,
    }

    const locationParameters = {
      country,
      state,
      city,
    }

    Object.keys(queryParameters).forEach(key => {
      if (queryParameters[key] !== undefined) {
        homesEndpoint += `&${key}=${queryParameters[key]}`
        homeIdsEndpoint += `&${key}=${queryParameters[key]}`
      }

      if (locationParameters[key]) {
        fullLocation += `${locationParameters[key]}, `
      }
    })

    // Clean up the location string
    fullLocation = fullLocation.trim().replace(/,\s*$/, '')

    let hasSwapEnabled = false;

    try {
      const {
        data: { current_page, last_page, data: homes },
      } = await $axios.get(homesEndpoint)

      const { data: homeIds } = await $axios.get(homeIdsEndpoint)

      // Check swap enabled status
      try {
        const { data } = await $axios.get('/home/<USER>/has-swap-enabled');
        hasSwapEnabled = data.hasSwapEnabled;
      } catch (swapError) {
        console.error('Error checking swap enabled status', swapError);
      }

      return {
        loading: false,
        page: current_page,
        pages: last_page,
        homes,
        fullLocation,
        serverLoaded: true,
        homeIds,
        hasSwapEnabled, // Persist the value
      }
    } catch (e) {
      console.error('Error fetching homes', e)

      return error({
        statusCode: 404,
        message: 'Page not found',
      })
    }
  },

  data() {
    return {
      loading: true,
      page: 1,
      pages: 1,
      homes: [] as HomeTo[],
      fullLocation: '',
      isGuestUser: false,
      serverLoaded: false,
      homeIds: [] as number[],
      showSignUpModal: false,
      hasSwapEnabled: false,
    }
  },

  head() {
      return {
        title: "Explore Vacation Homes | Rent or Swap Securely with Twimo",
        meta: [
          {
            hid: "description",
            name: "description",
            content: "Browse available homes for public rentals and secure home swaps. Explore unique vacation properties with direct, fee-free booking options."
          }
        ]
      };
    },

  computed: {
    isUserLoggedIn() {
      return this.$store.getters['auth/isLoggedIn']
    },

    userInfo(): User {
      return this.$store.getters['auth/getuser']
    },

    isRecommendations() {
      return this.$route.query.recommended
    },
  },

  watch: {
    page() {
      window.scrollTo(0, 0)
    },
  },

  watchQuery: [
    'page',
    'per_page',
    'country',
    'state',
    'city',
    'start',
    'end',
    'featured',
    'recommended',
    'ski_lease',
    'seasonal_lease',
    'allow_booking',
    'allow_swaps',
    'short_term_rentals',
    'long_term_rentals',
    'allow_points',
    'guests',
    'pets',
    'type_of_space',
    'min_price',
    'max_price',
    'bedrooms',
    'beds',
    'bathrooms',
    'petFriendly',
  ],

  mounted() {
    window.scrollTo(0, 0);
    this.checkSwapEnabled();
  },

  methods: {
    handlePageChange(page: number) {
      this.$router.push({
        query: { ...this.$route.query, page },
      })
    },

    async checkSwapEnabled() {
      if (this.isUserLoggedIn) {
        try {
          const { data } = await this.$axios.get('/home/<USER>/has-swap-enabled');
          this.hasSwapEnabled = data.hasSwapEnabled;
        } catch (error) {
          console.error('Error checking swap enabled status', error);
        }
      }
    },

    goToPrefs() {
      const path = this.isRecommendations
        ? '/preferences'
        : `/preferences?location=${this.fullLocation}`

      if (!this.isRecommendations && !this.isUserLoggedIn) {
        localStorage.setItem('lastSavedLocation', this.fullLocation)
      }

      this.$router.push({ path })
    },

    loadAllHomes() {
      this.$router.push({ path: '/home' })
    },

    decidePage(page: string) {
      let path = '/signup'

      if (this.isUserLoggedIn) {
        path = page === 'profile' ? '/profile' : page === 'home' ? '/homes' : path
      }

      this.$router.push({ path })
    },

    displaySignupModal() {
      this.showSignUpModal = true;
    },

    redirectToSignUp() {
      this.$router.push({ path: '/signup' });
    },

    upgradeToHostAccount() {
      const request = new FormData()
      request.append('role_id', '1')
      request.append('user_type', "host")

      this.$axios
        .post('user/update_account_type', request, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        })
        .then(response => {
          const user = this.$cookies.get('user');
          user.user_type = 'host';
          user.is_host = true;
          this.$cookies.set('user', user, cookieOptions);

          this.loadingAbout = false
          this.$toast.success('Profile successfully updated. Please wait..').goAway(3000)
          this.$router.go(0);
        })
        .catch(err => {
          const errorMessage = err.response?.data?.message || 'Error updating profile'
          this.$toast.error(errorMessage).goAway(3000)
        })
    },

    // Format location display to remove duplicate location names
    formatLocationDisplay(location) {
      if (!location) return '';

      // Remove duplicate adjacent components (like "California, California")
      return location.replace(/(\b\w+\b)(,\s*\1)+/gi, '$1')
        // Remove trailing commas
        .replace(/,\s*$/, '')
        // Remove any double commas that might have been created
        .replace(/,\s*,/g, ',');
    },

  }

})
</script>

<template>
  <v-row no-gutters class="tw-max-w-full">
    <v-dialog v-model="showSignUpModal" max-width="500px" @click:outside="showSignUpModal = false">
      <v-card v-if="isUserLoggedIn && userInfo.user_type == 'host' && !hasSwapEnabled" class="tw-bg-cover tw-bg-center tw-border-[0px]  tw-py-2 tw-px-2">
        <!--<div class="tw-absolute tw-inset-0 tw-bg-black tw-bg-opacity-60 tw-z-5"></div>-->
        <v-card-title class="headline tw-text-black tw-z-10 tw-relative">Enable Swap </v-card-title>
        <v-card-text class="tw-text-black tw-z-10 tw-relative">
          To swap homes, you need to be an active Twimo home with swap enabled.
        </v-card-text>
        <v-card-actions class="tw-pb-5 tw-block">
          <v-spacer></v-spacer>
          <GoodButton color="primary" class="tw-px-5" @click="$router.push({ path: '/homes' });">Enable Swap</GoodButton>
          <v-btn class="tw-normal-case tw-text-black" text @click="showSignUpModal = false">Cancel</v-btn>
        </v-card-actions>
      </v-card>
      <v-card v-else class="tw-bg-cover tw-bg-center tw-border-[10px]  tw-py-2 tw-px-2">
        <!--<div class="tw-absolute tw-inset-0 tw-bg-black tw-bg-opacity-60 tw-z-5"></div>-->
        <v-card-title class="headline tw-text-black tw-z-10 tw-relative">Become a Twimo Host</v-card-title>
        <v-card-text class="tw-text-black tw-z-10 tw-relative">
          <p>You want to start hosting on Twimo! As a Twimo Host you can share your vacation home privately with friends &amp; family, rent your home publicly, and/or swap homes with other Twimo hosts globally. </p>

          <p>Once you select upgrade you will be asked to Add Your Home. Payment will be required once you set your home from draft to active. After payment you can share, rent, and/or swap!</p>
        </v-card-text>
        <v-card-actions class="tw-pb-5 tw-block">
          <v-spacer></v-spacer>
          <GoodButton v-if="isUserLoggedIn" color="primary" class="tw-px-5" @click="upgradeToHostAccount">Upgrade to Host Account</GoodButton>
          <GoodButton v-if="!isUserLoggedIn" color="primary" class="tw-px-5" @click="redirectToSignUp">Sign Up</GoodButton>

          <v-btn class="tw-normal-case tw-text-black" text @click="showSignUpModal = false">Cancel</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>



    <!-- TRAVELER HOME BANNER -->
    <v-row no-gutters class="tw-pt-0 tw-w-[100%] tw-pb-4 tw-mb-[1rem]">
      <v-col cols="12" class="tw-py-0 tw-pb-0">
        <h5
          class="tw-text-[#6C6B6B] tw-pl-[25px] tw-pb-10 tw-pr-[10%] tw-text-center tw-text-2xl tw-font-light"
        >
          {{
            userInfo.first_name !== '' && userInfo.first_name != undefined
              ? userInfo.first_name + ', y'
              : ' Y'
          }}ou're just a few clicks away from your next
          <strong class="tw-font-bold">vacation</strong>
        </h5>
        <HomeFilter
          :server-loaded="serverLoaded"
          :home-ids="homeIds"
          :user-logged-in="userInfo.first_name != undefined"
        />
      </v-col>
    </v-row>

    <!-- HOMES LOADING SKELETON -->
    <v-col v-if="loading" cols="12">
      <v-row>
        <v-col v-for="index in 12" :key="index" cols="12" lg="3" md="6">
          <v-skeleton-loader max-width="600" type="card" />
        </v-col>
      </v-row>
    </v-col>

    <!-- HOME CARDS CONTAINER -->
    <v-col v-else cols="12">

      <v-row v-if="isUserLoggedIn && userInfo.user_type == 'host' && !hasSwapEnabled">
        <v-col cols="12" class="tw-pb-10 tw-pl-[25px] tw-pr-[10%] tw-text-center tw-text-xl">
            To view homes available for swapping, please <NuxtLink to="/homes" class=" tw-font-bold">add a home</NuxtLink> with the swap feature enabled.
        </v-col>
      </v-row>

      <v-row>
        <v-col v-for="home in homes" :key="home.id" cols="12" lg="3" md="6" xl="2">
          <HotHomeCard :data="home" :is-logged-in="isUserLoggedIn" :user-type="((userInfo.user_type !== undefined)? userInfo.user_type : 'public')" :has-swap-enabled="hasSwapEnabled"  @display-signup-modal="displaySignupModal" />
        </v-col>
      </v-row>

      <v-row>
        <v-col
          v-if="
            homes &&
            homes.length &&
            !loading &&
            page === pages &&
            (userInfo.home_completion !== 100 || userInfo.profile_completion !== 100)
          "
          cols="12"
          md="4"
        >
          <v-card
            outlined
            height="500px"
            class="d-flex flex-column text-center text-h5 py-16 font-weight-bold px-10 align-center justify-center"
          >
            <div>
              Complete your
              {{
                userInfo.home_completion !== 100 && userInfo.profile_completion !== 100
                  ? 'home and profile'
                  : userInfo.profile_completion !== 100
                    ? 'profile'
                    : 'home'
              }}
              setup to view all the homes available for swapping.
            </div>

            <GoodButton
              v-if="userInfo.profile_completion !== 100"
              class="mt-4"
              @click="decidePage('profile')"
            >
              Complete Profile
            </GoodButton>

            <GoodButton
              v-if="userInfo.home_completion !== 100 && userInfo.profile_completion === 100"
              large
              class="mt-4"
              @click="decidePage('home')"
            >
              Let's Go!
            </GoodButton>
          </v-card>
        </v-col>
      </v-row>

      <v-row
        v-if="homes && homes.length === 0 && !loading"
        style="background: #fafafa"
        class="mt-8 py-14"
      >
        <v-col cols="12" class="d-flex justify-center flex-column align-center">
          <h1 v-if="!isRecommendations" class="text-center text-h6 text-md-h5 font-weight-bold">
            <span v-if="$route.query.lat && $route.query.lng"
              >We couldn't find any home at this location</span
            >
            <span v-else
              >Sorry! we couldn’t find any homes in
              <span style="color: #000000"> {{ formatLocationDisplay(fullLocation) }} </span> matching your search.<br>Try adjusting your filters or searching in a nearby area.</span
            >
          </h1>

          <!-- <div
            :class="[
              isRecommendations ? 'mt-0 text-h6 text-md-h5 px-3' : 'mt-6 px-6 ',
              'text-center',
            ]"
          >
            Would you like to be notified when a home becomes available<span
              v-if="$route.query.lat && $route.query.lng"
            >
              at this location</span
            >?
          </div>

          <GoodButton :large="$vuetify.breakpoint.mdAndUp" class="mt-6" @click="goToPrefs">
            Yes! Add this location to my preferences
          </GoodButton>

          <GoodButton
            :large="$vuetify.breakpoint.mdAndUp"
            outlined
            class="mt-6"
            @click="loadAllHomes"
          >
            No thanks! I'll continue browsing
          </GoodButton> -->
        </v-col>
      </v-row>
    </v-col>

    <!-- PAGINATION CONTAINER -->
    <v-row v-if="homes && homes.length && !loading" style="width: 100%">
      <v-col cols="12" class="text-center tw-mt-10">
        <v-pagination
          v-model="page"
          circle
          color="black"
          :length="pages"
          :total-visible="5"
          @input="handlePageChange"
        />
      </v-col>
    </v-row>
  </v-row>
</template>

<style lang="scss" scoped>
.centered_home {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 45px;
  height: 100%;
}

h1 {
  font-weight: 700 !important;
}

.title-h1 {
  font-weight: 700 !important;
}

.fade-in {
  opacity: 1;
  animation-name: fadeInOpacity;
  animation-iteration-count: 1;
  animation-timing-function: ease-in;
  animation-duration: 0.5s;
}

.booked_homes {
  background: #f7f7f7;
  border-radius: 18px;
  box-shadow: 0 4px 4px rgba(0, 0, 0, 0.25);
  overflow: hidden;
  position: relative;
}

.booked_homes .primary {
  position: absolute;
  right: 10px;
  bottom: 10px;
}

@keyframes fadeInOpacity {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

::v-deep .v-pagination__item,
.v-pagination__item--active {
  box-shadow: none !important;
}

::v-deep .v-pagination__navigation {
  box-shadow: none !important;
}

::v-deep .v-text-field--outlined.v-input--is-focused fieldset {
  border: 0;
}

::v-deep .vue-daterange-picker .v-btn:before {
  border-radius: 0;
}
</style>
