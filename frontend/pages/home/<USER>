<script lang="ts">
// @ts-nocheck
import { defineComponent, onMounted, toRefs, useRouter, ref, useContext } from '@nuxtjs/composition-api'

import HostDashboard from '~/components/HostDashboard.vue'
import LegacyUserWelcome from '~/components/LegacyUserWelcome.vue'
import LegacyUserIdentityCheck from '~/components/LegacyUserIdentityCheck.vue'
import { HostDashboardData } from '~/types'
import { useAuthStore } from '~/composables/useAuthStore'
import { useApi } from '~/composables/useCommon'

export default defineComponent({
  name: 'Home',

  components: {
    HostDashboard,
    LegacyUserWelcome,
    LegacyUserIdentityCheck,
  },

  middleware: ['auth', 'isTraveler'],

  setup() {
    const { isVendorType, isTravelerType } = toRefs(useAuthStore())

    const api = useApi()

    const router = useRouter()

    const hostDashboardData = ref<HostDashboardData>({})

    onMounted(async () => {
      if (isVendorType?.value) {
        return router.push('/vendor')
      }

      if (isTravelerType?.value) {
        return router.push('/explore')
      }

      hostDashboardData.value = await api.get('/dashboard/host')
    })

    return {
      hostDashboardData,
    }
  },
})
</script>

<template>
  <v-row no-gutters class="tw-max-w-full">
    <HostDashboard ref="hostDashboard" :host-dashboard-data="hostDashboardData" />

    <LegacyUserWelcome />

    <LegacyUserIdentityCheck />
  </v-row>
</template>

<style lang="scss" scoped>
.centered_home {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 45px;
  height: 100%;
}

h1 {
  font-weight: 700 !important;
}

.title-h1 {
  font-weight: 700 !important;
}

.fade-in {
  opacity: 1;
  animation-name: fadeInOpacity;
  animation-iteration-count: 1;
  animation-timing-function: ease-in;
  animation-duration: 0.5s;
}

@keyframes fadeInOpacity {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

::v-deep .v-pagination__item,
.v-pagination__item--active {
  box-shadow: none !important;
}

::v-deep .v-pagination__navigation {
  box-shadow: none !important;
}
</style>
