<template>
  <v-row
    no-gutters
    class="tw-bg-[url('~/assets/signup_background_v2.webp')] tw-bg-cover tw-bg-center tw-min-h-[500px]"
    style="background: rgba(0, 0, 0, 0.4); background-blend-mode: multiply"
  >
    <v-col
      cols="12"
      md="6"
      class="tw-flex tw-flex-col sm:tw-justify-start tw-justify-end tw-items-left tw-p-8 sm:tw-gap-5 tw-gap-1 sm:tw-pt-[calc(12.5%+90px)]"
    >
      <h1 class="tw-font-bold tw-text-5xl sm:tw-text-[3rem] tw-text-white">Verify Your Email</h1>
      <h2 class="tw-font-light sm:tw-text-2xl tw-text-2xl tw-text-white tw-mt-0 tw-pt-0">
        Please check your inbox and click the verification link
      </h2>
    </v-col>

    <v-col
      cols="12"
      md="6"
      class="tw-flex tw-flex-col tw-justify-start tw-items-center tw-gap-8 sm:tw-pt-[10.5%] sm:tw-bg-[#787777]/60 tw-px-8 sm:tw-rounded-lg"
    >
      <div class="tw-w-full tw-flex tw-justify-center">
        <div class="sm:tw-w-[80%] tw-w-full">
          <div class="tw-flex tw-justify-center tw-pb-3">
            <img src="~/assets/plain_logo.webp" width="100" height="60" alt="twimo" />
          </div>

          <v-card flat class="tw-bg-transparent">
            <v-card-text class="tw-text-white">
              <p class="tw-text-center tw-mb-6 tw-text-lg">
                {{ verificationMessage }}
              </p>
              <div v-if="!verified" class="tw-text-center">
                <p class="tw-mb-6 tw-text-lg">
                  If you haven't received the email, you can request a new one:
                </p>
                <good-button class="tw-text-xl tw-w-1/2" @click="resendVerification">
                  Resend
                </good-button>
              </div>
            </v-card-text>
          </v-card>
        </div>
      </div>
    </v-col>
  </v-row>
</template>

<script>
import GoodButton from '~/components/GoodButton.vue'
import { useAuthStore } from '~/composables/useAuthStore'

export default {
  name: 'VerifyEmail',

  components: { GoodButton },

  // No middleware - this page should be accessible to both logged in and logged out users

  data: () => ({
    verified: false,
    email: '',
  }),

  computed: {
    verificationMessage() {
      return this.verified
        ? 'Your email has been verified successfully. You will be redirected shortly.'
        : "We've sent a verification email to your address. Please check your inbox and click the verification link."
    },
  },

  mounted() {
    const { id, hash, email } = this.$route.query
    this.email = email || ''

    if (id && hash) {
      this.verifyEmail(id, hash)
    } else if (this.email) {
      this.resendVerification()
    } else {
      console.error('Missing required query parameters')
      this.showToast('Missing verification parameters', 'error')
    }
  },

  methods: {
    async verifyEmail(id, hash) {
      try {
        const {
          data: { token, user },
        } = await this.$axios.get(`auth/email/verify/${id}/${hash}`)
        this.verified = true

        if (!token) {
          this.showToast('Something went wrong', 'error')
          return
        }

        await this.handleSuccessfulVerification(user, token)
      } catch (error) {
        this.handleVerificationError(error)
      }
    },

    async handleSuccessfulVerification(user, token) {
      const authStore = useAuthStore()
      await this.$store.commit('auth/login', { user, token })
      this.showToast('Email verified successfully')

      // Check for redirect after verification stored in localStorage
      let redirectPath = null
      if (process.client) {
        // First check for booking-specific redirect
        redirectPath = localStorage.getItem('redirectAfterVerification')
        console.log('Redirect path from localStorage:', redirectPath)

        // Only remove from localStorage if we're not going to a booking page
        // This provides an extra layer of protection for the booking flow
        if (redirectPath && !redirectPath.includes('/bookings/')) {
          localStorage.removeItem('redirectAfterVerification')
        }
      }

      // If no redirect from localStorage, check auth store
      if (!redirectPath) {
        const storedRedirect = authStore.getRedirectPath()
        console.log('Redirect path from auth store:', storedRedirect)

        if (storedRedirect) {
          redirectPath = storedRedirect
          // Don't clear the redirect path yet - we'll do it after navigation
        } else {
          // Use default based on user type if no stored redirect
          redirectPath = this.getUserTypePath(user?.user_type)
        }
      }

      console.log('Final redirect path:', redirectPath)

      // Navigate to the appropriate page
      setTimeout(() => {
        this.$router.push(redirectPath)

        // Clear stored redirect in auth store after navigation
        // This ensures the redirect happens before we clear the path
        if (authStore.getRedirectPath()) {
          // For booking pages, keep the redirect path in localStorage as a backup
          if (redirectPath && redirectPath.includes('/bookings/')) {
            console.log('Keeping booking redirect path in localStorage as backup')
            localStorage.setItem('redirectAfterVerification', redirectPath)
          }
          authStore.clearRedirectPath()
        }
      }, 1500) // Short delay to show the success message
    },

    getUserTypePath(userType) {
      return (
        {
          traveler: '/explore',
          vendor: '/vendor/',
          host: '/home',
        }[userType] || '/home'
      )
    },

    handleVerificationError(error) {
      console.error('Verification error:', error)
      this.showToast('Failed to verify email', 'error')
    },

    async resendVerification() {
      try {
        await this.$axios.post('auth/email/resend', { email: this.email })
        this.showToast('Verification email sent successfully')
      } catch (error) {
        this.showToast('Failed to resend verification email', 'error')
      }
    },

    showToast(message, type = 'success') {
      this.$toast[type](message).goAway(3000)
    },
  },
}
</script>
