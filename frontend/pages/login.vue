<script lang="ts">
// @ts-nocheck

import { defineComponent, computed } from '@nuxtjs/composition-api'

import alreadyLoggedIn from '~/middleware/alreadyLoggedIn'
import AuthLoginForm from '~/components/AuthLoginForm.vue'
import { useAuthStore } from '~/composables/useAuthStore'

export default defineComponent({
  name: 'Login',

  components: { AuthLoginForm },

  middleware: [alreadyLoggedIn],

  setup() {
    const authStore = useAuthStore()

    // Check if we're coming from a protected page
    const isFromProtectedPage = computed(() => {
      const redirectPath = authStore.getRedirectPath()
      return redirectPath?.includes('/protected')
    })

    return {
      isFromProtectedPage,
    }
  },

  head() {
      return {
        title: "Twimo Login | Manage Your Vacation Home Account",
        meta: [
          {
            hid: "description",
            name: "description",
            content: "Log in to manage your vacation home with Twimo. Share privately, rent securely, and swap homes globally—on your terms."
          }
        ]
      };
  },


})
</script>

<template>
  <v-row
    no-gutters
    class="tw-bg-[url('~/assets/signup_background_v2.webp')] tw-bg-cover tw-bg-center tw-min-h-[100vh]"
    style="background: rgba(0, 0, 0, 0.4); background-blend-mode: multiply"
  >




    <!--		Text-->
    <v-col
      cols="12"
      md="6"
      class="tw-flex tw-flex-col sm:tw-justify-start tw-justify-end tw-p-10 tw-items-left sm:tw-gap-5 tw-gap-2 sm:tw-pt-[calc(10.5%+90px)]"
    >
      <h1
        class="tw-font-bold tw-text-center md:tw-text-left sm:tw-text-6xl tw-text-4xl tw-text-white"
      >
        Welcome Back!
      </h1>
      <template v-if="!isFromProtectedPage">
        <h2
          class="tw-font-light tw-hidden md:tw-block sm:tw-text-2xl tw-text-2xl tw-text-white tw-mt-0 tw-pt-0"
        >
          Please Log In
        </h2>
      </template>
      <template v-else>
        <h2
          class="tw-font-light tw-hidden md:tw-block sm:tw-text-2xl tw-text-2xl tw-text-white tw-mt-0 tw-pt-0"
        >
          Log In to Access the Private Home
        </h2>
      </template>
    </v-col>
    <v-col
      cols="12"
      md="6"
      class="tw-flex tw-flex-col tw-justify-start tw-items-center tw-gap-8 sm:tw-pt-[10.5%] sm:tw-bg-[#787777]/60 tw-px-8 sm:tw-rounded-lg"
    >
      <div class="tw-w-full tw-flex tw-justify-center tw-pb-10">
        <div class="sm:tw-w-[80%] tw-w-full">
          <div class="tw-text-center tw-text-white tw-text-2xl"><strong>Log</strong> In</div>

          <div class="tw-flex tw-justify-center tw-pb-3">
            <a href="https://twimo.com" target="_blank">
              <img src="~/assets/plain_logo.webp" width="100" height="60" alt="twimo" />
            </a>
          </div>

          <AuthLoginForm />
        </div>
      </div>
    </v-col>
  </v-row>
</template>
