<script lang="ts">
import { defineComponent, ref, onMounted, useRoute } from '@nuxtjs/composition-api'

import { useApi, useToast } from '~/composables/useCommon'
import HotHomeCard from '~/components/HotHomeCard.vue'
import GoodButton from '~/components/GoodButton.vue'
import GoodButtonReverted from '~/components/GoodButtonReverted.vue'
import { TOAST_DURATION } from '~/constants'

export default defineComponent({
  name: 'SavedHomesCategoryDetail',

  components: {
    HotHomeCard,
    GoodButton,
    GoodButtonReverted,
  },

  setup() {
    const api = useApi()
    const toast = useToast()
    const category = ref<any>(null)
    const route = useRoute()

    const fetchCategoryHomes = async categorySlug => {
      try {
        const categoryResponse = await api.get(`saved-home-categories/${categorySlug}`)
        category.value = categoryResponse.data
      } catch (error) {
        toast.error('Failed to fetch saved homes').goAway(3000)
      }
    }

    const showDeleteDialog = ref(false)
    const homeToDelete = ref<any>(null)

    const deleteHome = (home: any) => {
      homeToDelete.value = home
      showDeleteDialog.value = true
    }

    const confirmDelete = async () => {
      try {
        const categorySlug = route.value.params.slug
        await api.delete(
          `saved-home-categories/${category.value?.id}/homes/${homeToDelete.value?.id}`
        )
        toast.success('Home removed from category').goAway(TOAST_DURATION)
        await fetchCategoryHomes(categorySlug)
      } catch (error) {
        toast.error('Failed to remove home').goAway(TOAST_DURATION)
      }
      showDeleteDialog.value = false
      homeToDelete.value = null
    }

    onMounted(() => {
      const categorySlug = route.value.params.slug
      fetchCategoryHomes(categorySlug)
    })

    return {
      category,
      deleteHome,
      showDeleteDialog,
      confirmDelete,
    }
  },
})
</script>

<template>
  <div class="tw-container tw-mx-auto tw-px-4 tw-py-8">
    <div class="tw-mb-8">
      <NuxtLink to="/saved-homes" class="tw-text-primary tw-no-underline">
        ← Back to Categories
      </NuxtLink>
      <h1 class="tw-text-3xl tw-font-semibold tw-mt-4 tw-text-zinc-600">
        {{ category?.name }}
      </h1>
    </div>

    <!-- Add empty state -->
    <div v-if="!category?.homes?.length" class="tw-text-center tw-py-12">
      <v-icon size="64" class="tw-text-gray-400 tw-mb-4">mdi-home-outline</v-icon>
      <h2 class="tw-text-3xl tw-text-zinc-600 tw-mb-2">No Homes in This Category</h2>
      <p class="tw-text-zinc-500 tw-text-xl">
        <NuxtLink to="/explore" class="tw-text-primary hover:tw-underline">
          Browse homes to add to this category
        </NuxtLink>
      </p>
    </div>

    <!-- Wrap existing grid in v-else -->
    <div v-else class="tw-grid tw-grid-cols-1 md:tw-grid-cols-2 lg:tw-grid-cols-3 tw-gap-6">
      <div v-for="home in category?.homes" :key="home.id" class="tw-relative">
        <button class="tw-absolute tw-top-2 tw-right-2 tw-z-10" @click.prevent="deleteHome(home)">
          <v-icon size="24" class="tw-text-white"> mdi-close-circle-outline </v-icon>
        </button>
        <HotHomeCard :data="home" :hide-save-icon="true" />
      </div>
    </div>

    <v-dialog v-model="showDeleteDialog" max-width="600">
      <v-card>
        <v-card-title class="tw-text-3xl tw-font-semibold tw-text-primary">
          Unsave Home
        </v-card-title>
        <v-card-text class="tw-text-xl"> Are you sure you want to unsave this home? </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <GoodButtonReverted @click="showDeleteDialog = false"> Cancel </GoodButtonReverted>
          <GoodButton @click="confirmDelete">Unsave</GoodButton>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>
