<script lang="ts">
import { defineComponent, ref, onMounted } from '@nuxtjs/composition-api'
import { storeToRefs } from 'pinia'

import GoodButton from '~/components/GoodButton.vue'
import GoodButtonReverted from '~/components/GoodButtonReverted.vue'
import { useApi, useToast } from '~/composables/useCommon'
import {
  useSavedHomeCategoriesStore,
  SavedHomeCategory,
} from '~/composables/useSavedHomeCategoriesStore'
import { TOAST_DURATION } from '~/constants'

export default defineComponent({
  name: 'SavedHomesCategories',

  components: { GoodButton, GoodButtonReverted },

  setup() {
    const api = useApi()
    const toast = useToast()
    const savedHomeCategoriesStore = useSavedHomeCategoriesStore()
    const showDeleteDialog = ref(false)
    const categoryToDelete = ref<SavedHomeCategory | null>(null)

    // Use computed property to get categories from the store
    const { categories, loading } = storeToRefs(savedHomeCategoriesStore)

    const deleteCategory = (category: any) => {
      categoryToDelete.value = category
      showDeleteDialog.value = true
    }

    const confirmDelete = async () => {
      try {
        await api.delete(`saved-home-categories/${categoryToDelete.value?.id}`)
        toast.success('Homes deleted successfully').goAway(TOAST_DURATION)
        // Refresh categories from the store after deletion
        await savedHomeCategoriesStore.refreshCategories(api)
      } catch (error) {
        toast.error('Failed to delete homes').goAway(TOAST_DURATION)

        console.error('Failed to delete category:', error)
      }
      showDeleteDialog.value = false
      categoryToDelete.value = null
    }

    onMounted(async () => {
      await savedHomeCategoriesStore.fetchCategories()
    })

    return {
      categories,
      deleteCategory,
      showDeleteDialog,
      confirmDelete,
      loading,
    }
  },
})
</script>

<template>
  <div class="tw-container tw-mx-auto tw-px-4 tw-py-8">
    <h1 class="tw-text-3xl tw-font-semibold tw-mb-4 tw-text-primary tw-tracking-wide">
      My Saved Homes
    </h1>

    <!-- Add loading state -->
    <div v-if="loading" class="tw-flex tw-justify-center tw-items-center tw-py-16">
      <v-progress-circular indeterminate color="primary" size="64" width="4"></v-progress-circular>
      <p class="tw-ml-4 tw-text-xl tw-text-zinc-600">Loading your saved categories...</p>
    </div>

    <!-- Add empty state -->
    <div v-else-if="!categories.length" class="tw-text-center tw-py-12">
      <v-icon size="64" class="tw-text-gray-400 tw-mb-4">mdi-folder-outline</v-icon>
      <h2 class="tw-text-3xl tw-text-zinc-600 tw-mb-2">No Saved Homes</h2>
      <p class="tw-text-zinc-500 tw-text-xl tw-mb-4">
        When browsing homes, click the bookmark icon to save the home.
      </p>
      <p class="tw-text-zinc-500 tw-text-xl">
        <NuxtLink to="/explore" class="tw-text-primary hover:tw-underline">
          Start exploring homes now
        </NuxtLink>
      </p>
    </div>

    <!-- Existing grid wrapped in v-else -->
    <div v-else class="tw-grid tw-grid-cols-1 md:tw-grid-cols-2 lg:tw-grid-cols-3 tw-gap-6">
      <div v-for="category in categories" :key="category.id" class="tw-relative">
        <button
          class="tw-absolute tw-top-2 tw-right-2 tw-z-10"
          @click.prevent="deleteCategory(category)"
        >
          <v-icon size="24" class="tw-text-white"> mdi-close-circle-outline </v-icon>
        </button>

        <NuxtLink :to="`/saved-homes/${category.slug}`" class="tw-cursor-pointer tw-no-underline">
          <div class="tw-rounded-lg tw-overflow-hidden tw-shadow-lg">
            <div class="tw-aspect-square">
              <BeastImage
                :src="category.thumbnail"
                :alt="category.name"
                class="tw-w-full tw-h-full tw-object-cover"
              />
            </div>
          </div>
          <h2 class="tw-text-2xl tw-text-zinc-600 tw-mt-3">
            {{ category.name }}
          </h2>
          <p class="tw-text-zinc-500">Category</p>
        </NuxtLink>
      </div>
    </div>
    <v-dialog v-model="showDeleteDialog" max-width="600">
      <v-card>
        <v-card-title class="tw-text-3xl tw-font-semibold tw-text-primary">
          Delete Homes
        </v-card-title>
        <v-card-text class="tw-text-xl">
          Are you sure you want to delete this category? This will unsave all homes.
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <GoodButtonReverted @click="showDeleteDialog = false">Cancel </GoodButtonReverted>
          <GoodButton @click="confirmDelete">Delete </GoodButton>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>
