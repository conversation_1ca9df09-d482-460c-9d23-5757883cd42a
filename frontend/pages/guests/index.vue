<template>
  <div class="tw-container tw-mx-auto tw-p-4">
    <h1 class="tw-text-3xl tw-font-semibold tw-mb-6 tw-text-primary tw-tracking-wide">
      My Guest List
    </h1>

    <!-- Current/Archived toggle -->
    <div class="tw-flex tw-w-1/4 tw-bg-gray-100 tw-rounded-lg tw-mb-6">
      <button
        class="tw-grow tw-px-4 tw-py-2 tw-rounded-l-lg"
        :class="{ 'tw-bg-white tw-shadow': activeTab === 'current' }"
        @click="setActiveTab('current')"
      >
        current
      </button>
      <button
        class="tw-grow tw-px-4 tw-py-2 tw-rounded-r-lg"
        :class="{ 'tw-bg-white tw-shadow': activeTab === 'archived' }"
        @click="setActiveTab('archived')"
      >
        archived
      </button>
    </div>

    <!-- Guest List -->
    <div class="tw-flex tw-flex-col tw-gap-4">
      <div
        v-for="guestItem in filteredGuestList"
        :key="guestItem.id"
        class="tw-bg-white tw-rounded-lg tw-shadow-md tw-p-6 tw-mb-4"
        :class="{ 'tw-opacity-50': guestItem.deleted_at }"
      >
        <v-row no-gutters>
          <v-col cols="12" md="2" class="tw-flex tw-flex-col tw-items-start">
            <div class="tw-w-36 tw-h-36 tw-flex-shrink-0">
              <BeastImage
                :src="guestItem.guest.avatar"
                :alt="guestItem.guest.first_name"
                class="tw-w-full tw-h-full tw-rounded-full"
              />
            </div>
          </v-col>
          <v-col cols="12" md="2" class="tw-flex tw-flex-col tw-items-start tw-gap-4">
            <h3 class="tw-text-lg tw-font-semibold tw-text-zinc-600">Guest</h3>
            <p class="tw-text-zinc-500">
              {{ guestItem.guest.first_name }} {{ guestItem.guest.last_name }}
            </p>
          </v-col>
          <v-col cols="12" md="2" class="tw-flex tw-flex-col tw-items-start tw-gap-4">
            <h3 class="tw-text-lg tw-font-semibold tw-text-zinc-600">Email</h3>
            <p class="tw-text-zinc-500">{{ guestItem.guest.email }}</p>
          </v-col>
          <v-col cols="12" md="2" class="tw-flex tw-flex-col tw-items-start tw-gap-4">
            <h3 class="tw-text-lg tw-font-semibold tw-text-zinc-600">Phone</h3>
            <p class="tw-text-zinc-500">
              {{ 'N/A' }}
            </p>
          </v-col>
          <v-col cols="12" md="4" class="tw-flex tw-flex-col tw-items-start tw-gap-4">
            <h3 class="tw-text-lg tw-font-semibold tw-text-zinc-600">Family & Friends Links</h3>
            <div class="tw-flex tw-flex-col tw-items-start tw-gap-4 tw-w-full">
              <div
                v-for="link in guestItem.accessible_sharable_links"
                :key="link.id"
                class="tw-flex tw-items-center tw-justify-between tw-w-full tw-gap-4"
              >
                <div
                  class="tw-font-semibold tw-text-zinc-500 tw-cursor-pointer tw-flex tw-items-center tw-gap-2 tw-flex-grow"
                >
                  <div class="tw-underline tw-flex tw-items-center" @click="viewLink(link)">
                    {{ link.home.title }} link at
                    {{ useDateFormat(link.created_at, 'MMMM D, YYYY').value }}
                    <svg
                      class="tw-w-4 tw-h-4 tw-ml-1"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                      @click.stop="copyLink(link)"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"
                      ></path>
                    </svg>
                  </div>
                </div>
                <v-switch
                  v-if="!guestItem.deleted_at"
                  v-model="link.is_allowed"
                  :label="`${link.is_allowed ? 'Allowed' : 'Blocked'}`"
                  dense
                  hide-details
                  class="tw-m-0 tw-p-0"
                  @change="toggleAccess(guestItem, link)"
                ></v-switch>
              </div>
            </div>
          </v-col>
          <v-col class="tw-flex tw-justify-center md:tw-justify-end tw-gap-4 tw-mt-8">
            <template v-if="activeTab === 'current'">
              <GoodButton class="tw-mb-2 md:tw-mb-0" text @click="sendMessage(guestItem)">
                Send Message
              </GoodButton>
              <GoodButtonReverted @click="confirmDelete(guestItem)">Archive</GoodButtonReverted>
            </template>
            <template v-else>
              <GoodButton @click="unarchiveGuest(guestItem)"> Unarchive </GoodButton>
            </template>
          </v-col>
        </v-row>
      </div>
    </div>

    <!-- Confirmation Modal -->
    <v-dialog v-model="showDeleteModal" max-width="600">
      <v-card class="tw-flex tw-flex-col tw-gap-4">
        <v-card-title class="tw-text-3xl tw-font-bold tw-text-primary">
          You Would Like to Archive this Guest?
        </v-card-title>
        <v-card-text>
          <p class="tw-text-zinc-500 tw-font-medium tw-text-lg">
            To archive this Guest from your Guest List, click Archive below.
          </p>
          <p class="tw-text-zinc-500 tw-font-medium tw-text-lg">
            This will remove their access with private Family & Friends bookings
          </p>
        </v-card-text>
        <v-card-actions class="tw-flex tw-justify-end tw-gap-4 tw-p-4">
          <GoodButtonReverted @click="showDeleteModal = false">Exit</GoodButtonReverted>
          <GoodButton class="tw-bg-primary tw-text-white" @click="deleteGuest">Archive</GoodButton>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- View Link Dialog -->
    <v-dialog v-model="viewLinkDialog" max-width="800">
      <GoodCard2 class="tw-flex tw-flex-col tw-gap-6 tw-p-8">
        <div class="tw-text-2xl tw-font-semibold tw-text-zinc-600">
          {{ selectedLink?.home.title }} link at
          {{ useDateFormat(selectedLink?.created_at, 'MMMM D, YYYY').value }}
        </div>

        <!-- Bookings List -->
        <div v-if="selectedLink?.guestBookings && selectedLink.guestBookings.length > 0">
          <div class="tw-text-lg tw-font-semibold tw-mb-2">Current Bookings:</div>
          <div v-for="booking in selectedLink.guestBookings" :key="booking.id" class="tw-mb-6">
            <v-card class="tw-rounded-lg tw-overflow-hidden tw-shadow-md">
              <v-row no-gutters class="tw-items-center">
                <v-col cols="12" sm="8" class="tw-p-6 tw-flex tw-items-center tw-gap-8">
                  <div class="tw-w-36 tw-h-36 tw-flex-shrink-0">
                    <BeastImage
                      :src="selectedLink.home.photos[0]?.src || ''"
                      :alt="selectedLink.home.title"
                      class="tw-rounded-full tw-w-full tw-h-full tw-object-cover"
                    ></BeastImage>
                  </div>
                  <div class="tw-flex-grow">
                    <p class="tw-text-lg tw-text-zinc-600 tw-mb-2">
                      <span class="tw-font-medium">Dates:</span>
                      {{ useDateFormat(booking?.start_at, 'MMMM D, YYYY').value }}
                      -
                      {{ useDateFormat(booking?.end_at, 'MMMM D, YYYY').value }}
                    </p>
                    <p class="tw-text-lg tw-text-zinc-600 tw-mb-2">
                      <span class="tw-font-medium">Guests:</span>
                      {{ booking.extra_info.guests }}
                    </p>
                    <p v-if="booking.extra_info.pets" class="tw-text-lg tw-text-zinc-600 tw-mb-2">
                      <span class="tw-font-medium">Pets:</span>
                      {{ booking.extra_info.pets.number }}
                      {{ booking.extra_info.pets.type }}(s)
                    </p>
                    <p class="tw-text-lg tw-text-zinc-600 tw-mb-2">
                      <span class="tw-font-medium">Status:</span>
                      {{ booking.status }}
                    </p>
                  </div>
                </v-col>
                <v-divider vertical></v-divider>
                <v-col cols="12" sm="4" class="tw-p-6 tw-bg-gray-50">
                  <h5 class="tw-text-xl tw-font-semibold tw-mb-4 tw-text-primary">Pricing</h5>
                  <p class="tw-text-lg tw-text-zinc-600 tw-mb-2">
                    <span class="tw-font-medium">Total:</span> ${{
                      booking.extra_info.priceInfo.getTotalMoney.toFixed(2)
                    }}
                  </p>
                  <p class="tw-text-lg tw-text-zinc-600 tw-mb-2">
                    <span class="tw-font-medium">Nightly Rate:</span> ${{
                      booking.extra_info.priceInfo.nightly_rate
                    }}
                  </p>
                  <p class="tw-text-lg tw-text-zinc-600 tw-mb-2">
                    <span class="tw-font-medium">Cleaning Fee:</span> ${{
                      booking.extra_info.priceInfo.getCleaningFee
                    }}
                  </p>
                  <p class="tw-text-lg tw-text-zinc-600 tw-mb-2">
                    <span class="tw-font-medium">Tax:</span> ${{
                      booking.extra_info.priceInfo.getTax.toFixed(2)
                    }}
                  </p>
                </v-col>
              </v-row>
            </v-card>
          </div>
        </div>
        <div v-else class="tw-text-zinc-500">No current bookings for this link.</div>

        <!-- Pricing Adjustment -->
        <div class="tw-text-lg tw-font-semibold tw-mb-2">Adjust Pricing:</div>
        <div class="tw-flex tw-flex-col sm:tw-flex-row tw-gap-2">
          <div class="tw-flex tw-flex-col tw-gap-1 tw-flex-1">
            <div class="tw-font-medium tw-text-zinc-500">Nightly Rate</div>
            <v-text-field
              v-model="adjustedPrices.nightly_rate"
              outlined
              dense
              prefix="$"
              hide-details
              type="number"
              required
            />
          </div>
          <div class="tw-flex tw-flex-col tw-gap-1 tw-flex-1">
            <div class="tw-font-medium tw-text-zinc-500">Cleaning Fee</div>
            <v-text-field
              v-model="adjustedPrices.cleaning_fee"
              outlined
              dense
              prefix="$"
              hide-details
              type="number"
              required
            />
          </div>
          <div class="tw-flex tw-flex-col tw-gap-1 tw-flex-1">
            <div class="tw-font-medium tw-text-zinc-500">Pet Fee</div>
            <v-text-field
              v-model="adjustedPrices.pet_fee"
              outlined
              dense
              prefix="$"
              hide-details
              type="number"
              required
            />
          </div>
          <div class="tw-flex tw-flex-col tw-gap-1 tw-flex-1">
            <div class="tw-font-medium tw-text-zinc-500">Taxes</div>
            <v-text-field
              v-model="adjustedPrices.tax_rate"
              outlined
              dense
              suffix="%"
              hide-details
              type="number"
              required
            />
          </div>
          <div class="tw-flex tw-flex-col tw-gap-1 tw-flex-1">
            <div class="tw-font-medium tw-text-zinc-500">Min Stay</div>
            <v-text-field
              v-model="adjustedPrices.minimum_stay"
              outlined
              dense
              hide-details
              type="number"
              required
            />
          </div>
        </div>

        <div class="tw-flex tw-justify-end tw-mt-4 tw-gap-4">
          <GoodButtonReverted @click="viewLinkDialog = false"> Cancel </GoodButtonReverted>
          <GoodButton @click="saveAdjustedPrice"> Save Changes </GoodButton>
        </div>
      </GoodCard2>
    </v-dialog>

    <!-- New Message Modal -->
    <NewMessageModal
      :is-open="isNewMessageModalOpen"
      :messageable-people="[selectedGuest]"
      :pre-selected-receiver-id="selectedGuest?.id"
      @update:is-open="isNewMessageModalOpen = $event"
      @message-sent="handleMessageSent"
    />
  </div>
</template>

<script lang="ts">
import { defineComponent, onMounted, ref, computed } from '@nuxtjs/composition-api'
import { useBrowserLocation, useClipboard, useDateFormat } from '@vueuse/core'

import BeastImage from '~/components/BeastImage.vue'
import GoodButton from '~/components/GoodButton.vue'
import GoodButtonReverted from '~/components/GoodButtonReverted.vue'
import GoodCard2 from '~/components/GoodCard2.vue'
import NewMessageModal from '~/components/NewMessageModal.vue'
import { useApi, useToast } from '~/composables/useCommon'
import { TOAST_DURATION } from '~/constants'
import { formatDateRange } from '~/helpers'
import { GuestItem, AccessibleSharableLink } from '~/types'

export default defineComponent({
  name: 'GuestsPage',
  components: {
    BeastImage,
    GoodButton,
    GoodButtonReverted,
    GoodCard2,
    NewMessageModal,
  },
  middleware: ['auth'],

  setup() {
    const guestList = ref<GuestItem[]>([])
    const activeTab = ref('current')
    const api = useApi()
    const toast = useToast()
    const { copy } = useClipboard()
    const { origin } = useBrowserLocation().value
    const showDeleteModal = ref(false)
    const guestToDelete = ref<GuestItem | null>(null)
    const viewLinkDialog = ref(false)
    const selectedLink = ref<AccessibleSharableLink | null>(null)
    const adjustedPrices = ref({
      nightly_rate: 0,
      cleaning_fee: 0,
      pet_fee: 0,
      tax_rate: 0,
      minimum_stay: 0,
    })
    const isNewMessageModalOpen = ref(false)
    const selectedGuest = ref<{ id: number; name: string } | null>(null)

    const filteredGuestList = computed(() => {
      return guestList.value.filter(guest =>
        activeTab.value === 'current' ? !guest.deleted_at : guest.deleted_at
      )
    })

    const fetchGuestList = async () => {
      try {
        const { data } = await api.get('/guest-list')
        guestList.value = data.map(guest => ({
          ...guest,
          allowAccess: guest.accessible_sharable_links.some(link => !link.is_blocked),
        }))
      } catch (error) {
        console.error('Error fetching guest list:', error)
        toast.error('Failed to fetch guest list').goAway(TOAST_DURATION)
      }
    }

    const toggleAccess = async (guestItem: GuestItem, link: AccessibleSharableLink) => {
      try {
        await api.put('/guest-list/block-access', {
          guest_id: guestItem?.guest?.id,
          sharable_link_id: link.id,
          is_blocked: link.is_allowed ? '0' : '1',
        })
        toast
          .success(`Access ${link.is_allowed ? 'allowed' : 'blocked'} for ${link.home.title}`)
          .goAway(TOAST_DURATION)
      } catch (error) {
        console.error('Error toggling access:', error)
        toast.error('Failed to update access').goAway(TOAST_DURATION)
        link.is_allowed = !link.is_allowed
      }
    }

    const copyLink = async (link: AccessibleSharableLink) => {
      const fullLink = `${origin}/${link.home.slug}?sharable_link=${link.link}`
      await copy(fullLink)
      toast.success('Link copied to clipboard').goAway(TOAST_DURATION)
    }

    const sendMessage = (guestItem: GuestItem) => {
      selectedGuest.value = {
        id: guestItem?.guest?.id,
        name: `${guestItem?.guest?.first_name} ${guestItem?.guest?.last_name}`,
      }
      isNewMessageModalOpen.value = true
    }

    const handleMessageSent = () => {
      toast.success('Message sent successfully').goAway(TOAST_DURATION)
      isNewMessageModalOpen.value = false
      selectedGuest.value = null
    }

    const confirmDelete = (guestItem: GuestItem) => {
      guestToDelete.value = guestItem
      showDeleteModal.value = true
    }

    const deleteGuest = async () => {
      try {
        await api.delete(`/guest-list/${guestToDelete.value?.id}`)
        await fetchGuestList()
        showDeleteModal.value = false
        guestToDelete.value = null
        toast.success('Guest deleted successfully').goAway(TOAST_DURATION)
      } catch (error) {
        console.error('Error deleting guest:', error)
        toast.error('Failed to delete guest').goAway(TOAST_DURATION)
      }
    }

    const unarchiveGuest = async (guestItem: GuestItem) => {
      try {
        await api.put(`/guest-list/${guestItem.id}/restore`)
        await fetchGuestList()
        toast.success('Guest unarchived successfully').goAway(TOAST_DURATION)
      } catch (error) {
        console.error('Error unarchiving guest:', error)
        toast.error('Failed to unarchive guest').goAway(TOAST_DURATION)
      }
    }

    const setActiveTab = (tab: 'current' | 'archived') => {
      activeTab.value = tab
    }

    const viewLink = (link: AccessibleSharableLink) => {
      selectedLink.value = link
      const priceInfo = link.price_info || {}
      adjustedPrices.value = {
        nightly_rate: priceInfo.nightly_rate || 0,
        cleaning_fee: priceInfo.cleaning_fee || 0,
        pet_fee: priceInfo.pet_fee || 0,
        tax_rate: priceInfo.tax_rate || 0,
        minimum_stay: priceInfo.minimum_stay || 0,
      }
      viewLinkDialog.value = true
    }

    const saveAdjustedPrice = async () => {
      try {
        await api.put(
          `/sharable-links/${selectedLink.value?.id}/adjust-price`,
          adjustedPrices.value
        )
        toast.success('Prices updated successfully').goAway(TOAST_DURATION)
        viewLinkDialog.value = false
        await fetchGuestList()
      } catch (error) {
        console.error('Error updating prices:', error)
        toast.error('Failed to update prices').goAway(TOAST_DURATION)
      }
    }

    onMounted(fetchGuestList)

    return {
      filteredGuestList,
      activeTab,
      setActiveTab,
      toggleAccess,
      copyLink,
      sendMessage,
      confirmDelete,
      showDeleteModal,
      guestToDelete,
      deleteGuest,
      unarchiveGuest,
      useDateFormat,
      viewLink,
      viewLinkDialog,
      selectedLink,
      adjustedPrices,
      saveAdjustedPrice,
      formatDateRange,
      isNewMessageModalOpen,
      selectedGuest,
      handleMessageSent,
    }
  },
})
</script>
