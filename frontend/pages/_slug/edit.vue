<script lang="ts">
// @ts-ignore

import { defineComponent } from '@nuxtjs/composition-api'

import HomeEditActivationWrapper from '~/components/HomeEditActivationWrapper.vue'
import LegacyHomeDetails from '~/components/LegacyHomeDetails.vue'

export default defineComponent({
  components: {
    HomeEditActivationWrapper,
    LegacyHomeDetails,
  },

  middleware: ['auth', 'isHost'],

  async asyncData({ params, $axios, redirect }) {
    const { slug } = params
    try {
      const { data } = await $axios.patch(`user/homes/${slug}`)
      // If home is draft and has createProgressStep, redirect to create flow
      if (
        data.status === 'draft' &&
        data.extra_info?.createProgressStep &&
        data.extra_info?.createProgressStep !== 'DONE'
      ) {
        return redirect(`/create?from=${slug}`)
      }

      return { homeData: data, slug }
    } catch (error) {
      console.error('[edit.vue] Error fetching home data:', error)
      return redirect(`/${slug}`)
    }
  },

  mounted() {},
})
</script>

<template>
  <client-only>
    <div>
      <HomeEditActivationWrapper :home-data="homeData" :prop-slug="slug" />
      <LegacyHomeDetails />
    </div>
  </client-only>
</template>

<style lang="scss" scoped></style>
