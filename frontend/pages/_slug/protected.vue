<script lang="ts">
// @ts-nocheck

import {
  computed,
  defineComponent,
  ref,
  useRoute,
  useRouter,
  useStore,
  watch,
} from '@nuxtjs/composition-api'

import GoodButton from '~/components/GoodButton.vue'
import AuthSignupForm from '~/components/AuthSignupForm.vue'
import { useApi, useToast } from '~/composables/useCommon'
import { useAuthStore } from '~/composables/useAuthStore'
import { TOAST_DURATION } from '~/constants'

export default defineComponent({
  name: 'Protected',

  components: { GoodButton, AuthSignupForm },

  setup() {
    const route = useRoute()
    const router = useRouter()
    const api = useApi()
    const toast = useToast()
    const store = useStore()
    const authStore = useAuthStore()

    // Make route parameters reactive
    const homeSlug = computed(() => route.value.params.slug)
    const sharableLink = computed(() => route.value.query.sharable_link)

    // Watch for route changes and validate parameters
    const isValidRoute = computed(() => {
      const slug = homeSlug.value
      const link = sharableLink.value
      console.log('[Protected] Route parameters:', { slug, link })
      return !!(slug && link)
    })

    // If route is invalid, redirect to home
    watch(isValidRoute, (isValid) => {
      if (!isValid && process.client) {
        console.error('[Protected] Missing required parameters:', {
          homeSlug: homeSlug.value,
          sharableLink: sharableLink.value
        })
        router.push('/')
      }
    }, { immediate: true })

    // Check if this specific link is already validated
    const isLinkValidated = computed(() => {
      const link = sharableLink.value
      return link ? store.getters['passwordProtected/isLinkValidated'](link) : false
    })
    const isPasswordValidated = computed(() => store.state.passwordProtected.isPasswordValidated)

    // Watch for changes in sharable link and update store
    watch(sharableLink, (newLink) => {
      if (newLink) {
        store.commit('passwordProtected/setCurrentSharableLink', newLink)
        // Store the return path for post-verification redirects
        const returnUrl = `/${homeSlug.value}?sharable_link=${newLink}`
        authStore.setRedirectPath(returnUrl)
      }
    }, { immediate: true })

    // Watch for validation state and redirect if already validated
    watch([isLinkValidated, isPasswordValidated, isValidRoute], ([linkValidated, passwordValidated, validRoute]) => {
      if (validRoute && (linkValidated || passwordValidated) && process.client) {
        console.log('[Protected] Link already validated, redirecting to home')
        router.replace(`/${homeSlug.value}?sharable_link=${sharableLink.value}`)
      }
    })

    const isUserLoggedIn = computed(() => store.getters['auth/isLoggedIn'])
    const fullPath = computed(() => route.value.fullPath)
    const showLogin = ref(false)
    const showHomePasswordForm = ref(false)
    const isComponentReady = ref(false)

    // Watch for route readiness
    watch([homeSlug, sharableLink], ([slug, link]) => {
      console.log('[Protected] Route parameters updated:', { slug, link })
      if (slug && link) {
        isComponentReady.value = true
        console.log('[Protected] Component ready with parameters:', {
          isUserLoggedIn: isUserLoggedIn.value,
          homeSlug: slug,
          sharableLink: link,
          isLinkValidated: isLinkValidated.value,
          isPasswordValidated: isPasswordValidated.value,
        })
      }
    }, { immediate: true })

    const formData = ref({
      homePassword: '',
    })
    const form = ref<HTMLFormElement | null>(null)
    const isLoading = ref(false)
    const isFormValid = ref(true)
    
    // Login form data
    const loginForm = ref<HTMLFormElement | null>(null)
    const loginData = ref({
      email: '',
      password: '',
    })
    const loginIsValid = ref(true)
    const loginErrors = ref<string[]>([])
    const loginRules = {
      email: [v => !!v || 'Email is required', v => /.+@.+\..+/.test(v) || 'Email must be valid'],
      password: [v => !!v || 'Password is required'],
    }

    // Signup home password
    const signupHomePassword = ref('')
    const signupHomePasswordRules = [v => !!v || 'Home Password is required']

    const validate = async (password) => {
      const currentHomeSlug = homeSlug.value
      const currentSharableLink = sharableLink.value

      if (!currentHomeSlug || !currentSharableLink) {
        console.error('[Protected] Missing parameters for password validation')
        toast.error('Missing required parameters').goAway(TOAST_DURATION)
        return false
      }

      isLoading.value = true

      try {
        const { data } = await api.post(
          `/user/homes/${currentHomeSlug}/validate-password`,
          { password, sharable_link: currentSharableLink }
        )

        if (!data.valid) {
          // Handle specific error types
          if (data.error_type === 'deleted') {
            toast.error(data.message || 'You no longer have access to this home.').goAway(TOAST_DURATION)
            // Redirect to home page after showing error
            setTimeout(() => {
              router.push('/')
            }, 2000)
            return false
          } else if (data.error_type === 'blocked') {
            toast.error(data.message || 'Your access to this home has been blocked.').goAway(TOAST_DURATION)
            // Redirect to home page after showing error
            setTimeout(() => {
              router.push('/')
            }, 2000)
            return false
          } else {
            toast.error(data.message || 'Invalid Home Password').goAway(TOAST_DURATION)
            return false
          }
        }

        // Set password as validated in store and track this specific link
        store.commit('passwordProtected/setPasswordValidated', true)
        store.commit('passwordProtected/addValidatedLink', currentSharableLink)
        toast.success('Access granted').goAway(TOAST_DURATION)

        // Use replace instead of push to avoid adding to browser history
        try {
          // Add a small delay to ensure store state is properly set before navigation
          await new Promise(resolve => setTimeout(resolve, 100))
          await router.replace(`/${currentHomeSlug}?sharable_link=${currentSharableLink}`)
          return true
        } catch (navigationError) {
          console.error('[Protected] Navigation error after password validation:', navigationError)
          // Fallback: try using push instead of replace
          await router.push(`/${currentHomeSlug}?sharable_link=${currentSharableLink}`)
          return true
        }
      } catch (error) {
        console.error(error)

        // Handle HTTP error responses
        if (error.response && error.response.data) {
          const errorData = error.response.data
          if (errorData.error_type === 'deleted') {
            toast.error(errorData.message || 'You no longer have access to this home.').goAway(TOAST_DURATION)
            setTimeout(() => {
              router.push('/')
            }, 2000)
            return false
          } else if (errorData.error_type === 'blocked') {
            toast.error(errorData.message || 'Your access to this home has been blocked.').goAway(TOAST_DURATION)
            setTimeout(() => {
              router.push('/')
            }, 2000)
            return false
          } else {
            toast.error(errorData.message || 'Invalid Home Password').goAway(TOAST_DURATION)
            return false
          }
        } else {
          toast.error('Invalid Home Password').goAway(TOAST_DURATION)
          return false
        }
      } finally {
        isLoading.value = false
      }
    }

    const submitHomePassword = async () => {
      if (!form.value?.validate()) {
        return
      }
      await validate(formData.value.homePassword)
    }

    const checkBlockList = async () => {
      const currentSharableLink = sharableLink.value

      if (!currentSharableLink) {
        console.error('[Protected] Missing sharable link for block check')
        return true // Assume blocked if we can't check
      }

      isLoading.value = true
      try {
        const { data: isBlocked } = await api.post(
          `/sharable-links/${currentSharableLink}/check-block-access`
        )
        if (isBlocked) {
          toast.error('Access denied').goAway(TOAST_DURATION)
          return true
        }
        return false
      } catch (error) {
        console.error(error)
        toast.error('Error checking access').goAway(TOAST_DURATION)
        return true
      } finally {
        isLoading.value = false
      }
    }

    const checkGuestList = async () => {
      const currentHomeSlug = homeSlug.value
      const currentSharableLink = sharableLink.value

      if (!currentHomeSlug || !currentSharableLink) {
        console.error('[Protected] Missing parameters for guest list check')
        return false
      }

      isLoading.value = true
      try {
        const isBlocked = await checkBlockList()

        if (isBlocked) {
          return false
        }

        const { data: isInGuestList } = await api.post(`/user/homes/${currentHomeSlug}/check-guest-list`)

        if (isInGuestList) {
          toast.success('Access granted for guest').goAway(TOAST_DURATION)
          store.commit('passwordProtected/setPasswordValidated', true)
          store.commit('passwordProtected/addValidatedLink', currentSharableLink)
          // Use replace instead of push to avoid adding to browser history
          try {
            // Add a small delay to ensure store state is properly set before navigation
            await new Promise(resolve => setTimeout(resolve, 100))
            await router.replace(`/${currentHomeSlug}?sharable_link=${currentSharableLink}`)
            return true
          } catch (navigationError) {
            console.error('[Protected] Navigation error after guest list validation:', navigationError)
            // Fallback: try using push instead of replace
            await router.push(`/${currentHomeSlug}?sharable_link=${currentSharableLink}`)
            return true
          }
        }
        return false
      } catch (error) {
        console.error(error)

        // Check if this is a 403 error indicating the user was deleted from guest list
        if (error.response && error.response.status === 403) {
          const errorData = error.response.data
          if (errorData.error_type === 'deleted') {
            toast.error(errorData.message || 'You no longer have access to this home.').goAway(TOAST_DURATION)
            setTimeout(() => {
              router.push('/')
            }, 2000)
            return false
          }
        }

        toast.error('Error checking guest list').goAway(TOAST_DURATION)
        return false
      } finally {
        isLoading.value = false
      }
    }
    
    const handleLogin = async () => {
      if (!loginForm.value?.validate()) return
      
      isLoading.value = true
      try {
        const result = await authStore.login({
          email: loginData.value.email,
          password: loginData.value.password,
        })
        
        if (result.success) {
          toast.success('Login successful').goAway(TOAST_DURATION)
          // Check if user is in guest list first
          const isInGuestList = await checkGuestList()
          
          // If not in guest list, validate the home password
          if (!isInGuestList) {
            // Show home password form
            showHomePasswordForm.value = true
          }
        } else {
          loginErrors.value = [
            result.error?.response?.data?.message || 'Login failed. Please try again.',
          ]
        }
      } catch (error) {
        console.error('Login error:', error)
        loginErrors.value = ['An unexpected error occurred']
      } finally {
        isLoading.value = false
      }
    }
    
    const handleSignupSuccess = async (userData) => {
      toast.success('Signup successful').goAway(TOAST_DURATION)
      
      // Check if user needs to verify email first
      if (!userData.email_verified_at) {
        // User will be redirected to verify email page
        // The return URL is already set via authStore.setRedirectPath above
        return
      }
      
      // After signup, check if they're on the guest list
      const isInGuestList = await checkGuestList()
      
      // If not in guest list, validate the home password
      if (!isInGuestList) {
        // Show home password form
        showHomePasswordForm.value = true
      }
    }
    
    const toggleAuthForm = () => {
      showLogin.value = !showLogin.value
    }

    // Watch for auth state changes
    watch(
      isUserLoggedIn,
      async (newValue, oldValue) => {
        console.log('[Protected] Auth state changed:', { from: oldValue, to: newValue })
        if (newValue) {
          // User is logged in, check if they're in guest list
          console.log('[Protected] User logged in, checking guest list access')
          try {
            const isInGuestList = await checkGuestList()

            // If not in guest list, they'll need to enter the password
            if (!isInGuestList) {
              console.log('[Protected] User not in guest list, showing password form')
              // Show home password form
              showHomePasswordForm.value = true
            } else {
              console.log('[Protected] User has guest list access')
            }
          } catch (error) {
            console.error('[Protected] Error checking guest list:', error)
            // Fallback to showing password form
            showHomePasswordForm.value = true
          }
        } else {
          console.log('[Protected] User not logged in, showing login form')
          showLogin.value = true
        }
      },
      { immediate: true }
    )

    return {
      isFormValid,
      formData,
      form,
      submitHomePassword,
      isLoading,
      isUserLoggedIn,
      showLogin,
      loginForm,
      loginData,
      loginIsValid,
      loginRules,
      loginErrors,
      signupHomePassword,
      signupHomePasswordRules,
      handleLogin,
      handleSignupSuccess,
      toggleAuthForm,
      showHomePasswordForm,
      isComponentReady,
      homeSlug,
      sharableLink,
    }
  },
})
</script>

<template>
  <!-- Loading state while route parameters are being resolved -->
  <div v-if="!isComponentReady" class="tw-flex tw-justify-center tw-items-center tw-min-h-screen tw-bg-gray-100">
    <div class="tw-text-center">
      <v-progress-circular indeterminate color="primary" size="64"></v-progress-circular>
      <p class="tw-mt-4 tw-text-lg tw-text-gray-600">Loading...</p>
    </div>
  </div>

  <!-- Main content when component is ready -->
  <v-row
    v-else
    no-gutters
    class="tw-bg-[url('~/assets/signup_background_v2.webp')] tw-bg-cover tw-bg-center tw-min-h-[100vh]"
    style="background: rgba(0, 0, 0, 0.4); background-blend-mode: multiply"
  >
    <v-col
      cols="12"
      md="6"
      class="tw-flex tw-flex-col sm:tw-justify-start tw-justify-end tw-items-left tw-p-8 sm:tw-gap-5 tw-gap-1 sm:tw-pt-[calc(12.5%+90px)]"
    >
      <h1 class="tw-font-bold tw-text-5xl sm:tw-text-[3rem] tw-text-white">
        Protected Home Access
      </h1>
      <h2
        class="tw-font-light sm:tw-text-2xl tw-text-2xl tw-text-white tw-mt-0 tw-pt-0 tw-max-w-[500px]"
      >
        <span v-if="isUserLoggedIn">
          Please enter the password provided to you to access this private home listing
        </span>
        <span v-else>
          Create an account or login to access this private home listing
        </span>
      </h2>
    </v-col>
    <v-col
      cols="12"
      md="6"
      class="tw-flex tw-flex-col tw-justify-start tw-items-center tw-gap-8 sm:tw-pt-[10.5%] sm:tw-bg-[#787777]/60 tw-px-8 sm:tw-rounded-lg"
    >
      <div class="tw-w-full tw-flex tw-justify-center">
        <div class="sm:tw-w-[80%] tw-w-full">
          <!-- When user is logged in and needs to enter home password, show password form -->
          <div v-if="isUserLoggedIn && showHomePasswordForm">
            <div class="tw-text-center tw-text-white tw-text-2xl">
              <strong>Enter Home Password</strong>
            </div>
            <div class="tw-flex tw-mb-5 tw-justify-center">
              <a href="https://twimo.com" target="_blank">
                <img src="~/assets/plain_logo.webp" width="100" height="60" alt="twimo" />
              </a>
            </div>
            <v-form
              ref="form"
              v-model="isFormValid"
              lazy-validation
              class="tw-flex tw-flex-col tw-gap-8 tw-mb-10"
            >
              <v-text-field
                v-model="formData.homePassword"
                :rules="[v => !!v || 'Home Password is required']"
                outlined
                hide-details
                type="password"
                label="Home Password"
                class="tw-bg-white"
                @keyup.enter.prevent="submitHomePassword"
              />

              <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
                <good-button
                  class="tw-text-xl tw-w-1/2"
                  :disabled="!isFormValid || isLoading"
                  :loading="isLoading"
                  @click.prevent="submitHomePassword"
                >
                  Enter
                </good-button>
              </div>
            </v-form>
          </div>
          
          <!-- When user is not logged in, show signup/login forms -->
          <div v-else>
            <div class="tw-text-center tw-text-white tw-text-2xl">
              <strong>{{ showLogin ? 'Login' : 'Guest Sign Up' }}</strong>
            </div>
            <div class="tw-flex tw-mb-5 tw-justify-center">
              <a href="https://twimo.com" target="_blank">
                <img src="~/assets/plain_logo.webp" width="100" height="60" alt="twimo" />
              </a>
            </div>
            
            <!-- Login Form -->
            <v-form
              v-if="showLogin"
              ref="loginForm"
              v-model="loginIsValid"
              lazy-validation
              class="tw-flex tw-flex-col tw-gap-8 tw-mb-10"
            >
              <v-text-field
                v-model="loginData.email"
                :rules="loginRules.email"
                outlined
                hide-details
                label="Email"
                class="tw-bg-white tw-rounded-xl tw-shadow-md"
              />
              
              <v-text-field
                v-model="loginData.password"
                :rules="loginRules.password"
                outlined
                hide-details
                type="password"
                label="Password"
                class="tw-bg-white tw-rounded-xl tw-shadow-md"
                @keyup.enter.prevent="handleLogin"
              />
              
              <span
                v-for="(error, index) in loginErrors"
                :key="index"
                class="tw-text-red-500 tw-text-center tw-mx-auto tw-font-semibold"
              >{{ error }}</span>
              
              <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
                <good-button
                  class="tw-text-xl sm:tw-w-1/2 tw-w-full"
                  :disabled="!loginIsValid || isLoading"
                  :loading="isLoading"
                  @click.prevent="handleLogin"
                >
                  {{ isLoading ? 'Logging in...' : 'Login' }}
                </good-button>
              </div>
              
              <div class="tw-text-white tw-text-center tw-text-lg">
                Don't have an account?
                <a class="tw-font-semibold tw-text-white tw-underline" @click="toggleAuthForm">Sign up</a>
              </div>
            </v-form>
            
            <!-- Signup Form -->
            <div v-else>
              <AuthSignupForm 
                user-type="traveler" 
                :show-login-link="false"
                @success="handleSignupSuccess"
              />
              
              <div class="tw-text-white tw-text-center tw-text-lg tw-mt-4">
                Already have an account?
                <a class="tw-font-semibold tw-text-white tw-underline" @click="toggleAuthForm">Login</a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </v-col>
  </v-row>
</template>
