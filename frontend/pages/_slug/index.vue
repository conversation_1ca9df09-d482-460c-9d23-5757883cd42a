<script lang="ts">
// @ts-nocheck

import { defineComponent } from '@nuxtjs/composition-api'
import { mapGetters } from 'vuex'

import AppLightbox from '~/components/AppLightbox.vue'
import AppWebImageGallery from '~/components/AppWebImageGallery.vue'
import AppHomeGallery from '~/components/AppHomeGallery.vue'
import HomeInfoSection from '~/components/HomeInfoSection.vue'
import SwapBox from '~/components/SwapBox.vue'
import BeastImage from '~/components/BeastImage.vue'
import GoodButton from '~/components/GoodButton.vue'
import HomeMap from '~/components/HomeMap.vue'
import { formatText } from '~/helpers'

export default defineComponent({
  components: {
    GoodButton,
    SwapBox,
    HomeInfoSection,
    AppWebImageGallery,
    AppLightbox,
    AppHomeGallery,
    BeastImage,
    HomeMap,
  },

  async asyncData({ route, redirect, store, $axios, error, $cookies }) {
    const sharableLink = route.query.sharable_link
    const slug = route.params.slug
    const user = $cookies.get('user')
    const userType = user?.user_type
    const currentBookingType = route.query.booking_type
    const isSwapMode = currentBookingType === 'swap'

    // Check password protection for this specific link
    const isPasswordValidated = store.state.passwordProtected.isPasswordValidated
    const isLinkValidated = store.getters['passwordProtected/isLinkValidated'](sharableLink)
    const isPasswordProtectedPage = route.path.endsWith('/protected')



    // Only redirect to protected page if we're not already on it and this specific link isn't validated
    if (sharableLink && !isPasswordValidated && !isLinkValidated && !isPasswordProtectedPage) {
      console.log('[Home] Redirecting to protected page for sharable link:', sharableLink)
      return redirect(`/${slug}/protected?sharable_link=${sharableLink}`)
    }

    try {
      // Get house data
      const { data: houseData } = await $axios.get(`/homes/${slug}?sharable_link=${sharableLink}`)

      // Handle error cases
      if (!houseData) {
        return error({ statusCode: 404, message: 'House not found' })
      }

      if (houseData.invalid_sharable_link) {
        return error({ statusCode: 404, message: 'Invalid sharable link' })
      }

      // Determine access type
      const isPrivateBooking = !!sharableLink

      // Access control logic
      // Case 1: Non-host users cannot access swap mode
      if (userType !== 'host' && isSwapMode) {
        // Only redirect if we're currently in swap mode
        return redirect(`/${slug}`)
      }

      // Case 2: Public access to homes that don't allow booking or swaps
      if (!isPrivateBooking && !houseData.allow_booking && !houseData.allow_swaps) {
        return error({ statusCode: 404, message: 'House not found' })
      }

      // Case 3: Redirect from swap mode if home doesn't allow swaps
      if (isSwapMode && !houseData.allow_swaps) {
        return redirect(`/${slug}`)
      }

      // We'll handle swap mode display in the component instead of redirecting

      return {
        houseInfo: houseData,
      }
    } catch (e) {
      console.error('Error in home detail page:', e)

      // Handle specific HTTP errors for better user experience
      if (e.response?.status === 401) {
        // User needs to authenticate - redirect to protected page
        return redirect(`/${slug}/protected?sharable_link=${sharableLink}`)
      } else if (e.response?.status === 403) {
        // Access denied - show appropriate error
        return error({ statusCode: 403, message: e.response?.data?.message || 'Access denied' })
      }

      return error({ statusCode: 404, message: 'House not found' })
    }
  },

  data() {
    return {
      houseInfo: {},
      seeMore: false,
      formattedDescription: '',
      formattedLocation: '',
      images: [],
    }
  },

  computed: {
    ...mapGetters({
      currentUser: 'auth/getuser',
    }),

    // Determine if the current user is a host
    isUserHost() {
      return this.currentUser?.user_type === 'host' || this.currentUser?.is_host
    },

    // Determine if the home is swap-only
    isSwapOnlyHome() {
      return this.houseInfo?.allow_swaps && !this.houseInfo?.allow_booking
    },

    // Determine if we should force swap mode
    shouldForceSwapMode() {
      // If it's a swap-only home and the user is a host, force swap mode
      return this.isSwapOnlyHome && this.isUserHost
    },

    // Determine if we should show the booking component at all
    shouldShowBookingComponent() {
      // Don't show for home owners
      if (this.isHomeOwner) return false

      // Check if this is a private booking via sharable link
      const isPrivateBooking = !!this.$route.query.sharable_link

      // Always show for private booking links regardless of allow_booking or allow_swaps settings
      if (isPrivateBooking) return true

      // For non-hosts, only show if booking is allowed
      if (!this.isUserHost) return this.houseInfo?.allow_booking

      // For hosts, show if either booking or swaps are allowed
      return this.houseInfo?.allow_booking || this.houseInfo?.allow_swaps
    },

  head() {
    return {
      title: `Book your stay at ${this.houseInfo.title} - Twimo`,
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: `Book ${this.houseInfo.title} on Twimo – a beautiful vacation rental in ${this.houseInfo.city_long}`,
        },
        {
          hid: 'og:url',
          property: 'og:url',
          content: `https://twimo.com/view/house/${this.houseInfo.id}`,
        },
        {
          hid: 'og:title',
          property: 'og:title',
          content: `Book your stay at ${this.houseInfo.title} - Twimo`,
        },
        {
          hid: 'og:description',
          property: 'og:description',
          content: `Book ${this.houseInfo.title} on Twimo – a beautiful vacation rental in ${this.houseInfo.city_long}`,
        },
        {
          hid: 'og:type',
          property: 'og:type',
          content: 'website',
        },
        {
          hid: 'og:image',
          property: 'og:image',
          content: this.images && this.images.length > 0 ? this.images[0].src : '',
        },
        {
          hid: 'og:locale',
          property: 'og:locale',
          content: 'en_US',
        },
        { property: 'og:image:width', content: '740' },
        { property: 'og:image:height', content: '300' },
        { name: 'twitter:site', content: '@twimo' },
        {
          hid: 'twitter:url',
          name: 'twitter:url',
          content: `https://twimo.com/view/house/${this.houseInfo.id}`,
        },
        {
          hid: 'twitter:title',
          name: 'twitter:title',
          content: `${this.houseInfo.title} - ${this.houseInfo.country_long}`,
        },
        {
          hid: 'twitter:description',
          name: 'twitter:description',
          content: this.houseInfo.description.slice(0, 300) + '...',
        },
        {
          hid: 'twitter:image',
          name: 'twitter:image',
          content: this.images && this.images.length > 0 ? this.images[0].src : '',
        },
      ],
      link: [
        {
          rel: 'canonical',
          href: `https://twimo.com/view/house/${this.houseInfo.id}`,
        },
      ],
    }
  },

  // Other computed properties

    hasPhotos() {
      return this.houseInfo && this.houseInfo.photos && this.houseInfo.photos.length > 0
    },

    averageRating() {
      if (!this.houseInfo?.reviews?.length) {
        return 'No ratings yet'
      }
      const guestReviews = this.houseInfo.reviews.filter(review => review.type === 'guest_review')
      if (!guestReviews.length) {
        return 'No ratings yet'
      }

      const sum = guestReviews.reduce((acc, review) => acc + review.rating, 0)
      return (sum / guestReviews.length).toFixed(1)
    },

    totalReviews() {
      if (!this.houseInfo?.reviews) {
        return 0
      }
      return this.houseInfo.reviews.filter(review => review.type === 'guest_review').length
    },

    guestReviews() {
      if (!this.houseInfo?.reviews) {
        return []
      }
      return this.houseInfo.reviews
        .filter(review => review.type === 'guest_review')
        .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
    },

    isHomeOwner() {
      return this.currentUser?.id === this.houseInfo?.user?.id
    },
  },

  watch: {
    seeMore: function (newValue) {
      if (newValue) {
        this.formattedDescription = formatText(this.houseInfo.description)
        this.formattedLocation = formatText(this.houseInfo.location)
      } else {
        this.loadDescriptionEllipsis()
      }
    },
  },

  mounted() {
    this.initializePage()
  },

  beforeUnmount() {
    // Reset password validation state when navigating away from this page
    // This ensures that if the user visits another protected home, they'll need to validate again
    // But keep the validated links for this session
    this.$store.commit('passwordProtected/setPasswordValidated', false)
    this.$store.commit('passwordProtected/setCurrentSharableLink', null)
  },

  methods: {
    initializePage() {
      this.loadDescriptionEllipsis()
      this.loadLocationEllipsis()
      this.images = this.houseInfo.photos
    },

    loadDescriptionEllipsis() {
      const descriptionEllipsis = this.houseInfo.description.length > 600 ? '...' : ''
      this.formattedDescription =
        formatText(this.houseInfo.description).slice(0, 600) + descriptionEllipsis
    },

    loadLocationEllipsis() {
      const locationEllipsis = this.houseInfo.location.length > 600 ? '...' : ''
      this.formattedLocation = formatText(this.houseInfo.location).slice(0, 600) + locationEllipsis
    },

    showLightBoxWeb(index) {
      this.$refs.lightbox.toggleLightBox(this.images, index, false)
    },

    showLightBox(url, index, isMobile = false) {
      this.$refs.lightbox.toggleLightBox(url, index, isMobile)
    },

    showMore() {
      this.seeMore = !this.seeMore
      if (!this.seeMore) {
        window.scrollTo({
          top: 350,
          left: 0,
        })
      }
    },

    formatDate(date) {
      return new Date(date).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      })
    },

    isMobile() {
      return this.$vuetify.breakpoint.mdAndDown
    },
  },
})
</script>

<template>
  <client-only>
    <v-row :class="!isMobile ? 'tw-max-w-full tw-px-0' : 'tw-px-4 md:tw-px-0'">
      <v-col cols="12" class="tw-pb-0">
        <AppLightbox ref="lightbox" />

        <!-- Mobile Gallery -->
        <AppHomeGallery
          v-if="hasPhotos && $vuetify.breakpoint.mdAndDown"
          :photos="houseInfo"
          class="tw-rounded-lg tw-overflow-hidden tw-shadow-md"
          @show-lightbox="showLightBox(houseInfo.photos, 0, true)"
        />

        <!-- Desktop Gallery -->
        <AppWebImageGallery
          v-if="hasPhotos && $vuetify.breakpoint.lgAndUp"
          :house-info="houseInfo"
          class="tw-shadow-sm"
          @show-lightbox="showLightBoxWeb"
        />

        <!-- No Photos State -->
        <v-row
          v-if="!hasPhotos && isHomeOwner"
          no-gutters
          class="tw-flex tw-flex-col tw-justify-center tw-py-12 tw-items-center tw-bg-gray-50 tw-rounded-lg tw-border tw-border-gray-200"
        >
          <v-icon size="48" color="grey lighten-1"> mdi-image-outline </v-icon>
          <p class="tw-text-2xl tw-text-zinc-500 tw-mt-4">No Photos Uploaded Yet</p>
          <GoodButton class="tw-mt-6" @click="$router.push({ path: `/${houseInfo.slug}/edit` })">
            Upload Photos
          </GoodButton>
        </v-row>

        <v-row v-if="!hasPhotos && !isHomeOwner" no-gutters class="tw-max-w-[320px]">
          <BeastImage
            class="border-left-top border-right-top tw-rounded-b-lg tw-rounded-t-lg explore-image-container tw-object-contain tw-h-[260px] tw-max-w-full"
            :src="require('~/assets/explore-missing-home.webp')"
            alt="Image missing"
            title="Image missing"
          />
        </v-row>

        <!-- Main Content -->
        <v-row no-gutters class="tw-mt-6">
          <!-- Left Column: Info and Reviews -->
          <v-col cols="12" md="7" class="tw-pr-0 md:tw-pr-8">
            <!-- Rating Summary -->
            <div v-if="guestReviews.length" class="tw-mb-8">
              <div class="tw-flex tw-items-center tw-gap-4 tw-mb-4">
                <div class="tw-flex tw-items-center tw-gap-2">
                  <v-rating
                    :value="Number(averageRating)"
                    color="primary"
                    background-color="grey lighten-2"
                    readonly
                    dense
                    half-increments
                    size="20"
                  />
                  <span class="tw-text-lg tw-font-medium"
                    >{{ averageRating }} ({{ totalReviews }} reviews)</span
                  >
                </div>
              </div>
            </div>

            <!-- Home Info Section -->
            <HomeInfoSection
              :house-info="houseInfo"
              :formatted-description="formattedDescription"
              :see-more="seeMore"
              :home-owner-houses-count="0"
              @toggle-see-more="showMore"
            />

            <!-- Map Section -->
            <div class="tw-mt-10 tw-mb-8 map-container">
              <HomeMap :house-info="houseInfo" />
            </div>

            <!-- Reviews Section -->
            <div v-if="guestReviews.length" class="tw-mt-10 tw-mb-20 md:tw-mb-0 review-section">
              <div class="tw-flex tw-items-center tw-gap-4 tw-mb-6">
                <h2 class="tw-text-2xl tw-font-semibold tw-text-zinc-700">Reviews</h2>
                <div class="tw-flex tw-items-center tw-gap-2">
                  <v-rating
                    :value="Number(averageRating)"
                    color="primary"
                    background-color="grey lighten-2"
                    readonly
                    dense
                    half-increments
                    size="20"
                  />
                  <span class="tw-text-lg tw-font-medium"
                    >{{ averageRating }} ({{ totalReviews }} reviews)</span
                  >
                </div>
              </div>

              <!-- Review Cards -->
              <div class="tw-grid tw-grid-cols-1 md:tw-grid-cols-2 tw-gap-5">
                <div
                  v-for="review in guestReviews"
                  :key="review.id"
                  class="tw-flex tw-flex-col tw-gap-3 tw-p-5 tw-bg-white tw-rounded-lg tw-shadow-sm tw-border tw-border-gray-100 hover:tw-shadow-md tw-transition-shadow tw-duration-300"
                >
                  <div class="tw-flex tw-items-center tw-gap-4">
                    <BeastImage
                      :src="review.reviewer.avatar || ''"
                      :alt="review.reviewer.first_name"
                      class="tw-rounded-full tw-w-16 tw-h-16 tw-object-cover"
                    />
                    <div class="tw-flex tw-flex-col">
                      <div class="tw-font-semibold tw-text-zinc-800">
                        {{ review.reviewer.first_name }}
                        {{ review.reviewer.last_name?.charAt(0) }}.
                      </div>
                      <div class="tw-text-sm tw-text-zinc-500">
                        {{ formatDate(review.created_at) }}
                      </div>
                    </div>
                  </div>
                  <v-rating
                    :value="review.rating"
                    color="primary"
                    background-color="grey lighten-2"
                    readonly
                    dense
                    half-increments
                    size="20"
                  />
                  <div class="tw-text-zinc-600 tw-leading-relaxed">
                    {{ review.feedback }}
                  </div>
                </div>
              </div>
            </div>
          </v-col>

          <!-- Right Column: Booking Box -->
          <v-col cols="12" md="5" class="tw-relative">
            <SwapBox
              v-if="shouldShowBookingComponent"
              :house-info="houseInfo"
              :force-swap-mode="shouldForceSwapMode"
              :is-user-host="isUserHost"
            />
          </v-col>
        </v-row>
      </v-col>
    </v-row>
  </client-only>
</template>

<style scoped>
/* Use component-specific classes rather than targeting Tailwind classes */
.map-container {
  position: relative;
  z-index: 1; /* Ensure map is behind the booking card */
}

.review-section {
  margin-bottom: var(--bottom-spacing, 0);
}

@media (max-width: 960px) {
  :deep(.leaflet-control-container) {
    z-index: 800 !important;
  }

  :root {
    --bottom-spacing: 120px;
  }
}
</style>
