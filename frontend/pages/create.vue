<script lang="ts">
// @ts-nocheck

import {
  defineComponent,
  onBeforeUnmount,
  onMounted,
  watch,
  useRoute,
  useRouter,
  ref,
} from '@nuxtjs/composition-api'
import { storeToRefs } from 'pinia'

import MyHomePageTitle from '~/components/MyHomePageTitle.vue'
import GoodButtonReverted from '~/components/GoodButtonReverted.vue'
import { useApi } from '~/composables/useCommon'
import CreateHomeTitle from '~/components/CreateHomeTitle.vue'
import CreateHomeRentalPlan from '~/components/CreateHomeRentalPlan.vue'
import CreateHomeSpaceType from '~/components/CreateHomeSpaceType.vue'
import CreateHomeBookingAccess from '~/components/CreateHomeBookingAccess.vue'
import CreateHomeSwap from '~/components/CreateHomeSwap.vue'
import CreateHomeAddress from '~/components/CreateHomeAddress.vue'
import CreateHomeSkipTo from '~/components/CreateHomeSkipTo.vue'
import CreateHomeRoomAmount from '~/components/CreateHomeRoomAmount.vue'
import CreateHomeAmenity from '~/components/CreateHomeAmenity.vue'
import CreateHomePhotos from '~/components/CreateHomePhotos.vue'
import CreateHomeDescription from '~/components/CreateHomeDescription.vue'
import CreateHomePricing from '~/components/CreateHomePricing.vue'
import CreateHomeTermAgreement from '~/components/CreateHomeTermAgreement.vue'
import CreateHomeDone from '~/components/CreateHomeDone.vue'
import CreateHomeAirBnbUrl from '~/components/CreateHomeAirBnbUrl.vue'
import CreateHomeAirBnbWaiting from '~/components/CreateHomeAirBnbWaiting.vue'
import { useCreateHomeProgressStore } from '~/composables/useCreateHomeStore'
import CreateHomeProgressBar from '~/components/CreateHomeProgressBar.vue'
import GoodButton from '~/components/GoodButton.vue'
import ImagesRequiredModal from '~/components/ImagesRequiredModal.vue'

export default defineComponent({
  components: {
    CreateHomeAirBnbWaiting,
    CreateHomeAirBnbUrl,

    CreateHomeDone,
    CreateHomeTermAgreement,
    CreateHomePricing,
    CreateHomeDescription,
    CreateHomePhotos,
    CreateHomeAmenity,
    CreateHomeRoomAmount,
    CreateHomeSkipTo,
    CreateHomeAddress,
    CreateHomeSwap,
    CreateHomeBookingAccess,
    CreateHomeSpaceType,
    CreateHomeRentalPlan,
    CreateHomeTitle,
    GoodButtonReverted,
    MyHomePageTitle,
    CreateHomeProgressBar,
    GoodButton,
    ImagesRequiredModal,
  },

  middleware: ['auth'],
  
  transition: 'page',

  setup() {
    const api = useApi()
    const store = useCreateHomeProgressStore()
    const router = useRouter()
    const {
      createHomeData,
      validateCreateHomeData,
      setCurrentStep,
      back,
      next,
      createHome,
      resetCreateHomeData,
      saveProgress,
      closeImagesRequiredModal,
      goToPhotosFromModal,
      selectedBookingTypes,
    } = store

    const { showImagesRequiredModal } = storeToRefs(store)

    watch(
      () => showImagesRequiredModal.value,
      newVal => {
        console.log('Create page detected modal visibility change:', newVal)
      },
      { immediate: true }
    )

    const alreadySaved = ref(false)

    const handleExit = async () => {
      console.log('Exit button clicked - preserving current step:', currentStep.value)
      alreadySaved.value = true
      await saveProgress(api, true)
      router.push('/homes')
    }

    const handleSave = async () => {
      console.log('Save button clicked - preserving current step:', currentStep.value)
      alreadySaved.value = true
      await saveProgress(api, true)
      router.push('/homes')
    }

    onBeforeUnmount(async () => {
      console.log('onBeforeUnmount triggered, alreadySaved:', alreadySaved.value)
      if (!alreadySaved.value) {
        await saveProgress(api, false)
      }
    })

    const {
      currentStep,
      currentStepIndex,
      totalSteps,
      isAtLastStep,
      isAtSaveStep,
      currentStepComponent,
    } = storeToRefs(store)

    onMounted(() => {
      resetCreateHomeData()
    })

    return {
      createHomeData,
      currentStep,
      currentStepIndex,
      totalSteps,
      validateCreateHomeData,
      setCurrentStep,
      back,
      next,
      isAtLastStep,
      isAtSaveStep,
      createHome,
      api,
      currentStepComponent,
      saveProgress,
      handleExit,
      handleSave,
      showImagesRequiredModal,
      closeImagesRequiredModal,
      goToPhotosFromModal,
      selectedBookingTypes,
    }
  },
})
</script>

<template>
  <v-row no-gutters>
    <template v-if="!isAtLastStep">
      <CreateHomeProgressBar :current-step="currentStepIndex" :total-steps="totalSteps" />

      <div class="tw-flex tw-justify-between tw-items-center tw-mt-4 tw-w-full">
        <GoodButtonReverted @click="handleExit"> Exit </GoodButtonReverted>

        <GoodButton @click="handleSave"> Save </GoodButton>
      </div>
    </template>

    <MyHomePageTitle />

    <transition name="fade" mode="out-in">
      <component :is="currentStepComponent" :key="currentStep" />
    </transition>

    <v-col cols="12" class="tw-mt-8 tw-flex tw-justify-between">
      <GoodButtonReverted v-if="!isAtLastStep" class="tw-py-4 tw-px-8" @click="back">
        <v-icon small> mdi-arrow-left</v-icon>
        <span>back</span>
      </GoodButtonReverted>
      <GoodButtonReverted
        v-if="!isAtLastStep && !isAtSaveStep"
        class="tw-py-4 tw-px-8"
        @click="next(api)"
      >
        <span>next</span>
        <v-icon small> mdi-arrow-right</v-icon>
      </GoodButtonReverted>
      <GoodButtonReverted
        v-if="!isAtLastStep && isAtSaveStep"
        class="tw-py-4 tw-px-8"
        @click="createHome(api)"
      >
        Complete
      </GoodButtonReverted>
    </v-col>

    <div
      class="tw-fixed tw-bottom-0 tw-right-0 tw-p-2 tw-bg-gray-100 tw-text-xs tw-z-50"
      style="display: none"
    >
      Modal state: {{ showImagesRequiredModal ? 'VISIBLE' : 'HIDDEN' }}
    </div>

    <ImagesRequiredModal
      v-model="showImagesRequiredModal"
      :selected-types="selectedBookingTypes"
      @close="closeImagesRequiredModal"
      @go-to-photos="goToPhotosFromModal"
    />
  </v-row>
</template>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.fade-enter,
.fade-leave-to {
  opacity: 0;
  transform: translateY(10px);
}
</style>
