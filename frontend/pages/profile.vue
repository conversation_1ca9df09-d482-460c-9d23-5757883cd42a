<script lang="ts">
// Client-only page for authenticated users

import {
  computed,
  defineComponent,
  onMounted,
  reactive,
  ref,
  toRefs,
  useContext,
  useStore,
  useRoute,
  watch,
} from '@nuxtjs/composition-api'
import QrcodeVue from 'qrcode.vue'

import ProfileAvatar from '~/components/ProfileAvatar.vue'
import GoodButton from '~/components/GoodButton.vue'
import PaywallModal from '~/components/PaywallModal.vue'
import GoodCard2 from '~/components/GoodCard2.vue'
import GoodButtonReverted from '~/components/GoodButtonReverted.vue'
import StripeIdentityModal from '~/components/StripeIdentityModal.vue'
import { useSubscriptionStore } from '~/composables/useSubscriptionStore'
import { useApi, useToast } from '~/composables/useCommon'
import { useStripeConnectStore } from '~/composables/useStripeConnectStore'
import { useVenmoStore } from '~/composables/useVenmoStore'
import { useAuthStore } from '~/composables/useAuthStore'
import { useVerificationStore } from '~/composables/useVerificationStore'
import { useHostActivationStore } from '~/composables/useHostActivationStore'

export default defineComponent({
  components: {
    GoodButton,
    ProfileAvatar,
    PaywallModal,
    GoodCard2,
    GoodButtonReverted,
    QrcodeVue,
    StripeIdentityModal,
    HostActivationTracker: () => import('~/components/HostActivationTracker.vue'),
    HostActivationModal: () => import('~/components/HostActivationModal.vue'),
    BasicInfoSection: () => import('~/components/profile/BasicInfoSection.vue'),
    AboutSection: () => import('~/components/profile/AboutSection.vue'),
    AvatarSection: () => import('~/components/profile/AvatarSection.vue'),
  },

  middleware: ['auth'],

  setup() {
    // Setup context and refs
    const { $axios, $toast, $router } = useContext()
    const profileAvatarRef = ref(null)
    const contactFormRef = ref(null)
    const profileFormRef = ref(null)

    // Store initialization
    const subscriptionStore = useSubscriptionStore()
    const stripeConnect = useStripeConnectStore()
    const api = useApi()
    const venmoStore = useVenmoStore()
    const authStore = useAuthStore()
    const verificationStore = useVerificationStore()
    const hostActivationStore = useHostActivationStore()
    const store = useStore()
    const toast = useToast()

    // UI state refs
    const isLoading = ref(true)
    const isUserLoaded = ref(false)
    const showIdentityModal = ref(false)
    const aboutFocused = ref(false)
    const loadingAbout = ref(false)
    const contactSectionError = ref(false)
    const aboutSectionError = ref(false)
    const isNavigating = ref(false)

    // Computed properties
    const user = computed(() => store.getters['auth/getuser'])
    const isHostType = computed(() => authStore.isHostType)
    const showPaymentSection = computed(() => {
      return !isLoading.value && isUserLoaded.value && user.value && isHostType.value
    })

    // Form data
    const formData = reactive({
      first_name: '',
      last_name: '',
      email: '',
      about: '',
      countryCode: '+1',
      phone_number: '',
      preview: null,
      preview_img: null,
      avatar: null,
    })

    // Form validation rules
    const formRules = reactive({
      firstName: [v => !!v || 'First Name is required'],
      lastName: [v => !!v || 'Last Name is required'],
      countryCode: [
        v => !!v || 'Country code is required',
        v => v.startsWith('+') || 'Country code must start with +',
        v => /^\+[0-9]{1,4}$/.test(v) || 'Invalid country code format',
      ],
      phone: [
        v => !!v || 'Phone number is required',
        v => v.replace(/\D/g, '').length === 10 || 'Phone number must be exactly 10 digits',
        v => /^[0-9]+$/.test(v.replace(/\D/g, '')) || 'Phone number should contain only digits',
      ],
      email: [v => !!v || 'Email is required', v => /.+@.+\..+/.test(v) || 'Email must be valid'],
      about: [v => !v || v.length <= 500 || 'About me must not exceed 500 characters'],
    })

    // Venmo validation rules
    const venmo_rules = [
      v => !!v || 'Username is required',
      v => {
        if (!v) {
          return true
        }

        const cleanUsername = v.toString().replace('@', '')
        return /^[a-zA-Z0-9._-]+$/.test(cleanUsername) || 'Invalid Venmo username format'
      },
      v => {
        if (!v) {
          return true
        }

        return v.length <= 20 || 'Username must be 20 characters or less'
      },
    ]

    // Utility functions
    const formatPhoneNumber = () => {
      // Clean the phone number to contain only digits
      const phone = formData.phone_number.replace(/\D/g, '')
      // Limit to exactly 10 digits
      formData.phone_number = phone.slice(0, 10)
    }

    const formatCountryCode = () => {
      // Always set to US country code (+1)
      formData.countryCode = '+1'
    }

    const validateFileType = file => {
      const allowedTypes = ['image/jpeg', 'image/png', 'image/webp']
      const maxSize = 5 * 1024 * 1024 // 5MB

      if (!allowedTypes.includes(file.type)) {
        toast.error('Only JPG, PNG and WebP images are allowed').goAway(3000)
        return false
      }

      if (file.size > maxSize) {
        toast.error('Image size should not exceed 5MB').goAway(3000)
        return false
      }

      return true
    }

    // Form validation
    const validateForm = () => {
      const basicFieldsValid =
        formData.first_name &&
        formData.last_name &&
        formData.email &&
        formData.countryCode &&
        formData.phone_number &&
        formData.countryCode.length >= 2 &&
        formData.phone_number.replace(/\D/g, '').length >= 10 &&
        /.+@.+\..+/.test(formData.email)

      const aboutValid = !formData.about || formData.about.length <= 500

      return basicFieldsValid && aboutValid
    }

    // Data loading functions
    const loadPaymentData = async () => {
      try {
        await Promise.all([
          subscriptionStore.checkSubscriptionStatus(api),
          stripeConnect.fetchStatus(api),
          verificationStore.checkVerificationStatus(api),
        ])
      } catch (error) {
        console.error('Error loading payment data:', error)
      } finally {
        isLoading.value = false
      }
    }

    // Input handlers
    const handleAboutInput = () => {
      if (formData.about && formData.about.length > 500) {
        formData.about = formData.about.slice(0, 500)
      }
      if (formData.about === null) {
        formData.about = ''
      }
    }

    // Refs for section scrolling
    const stripeSection = ref(null)
    const venmoSection = ref(null)

    // Ref for payment section
    const paymentSection = ref(null)

    // Function to scroll to a specific section
    const scrollToSection = (sectionName) => {
      if (!sectionName) return

      console.log(`Attempting to scroll to section: ${sectionName}`)

      // Function to attempt scrolling with retries
      const attemptScroll = (attempts = 0) => {
        // Maximum number of attempts
        const maxAttempts = 5

        // Wait for DOM to be ready
        setTimeout(() => {
          let targetSection = null

          if (sectionName === 'stripe' && stripeSection.value) {
            targetSection = stripeSection.value
            console.log('Found stripe section')
          } else if (sectionName === 'venmo' && venmoSection.value) {
            targetSection = venmoSection.value
            console.log('Found venmo section')
          } else if (sectionName === 'payment' && paymentSection.value) {
            targetSection = paymentSection.value
            console.log('Found payment section')
          }

          if (targetSection && targetSection.$el) {
            console.log(`Scrolling to ${sectionName} section`)
            // Scroll to the section
            targetSection.$el.scrollIntoView({ behavior: 'smooth' })

            // Add a highlight effect
            targetSection.$el.classList.add('tw-bg-blue-50')
            setTimeout(() => {
              targetSection.$el.classList.remove('tw-bg-blue-50')
            }, 2000)
          } else {
            // If section not found and we haven't exceeded max attempts, try again
            if (attempts < maxAttempts) {
              console.log(`Section ${sectionName} not found, retrying... (${attempts + 1}/${maxAttempts})`)
              attemptScroll(attempts + 1)
            } else {
              console.log(`Failed to find section ${sectionName} after ${maxAttempts} attempts`)
            }
          }
        }, 500) // Short delay between attempts
      }

      // Start the first attempt
      attemptScroll()
    }

    // Get the current route
    const route = useRoute()

    // Watch for route changes to handle section parameter
    watch(() => route.value.query.section, (newSection) => {
      if (newSection) {
        // Wait for components to be mounted
        setTimeout(() => scrollToSection(newSection), 300)
      }
    })

    // Watch for navigation changes to prevent showing error during logout
    watch(() => route.value.path, (newPath, oldPath) => {
      if (oldPath && newPath !== oldPath) {
        isNavigating.value = true
        // Reset navigation flag after a short delay
        setTimeout(() => {
          isNavigating.value = false
        }, 1000)
      }
    })

    // Initialize data on component mount
    onMounted(async () => {
      isLoading.value = true
      try {
        // Execute each promise separately to handle individual failures
        const promises = [
          subscriptionStore.checkSubscriptionStatus(api).catch(err => {
            console.error('Error checking subscription status:', err)
            return null
          }),
          stripeConnect.fetchStatus(api).catch(err => {
            console.error('Error fetching stripe connect status:', err)
            return null
          }),
          venmoStore.fetchVenmoStatus(api).catch(err => {
            console.error('Error fetching venmo status:', err)
            return null
          }),
          verificationStore.checkVerificationStatus(api).catch(err => {
            console.error('Error checking verification status:', err)
            return null
          }),
          hostActivationStore.checkActivationStatus(api).catch(err => {
            console.error('Error checking host activation status:', err)
            return null
          }),
        ]

        await Promise.all(promises)

        // Check for section parameter in URL
        if (route.value.query.section) {
          // Wait for components to be mounted
          setTimeout(() => scrollToSection(route.value.query.section), 300)
        }
      } catch (error) {
        console.error('Error loading profile data:', error)
      } finally {
        isLoading.value = false
        isUserLoaded.value = true
      }
    })

    // Identity verification functions
    const startIdentityVerification = async () => {
      try {
        await verificationStore.createVerificationSession(api)
        showIdentityModal.value = true
      } catch (error) {
        console.error('Error starting verification:', error)
      }
    }

    const onVerificationComplete = async () => {
      try {
        await verificationStore.checkVerificationStatus(api)
      } catch (error) {
        console.error('Error checking verification status:', error)
      }
    }

    const onProfileSaved = (userData) => {
      // Update the store with the new user data
      store.commit('auth/updateUser', userData)

      // Update local form data to reflect the saved changes
      if (userData.first_name) formData.first_name = userData.first_name
      if (userData.last_name) formData.last_name = userData.last_name
      if (userData.about !== undefined) formData.about = userData.about

      // Clear any error states
      contactSectionError.value = false
      aboutSectionError.value = false
    }

    const setAboutFocused = val => {
      aboutFocused.value = val
    }

    // Watch for user data changes
    watch(
      user,
      (newUser, oldUser) => {
        if (newUser) {
          isUserLoaded.value = true
          if (authStore.isHostType) {
            loadPaymentData()
          }
          formData.first_name = newUser.first_name || ''
          formData.last_name = newUser.last_name || ''
          formData.email = newUser.email || ''
          // Parse phone number to separate country code and number
          const fullPhone = newUser.phone_number || ''

          // Always set country code to +1 (US)
          formData.countryCode = '+1'

          // Extract the phone number part
          if (fullPhone.startsWith('+')) {
            // Remove the country code part (could be +1, +11, +123, etc.)
            const phoneNumberPart = fullPhone.replace(/^\+[0-9]+/, '')
            formData.phone_number = phoneNumberPart

            // If the resulting phone number is too short, try to fix it
            if (phoneNumberPart.length < 10) {
              // The country code might have captured part of the phone number
              // Try to extract a proper 10-digit number from the full phone
              const digits = fullPhone.replace(/\D/g, '')
              if (digits.length >= 10) {
                // Take the last 10 digits as the phone number
                formData.phone_number = digits.slice(-10)
              }
            }
          } else {
            formData.phone_number = fullPhone
          }

          // Ensure phone number is properly formatted
          formData.phone_number = formData.phone_number.replace(/\D/g, '')
          formData.about = newUser.about || ''
          formData.avatar = newUser.avatar
        } else if (oldUser && !newUser) {
          // User was logged out - set navigation flag to prevent error message
          isNavigating.value = true
          // Set default values if user is not available
          formData.first_name = ''
          formData.last_name = ''
          formData.email = ''
          formData.countryCode = '+1'
          formData.phone_number = ''
          formData.about = ''
          formData.avatar = null
        } else {
          // Initial load with no user
          formData.first_name = ''
          formData.last_name = ''
          formData.email = ''
          formData.countryCode = '+1'
          formData.phone_number = ''
          formData.about = ''
          formData.avatar = null
        }
      },
      { immediate: true }
    )

    // Navigation functions
    const goToOnboarding = async () => {
      const url = await stripeConnect.getOnboardingUrl(api)
      window.location.href = url
    }

    const goToStripeDashboard = async () => {
      const url = await stripeConnect.getDashboardUrl(api)
      window.location.href = url
    }

    // Avatar computed property
    const avatar = computed(() => formData.avatar)

    // Safe about text computed property
    const safeAboutText = computed({
      get: () => {
        if (
          !user.value ||
          formData.about === null ||
          formData.about === 'null' ||
          formData.about === undefined
        ) {
          return ''
        }
        return formData.about
      },
      set: value => {
        formData.about = value || ''
      },
    })

    // Get avatar image computed property
    const getAvatarImage = computed(() => {
      if (avatar.value) {
        return avatar.value
      }
      return null
    })

    // Methods from Options API
    const trigger = () => {
      document.getElementById('fileInput')?.click()
    }

    const onFileChanged = e => {
      const file = e.target.files[0]
      if (!file) {
        return
      }

      if (!validateFileType(file)) {
        e.target.value = ''
        return
      }

      formData.preview = file
      updatePreview(file)
    }

    const updatePreview = file => {
      formData.preview = file
      profileAvatarRef.value?.showPreview(URL.createObjectURL(file))
      formData.avatar = URL.createObjectURL(file)
      $toast.success('Image successfully uploaded').goAway(3000)
      updateProfile()
    }

    const validate = () => {
      // Check if user exists before proceeding
      if (!user.value) {
        $toast.error('User data not available. Please try again later.').goAway(3000)
        return
      }

      contactSectionError.value = false
      aboutSectionError.value = false

      const contactFormValid = contactFormRef.value?.validate?.() ?? true
      const profileFormValid = profileFormRef.value?.validate?.() ?? true

      if (!contactFormValid) {
        contactSectionError.value = true
        $toast.error('Please check your contact information').goAway(3000)
        return
      }

      if (!profileFormValid) {
        aboutSectionError.value = true
        $toast.error('Please check your about information').goAway(3000)
        return
      }

      if (!validateForm()) {
        const basicFieldsValid =
          formData.first_name &&
          formData.last_name &&
          formData.email &&
          formData.countryCode &&
          formData.phone_number &&
          formData.countryCode.length >= 2 &&
          formData.phone_number.replace(/\D/g, '').length >= 10 &&
          /.+@.+\..+/.test(formData.email)

        if (!basicFieldsValid) {
          contactSectionError.value = true
        }

        const aboutValid = !formData.about || formData.about.length <= 500
        if (!aboutValid) {
          aboutSectionError.value = true
        }

        $toast.error('Please fill all required fields correctly').goAway(3000)
        return
      }

      updateProfile()
    }

    const updateProfile = () => {
      // Check if user exists before proceeding
      if (!user.value) {
        $toast.error('User data not available. Please try again later.').goAway(3000)
        return
      }

      loadingAbout.value = true

      const request = new FormData()
      request.append(
        'about',
        formData.about === null || formData.about === 'null' ? '' : formData.about
      )
      request.append('first_name', formData.first_name)
      request.append('last_name', formData.last_name)

      // Temporarily use the original user email and phone number instead of form values
      // This ensures these fields can't be changed even if someone bypasses the disabled attribute
      request.append('email', user.value.email)
      request.append('phone_number', user.value.phone_number)

      request.append('avatar', formData.avatar)
      if (formData.preview) {
        request.append('avatar', formData.preview)
      }

      $axios
        .post('user/update', request, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        })
        .then(response => {
          loadingAbout.value = false
          $toast.success('Profile successfully updated').goAway(3000)
          store.commit('auth/updateUser', response.data)

          contactSectionError.value = false
          aboutSectionError.value = false
        })
        .catch(err => {
          loadingAbout.value = false
          console.error(err)
          const errorMessage = err.response?.data?.message || 'Error updating profile'
          $toast.error(errorMessage).goAway(3000)
        })
    }

    // Destructure formData for template access
    const { first_name, last_name, email, about, countryCode, phone_number } = toRefs(formData)

    // Return all variables and functions needed in the template
    return {
      subscriptionStore,
      stripeConnect,
      verificationStore,
      goToStripeDashboard,
      goToOnboarding,
      api,
      venmoStore,
      isHostType,
      isLoading,
      showPaymentSection,
      showIdentityModal,
      startIdentityVerification,
      onVerificationComplete,
      onProfileSaved,
      ...toRefs(formData),
      avatar,
      safeAboutText,
      // Section refs for scrolling
      stripeSection,
      venmoSection,
      paymentSection,
      scrollToSection,
      getAvatarImage,
      formRules,
      formatPhoneNumber,
      formatCountryCode,
      validateForm,
      validateFileType,
      handleAboutInput,
      aboutFocused,
      loadingAbout,
      contactSectionError,
      aboutSectionError,
      venmo_rules,
      setAboutFocused,
      trigger,
      onFileChanged,
      updatePreview,
      validate,
      updateProfile,
      profileAvatarRef,
      contactFormRef,
      profileFormRef,
      user,
      formData,
      hostActivationStore,
      isNavigating,
    }
  },
})
</script>

<template>
  <client-only>
    <v-row no-gutters>
      <v-col cols="12" md="3" class="tw-bg-gray-50 tw-p-6">
        <div
          v-if="isLoading || !user"
          class="tw-flex tw-flex-col tw-items-center tw-justify-center tw-h-full"
        >
          <h3 class="tw-text-3xl tw-font-semibold tw-mb-8 tw-text-center tw-text-primary tw-tracking-wide">
            My Profile
          </h3>
          <v-progress-circular v-if="isLoading" indeterminate color="primary" size="48" width="3" />
        </div>
        <div v-else class="tw-flex tw-flex-col tw-items-center">
          <h3 class="tw-text-3xl tw-font-semibold tw-mb-8 tw-text-center tw-text-primary tw-tracking-wide">
            My Profile
          </h3>

          <div class="tw-flex tw-flex-col tw-items-center tw-gap-4 tw-mb-8 md:tw-mb-0">
            <!-- Profile Avatar Section -->
            <AvatarSection
              :user="user"
              :avatar="avatar"
              @saved="onProfileSaved"
            />

            <!-- Welcome Message -->
            <div class="tw-text-2xl tw-font-semibold tw-text-zinc-700 tw-text-center tw-mt-2">
              Hi, <span class="tw-text-primary">{{ formData.first_name }}</span>
            </div>

            <!-- Host Activation Tracker (only for hosts) -->
            <div v-if="isHostType" class="tw-w-full tw-mt-6">
              <div
                class="tw-bg-gradient-to-br tw-from-white tw-to-purple-50 tw-rounded-xl tw-shadow-md tw-p-5 tw-border-t-4 tw-border-primary"
              >
                <div class="tw-flex tw-items-center tw-justify-center tw-gap-2 tw-mb-4">
                  <div class="tw-relative">
                    <div
                      class="tw-bg-primary tw-rounded-full tw-p-1.5 tw-absolute tw-inset-0 tw-opacity-10 tw-animate-pulse"
                    ></div>
                    <v-icon color="primary" size="24"> mdi-home-account</v-icon>
                  </div>
                  <span class="tw-font-bold tw-text-primary tw-text-lg">Host Activation</span>
                </div>

                <HostActivationTracker @navigate="$router.push($event)" />
              </div>
            </div>


          </div>
        </div>
      </v-col>

      <v-col cols="12" md="9" class="tw-bg-white tw-p-4 md:tw-p-6">
        <!-- Loading state -->
        <div
          v-if="isLoading"
          class="tw-flex tw-flex-col tw-items-center tw-justify-center tw-py-12"
        >
          <v-progress-circular indeterminate color="primary" size="64" width="4" />
          <p class="tw-mt-4 tw-text-lg tw-text-gray-600">Loading your profile...</p>
        </div>

        <!-- Error state when user is not available (but not during navigation/logout) -->
        <div
          v-else-if="!user && !isNavigating"
          class="tw-flex tw-flex-col tw-items-center tw-justify-center tw-py-12 tw-px-4 tw-text-center"
        >
          <v-icon color="error" size="64">mdi-alert-circle</v-icon>
          <h3 class="tw-text-xl tw-font-semibold tw-mt-4 tw-text-red-600">
            Unable to load profile
          </h3>
          <p class="tw-mt-2 tw-text-gray-600">
            We couldn't load your profile information. Please try refreshing the page.
          </p>
          <GoodButton class="tw-mt-6" @click="$router.go()">Refresh Page</GoodButton>
        </div>

        <!-- Profile content when user is available -->
        <v-col v-else cols="12" md="8" class="tw-mx-auto">
          <v-row v-if="phone_number == null || phone_number == '' || (user.role_id == 4 && venmoStore.isConnected == false)">
            <v-col class="tw-text-center tw-bg-[#672093] tw-text-white tw-mb-10">
              <h2>You haven't yet finished your profile, finish now!</h2>
            </v-col>
          </v-row>

          <!-- Basic Information Section -->
          <BasicInfoSection
            :first-name="formData.first_name"
            :last-name="formData.last_name"
            @update:first-name="formData.first_name = $event"
            @update:last-name="formData.last_name = $event"
            @saved="onProfileSaved"
          />

          <!-- Contact Information (Read-only) -->
          <v-card flat class="tw-mt-8 tw-bg-white tw-shadow-sm tw-rounded-xl">
            <v-card-title class="tw-flex tw-items-center tw-gap-2 tw-pb-6">
              <v-icon color="primary" size="24"> mdi-account-circle</v-icon>
              <span class="tw-text-2xl tw-font-semibold tw-text-zinc-700">Contact Information</span>
            </v-card-title>

            <v-card-text>
              <div class="tw-grid md:tw-grid-cols-2 tw-gap-6">
                <v-text-field
                  v-model="email"
                  light
                  dense
                  label="Email*"
                  required
                  outlined
                  disabled
                  type="email"
                  class="tw-rounded-lg"
                >
                  <template #append>
                    <v-tooltip bottom>
                      <template #activator="{ on, attrs }">
                        <v-icon
                          color="grey darken-1"
                          v-bind="attrs"
                          v-on="on"
                        >
                          mdi-information-outline
                        </v-icon>
                      </template>
                      <span>Email editing is temporarily disabled for security reasons. <NAME_EMAIL> for assistance.</span>
                    </v-tooltip>
                  </template>
                </v-text-field>

                <div class="tw-flex tw-gap-2">
                  <v-text-field
                    v-model="countryCode"
                    light
                    dense
                    label="Country Code*"
                    placeholder="+1"
                    required
                    outlined
                    disabled
                    type="text"
                    class="tw-rounded-lg country-code-field"
                  />
                  <v-text-field
                    v-model="phone_number"
                    light
                    dense
                    label="Phone number*"
                    placeholder="1234567890"
                    required
                    outlined
                    disabled
                    type="text"
                    class="tw-rounded-lg"
                  >
                    <template #append>
                      <v-tooltip bottom>
                        <template #activator="{ on, attrs }">
                          <v-icon
                            color="grey darken-1"
                            v-bind="attrs"
                            v-on="on"
                          >
                            mdi-information-outline
                          </v-icon>
                        </template>
                        <span>Phone number editing is temporarily disabled for security reasons. <NAME_EMAIL> for assistance.</span>
                      </v-tooltip>
                    </template>
                  </v-text-field>
                </div>
              </div>
            </v-card-text>
          </v-card>

          <!-- Venmo Integration -->
        <GoodCard2
            v-if="user.role_id == 4"
            class="tw-ml-5 tw-mr-5">
          <div class="tw-flex tw-flex-col tw-gap-4">
            <div
              class="tw-flex tw-flex-col md:tw-flex-row tw-justify-between md:tw-items-center tw-gap-4"
            >
              <div class="tw-flex tw-flex-col">
                <span class="tw-text-xl tw-font-semibold tw-text-zinc-600">Venmo Payment</span>
                <div class="tw-text-zinc-500 tw-mt-2">
                  {{
                    venmoStore.isConnected
                      ? `Connected: ${venmoStore.displayUsername}`
                      : 'No Venmo account connected'
                  }}
                </div>
              </div>

              <div class="tw-flex tw-gap-2 md:tw-flex-row tw-flex-col">
                <GoodButton
                  v-if="venmoStore.isConnected"
                  class="tw-w-full md:tw-w-auto"
                  @click="venmoStore.preview.showDialog = true"
                >
                  Preview QR
                </GoodButton>
                <GoodButton
                  v-if="!venmoStore.isConnected"
                  class="tw-w-full md:tw-w-auto"
                  @click="venmoStore.showEditDialog = true"
                >
                  Add Venmo
                </GoodButton>
                <GoodButton
                  v-else
                  :loading="venmoStore.loading.remove"
                  class="tw-w-full md:tw-w-auto"
                  @click="venmoStore.removeUsername(api)"
                >
                  Remove Venmo
                </GoodButton>
              </div>
            </div>
          </div>
        </GoodCard2>

          <!-- About Section -->
          <div v-if="user && user.role_id != 4" class="tw-mt-8">
            <AboutSection
              :about="formData.about"
              @update:about="formData.about = $event"
              @saved="onProfileSaved"
            />
          </div>

          <!-- Payment Settings Section -->
          <v-card
            v-if="showPaymentSection"
            ref="paymentSection"
            flat
            class="tw-mt-8 tw-bg-white tw-shadow-sm tw-rounded-xl"
          >
            <v-card-title class="tw-flex tw-items-center tw-gap-2 tw-pb-6">
              <v-icon color="primary" size="24"> mdi-credit-card</v-icon>
              <span class="tw-text-2xl tw-font-semibold tw-text-zinc-700">Payment Settings</span>
            </v-card-title>

            <!-- Host Subscription -->
            <GoodCard2 class="tw-mb-6">
              <div
                class="tw-flex tw-flex-col md:tw-flex-row tw-justify-between tw-items-start md:tw-items-center tw-gap-4"
              >
                <div class="tw-flex tw-flex-col">
                  <span class="tw-text-xl tw-font-semibold tw-mb-2">Subscription</span>

                  <!-- Subscription Status -->
                  <div class="tw-flex tw-flex-col tw-gap-1">
                    <span
                      :class="[
                        'tw-font-medium',
                        subscriptionStore.hasSubscription ? 'tw-text-green-600' : 'tw-text-red-600',
                      ]"
                    >
                      {{
                        subscriptionStore.hasSubscription
                          ? 'Active Subscription'
                          : 'No Active Subscription'
                      }}
                    </span>

                    <!-- Plan Details -->
                    <span
                      v-if="subscriptionStore.hasSubscription && subscriptionStore.currentPlan"
                      class="tw-text-zinc-600"
                    >
                      {{ subscriptionStore.currentPlan.displayName }}
                    </span>
                    <span v-else class="tw-text-sm tw-text-zinc-500">
                      Subscribe to access premium features
                    </span>
                  </div>
                </div>

                <!-- Action Buttons -->
                <div class="tw-flex tw-gap-4">
                  <GoodButton
                    v-if="!subscriptionStore.hasSubscription"
                    :loading="subscriptionStore.loading"
                    class="tw-h-[40px] tw-w-full md:tw-w-auto"
                    @click="subscriptionStore.showPaywallModal = true"
                  >
                    Subscribe Now
                  </GoodButton>

                  <GoodButton
                    v-else
                    class="tw-w-full md:tw-w-auto"
                    @click="subscriptionStore.cancelSubscription(api)"
                  >
                    Cancel Subscription
                  </GoodButton>
                </div>
              </div>
            </GoodCard2>

            <!-- Stripe Connect -->
            <GoodCard2 ref="stripeSection" class="mb-6 tw-transition-all tw-duration-500">
              <div class="tw-flex tw-flex-col tw-gap-6">
                <div
                  class="tw-flex tw-flex-col md:tw-flex-row tw-justify-between md:tw-items-center tw-gap-4"
                >
                  <div class="tw-flex tw-flex-col">
                    <span class="tw-text-xl tw-font-semibold tw-text-zinc-600"
                      >Payment Processing</span
                    >
                    <div class="tw-flex tw-flex-col tw-gap-2 tw-mt-2">
                      <div class="tw-flex tw-items-center">
                        <v-icon
                          :color="
                            stripeConnect.status.is_connected &&
                            stripeConnect.status.charges_enabled
                              ? 'success'
                              : 'error'
                          "
                          class="mr-2"
                        >
                          {{
                            stripeConnect.status.is_connected &&
                            stripeConnect.status.charges_enabled
                              ? 'mdi-check-circle'
                              : 'mdi-close-circle'
                          }}
                        </v-icon>
                        <span class="tw-text-zinc-500">Stripe Connected</span>
                      </div>
                    </div>
                  </div>

                  <div class="tw-flex tw-flex-col tw-gap-2 md:tw-flex-row">
                    <GoodButton
                      v-if="stripeConnect.pendingRequirements.length > 0"
                      :loading="stripeConnect.loading.onboarding"
                      class="tw-w-full md:tw-w-auto"
                      @click="goToOnboarding"
                    >
                      {{ stripeConnect.pendingRequirements[0] }}
                    </GoodButton>

                    <GoodButton
                      v-if="stripeConnect.status.charges_enabled"
                      class="tw-w-full md:tw-w-auto"
                      @click="goToStripeDashboard"
                    >
                      Stripe Dashboard
                    </GoodButton>
                  </div>
                </div>
              </div>
            </GoodCard2>

            <!-- Venmo Integration -->
            <GoodCard2 ref="venmoSection" class="tw-transition-all tw-duration-500">
              <div class="tw-flex tw-flex-col tw-gap-4">
                <div
                  class="tw-flex tw-flex-col md:tw-flex-row tw-justify-between md:tw-items-center tw-gap-4"
                >
                  <div class="tw-flex tw-flex-col">
                    <span class="tw-text-xl tw-font-semibold tw-text-zinc-600">Venmo Payment</span>
                    <div class="tw-text-zinc-500 tw-mt-2">
                      {{
                        venmoStore.isConnected
                          ? `Connected: ${venmoStore.displayUsername}`
                          : 'No Venmo account connected'
                      }}
                    </div>
                  </div>

                  <div class="tw-flex tw-gap-2 md:tw-flex-row tw-flex-col">
                    <GoodButton
                      v-if="venmoStore.isConnected"
                      class="tw-w-full md:tw-w-auto"
                      @click="venmoStore.preview.showDialog = true"
                    >
                      Preview QR
                    </GoodButton>
                    <GoodButton
                      v-if="!venmoStore.isConnected"
                      class="tw-w-full md:tw-w-auto"
                      @click="venmoStore.showEditDialog = true"
                    >
                      Add Venmo
                    </GoodButton>
                    <GoodButton
                      v-else
                      :loading="venmoStore.loading.remove"
                      class="tw-w-full md:tw-w-auto"
                      @click="venmoStore.removeUsername(api)"
                    >
                      Remove Venmo
                    </GoodButton>
                  </div>
                </div>
              </div>
            </GoodCard2>
          </v-card>

          <!-- Venmo Dialog -->
          <v-dialog v-model="venmoStore.showEditDialog" max-width="400">
            <GoodCard2>
              <div class="tw-flex tw-flex-col tw-gap-4 tw-p-4">
                <div class="tw-text-xl tw-font-semibold tw-text-zinc-600">Connect Venmo</div>
                <div class="tw-text-sm tw-text-zinc-500">
                  Enter your Venmo username to generate your payment QR code
                </div>

                <v-text-field
                  v-model="venmoStore.username"
                  label="Venmo Username"
                  placeholder="@username"
                  :prefix="'@'"
                  outlined
                  dense
                  :rules="venmo_rules"
                />

                <div class="tw-flex tw-justify-end tw-gap-2">
                  <GoodButtonReverted @click="venmoStore.closeEditDialog">
                    Cancel
                  </GoodButtonReverted>
                  <GoodButton
                    :loading="venmoStore.loading.save"
                    :disabled="!venmoStore.username"
                    @click="venmoStore.saveUsername(api)"
                  >
                    Save
                  </GoodButton>
                </div>
              </div>
            </GoodCard2>
          </v-dialog>

          <!-- Add new QR Preview Dialog -->
          <v-dialog v-model="venmoStore.preview.showDialog" max-width="500">
            <GoodCard2>
              <div class="tw-flex tw-flex-col tw-gap-4 tw-p-4">
                <div class="tw-text-xl tw-font-semibold tw-text-zinc-600">Venmo Payment QR</div>

                <div class="tw-flex tw-gap-4">
                  <v-text-field
                    v-model="venmoStore.preview.amount"
                    label="Amount"
                    type="number"
                    outlined
                    dense
                    prefix="$"
                  />
                  <v-text-field v-model="venmoStore.preview.note" label="Note" outlined dense />
                </div>

                <div class="tw-flex tw-justify-center">
                  <GoodButton @click="venmoStore.generateQrPreview"> Generate QR Code</GoodButton>
                </div>

                <div
                  v-if="venmoStore.preview.qrUrl"
                  class="tw-flex tw-flex-col tw-items-center tw-gap-2"
                >
                  <qrcode-vue
                    :value="venmoStore.preview.qrUrl"
                    :size="200"
                    level="H"
                    render-as="svg"
                  />
                  <a
                    :href="venmoStore.preview.qrUrl"
                    target="_blank"
                    class="tw-text-sm tw-text-primary hover:tw-underline"
                    >Open in Venmo</a
                  >
                </div>
              </div>
            </GoodCard2>
          </v-dialog>

          <!-- Add new QR Preview Dialog for Vendor -->
          <v-dialog v-model="venmoStore.showVendorQRPreviewDialog" max-width="500">
            <GoodCard2>
              <div class="tw-flex tw-flex-col tw-gap-4 tw-p-4">
                <div class="tw-text-xl tw-font-semibold tw-text-center tw-text-zinc-600">
                  Venmo Payment QR
                </div>

                <div
                  v-if="venmoStore.preview.qrUrl"
                  class="tw-flex tw-flex-col tw-items-center tw-gap-2"
                >
                  <qrcode-vue
                    :value="venmoStore.preview.qrUrl"
                    :size="200"
                    level="H"
                    render-as="svg"
                  />
                  <a
                    :href="venmoStore.preview.qrUrl"
                    target="_blank"
                    class="tw-text-sm tw-text-primary hover:tw-underline"
                    >Open in Venmo</a
                  >
                </div>
              </div>
            </GoodCard2>
          </v-dialog>

          <PaywallModal
            :model-value="subscriptionStore.showPaywallModal"
            @update:model-value="subscriptionStore.showPaywallModal = $event"
          />

          <!-- Identity Verification Modal -->
          <StripeIdentityModal
            v-model="showIdentityModal"
            @verification-complete="onVerificationComplete"
          />

          <!-- Host Activation Modal -->
          <HostActivationModal v-if="isHostType" />
        </v-col>
      </v-col>
    </v-row>
  </client-only>
</template>

<style lang="scss" scoped>
.side-dialog {
  background: green;
  margin-top: 80%;
}

h1 {
  font-weight: 600 !important;
}

label {
  color: #000;
}

.completion {
  margin-top: 50px;
}

.review-box {
  color: #337ca0;
  font-size: 13px;
  background: #f5f5f5;
  border-radius: 3px;
  padding: 0px 5px;
}

button {
  background: #7c0cb1;
  border: 1px solid #7c0cb1;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
  border-radius: 14px;
  padding: 0px 15px !important;
  color: #fff !important;
}

h3.profile_title {
  font-style: normal;
  font-weight: 600;
  font-size: 2.2rem;
  line-height: 50px;
  letter-spacing: 0.02em;
  margin-bottom: 30px;
  color: #858585;
  text-align: center;
}

h3.profile_title span {
  color: #7c0cb1;
}

@media only screen and (min-width: 1264px) {
  .custom-grid_form {
    display: grid;
    grid-template-columns: repeat(2, 50%);
  }
}

.v-card {
  border-radius: 12px;
}

.v-dialog {
  border-radius: 12px;
}

.country-code-field {
  max-width: 120px;
  min-width: 100px;
}
</style>
