<script lang="ts">
import { defineComponent } from '@nuxtjs/composition-api'

import AboutMember from '~/components/AboutMember.vue'

export default defineComponent({
  name: 'AboutUs',

  components: {
    AboutMember,
  },

  layout: 'static',

  props: {},

  data() {
    return {}
  },

  head() {
    return {
      title: "About Twimo | Built for Private Vacation Home Sharing",
      meta: [
        {
          hid: "description",
          name: "description",
          content: "Discover how Twimo is redefining second-home sharing. Learn about our mission to make vacation rentals and home swaps more private, flexible, and secure."
        }
      ]
    };
  },

  mounted() {
    this.animateOnScroll()
  },

  methods: {
    // Function to observe elements and trigger animation
    animateOnScroll() {
      const elements = document.querySelectorAll('.animate')

      const observer = new IntersectionObserver(
        (entries, observer) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              entry.target.classList.add('visible')
              observer.unobserve(entry.target) // Stop observing after it's visible
            }
          })
        },
        { threshold: 0.5 }
      )

      elements.forEach(element => {
        observer.observe(element)
      })
    },
  }

})
</script>
<template>
  <main class="about-page font-figtree tw-bg-[#FFF]">
    <section
      class="tw-bg-center tw-bg-no-repeat tw-bg-cover tw-min-h-[50vh] md:tw-min-h-[50vh] tw-flex"
    >
      <div
        class="tw-container animate tw-mx-auto tw-px-5 tw-py-5 tw-pb-[2rem] md:tw-pb-[2rem] tw-flex tw-flex-col tw-gap-4 tw-justify-end"
      >
        <div>
          <p
            class="tw-text-2xl md:tw-text-4xl tw-text-center tw-text-[#2C005B] tw-leading-[40px] md:tw-leading-[55px] tw-font-[Poppins] tw-font-regular tw-mb-5"
          >
            We help owners fully utilize their vacation
            <br class="tw-hidden md:tw-block" />
            homes while enabling <strong>adventure</strong>
          </p>

          <p class="tw-text-center tw-w-[100%] tw-block md:tw-hidden">
            <AnchorLinkStyleOne
              text="Sign up now"
              link="/signup"
              font_size="tw-text-base tw-text-center"
            />
          </p>
        </div>
      </div>
    </section>
    <section class="tw-py-10 tw-bg-gradient-to-t tw-from-[#fff]">
      <div class="tw-containertw-mx-auto tw-px-5">
        <div
          class="tw-bg-[url('@/assets/about-bg.jpg')] tw-bg-cover tw-bg-center tw-max-w-[940px] animate tw-ml-auto tw-mr-auto tw-flex tw-gap-0 md:tw-gap-14 tw-flex-col md:tw-flex-row tw-items-center tw-justify-between tw-rounded-[10px] tw-shadow-[0_2px_3px_rgba(0,0,0,0.4)]"
        >
          <div class="md:tw-w-5/12 tw-text-center">
            
          </div>
          <!-- <div class="md:tw-w-1/12"></div> -->
          <div class="md:tw-w-7/12 tw-rounded-[10px] tw-px-[25px] md:tw-px-10 tw-py-[2rem] right-content-container">
            <h3 class="tw-text-[#FFF] tw-text-sm md:tw-text-base tw-font-medium tw-font-[Poppins]">
              WHY WE ARE HERE
            </h3>
            <h1 class="tw-text-[#FFF] tw-text-2xl md:tw-text-3xl tw-font-medium tw-mb-5 tw-font-[Poppins]">
              The world is my oyster
            </h1>
            <p
              class="tw-text-[#FFF] tw-text-[0.9rem] md:tw-text-[0.9rem] tw-leading-6 tw-mb-3 tw-font-light tw-font-[Poppins]"
            >
              Twimo grew out of Second Home Care Inc, where we have managed thousands of homes in
              predominant vacation areas. In recent years our new clients have experienced sharp
              increases in mortgage rates and home values. Globally, vacation home owners are
              experiencing increasingly strict short term rental regulations.
            </p>
            <p
              class="tw-text-[#FFF] tw-text-[0.9rem] md:tw-text-[0.9rem] tw-leading-6 tw-mb-3 tw-font-light tw-font-[Poppins]"
            >
              With their larger investments, clients are looking for greater utilization or return
              on their investments. With greater restrictions on standard short term rentals, they
              are seeking flexibility on how they achieve their targets.
            </p>
            <p
              class="tw-text-[#FFF] tw-text-[0.9rem] md:tw-text-[0.9rem] tw-leading-6 tw-mb-5 tw-font-light tw-font-[Poppins]"
            >
              We launched Twimo to provide the flexibility every second homeowner requires to
              increase the ROI of their vacation home.
            </p>
          </div>
        </div>
      </div>
    </section>
    <section
      class="tw-my-0 tw-pb-11 tw-pt-[5rem] tw-font-figtree tw-bg-[#FFF] tw-position-relative meet_people_container tw-rounded-t-[50px]"
    >
      <h2
        class="tw-text-[#2C005B] animate tw-text-base md:tw-text-xl tw-text-left md:tw-text-center tw-mb-1 tw-font-medium tw-uppercase tw-px-5 tw-font-[Poppins]"
      >
        Meet the people behind Twimo
      </h2>
      <h3
        class="tw-text-[#2C005B] animate tw-text-2xl md:tw-text-4xl tw-text-left md:tw-text-center tw-mb-6 tw-font-medium tw-px-5 tw-font-[Poppins]"
      >
        We believe in investing in people, not robots
      </h3>

      <AboutMember
        name="Jonathan Luster"
        role="Founder, CEO"
        :image="require('~/assets/jonathan-luster.png')"
        description="Jon has over twenty years of experience in home services and hospitality, leveraging built and bought technology from the start. He has been with Second Home Care since 2018, resetting processes, technology, and team structure, exponentially increasing productivity and flow-through. Before Second Home Care, Jonathan was with Lowe’s Home Improvement for 14 years, serving as vice president of new markets and concept development, where his team designed, built, and operated various online and offline businesses."
      />

      <AboutMember
        name="Haley Sandberg"
        role="Head of Product Design"
        :image="require('~/assets/haley-sandberg.png')"
        description="Haley has over 7 years of startup experience, co-founding property management software, TrueRent in 2017. After the acquisition of her previous company she wanted to embark on another startup where her background and interest would intertwine. With that, she became a founding member of Twimo. Leading the design efforts, with a user-centric approach, expanding the capabilities of the product for the Twimo customer."
        :revese-order="true"
      />

      <AboutMember
        name="Hoang Pham"
        role="Lead Developer, Backend"
        :image="require('~/assets/hoang-pham.png')"
        description="Hoang is passionate about building software with a user-centric approach. With 6 years in tech, he has worked on both e-commerce and property management solutions while contributing to open source projects. He executes clean architecture while building reliable systems. His expertise spans the full development lifecycle, including UI, server-side programming, and integration across multiple platforms, both web and mobile."
      />

      <AboutMember
        name="Shashi Kumar"
        role="Lead Developer, Frontend "
        :image="require('~/assets/shashi-kumar.png')"
        description="Shashi Kumar is a seasoned full stack developer with over 10 years of experience in application development. With a strong foundation in both front-end and back-end technologies, Shashi has consistently delivered high-quality, scalable solutions that enhance user experience and drive business objectives. His expertise spans the full development lifecycle, including intuitive UI design, efficient server-side programming, and seamless integration across multiple platforms. Known for his problem-solving skills and attention to detail, Shashi combines technical acumen with a passion for innovation, making him an invaluable contributor to any development team."
        :revese-order="true"
      />
    </section>
  </main>
</template>

<style scoped>
.meet_people_container {
  position: relative;
}

/* .meet_people_container:before {
		background: #f2f2f2;
		transform: skewY(5deg);
		position: absolute;
		left: 0px;
		top: -60px;
		content: ' ';
		width: 100%;
		height: 120px;
		z-index: 1;
	} */

.animate {
  opacity: 0;
  transform: translateY(50px);
  transition:
    opacity 0.6s ease,
    transform 0.6s ease;
}

.animate.visible {
  opacity: 1;
  transform: translateY(0);
}

.right-content-container{
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2)), rgba(44, 0, 91, 0.6);
}

</style>
