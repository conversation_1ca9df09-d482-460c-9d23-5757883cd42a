<template>
  <div class="messages-page">
    <!-- New Message Button (Fixed Position for Desktop) -->
    <div class="fixed-new-message-btn md:tw-block tw-hidden">
      <GoodButton @click="openNewMessageModal"> + New Message </GoodButton>
    </div>

    <div class="tw-container tw-mx-auto tw-p-4 messages-container">
      <!-- Header with title -->
      <div class="header-container tw-mb-6">
        <div class="title-container">
          <MyHomePageTitle
            :title="'Messages'"
            class="tw-mb-0"
          />
        </div>
        <div class="mobile-actions tw-flex tw-items-center">
          <!-- Mobile New Message Button -->
          <div class="md:tw-hidden tw-mr-2">
            <GoodButton class="mobile-new-message-btn" @click="openNewMessageModal"> + New Message </GoodButton>
          </div>
          <!-- Toggle button for inbox on mobile -->
          <button
            class="md:tw-hidden tw-p-2 tw-bg-gray-200 tw-rounded-full"
            @click="toggleInbox"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="tw-h-6 tw-w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M4 6h16M4 12h16M4 18h16"
              />
            </svg>
          </button>
        </div>
      </div>

      <!-- Main content -->
      <div class="tw-flex tw-flex-col md:tw-flex-row tw-space-y-6 md:tw-space-y-0 md:tw-space-x-6">
      <!-- Left column: List of messageables -->
      <div :class="['tw-w-full md:tw-w-1/3 lg:tw-w-1/4', { 'tw-hidden md:tw-block': !showInbox }]">
        <!-- Inbox/Archived toggle -->
        <div class="tw-flex tw-w-full tw-bg-gray-100 tw-rounded-lg tw-mb-4">
          <button
            class="tw-grow tw-px-4 tw-py-2 tw-rounded-l-lg"
            :class="{ 'tw-bg-white tw-shadow': activeTab === 'inbox' }"
            @click="setActiveTab('inbox')"
          >
            Inbox
          </button>
          <button
            class="tw-grow tw-px-4 tw-py-2 tw-rounded-r-lg"
            :class="{ 'tw-bg-white tw-shadow': activeTab === 'archived' }"
            @click="setActiveTab('archived')"
          >
            Archived
          </button>
        </div>

        <!-- List of messageables -->
        <div
          v-for="(messages, messageableKey) in sortedFilteredGroupedMessages"
          :key="messageableKey"
          class="tw-bg-white tw-rounded-lg tw-shadow-md tw-p-4 tw-mb-4 tw-cursor-pointer tw-relative"
          :class="{
            'tw-border-l-4 tw-bg-zinc-200 tw-border-primary': isUnread(messages),
          }"
          @click="selectMessageable(messageableKey)"
        >
          <div class="tw-flex tw-items-center tw-flex-row">
            <BeastImage
              :src="getLastMessage(messages).sender.avatar"
              :alt="getLastMessage(messages).sender.first_name"
              :is-avatar="true"
              :avatar-size="48"
              class="tw-mr-3"
            />
            <div class="tw-flex-grow tw-flex tw-flex-col">
              <div class="tw-flex tw-flex-row tw-justify-between tw-items-center">
                <div
                  class="tw-font-semibold tw-text-zinc-600 tw-text-lg"
                  :class="{ 'tw-font-bold': isUnread(messages) }"
                >
                  {{ getLastMessage(messages).sender.first_name }}
                </div>
                <div
                  class="tw-ml-auto tw-text-primary tw-text-sm tw-font-semibold"
                  :class="{
                    'tw-text-gray-500': !isUnread(messages),
                  }"
                >
                  {{ formatDate(getLastMessage(messages).created_at) }}
                </div>
              </div>
              <div
                class="tw-text-zinc-500"
                :class="{
                  'tw-font-semibold': isUnread(messages),
                }"
                v-html="highlightText(getLastMessage(messages).subject)"
              ></div>
            </div>
          </div>
          <button
            v-if="activeTab === 'inbox'"
            class="tw-absolute tw-bottom-2 tw-right-2 tw-text-gray-400 hover:tw-text-red-500"
            @click.stop="archiveMessageGroup(messageableKey)"
          >
            <svg
              class="tw-w-5 tw-h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4"
              ></path>
            </svg>
          </button>
          <button
            v-else
            class="tw-absolute tw-bottom-2 tw-right-2 tw-text-gray-400 hover:tw-text-green-500"
            @click.stop="unarchiveMessageGroup(messageableKey)"
          >
            <svg
              class="tw-w-5 tw-h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"
              ></path>
            </svg>
          </button>
        </div>
      </div>

      <!-- Right column: Message details and search -->
      <div
        :class="[
          'tw-flex-grow tw-flex tw-flex-col tw-w-full md:tw-max-w-3xl',
          { 'tw-hidden md:tw-flex': showInbox },
        ]"
      >
        <!-- Search bar -->
        <div class="tw-mb-4 tw-w-full">
          <div
            class="tw-relative tw-rounded-lg tw-border tw-border-gray-300 focus-within:tw-border-primary"
          >
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Search all messages"
              class="tw-w-full tw-pl-10 tw-pr-4 tw-py-2 focus:tw-outline-none tw-rounded-lg"
            />
            <span class="tw-absolute tw-left-3 tw-top-1/2 tw-transform -tw-translate-y-1/2">
              <svg
                class="tw-h-5 tw-w-5 tw-text-gray-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </svg>
            </span>
          </div>
        </div>

        <!-- Message details -->
        <div
          v-if="currentMessageable && Object.keys(sortedFilteredGroupedMessages).length > 0"
          class="tw-rounded-lg tw-shadow-md tw-flex-grow tw-flex tw-flex-col"
        >
          <!-- Messageable info header -->
          <div
            class="tw-bg-zinc-100 tw-rounded-t-lg tw-p-4 md:tw-p-6 tw-flex tw-flex-col md:tw-flex-row tw-justify-between tw-items-start"
          >
            <!-- Left side: Text content -->
            <div class="tw-flex tw-flex-col tw-gap-2 tw-flex-grow">
              <div class="tw-text-2xl tw-font-semibold tw-text-zinc-600">
                {{ currentMessageableInfo.title }}
              </div>
              <div class="tw-text-zinc-500 tw-text-xl">
                {{ currentMessageableInfo.subtitle }}
              </div>
              <div class="tw-text-zinc-500 tw-text-lg">
                {{ currentMessageableInfo.description }}
              </div>
              <div class="tw-flex tw-gap-4 tw-mt-2">
                <div
                  v-if="currentMessageableInfo.homeLink"
                  class="tw-text-primary tw-font-semibold tw-cursor-pointer"
                  @click="$router.push(currentMessageableInfo.homeLink)"
                >
                  Go to Home
                </div>
                <div
                  v-if="
                    currentMessageableInfo.bookingLink && currentMessageableInfo.showBookingLink
                  "
                  class="tw-text-primary tw-font-semibold tw-cursor-pointer"
                  @click="$router.push(currentMessageableInfo.bookingLink)"
                >
                  Go to Booking
                </div>
              </div>
            </div>

            <!-- Right side: Avatar images -->
            <div class="tw-hidden md:tw-flex tw-items-center tw-mt-4 md:tw-mt-0 md:tw-ml-4">
              <BeastImage
                :src="currentMessageableInfo.senderAvatar"
                :is-avatar="true"
                :avatar-size="96"
                class="tw-border-2 tw-border-white tw-shadow-md tw-z-10"
              />
              <BeastImage
                :src="currentMessageableInfo.receiverAvatar"
                :is-avatar="true"
                :avatar-size="96"
                class="tw-border-2 tw-border-white tw-shadow-md tw--ml-4"
              />
            </div>
          </div>

          <!-- Messages -->
          <div
            class="tw-flex-grow tw-overflow-y-auto tw-gap-4 tw-p-4 md:tw-p-6 tw-min-h-[calc(100vh-650px)]"
          >
            <div
              v-for="message in sortedFilteredGroupedMessages[currentMessageable]"
              :key="message.id"
            >
              <div
                class="tw-flex tw-mb-2"
                :class="{
                  'tw-flex-row-reverse': message.sender.id === currentUser.id,
                }"
              >
                <BeastImage
                  :src="message.sender.avatar"
                  :alt="message.sender.first_name"
                  :is-avatar="true"
                  :avatar-size="64"
                  :class="[
                    message.sender.id === currentUser.id ? 'tw-ml-3' : 'tw-mr-3',
                  ]"
                />
                <div
                  class="tw-rounded-lg tw-p-3 tw-max-w-md"
                  :class="
                    message.sender.id === currentUser.id ? 'tw-bg-purple-100' : 'tw-bg-gray-100'
                  "
                >
                  <p class="tw-text-sm" v-html="highlightText(message.body)"></p>
                </div>
              </div>
              <p
                class="tw-text-xs tw-text-gray-400"
                :class="{
                  'tw-text-right': message.sender.id === currentUser.id,
                }"
              >
                {{ formatDate(message.created_at) }}
              </p>
            </div>
          </div>

          <!-- Message input -->
          <div class="tw-relative">
            <v-textarea
              v-model="newMessage"
              no-resize
              outlined
              dense
              hide-details
              placeholder="Type a message....."
              class="tw-border-t tw-border-gray-200 tw-rounded-t-lg tw-p-4 custom-textarea"
              @keyup.enter="sendMessage"
            />
            <GoodButton class="tw-absolute tw-right-4 tw-bottom-4" @click="sendMessage">
              Send
            </GoodButton>
          </div>
        </div>
      </div>
    </div>

    <!-- New Message Modal -->
    <NewMessageModal
      :is-open="isNewMessageModalOpen"
      :messageable-people="messageablePeople"
      @update:is-open="isNewMessageModalOpen = $event"
      @message-sent="handleMessageSent"
    />
    </div>
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  onMounted,
  onUnmounted,
  ref,
  watch,
} from '@nuxtjs/composition-api'
import { useIntervalFn } from '@vueuse/core'

import BeastImage from '~/components/BeastImage.vue'
import GoodButton from '~/components/GoodButton.vue'
import { useApi, useCurrentUser } from '~/composables/useCommon'
import { MESSAGES_POLLING_INTERVAL } from '~/constants'
import { MessageV2, Booking } from '~/types'
import NewMessageModal from '~/components/NewMessageModal.vue'

export default defineComponent({
  name: 'MessagesPage',
  components: {
    BeastImage,
    GoodButton,
    NewMessageModal,
  },
  middleware: ['auth'],

  setup() {
    const messages = ref<MessageV2[]>([])
    const groupedMessages = ref<{
      [key: string]: { messages: MessageV2[]; latestTimestamp: number }
    }>({})
    const currentMessageable = ref<string | null>(null)
    const api = useApi()
    const activeTab = ref('inbox')
    const searchQuery = ref('')

    const fetchMessages = async () => {
      try {
        const { data } = await api.get('/messages-v2')
        messages.value = data
        groupMessages()
      } catch (error) {
        console.error('Error fetching messages:', error)
      }
    }

    const groupMessages = () => {
      groupedMessages.value = messages.value.reduce(
        (acc, message) => {
          const isArchived =
            activeTab.value === 'archived'
              ? (message.sender_id === currentUser.value.id && message.archived_by_sender) ||
                (message.receiver_id === currentUser.value.id && message.archived_by_receiver)
              : (message.sender_id === currentUser.value.id && !message.archived_by_sender) ||
                (message.receiver_id === currentUser.value.id && !message.archived_by_receiver)

          if (isArchived) {
            let groupKey: string

            if (!message.messageable_id) {
              groupKey = `subject_${message.subject}`
            } else if (message.messageable_id && message.messageable_type) {
              groupKey = `${message.messageable_type}_${message.messageable_id}`
            } else {
              groupKey = `message_${message.id}`
            }

            if (!acc[groupKey]) {
              acc[groupKey] = {
                messages: [],
                latestTimestamp: new Date(message.created_at).getTime(),
              }
            } else {
              // Update the latest timestamp if this message is newer
              acc[groupKey].latestTimestamp = Math.max(
                acc[groupKey].latestTimestamp,
                new Date(message.created_at).getTime()
              )
            }
            acc[groupKey].messages.push(message)
          }
          return acc
        },
        {} as {
          [key: string]: { messages: MessageV2[]; latestTimestamp: number }
        }
      )
    }

    const filteredGroupedMessages = computed(() => {
      if (!searchQuery.value) return groupedMessages.value

      const filtered: {
        [key: string]: { messages: MessageV2[]; latestTimestamp: number }
      } = {}
      Object.entries(groupedMessages.value).forEach(([messageableKey, messageGroup]) => {
        const filteredMessages = messageGroup.messages.filter(
          message =>
            message.subject.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
            message.body.toLowerCase().includes(searchQuery.value.toLowerCase())
        )
        if (filteredMessages.length > 0) {
          filtered[messageableKey] = {
            messages: filteredMessages,
            latestTimestamp: messageGroup.latestTimestamp,
          }
        }
      })
      return filtered
    })

    const sortedFilteredGroupedMessages = computed(() => {
      const filtered = filteredGroupedMessages.value

      return Object.entries(filtered)
        .sort(([, a], [, b]) => b.latestTimestamp - a.latestTimestamp)
        .reduce(
          (acc, [key, value]) => {
            acc[key] = value.messages
            return acc
          },
          {} as { [key: string]: MessageV2[] }
        )
    })

    const currentUser = useCurrentUser()

    const selectMessageable = async (messageableKey: string | number) => {
      const messageKey = String(messageableKey);
      currentMessageable.value = messageKey;

      // Mark messages as read
      if (groupedMessages.value[messageKey]) {
        const unreadMessages = groupedMessages.value[messageKey].messages.filter(
          m => !m.viewed && m.receiver_id === currentUser.value.id
        )

        if (unreadMessages.length > 0) {
          try {
            await api.patch('/messages-v2/mark-as-read', {
              message_ids: unreadMessages.map(m => m.id),
            })
            // Update local state
            groupedMessages.value[messageKey].messages = groupedMessages.value[
              messageKey
            ].messages.map(m => {
              if (unreadMessages.some(unread => unread.id === m.id)) {
                return { ...m, viewed: true }
              }
              return m
            })
          } catch (error) {
            console.error('Error marking messages as read:', error)
          }
        }
      }

      if (window.innerWidth < 768) {
        showInbox.value = false
      }
    }

    const formatDate = (dateString: string) => {
      return new Date(dateString).toLocaleDateString()
    }

    const setActiveTab = (tab: 'inbox' | 'archived') => {
      activeTab.value = tab
      groupMessages()
    }

    const highlightText = (text: string) => {
      if (!searchQuery.value) return text
      const regex = new RegExp(`(${searchQuery.value})`, 'gi')
      return text.replace(regex, '<mark class="tw-bg-yellow-200">$1</mark>')
    }

    watch(searchQuery, () => {
      if (currentMessageable.value && !filteredGroupedMessages.value[currentMessageable.value]) {
        currentMessageable.value = null
      }
    })

    const newMessage = ref('')

    const currentMessageableInfo = computed(() => {
      if (!currentMessageable.value || !groupedMessages.value[currentMessageable.value]) {
        return { title: '', subtitle: '', link: '', description: '', url: '' }
      }

      const messages = groupedMessages.value[currentMessageable.value].messages
      const firstMessage = messages[0]

      if (firstMessage.messageable_id) {
        const booking = firstMessage.messageable as Booking
        const isHost = booking.toHome.user.id === currentUser.value.id
        const isCompleted = booking.status === 'completed'

        return {
          title: `Friends & Family Booking`,
          subtitle: `${booking.toHome.title}`,
          description: `${booking.user.first_name}, ${formatDate(booking.start_at)} - ${formatDate(booking.end_at)}`,
          homeLink: `/${booking.toHome.id}`,
          bookingLink: `/bookings/${booking.id}`,
          showBookingLink: !isHost || (isHost && isCompleted),
          senderAvatar: booking.user.avatar,
          receiverAvatar: booking.toHome.user.avatar,
        }
      } else {
        // Handle non-booking messages or messages without messageable
        const sender =
          firstMessage.sender.id === currentUser.value.id
            ? firstMessage.receiver
            : firstMessage.sender
        return {
          title: `Conversation with ${sender.first_name}`,
          subtitle: firstMessage.subject,
          description: `Started on ${formatDate(firstMessage.created_at)}`,
          homeLink: '',
          bookingLink: '',
          showBookingLink: false,
          senderAvatar: firstMessage.sender.avatar,
          receiverAvatar: firstMessage.receiver.avatar,
        }
      }
    })

    const sendMessage = async () => {
      if (!newMessage.value.trim() || !currentMessageable.value) return

      try {
        const currentGroup = groupedMessages.value[currentMessageable.value].messages[0]
        await api.post('/messages-v2', {
          messageable_id: currentGroup.messageable_id,
          messageable_type: currentGroup.messageable_type,
          receiver_id:
            currentGroup.sender.id === currentUser.value.id
              ? currentGroup.receiver.id
              : currentGroup.sender.id,
          body: newMessage.value,
        })

        // Reload all messages from the API
        await fetchMessages()

        newMessage.value = ''
      } catch (error) {
        console.error('Error sending message:', error)
      }
    }

    const selectFirstMessageable = () => {
      const firstMessageableKey = Object.keys(sortedFilteredGroupedMessages.value)[0]
      if (firstMessageableKey) {
        currentMessageable.value = firstMessageableKey
      } else {
        currentMessageable.value = null
      }
    }

    // Watch for changes in sortedFilteredGroupedMessages
    watch(
      sortedFilteredGroupedMessages,
      () => {
        if (
          !currentMessageable.value ||
          !sortedFilteredGroupedMessages.value[currentMessageable.value]
        ) {
          selectFirstMessageable()
        }
      },
      { immediate: true }
    )

    // Watch for changes in the activeTab
    watch(activeTab, () => {
      selectFirstMessageable()
    })

    const { pause, resume } = useIntervalFn(fetchMessages, MESSAGES_POLLING_INTERVAL)

    onMounted(() => {
      fetchMessages().then(() => {
        selectFirstMessageable()
      })
      resume()
    })

    onUnmounted(() => {
      pause()
    })

    const getLastMessage = (messages: MessageV2[]) => {
      return messages[messages.length - 1]
    }

    const isUnread = (messages: MessageV2[]) => {
      return messages.some(
        message => message.receiver_id === currentUser.value.id && !message.viewed
      )
    }

    const isNewMessageModalOpen = ref(false)
    const messageablePeople = ref<{ id: number; name: string }[]>([])

    const openNewMessageModal = async () => {
      try {
        // Fetch messageable people first
        const { data } = await api.get('/messages-v2/messageable-people')
        messageablePeople.value = data.map((person: any) => ({
          id: person.id,
          name: `${person.first_name} ${person.last_name}`,
        }))

        // Then open the modal
        isNewMessageModalOpen.value = true
      } catch (error) {
        console.error('Error fetching messageable people:', error)
      }
    }

    const handleMessageSent = async () => {
      // Fetch messages first to update the list
      await fetchMessages()
      // Then close the modal
      isNewMessageModalOpen.value = false
    }

    const archiveMessageGroup = async (messageableKey: string | number) => {
      const messageKey = String(messageableKey);
      try {
        const messageIds = groupedMessages.value[messageKey].messages.map(message => message.id)
        await api.post('/messages-v2/archive', { message_ids: messageIds })

        // Update local state
        groupedMessages.value[messageKey].messages.forEach(message => {
          if (message.sender_id === currentUser.value.id) {
            message.archived_by_sender = true
          } else {
            message.archived_by_receiver = true
          }
        })

        // Remove the archived group from the current view if we're in the inbox
        if (activeTab.value === 'inbox') {
          delete groupedMessages.value[messageKey]
        }

        // If the archived group was selected, select the first available group
        if (currentMessageable.value === messageKey) {
          selectFirstMessageable()
        }
      } catch (error) {
        console.error('Error archiving message group:', error)
      }
    }

    const unarchiveMessageGroup = async (messageableKey: string | number) => {
      const messageKey = String(messageableKey);
      try {
        const messageIds = groupedMessages.value[messageKey].messages.map(message => message.id)
        await api.post('/messages-v2/unarchive', { message_ids: messageIds })

        // Update local state
        groupedMessages.value[messageKey].messages.forEach(message => {
          if (message.sender_id === currentUser.value.id) {
            message.archived_by_sender = false
          } else {
            message.archived_by_receiver = false
          }
        })

        // Remove the unarchived group from the current view if we're in the archived tab
        if (activeTab.value === 'archived') {
          delete groupedMessages.value[messageKey]
        }

        // If the unarchived group was selected, select the first available group
        if (currentMessageable.value === messageKey) {
          selectFirstMessageable()
        }
      } catch (error) {
        console.error('Error unarchiving message group:', error)
      }
    }

    const showInbox = ref(false)

    const toggleInbox = () => {
      showInbox.value = !showInbox.value
    }

    return {
      sortedFilteredGroupedMessages,
      currentMessageable,
      selectMessageable,
      formatDate,
      activeTab,
      setActiveTab,
      searchQuery,
      highlightText,
      newMessage,
      sendMessage,
      currentUser,
      currentMessageableInfo,
      selectFirstMessageable,
      getLastMessage,
      isUnread,
      isNewMessageModalOpen,
      messageablePeople,
      openNewMessageModal,
      handleMessageSent,
      archiveMessageGroup,
      unarchiveMessageGroup,
      showInbox,
      toggleInbox,
    }
  },
})
</script>

<style scoped>
.custom-textarea.v-text-field--outlined >>> fieldset {
  border: none;
}

@media (max-width: 768px) {
  .custom-textarea.v-text-field--outlined >>> .v-input__control {
    min-height: 100px;
  }
}

/* Fix for New Message button positioning */
.messages-page {
  width: 100%;
  position: relative;
}

.messages-container {
  width: 100%;
  box-sizing: border-box;
}

.header-container {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  position: relative;
}

.title-container {
  flex-grow: 1;
}

.mobile-actions {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

/* Mobile New Message button styling */
.mobile-new-message-btn {
  white-space: nowrap;
  transform: scale(0.85);
  transform-origin: right center;
  margin-right: -8px;
}

/* Fixed position button styling for desktop */
.fixed-new-message-btn {
  position: fixed;
  top: 118px; /* Adjust based on your header height */
  right: 20px;
  z-index: 10;
}

/* Mobile-specific header layout */
@media (max-width: 767px) {
  .header-container {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }

  .mobile-actions {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    flex-shrink: 0;
  }

  .mobile-new-message-btn {
    font-size: 0.75rem !important;
    padding: 0 12px !important;
    height: 32px !important;
    min-width: 0 !important;
  }
}
</style>
