<script lang="ts">
import { defineComponent } from '@nuxtjs/composition-api'

import FaqItemV2 from '~/components/FaqItemV2.vue'
import GoodButton from '~/components/GoodButton.vue'
export default defineComponent({
  name: 'Faqs',

  components: {
    FaqItemV2,
    GoodButton,
  },

  layout: 'static',

  props: {},

  data() {
    return {
      familyAndFriendsBooking: [
        {
          title: 'How do I make sure my booking type is private?',
          content:
            'All Twimo Users automatically have the Family & Friends Booking Type turned ON. You will have the ability to share a private, password-protected link with your friends & family.',
        },
        {
          title: 'How do I set my password for my booking page?',
          content:
            'When you Share your private link for the first time, Twi<PERSON> will prompt you to create a password.',
        },
        {
          title: 'Can I share my private booking page with multiple people? ',
          content: 'Yes! You can share your private booking page with as many people as you want!',
        },
        {
          title: 'Can I adjust my pricing based on who is booking my home?',
          content:
            'You have the ability to adjust pricing each time you share your home, if you want to charge certain guests, and not others, just charge a cleaning, you have the ability to choose!',
        },
      ],
      publicBooking: [
        {
          title: 'Is there guest verification?',
          content:
            "Yes, we've integrated with <a href='https://superhog.com/' target='_blank'>Superhog</a>, now known as <a href='https://truvi.com/' target='_blank'>Truvi</a>. This platform is automatically integrated into our public booking system. Anytime a guest request to book your home, they will go through a guest screening. Once the screening is conducted Superhog will notify you regarding their approval recommendation. Superhog is also inclusive of a damage deposit and protection services",
        },
        {
          title: 'Can I sync my airbnb listing? ',
          content:
            'Yes, when adding your home you will have the ability to sync your airbnb listing. Our system will pull your data to build your home listing. You can also sync your airbnb calendar to Twimo, and vice a versa.',
        },
      ],
      homeSwap: [
        {
          title: 'Are Home Swaps free with a Twimo Membership ',
          content:
            'Yes! If you have an active Twimo Membership you have the ability to swap amongst other Twimo Host participating in our home swap network. ',
        },
        {
          title: 'Can I decline Home Swaps? ',
          content: 'Yes, if another Host request to swap approval is always required.',
        },
        {
          title: 'Can I Swap with another Host, but swap at different times?',
          content:
            'Yes, you can request to Swap with another Host for specific dates. If those dates don’t work for both Hosts you can coordinate to Swap at different times.',
        },
        {
          title:
            'Is there Host Verification and Home Verification to make sure the house really exists? ',
          content:
            "Yes, every Twimo Host will be verified via <a href='https://stripe.com/identity' target='_blank'>Stripe Identity.</a>",
        },
      ],
      vendorManagement: [
        {
          title: 'How do I add my vendors on Twimo?',
          content:
            'Once you sign up for Twimo, you can add your Vendors within MyCrew - your vendor management platform. You will simply add your vendor via email, and they will receive an invite to create a Twimo account to collect payment, and provide progress updates.',
        },
        {
          title: 'Can I pay my vendors directly on Twimo? ',
          content:
            'Yes, once a vendor accepts their invite to Twimo they create their profile. While creating their profile they can sync their Venmo account, allowing you to directly pay your vendors online.',
        },
      ],
      pricing: [
        {
          title: 'How much is a Twimo Membership? ',
          content:
            'A Twimo Membership for a Host is paid monthly or annually. Monthly cost is $14/month, annual cost is $144/year.',
        },
        {
          title: 'To rent a home on Twimo do I need a membership? ',
          content:
            'You will not need a paid membership to rent a home on Twimo. You can explore and rent homes with a free account. When you do book a home, you will be asked to create a free Twimo account. This will allow you to pay directly, communicate with your Host, and stay on top of your bookings.',
        },
        {
          title: 'Is there a fee for private listings? ',
          content:
            'No, there are no additional fees charged to the guest or host when listing privately. Zero service fees.',
        },
        {
          title: 'Is there a fee for public listings? ',
          content: 'No, there are zero service fees for public listings.',
        },
      ],
      homeUtilizationTraking: [
        {
          title: 'What is Home Utilization? ',
          content:
            'Home Utilization is a way for Twimo to help you keep track of your goals! Whether you want to spend more time at your home, cover HOA fees, generate supplemental income, reach a certain amount of rental days, or track expenses. Twimo helps you set your goals and reach them.',
        },
      ],
      generalQuestions: [
        { title: 'What does Twimo mean?', content: 'The World is My Oyster' },
        {
          title: 'How do I contact customer support?',
          content:
            "If you need to contact customer support, please reach us at <a href='mailto:<EMAIL>'><EMAIL></a> or visit our Help Page for demonstrational videos.",
        },
      ],
      formData: {
        name: '',
        email: '',
        message: '',
      },
      formError: {
        name: '',
        email: '',
        message: '',
      },
      formMessage: {},

      email: '',
      fname: '',
      message: '',
      errors: [],
      pending: false,
      invalid: false,
      valid: true,
      success: false,
    }
  },

  head() {
      return {
        title: "FAQs | Twimo Private Bookings, Swaps & Vacation Rentals",
        meta: [
          {
            hid: "description",
            name: "description",
            content: "Find answers to your questions about private bookings, home swaps, vacation rentals, vendor coordination, and more on Twimo."
          }
        ]
      };
  },

  computed: {
    form(): any {
      return this.$refs.contactForm
    },
  },

  mounted() {
    this.animateOnScroll()

    const nav = this.$refs.nav
  },

  methods: {
    validate_email() {},

    validate_name() {},

    validate_message() {},

    async handleFormSubmit() {
      const isValid = this.form.validate()
      if (!isValid) return

      if (!this.valid) return

      this.errors = []

      try {
        const { email, fname, message } = this

        const { data } = await this.$axios.post('/forms/contactSubmission', {
          email: email.trim(),
          fname: fname.trim(),
          message: message.trim(),
        })

        if (data.success == true) {
          this.form.reset()
          this.success = true
        }
      } catch (error) {
      } finally {
      }
    },

    // Function to observe elements and trigger animation
    animateOnScroll() {
      const elements = document.querySelectorAll('.animate')

      const observer = new IntersectionObserver(
        (entries, observer) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              entry.target.classList.add('visible')
              observer.unobserve(entry.target) // Stop observing after it's visible
            }
          })
        },
        { threshold: 0.5 }
      )

      elements.forEach(element => {
        observer.observe(element)
      })
    },

    scrollToSection(container) {
      const section = this.$refs?.[container]
      const offset = 150
      const sectionTop = (section as HTMLElement)?.offsetTop
      const targetPosition = sectionTop - offset

      // Scroll to the position with smooth behavior
      window.scrollTo({
        top: targetPosition,
        behavior: 'smooth',
      })
    },
  },
})
</script>

<template>
  <main class="faqs-page font-figtree tw-bg-white tw-pb-[0rem]">
    <section
      class="tw-bg-[url('@/assets/faqs-banner.jpg')] tw-bg-center tw-bg-no-repeat tw-bg-cover tw-relative faq-banner"
    >
      <div
        class="tw-min-h-[500px] md:tw-min-h-[500px] tw-mx-auto tw-pt-[4rem] tw-pb-[4rem] animate tw-flex tw-flex-col tw-justify-center tw-items-center px-5"
      >
        <h2
          class="tw-text-white tw-font-medium tw-text-xl md:text-2xl tw-mb-1 tw-text-center"
          style="text-shadow: 0 2px 3px #0000008f"
        >
          Answers to our most common questions
        </h2>
        <h1
          class="tw-text-white tw-font-bold tw-text-3xl md:tw-text-4xl tw-mb-4 tw-text-center"
          style="text-shadow: 0 2px 3px #0000008f"
        >
          Frequently Asked Questions
        </h1>

        <AnchorLinkStyleOne
          text="Sign up now"
          link="/signup"
          font_size="tw-text-base tw-inline md:tw-hidden"
        />
      </div>
      <div
        ref="nav"
        class="animate tw-flex tw-flex-col tw-gap-4 tw-justify-end faq-nav-container tw-left-0 tw-right-0 tw-absolute tw-bottom-0 tw-bg-[#F5F5F5CC] tw-transition-all tw-duration-300"
      >
        <div
          class="tw-p-4 tw-flex tw-gap-3 tw-items-center tw-justify-evenly [a]:tw-text-[#2C005B] tw-font-medium tw-flex-wrap faq-nav"
        >
          <a
            href="#family-friends-booking"
            @click.prevent="scrollToSection('family-friends-booking')"
            >Family & Friends</a
          >
          <a href="#public-booking" @click.prevent="scrollToSection('public-booking')"
            >Public Booking</a
          >
          <a href="#home-swaps" @click.prevent="scrollToSection('home-swaps')">Home Swaps</a>
          <a href="#vendor-management" @click.prevent="scrollToSection('vendor-management')"
            >Vendor Management</a
          >
          <a href="#pricing" @click.prevent="scrollToSection('pricing')">Pricing</a>
          <a href="#general-questions" @click.prevent="scrollToSection('general-questions')"
            >General</a
          >
        </div>
      </div>
    </section>

    <section>
      <div class="tw-container tw-px-5 tw-mx-auto tw-pt-24">
        <div class="tw-max-w-[900px]">
          <a id="family-friends-booking" href=""></a>
          <h2
            ref="family-friends-booking"
            class="tw-text-[#2C005B] animate tw-font-medium tw-text-xl md:tw-text-2xl tw-mb-3"
          >
            Family & Friends Booking
          </h2>
          <FaqItemV2 :items="familyAndFriendsBooking" :show-symbol="true" />
          <a id="public-booking" href=""></a>
          <h2
            ref="public-booking"
            class="tw-text-[#2C005B] animate tw-mt-14 tw-font-medium tw-text-xl md:tw-text-2xl tw-mb-3"
          >
            Public Booking
          </h2>
          <FaqItemV2 :items="publicBooking" :show-symbol="true" />

          <a id="home-swaps" href=""></a>
          <h2
            ref="home-swaps"
            class="tw-text-[#2C005B] animate tw-mt-14 tw-font-medium tw-text-xl md:tw-text-2xl tw-mb-3"
          >
            Home Swaps
          </h2>
          <FaqItemV2 :items="homeSwap" :show-symbol="true" />
          <a id="vendor-management" href=""></a>
          <h2
            ref="vendor-management"
            class="tw-text-[#2C005B] animate tw-mt-14 tw-font-medium tw-text-xl md:tw-text-2xl tw-mb-3"
          >
            Vendor Management
          </h2>
          <FaqItemV2 :items="vendorManagement" :show-symbol="true" />
          <a id="pricing" href=""></a>
          <h2
            ref="pricing"
            class="tw-text-[#2C005B] animate tw-mt-14 tw-font-medium tw-text-xl md:tw-text-2xl tw-mb-3"
          >
            Pricing
          </h2>
          <FaqItemV2 :items="pricing" :show-symbol="true" />

          <!-- <a href="" id="home-utilization-tracking"></a>
					<h2 ref="home-utilization-tracking"
						class="tw-text-[#2C005B] animate tw-mt-14 tw-font-medium tw-text-xl md:tw-text-2xl tw-mb-3"
					>
						Home Utilization Tracking
					</h2>
					<FaqItemV2 :items="homeUtilizationTraking" /> -->

          <a id="general-questions" href=""></a>
          <h2
            ref="general-questions"
            class="tw-text-[#2C005B] animate tw-mt-14 tw-font-medium tw-text-xl md:tw-text-2xl tw-mb-3"
          >
            General Questions
          </h2>
          <FaqItemV2 :items="generalQuestions" :show-symbol="true" />
        </div>
      </div>
      <a id="contact" href=""></a>
    </section>

    <section class="tw-bg-[#F5F5F566] tw-py-16 tw-mt-40 tw-rounded-t-[50px]">
      <div class="tw-container tw-px-5 tw-mx-auto">
        <h3
          class="animate tw-text-[#2C005B] tw-text-2xl md:tw-text-3xl tw-text-center tw-mb-3 tw-font-medium"
        >
          Get in Touch
        </h3>
        <p
          class="animate tw-text-center tw-text-[#2C005B] tw-font-medium tw-mb-12 tw-text-base tw-leading-7"
        >
          Say hi! Whether you have questions, or are interested in learning more about Twimo
          <br class="tw-hidden md:tw-block" />contact us by filling out the information below
        </p>

        <v-form
          ref="contactForm"
          v-model="valid"
          lazy-validation
          class="tw-flex tw-flex-col tw-gap-8 tw-max-w-[500px] tw-ml-auto tw-mr-auto animate"
        >
          <v-text-field
            id="fname"
            v-model="fname"
            :rules="[v => !!v || 'Full name is required']"
            type="text"
            hide-details
            outlined
            label="Full Name"
            class="tw-bg-white tw-rounded-xl tw-shadow-custom"
            @focus="invalid = false"
            @keyup.enter.prevent="handleFormSubmit"
          />

          <v-text-field
            id="email"
            v-model="email"
            :rules="[v => !!v || 'Email is required']"
            type="email"
            hide-details
            outlined
            label="Email Address"
            class="tw-bg-white tw-rounded-xl tw-shadow-custom"
            @focus="invalid = false"
            @keyup.enter.prevent="handleFormSubmit"
          />

          <v-textarea
            id="message"
            v-model="message"
            no-resize
            outlined
            hide-details
            :rules="[v => !!v || 'Message is required']"
            label="Message"
            class="tw-bg-white tw-rounded-xl tw-shadow-custom"
            @focus="invalid = false"
            @keyup.enter.prevent="handleFormSubmit"
          ></v-textarea>

          <span
            v-for="(error, index) in errors"
            :key="index"
            class="tw-text-red-500 tw-text-center tw-mx-auto tw-font-semibold"
            >{{ error }}</span
          >

          <span v-if="success" class="tw-text-green-500 tw-text-center tw-mx-auto tw-font-semibold"
            >Thank you for contacting us. One of our support agent will contact you soon.</span
          >

          <div class="tw-flex tw-flex-col tw-justify-center tw-items-center tw-gap-4">
            <good-button
              class="tw-text-base sm:tw-w-1/2 tw-w-full"
              :disabled="!valid"
              @click.prevent="handleFormSubmit"
            >
              Send Message
            </good-button>
          </div>
        </v-form>
      </div>
    </section>
  </main>
</template>
<style scoped>
.animate {
  opacity: 0;
  transform: translateY(50px);
  transition:
    opacity 0.6s ease,
    transform 0.6s ease;
}

.animate.visible {
  opacity: 1;
  transform: translateY(0);
}
.faq-nav a {
  color: #2c005b;
}

/* .faqs-page {
		background: rgb(255, 255, 255);
		background: linear-gradient(
			171deg,
			rgba(255, 255, 255, 0.5452774859943977) 0%,
			rgba(77, 13, 127, 0.15) 8%,
			rgba(255, 255, 255, 1) 25%
		);
	} */
</style>
