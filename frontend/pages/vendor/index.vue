<script lang="ts">
// @ts-nocheck

import { defineComponent, useStore, computed, useRouter } from '@nuxtjs/composition-api'

import { MyCrew } from '~/types'
import MyCrewInfoThumbItem from '~/components/MyCrewInfoThumbItem.vue'
import MyCrewDashboardTitle from '~/components/MyCrewDashboardTitle.vue'
import MyCrewPendingItem from '~/components/MyCrewPendingItem.vue'
import screenCheckerMixins from '~/mixins/screenChecker.mixins'
import toastable from '~/mixins/toastable'
import GoodDataTable from '~/components/GoodDataTable.vue'
import GoodCard from '~/components/GoodCard.vue'
import GoodOkButton from '~/components/GoodOkButton.vue'
import GoodCancelButton from '~/components/GoodCancelButton.vue'
import GoodCardTitle from '~/components/GoodCardTitle.vue'
import ImagesUploader from '~/components/ImagesUploader.vue'
import GoodButton from '~/components/GoodButton.vue'

export default defineComponent({
  components: {
    Good<PERSON>ardTitle,
    GoodCancelButton,
    GoodOkButton,
    GoodCard,
    GoodDataTable,
    MyCrewInfoThumbItem,
    MyCrewDashboardTitle,
    MyCrewPendingItem,
    ImagesUploader,
    GoodButton,
  },

  mixins: [screenCheckerMixins, toastable],

  middleware: 'auth',

  setup() {
    const store = useStore()
    const user = computed(() => store.getters['auth/getuser'])

    const router = useRouter()
    console.log(user.value.role_id, user.value.phone_number)
    if (user.value.role_id == 4 && user.value.phone_number === null  || (user.value.role_id == 4 && (user.value.venmo_username == null || user.value.venmo_username == ''))) {
      router.push({ path: '/profile' })
    }

  },

  data() {
    return {
      crews: [] as MyCrew[],

      dashboardData: [],
      pendingJobs: [],
      userInfo: [],
    }
  },

  computed: {},

  async mounted() {
    // Get Pending Jobs
    try {
      const { data } = await this.$axios.get('mycrew/get-pending-jobs')
      this.pendingJobs = data.jobs;
    } catch (e) {
      console.log(e)
    }

    // Get Dashboard Data
    try {
      const { data } = await this.$axios.get('mycrew/get-dashboard-info')
      this.dashboardData = data.dashboard_data
    } catch (e) {
      console.log(e)
    }
  },

  methods: {
    goToMyCrewJobs() {
      this.$router.push({ path: '/vendor/my-crew-jobs' })
    },
  },
})
</script>

<template>
  <div class="container-fluid">
    <v-row class="mycrew-dashboard-header">
      <v-col>
        <div
          class="md:tw-px-[10px] tw-px-4 tw-bg-center tw-bg-no-repeat tw-bg-cover tw-overflow-hidden"
        >
          <div
            class="tw-mt-[10px] tw-text-[1.4rem] md:tw-text-[2rem] tw-font-semibold tw-text-left tw-text-[#672093]"
          >
            MyCrew
          </div>

          <div
            class="tw-mt-[30px] tw-text-[1rem] md:tw-text-[1.5rem] tw-font-normal tw-text-left tw-text-[#5E5E5E]"
          >
            MyCrew Vendor Dashboard
          </div>

          <div
            class="tw-mt-[5px] tw-text-[1.1rem] md:tw-text-[1.2rem] tw-font-light tw-text-left tw-text-[#6C6C6C] md:tw-max-w-[90%]"
          >
            This month's snapshot of MyCrew Updates
          </div>

          <div
            class="tw-flex tw-mt-[30px] tw-mb-[50px] md:tw-flex-nowrap tw-flex-nowrap tw-gap-[1rem] md:tw-gap-[2rem] sm:tw-gap-[1rem] tw-overflow-x-auto tw-p-[10px] md:tw-p-[10px]"
          >
            <MyCrewInfoThumbItem
              title="Pending"
              :value="dashboardData.pending"
              :description="`Response Needed`"
            />

            <MyCrewInfoThumbItem
              title="Calendar"
              :value="dashboardData.calendar"
              :description="`MyCrew Jobs`"
            />

            <MyCrewInfoThumbItem
              title="Messages"
              :value="dashboardData.messages"
              :description="`Unread`"
            />

            <MyCrewInfoThumbItem title="Jobs" :value="dashboardData.jobs" :description="`Active`" />
          </div>
        </div>
      </v-col>
    </v-row>

    <div class="tw-pt-[1rem]">
      <v-col cols="12" class="tw-mt-8 md:tw-flex tw-justify-between">
        <div class="tw-text-3xl tw-font-semibold tw-text-left tw-tracking-wide">
          <span class="tw-text-zinc-500">MyCrew Alerts</span>
        </div>
      </v-col>
    </div>

    <v-row>
      <v-col>
        <div v-if="pendingJobs.length == 0">
          <div class="no-vendor-alerts">
            No Pending Request.....however check the Status of Your current Jobs with your MyCrew
            Jobs
          </div>
          <div class="tw-text-right">
            <v-btn
              class="tw-normal-case tw-rounded-xl tw-position-absolute tw-font-semibold tw-text-sm tw-pt-2 tw-pb-2 primary tw-mr-[10px] tw-mt-5"
              @click="goToMyCrewJobs"
            >
              MyCrew Jobs
            </v-btn>
          </div>
        </div>

        <div v-if="pendingJobs.length != 0" class="tw-pl-[15px]">
          <h3 class="tw-text-2xl tw-text-zinc-500">
            You have a Pending Job Request Waiting for
            <span class="tw-text-[#672093]">Approval!</span>
          </h3>

          <div class="tw-pt-4 tw-pb-4 tw-flex tw-flex-wrap tw-w-full">
            <MyCrewPendingItem v-for="(item, index) in pendingJobs" :key="index" :item="item" />
          </div>

          <div class="tw-text-left tw-pb-[5rem]">
            <good-button
              class="tw-normal-case tw-rounded-xl tw-position-absolute tw-font-semibold tw-text-sm tw-pt-2 tw-pb-2 primary tw-mr-[10px] tw-mt-5"
              @click="goToMyCrewJobs"
            >
              Go to MyCrew Jobs for Approval
            </good-button>
          </div>
        </div>

        <!--<MyCrewJobsList
						type = 'host'
						ref="jobsList"
					/>-->
      </v-col>
    </v-row>
  </div>
</template>

<style scoped>
#crew-card:focus::before {
  opacity: 0 !important;
}

#delete-crew-icon:focus::after {
  opacity: 0 !important;
}

.sort_mycrew {
  border: 1px solid rgba(133, 133, 133, 0.2);
  box-shadow: 0px 2px 3px rgba(0, 0, 0, 0.25);
  border-radius: 10px;
}

.sort_mycrew > div:first-child {
  border-right: 1px solid rgba(133, 133, 133, 0.2);
  align-items: center;
  display: flex;
}

.custom-input {
  border-radius: 10px !important;
}

.custom-input >>> fieldset {
  border-color: #8585851a !important;
}

.error--text.custom-input >>> fieldset {
  border-color: #ff5252 !important;
}

.custom-input >>> .v-input__slot {
  min-height: 45px !important;
}

.custom-input >>> .v-label {
  top: 13px !important;
}

.custom-input >>> .v-label--active {
  top: 17px !important;
  color: #7c0cb1;
  font-weight: 600;
}

.custom-input >>> .v-input__append-inner {
  margin-top: 10px;
}

.custom-input >>> .v-select__selections {
  padding: 0px !important;
}

.no-vendor-alerts {
  background: linear-gradient(90deg, #ffffff 21%, rgba(245, 245, 245, 0.5) 100%);
  border: 1px solid rgba(133, 133, 133, 0.1);
  box-shadow:
    0px 2px 3px rgba(0, 0, 0, 0.25),
    0px 3px 3px rgba(0, 0, 0, 0.15);
  border-radius: 18px;
  padding: 3rem 2rem;
  margin: 12px;
}

>>> .no-outline .v-input__slot:before {
  border: none !important;
  display: none !important;
}

@media screen and (max-width: 640px) {
  .sort_mycrew > div:first-child {
    border-right: 0px;
    border-bottom: 1px solid rgba(133, 133, 133, 0.2);
    align-items: center;
    display: flex;
  }
  #drop-region {
    height: 300px;
  }
}
</style>
