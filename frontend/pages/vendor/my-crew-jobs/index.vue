<script lang="ts">
// @ts-nocheck

import { defineComponent } from '@nuxtjs/composition-api'

import { MyCrew } from '~/types'
import MyCrewInfoThumbItem from '~/components/MyCrewInfoThumbItem.vue'
import MyCrewDashboardTitle from '~/components/MyCrewDashboardTitle.vue'
import MyCrewJobsList from '~/components/MyCrewJobsList.vue'
import screenCheckerMixins from '~/mixins/screenChecker.mixins'
import toastable from '~/mixins/toastable'
import GoodDataTable from '~/components/GoodDataTable.vue'
import GoodCard from '~/components/GoodCard.vue'
import GoodOkButton from '~/components/GoodOkButton.vue'
import GoodCancelButton from '~/components/GoodCancelButton.vue'
import GoodCardTitle from '~/components/GoodCardTitle.vue'
import ImagesUploader from '~/components/ImagesUploader.vue'

export default defineComponent({
  components: {
    GoodCardTitle,
    GoodCancelButton,
    GoodOkButton,
    GoodCard,
    GoodDataTable,
    MyCrewInfoThumbItem,
    MyCrewDashboardTitle,
    MyCrewJobsList,
    ImagesUploader,
  },

  mixins: [screenCheckerMixins, toastable],

  middleware: 'auth',

  async asyncData({ $axios }) {
    // const { data } = await $axios.get('/service-providers')

    // return { crews: data }
  },

  data() {
    return {
      // crews: [] as MyCrew[],
      dialog: false,
      deleteDialog: false,
      // selectedCrew: {} as MyCrew,
      valid: true,
      tab: 0,
      sortOptions: [
        { text: 'Date', value: 'date' },
        { text: 'Priority', value: 'priority' },
        { text: 'Home', value: 'home' },
        { text: 'Category', value: 'category' },
      ],
      sortOption: 'date',
      dashboardData: [],

      categories: [],
      formDueDateMenu: false,
      priorityList: [
        { text: `Low`, value: 'low' },
        { text: `Medium`, value: 'medium' },
        { text: `High`, value: 'high' },
        { text: `None`, value: 'none' },
      ],
      paymentTypeList: [
        { text: `Venmo`, value: 'venmo' },
        { text: `Check`, value: 'check' },
        { text: `Cash`, value: 'cash' },
      ],
      homesList: [],
      vendorsList: [],
      jobForm: {
        jobTitle: null,
        category: null,
        dueDate: null,
        vendor: null,
        dueDate: null,
        priorityLevel: null,
        home: null,
        details: null,
        paymentType: `venmo`,
        paymentAmount: null,
        vendorEmail: null,
      },
    }
  },

  computed: {
    form(): any {
      return this.$refs.form
    },
    formattedDueDate() {
      return this.formatDate(this.jobForm.dueDate)
    },
  },

  async mounted() {},

  methods: {
    formatDate(date) {
      if (!date) return ''
      const options = {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        timeZone: 'UTC',
      }
      return new Date(date).toLocaleDateString('en-US', options)
    },
  },
})
</script>

<template>
  <div class="container-fluid">
    <div class="tw-pt-[0.2rem]">
      <!-- PAGE TITLE-->
      <MyCrewDashboardTitle> </MyCrewDashboardTitle>
    </div>

    <v-row>
      <v-col>
        <div class="sort_mycrew md:tw-flex tw-text-[#858585] tw-max-w-[700px] tw-mb-10 tw-ml-3">
          <div class="sort_label tw-w-100% md:tw-w-[75%] tw-text-[1.1rem] tw-p-[10px]">
            Jobs are sorted by Date, resort by selecting additional filters
          </div>
          <div class="tw-w-[100%] md:tw-w-[25%] tw-text-right tw-px-5 tw-pt-[3px]">
            <v-select
              v-model="sortOption"
              :items="sortOptions"
              hide-details
              class="tw-p-0 no-outline"
              @change="handleSortOptions"
            ></v-select>
          </div>
        </div>
      </v-col>
    </v-row>

    <MyCrewJobsList
      ref="jobsList"
      type="host"
      :filter="sortOption"
      :mark-complete-dialog="markCompleteDialog"
    />
  </div>
</template>

<style scoped>
#crew-card:focus::before {
  opacity: 0 !important;
}

#delete-crew-icon:focus::after {
  opacity: 0 !important;
}

.sort_mycrew {
  border: 1px solid rgba(133, 133, 133, 0.2);
  box-shadow: 0px 2px 3px rgba(0, 0, 0, 0.25);
  border-radius: 10px;
}

.sort_mycrew > div:first-child {
  border-right: 1px solid rgba(133, 133, 133, 0.2);
  align-items: center;
  display: flex;
}

.custom-input {
  border-radius: 10px !important;
}

.custom-input >>> fieldset {
  border-color: #8585851a !important;
}

.error--text.custom-input >>> fieldset {
  border-color: #ff5252 !important;
}

.custom-input >>> .v-input__slot {
  min-height: 45px !important;
}

.custom-input >>> .v-label {
  top: 13px !important;
}

.custom-input >>> .v-label--active {
  top: 17px !important;
  color: #7c0cb1;
  font-weight: 600;
}

.custom-input >>> .v-input__append-inner {
  margin-top: 10px;
}

.custom-input >>> .v-select__selections {
  padding: 0px !important;
}

>>> .no-outline .v-input__slot:before {
  border: none !important;
  display: none !important;
}

@media screen and (max-width: 640px) {
  .sort_mycrew > div:first-child {
    border-right: 0px;
    border-bottom: 1px solid rgba(133, 133, 133, 0.2);
    align-items: center;
    display: flex;
  }
  #drop-region {
    height: 300px;
  }
}
</style>
