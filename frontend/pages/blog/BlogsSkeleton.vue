<script setup>
import { ref } from 'vue'
</script>
<template>
  <div class="tw-mx-auto tw-mb-10 tw-max-w-[960px] tw-space-y-8 tw-p-4">
    <!-- Recent Blog Skeleton -->
    <div class="tw-flex tw-animate-pulse tw-gap-4 tw-flex-col md:tw-flex-row">
      <div class="md:tw-w-3/5 tw-h-60 tw-bg-gray-300 tw-rounded-xl"></div>
      <div class="md:tw-w-2/5 tw-space-y-4">
        <div class="tw-h-6 tw-bg-gray-300 tw-rounded-md tw-w-3/4"></div>
        <div class="tw-h-4 tw-bg-gray-300 tw-rounded-md tw-w-full"></div>
        <div class="tw-h-4 tw-bg-gray-300 tw-rounded-md tw-w-5/6"></div>
        <div class="tw-h-4 tw-bg-gray-300 tw-rounded-md tw-w-2/3"></div>
        <div class="tw-h-10 tw-w-24 tw-bg-gray-300 tw-rounded-md"></div>
      </div>
    </div>

    <!-- All Blogs Skeleton (4 total) -->
    <div class="tw-grid tw-grid-cols-1 md:tw-grid-cols-2 tw-gap-6">
      <div v-for="n in 4" :key="n" class="tw-animate-pulse tw-space-y-4 tw-rounded-xl">
        <div class="tw-h-56 tw-bg-gray-300 tw-rounded-lg"></div>
        <div class="tw-h-5 tw-bg-gray-300 tw-rounded-md tw-w-3/4"></div>
        <div class="tw-h-4 tw-bg-gray-300 tw-rounded-md tw-w-full"></div>
        <div class="tw-h-4 tw-bg-gray-300 tw-rounded-md tw-w-5/6"></div>
        <div class="tw-h-10 tw-w-20 tw-bg-gray-300 tw-rounded-md"></div>
      </div>
    </div>
  </div>
</template>
