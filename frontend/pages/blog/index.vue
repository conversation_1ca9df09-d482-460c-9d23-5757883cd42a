<script lang="ts">
import { defineComponent } from '@nuxtjs/composition-api'

import AboutMember from '~/components/AboutMember.vue'

import BlogsSkeleton from './BlogsSkeleton.vue'

export default defineComponent({
  name: 'Blog',

  components: {
    AboutMember,
    BlogsSkeleton,
  },

  layout: 'static',

  props: {},

  data() {
    return {
      isLoading: true,
      recent_article: {
        slug: '',
        image: '',
        title: '',
        date: '',
        excerpt: '',
      } as any,
      articles: [] as any[],
    }
  },

  head() {
      return {
        title: "Twimo Blog | Travel Tips & Vacation Home Management",
        meta: [
          {
            hid: "description",
            name: "description",
            content: "Get insights on vacation home rental and management, home swapping and travel tips from the Twimo blog."
          }
        ]
      };
  },

  mounted() {
    this.animateOnScroll()
    this.fetchData()
  },

  methods: {
    // Function to observe elements and trigger animation
    animateOnScroll() {
      const elements = document.querySelectorAll('.animate')

      const observer = new IntersectionObserver(
        (entries, observer) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              entry.target.classList.add('visible')
              observer.unobserve(entry.target) // Stop observing after it's visible
            }
          })
        },
        { threshold: 0.5 }
      )

      elements.forEach(element => {
        observer.observe(element)
      })
    },

    async fetchData() {
      try {
        this.isLoading = true
        const { data } = await this.$axios.get('https://blog.twimo.com/wp-json/blog-api/v1/posts')
        this.recent_article = data[0]
        this.articles = [...data.slice(1)]
      } catch (error) {
        console.error(error)
      } finally {
        this.isLoading = false
      }
    },
  }

})
</script>
<template>
  <main class="help-page font-figtree tw-bg-[#FFF]">
    <!-- <section
			class="tw-bg-[url('@/assets/blog-bg.jpg')] tw-bg-cover tw-bg-center tw-bg-no-repeat"
		>
			<div
				class="tw-container animate tw-min-h-[320px] md:tw-min-h-[450px] tw-mx-auto tw-px-5 tw-py-5 tw-pt-[5rem] tw-flex tw-flex-col tw-gap-4 tw-justify-center"
			>
				<div>
					<h1
						class="tw-mt-10 tw-font-extrabold tw-text-white tw-text-center tw-text-3xl md:tw-text-5xl md:tw-leading-[70px] tw-relative tw-z-10"
					>
						The Twimo Times
					</h1>
					<p
						class="tw-text-base md:tw-text-2xl tw-text-center tw-text-white tw-leading-7"
					>
						Check out our latest blog posts to keep up
						<br class="tw-hidden md:tw-block" />to date on rental regulations,
						seasonal <br class="tw-hidden md:tw-block" />maintenance, and more
					</p>
				</div>
			</div>
		</section> -->

    <section class="tw-py-10">
      <div class="tw-containertw-mx-auto tw-px-5">
        <div class="tw-max-w-[940px] tw-ml-auto tw-mr-auto tw-mb-5 tw-mt-32">
          <h4 class="tw-text-[#2C005B] tw-text-3xl md:tw-text-4xl tw-font-extrabold">
            The Twimo Times
          </h4>
        </div>

        <span v-if="isLoading"> <BlogsSkeleton /> </span>
        <div
          v-else-if="recent_article"
          class="tw-flex tw-items-center tw-justify-between tw-flex-col md:tw-flex-row tw-gap-4 tw-container tw-mx-auto tw-mb-10 tw-max-w-[940px]"
        >
          <div class="md:tw-w-3/5">
            <NuxtLink :to="`/blog/${recent_article.slug}`">
              <img
                :src="recent_article.image"
                :alt="recent_article.title"
                class="tw-object-cover tw-aspect-square md:tw-aspect-[16/12] tw-rounded-2xl tw-w-[100%] tw-block md:tw-size-[100%]"
              />
            </NuxtLink>
          </div>
          <div class="md:tw-w-2/5 tw-text-[#2C005B]">
            <h4 class="tw-font-bold tw-text-base tw-mb-1">RECENT BLOG POSTS</h4>
            <h3 class="tw-font-medium tw-text-2xl md:tw-text-3xl tw-mb-1">
              <NuxtLink
                :to="`/blog/${recent_article.slug}`"
                class="tw-text-[#2C005B] tw-font-medium tw-text-2xl md:tw-text-3xl"
                :aria-label="recent_article.title"
              >
                {{ recent_article.title }}
              </NuxtLink>
            </h3>
            <p class="tw-font-light tw-text-sm tw-mb-2">
              {{ recent_article.date }}
            </p>
            <p class="tw-text-[#2C005B] tw-font-light mb-1 tw-leading-6">
              {{ recent_article.excerpt.slice(0, 100) + ' ...' }}
            </p>
            <NuxtLink
              :to="`/blog/${recent_article.slug}`"
              class="tw-text-[#2C005B] tw-font-bold tw-text-base"
              :aria-label="recent_article.title"
            >
              read more
            </NuxtLink>
          </div>
        </div>
        <div
          class="tw-max-w-[940px] tw-ml-auto tw-mr-auto tw-flex tw-gap-10 tw-flex-col md:tw-flex-row tw-items-top tw-justify-between tw-pt-2 md:tw-pt-5 tw-pb-[3rem] md:tw-pb-[9rem]"
        >
          <div
            v-for="(blog, i) in articles"
            :key="i"
            class="tw-pb-1 tw-relative tw-flex tw-flex-col tw-justify-between md:tw-w-1/2"
          >
            <div>
              <div>
                <NuxtLink :to="`/blog/${blog.slug}`" :aria-label="blog.title">
                  <img
                    :src="blog.image"
                    :alt="blog.title"
                    class="tw-object-cover tw-aspect-square tw-rounded-2xl tw-w-[100%] tw-block md:tw-size-[100%]"
                  />
                </NuxtLink>
              </div>
              <h2 class="tw-text-[#2C005B] tw-text-xl tw-mt-2 tw-mb-2 tw-font-medium">
                <NuxtLink
                  :to="`/blog/${blog.slug}`"
                  :aria-label="blog.title"
                  class="tw-text-[#2C005B]"
                >
                  {{ blog.title }}
                </NuxtLink>
              </h2>
            </div>
            <div>
              <p class="tw-text-[#2C005B] tw-font-light mb-1">
                {{ blog.excerpt.slice(0, 100) + ' ...' }}
              </p>
              <NuxtLink
                :to="`/blog/${blog.slug}`"
                class="tw-text-[#2C005B] tw-font-bold tw-text-base"
                :aria-label="blog.title"
              >
                read more
              </NuxtLink>
            </div>
          </div>
        </div>
      </div>
    </section>
  </main>
</template>

<style scoped>
.meet_people_container {
  position: relative;
}

.meet_people_container:before {
  background: #f2f2f2;
  transform: skewY(5deg);
  position: absolute;
  left: 0px;
  top: -60px;
  content: ' ';
  width: 100%;
  height: 120px;
  z-index: 1;
}
</style>
