<script lang="ts">
import { defineComponent } from '@nuxtjs/composition-api'

import AboutMember from '~/components/AboutMember.vue'

import SingleBlogSkeleton from './SingleBlogSkeleton.vue'
export default defineComponent({
  name: 'BlogArticle',

  components: {
    AboutMember,
    SingleBlogSkeleton,
  },

  layout: 'static',

  props: {},

  data() {
    return {
      post: {} as { title: string; content: string; excerpt: string; meta_title: string; meta_description: string; date: string; image?: string; next_post?: boolean } | null,
      slug: null,
    }
  },

  async asyncData({ params, $axios }) {
    try {
      const { data } = await $axios.get(
        'https://blog.twimo.com/wp-json/blog-api/v1/post/' + params.slug
      );
      return { post: data };
    } catch (error) {
      console.error(error);
      return { post: null };
    }
  },

  head() {
      return {
        title: this.post ? ((this.post.meta_title)? this.post.meta_title : this.post.title) : "Twimo Blog",
        meta: [
          {
            hid: "description",
            name: "description",
            content: this.post ? ((this.post.meta_description)? this.post.meta_description : this.post.excerpt) : "",
          },
          {
            hid: "twitter:title",
            name: "twitter:title",
            content: this.post ? ((this.post.meta_title)? this.post.meta_title : this.post.title) : "",
          },
          {
            hid: "twitter:description",
            name: "twitter:description",
            content: this.post ? ((this.post.meta_description)? this.post.meta_description : this.post.excerpt) : "",
          },
          {
            hid: "og:description",
            name: "og:description",
            content: this.post ? ((this.post.meta_description)? this.post.meta_description : this.post.excerpt) : "",
          },
        ]
      };
  },

  mounted() {
  },

  methods: {
  },
})
</script>
<template>
  <main class="help-page font-figtree tw-bg-[#FFF]">
    <section class="tw-bg-[url('@/assets/blog-bg.jpg')] tw-bg-cover tw-bg-center tw-bg-no-repeat">
      <div
        class="tw-container animate tw-min-h-[320px] md:tw-min-h-[450px] tw-mx-auto tw-px-5 tw-py-5 tw-pt-[5rem] tw-flex tw-flex-col tw-gap-4 tw-justify-center"
      >
        <div>
          <h1
            class="tw-mt-10 tw-font-extrabold tw-text-white tw-text-center tw-text-3xl md:tw-text-5xl md:tw-leading-[70px] tw-relative tw-z-10"
          >
            The Twimo Times
          </h1>
          <p class="tw-text-base md:tw-text-2xl tw-text-center tw-text-white tw-leading-7">
            Check out our latest blog posts to keep up
            <br class="tw-hidden md:tw-block" />to date on rental regulations, seasonal
            <br class="tw-hidden md:tw-block" />maintenance, and more
          </p>
        </div>
      </div>
    </section>

    <section class="tw-py-10 tw-bg-gradient-to-t tw-from-[#36087717]">
      <div class="tw-container tw-mx-auto tw-px-5">
        <div v-if="isLoading" class="tw-max-w-[940px] tw-mx-auto">
          <SingleBlogSkeleton />
        </div>
        <div v-else-if="post">
          <div class="tw-max-w-[940px] tw-ml-auto tw-mr-auto tw-mb-5">
            <h2 class="tw-text-[#535353] tw-text-2xl tw-text-center tw-mb-5">
              {{ post.title }}
            </h2>
            <img
              v-if="post.image"
              :src="post.image"
              :alt="post.title"
              class="tw-object-cover tw-rounded-2xl tw-w-full tw-mb-3"
            />
            <p class="tw-text-[#535353] md:tw-text-base tw-font-light tw-text-end">
              {{ post.date }}
            </p>
            <div class="blog-content" v-html="post.content"></div>
            <!--v-if="post.next_post"-->
            <div  class="tw-flex tw-justify-end tw-mt-10">
              <NuxtLink
                to="/blog"
                class="tw-max-w-[500px] tw-text-[#535353] tw-text-2xl md:tw-text-3xl"
              >
                <img src="@/assets/arrow.png" alt="Next Arrow" class="tw-inline-block tw-w-10" />
              </NuxtLink>
            </div>
          </div>
        </div>
      </div>
    </section>
  </main>
</template>

<style scoped>
>>> .blog-content * {
  color: #141414;
}

>>> .blog-content ul {
  list-style: disc;
}

>>> .blog-content li {
  padding-bottom: 5px;
}

>>> .blog-content h3 {
  font-size: 1.5rem;
}
</style>
