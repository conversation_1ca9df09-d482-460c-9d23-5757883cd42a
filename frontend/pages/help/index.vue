<script lang="ts">
import { defineComponent } from '@nuxtjs/composition-api'

import AboutMember from '~/components/AboutMember.vue'

export default defineComponent({
  name: 'Help',

  components: {
    AboutMember,
  },

  layout: 'static',

  props: {},

  data() {
    return {
      isLoading: true,
      articles: null,
    }
  },

  head() {
      return {
        title: "Twimo Help Center | Support for Hosts, Guests & Vendors",
        meta: [
          {
            hid: "description",
            name: "description",
            content: "Need help? Visit the Twimo Help Center for guidance on home swaps, sharing your home privately, account setup, and vacation rental support."
          }
        ]
      };
  },

  mounted() {
    this.animateOnScroll()
    this.fetchData()
  },

  methods: {
    // Function to observe elements and trigger animation
    animateOnScroll() {
      const elements = document.querySelectorAll('.animate')

      const observer = new IntersectionObserver(
        (entries, observer) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              entry.target.classList.add('visible')
              observer.unobserve(entry.target) // Stop observing after it's visible
            }
          })
        },
        { threshold: 0.5 }
      )

      elements.forEach(element => {
        observer.observe(element)
      })
    },

    async fetchData() {
      try {
        this.isLoading = true
        const { data } = await this.$axios.get(
          'https://blog.twimo.com/wp-json/help/v1/posts-grouped?post_type=help_center&taxonomy=help_category'
        )
        this.articles = data
      } catch (error) {
        console.error(error)
      } finally {
        this.isLoading = false
      }
    },
  },
})
</script>
<template>
  <main class="help-page font-figtree tw-bg-[#FFF]">
    <section class="tw-bg-[url('@/assets/help-bg.jpg')] tw-bg-cover tw-bg-center tw-bg-no-repeat">
      <div
        class="tw-container tw-min-h-[420px] md:tw-min-h-[450px] tw-mx-auto tw-px-5 tw-py-5 tw-pt-[3rem] tw-flex tw-flex-col tw-gap-4 tw-justify-center"
      >
        <div>
          <p class="tw-text-base md:tw-text-2xl tw-text-center tw-text-white tw-leading-7">
            Review step by step instructional videos
            <br class="tw-hidden md:tw-block" />to get all your questions answered
          </p>

          <h1
            class="tw-mt-3 tw-font-extrabold tw-text-white tw-text-center tw-text-3xl md:tw-text-5xl md:tw-leading-[70px] tw-relative tw-z-10"
          >
            Twimo Help Center
          </h1>

          <div class="tw-text-center tw-pt-10">
            <AnchorLinkStyleOne
              text="Sign up now"
              link="/signup"
              font_size="tw-text-base tw-inline md:tw-hidden"
            />
          </div>
        </div>
      </div>
    </section>

    <section class="tw-py-10 tw-bg-gradient-to-t tw-from-[#36087717] tw-pb-5">
      <div class="tw-containertw-mx-auto tw-px-5 tw-pt-25 tw-pb-[4rem]">
        <div v-for="(term, i) in articles" v-if="articles.length > 0" :key="i">
          <div class="tw-max-w-[940px] tw-ml-auto tw-mr-auto tw-mb-5 tw-mt-10">
            <h4 class="tw-text-[#360877] tw-text-2xl">
              {{ term.term_name }}
            </h4>
          </div>

          <div
            class="tw-max-w-[940px] tw-ml-auto tw-mr-auto tw-flex tw-gap-10 tw-flex-col md:tw-flex-row tw-items-center tw-flex-nowrap tw-overflow-x-auto tw-pt-2 md:tw-pt-0 tw-pb-[1rem] md:tw-pb-[1rem] tw-text-[#000] tw-pl-3"
          >
            <div v-for="(blog, i) in term.posts" :key="i" class="tw-min-w-[350px] tw-w-[350px]">
              <a :href="`${blog.youtube_url}`" target="_blank" class="hover:tw-text-[#000]">
                <div
                  class="tw-bg-[#F1F1F1] tw-shadow-custom tw-rounded-2xl tw-p-10 tw-pb-[1rem] tw-relative"
                >
                  <img
                    :src="blog.featured_image"
                    :alt="blog.title"
                    class="tw-object-cover tw-aspect-square tw-rounded-2xl tw-w-[100%]"
                  />
                  <p
                    class="tw-text-lg tw-text-[#535353] tw-font-regular tw-mb-[5px]"
                    v-html="blog.title"
                  ></p>
                </div>
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
  </main>
</template>

<style scoped>
.meet_people_container {
  position: relative;
}

.meet_people_container:before {
  background: #f2f2f2;
  transform: skewY(5deg);
  position: absolute;
  left: 0px;
  top: -60px;
  content: ' ';
  width: 100%;
  height: 120px;
  z-index: 1;
}

.animate {
  opacity: 0;
  transform: translateY(50px);
  transition:
    opacity 0.6s ease,
    transform 0.6s ease;
}

.animate.visible {
  opacity: 1;
  transform: translateY(0);
}
</style>
