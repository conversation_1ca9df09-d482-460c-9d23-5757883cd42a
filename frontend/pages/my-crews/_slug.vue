<script lang="ts">
// @ts-nocheck
import { defineComponent } from '@nuxtjs/composition-api'

import { MyCrew } from '~/types'
import screenCheckerMixins from '~/mixins/screenChecker.mixins'
import toastable from '~/mixins/toastable'
import GoodCard from '~/components/GoodCard.vue'
import GoodOkButton from '~/components/GoodOkButton.vue'
import GoodCancelButton from '~/components/GoodCancelButton.vue'
import GoodCardTitle from '~/components/GoodCardTitle.vue'

export default defineComponent({
  components: { GoodCardTitle, GoodCancelButton, GoodOkButton, GoodCard },

  mixins: [screenCheckerMixins, toastable],
  middleware: 'auth',

  async asyncData({ $axios, params }) {
    const { data } = await $axios.get(`/service-providers/${params.slug}`)
    return { crew: data }
  },

  data() {
    return {
      crew: {} as MyCrew,
      valid: true,
      deleteDialog: false,
    }
  },

  computed: {
    form(): any {
      return this.$refs.form
    },
  },

  methods: {
    validate() {
      this.form.validate()
    },
    reset() {
      this.form.reset()
    },
    resetValidation() {
      this.form.resetValidation()
    },

    async update() {
      this.validate()

      if (!this.valid) return

      try {
        const originSlug = this.crew.slug

        const { data } = await this.$axios.put(`/service-providers/${this.crew.slug}`, this.crew)

        if (originSlug !== data.slug) {
          this.resetValidation()
          this.toastSuccess('Crew successfully saved!')

          this.$router.replace(`/my-crews/${data.slug}`)
        } else {
          this.crew = data

          this.resetValidation()
          this.toastSuccess('Crew successfully saved!')
        }
      } catch (e) {
        console.log(e)
      }
    },

    async deleteCrew() {
      this.deleteDialog = true
    },

    async confirmDelete() {
      try {
        await this.$axios.delete(`/service-providers/${this.crew.slug}`)

        await this.$router.push('/my-crews')

        this.toastSuccess('Crew successfully deleted!')
      } catch (e) {
        console.log(e)
      }
    },

    cancelDelete() {
      this.deleteDialog = false
    },
  },
})
</script>

<template>
  <v-container>
    <!--    DELETE CREW DIALOG-->
    <v-dialog v-model="deleteDialog" :max-width="isDesktop ? '30%' : '100%'">
      <good-card card-text-classes="tw-p-5">
        <good-card-title title="Confirm to delete crew?" />

        <div class="tw-flex tw-justify-end">
          <good-cancel-button @click="cancelDelete" />

          <good-ok-button @click="confirmDelete" />
        </div>
      </good-card>
    </v-dialog>

    <v-row>
      <v-col cols="12" md="8">
        <div class="tw-flex tw-flex-row tw-items-center">
          <v-btn icon outlined small :to="`/my-crews`">
            <v-icon>mdi-arrow-left</v-icon>
          </v-btn>
          <h1 class="tw-text-2xl tw-font-semibold tw-ml-2 tw-grow tw-text-center">Crew details</h1>
        </div>
      </v-col>

      <v-col cols="12" md="4">
        <v-card
          class="tw-border-solid tw-border-slate-300 tw-border tw-rounded-lg tw-w-full tw-drop-shadow-2xl"
          :elevation="0"
        >
          <v-card-text>
            <v-form ref="form" v-model="valid">
              <v-text-field
                v-model="crew.name"
                :rules="[
                  v => !!v || 'Name is required',
                  v => (v && v.length <= 255) || 'Name must be less than 255 characters',
                ]"
                label="Name"
                outlined
                dense
                class="tw-w-full tw-mt-5"
              />

              <v-text-field
                v-model="crew.email"
                :rules="[
                  v => !!v || 'E-mail is required',
                  v => /.+@.+\..+/.test(v) || 'E-mail must be valid',
                ]"
                label="Email"
                outlined
                dense
                class="tw-w-full tw-mt-5"
              />

              <v-text-field
                v-model="crew.mobile_number"
                label="Mobile Number"
                outlined
                dense
                class="tw-w-full tw-mt-5"
              />

              <div class="tw-flex tw-justify-end">
                <v-btn color="red darken-1" text class="tw-normal-case" @click="deleteCrew"
                  >Delete
                </v-btn>

                <v-btn color="green darken-1" text class="tw-normal-case" @click="update"
                  >Update
                </v-btn>
              </div>
            </v-form>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>
