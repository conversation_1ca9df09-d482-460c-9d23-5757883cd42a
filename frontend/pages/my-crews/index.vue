<script lang="ts">
// @ts-nocheck

import { defineComponent, ref } from '@nuxtjs/composition-api'

import { MyCrew } from '~/types'
import MyCrewInfoThumbItem from '~/components/MyCrewInfoThumbItem.vue'
import MyCrewDashboardTitle from '~/components/MyCrewDashboardTitle.vue'
import MyCrewJobsList from '~/components/MyCrewJobsList.vue'
import screenCheckerMixins from '~/mixins/screenChecker.mixins'
import toastable from '~/mixins/toastable'
import GoodDataTable from '~/components/GoodDataTable.vue'
import GoodCard from '~/components/GoodCard.vue'
import GoodButton from '~/components/GoodButton.vue'
import GoodOkButton from '~/components/GoodOkButton.vue'
import GoodCancelButton from '~/components/GoodCancelButton.vue'
import GoodCardTitle from '~/components/GoodCardTitle.vue'
import MyCrewImagesUploader from '~/components/MyCrewImagesUploader.vue'
import { useCreateHomeImageStore } from '~/composables/useCreateHomeImageStore'

export default defineComponent({
  components: {
    GoodCardTitle,
    GoodCancelButton,
    GoodOkButton,
    GoodCard,
    GoodDataTable,
    MyCrewInfoThumbItem,
    MyCrewDashboardTitle,
    MyCrewJobsList,
    MyCrewImagesUploader,
    GoodButton,
  },

  mixins: [screenCheckerMixins, toastable],

  middleware: 'auth',

  async asyncData({ $axios }) {
    // const { data } = await $axios.get('/service-providers')

    return { crews: [] }
  },

  data() {
    return {
      crews: [] as MyCrew[],
      dialog: false,
      deleteDialog: false,
      selectedCrew: {} as MyCrew,
      valid: true,
      tab: 0,
      sortOptions: [
        { text: 'Date', value: 'date' },
        { text: 'Priority', value: 'priority' },
        { text: 'Home', value: 'home' },
        { text: 'Category', value: 'category' },
      ],
      sortOption: 'date',
      dashboardData: [],

      categories: [],
      formDueDateMenu: false,
      priorityList: [
        { text: `Low`, value: 'low' },
        { text: `Medium`, value: 'medium' },
        { text: `High`, value: 'high' },
        { text: `None`, value: 'none' },
      ],
      paymentTypeList: [
        { text: `Venmo`, value: 'venmo' },
        { text: `Check`, value: 'check' },
        { text: `Cash`, value: 'cash' },
      ],
      homesList: [],
      vendorsList: [],
      jobForm: {
        jobTitle: null,
        category: null,
        dueDate: null,
        vendor: null,
        dueDate: null,
        priorityLevel: null,
        home: null,
        details: null,
        paymentType: `venmo`,
        paymentAmount: null,
        vendorEmail: null,
        photos: [],
      },
      imageStore: useCreateHomeImageStore(),
      processingFiles: false
    }
  },

  computed: {
    form(): any {
      return this.$refs.form
    },
    formattedDueDate() {
      return this.formatDate(this.jobForm.dueDate)
    },
    displayImages() {
      // Safely access the images array with fallback to empty array
      const storeImages = this.imageStore.images || []

      return storeImages.map(img => ({
        ...img,
        id: img.id,
        name: img.name || (img.file ? img.file.name : 'Image'),
        size: img.size || (img.file ? img.file.size : 0),
      }))
    }
  },

  async mounted() {
    // Get Categories
    try {
      const { data } = await this.$axios.get('mycrew/get-categories')
      for (const c of data.categories) {
        this.categories.push({ text: c.label, value: c.id })
      }
    } catch (e) {
      console.log(e)
    }

    // Get Dashboard Data
    try {
      const { data } = await this.$axios.get('mycrew/get-dashboard-info')
      this.dashboardData = data.dashboard_data
    } catch (e) {
      console.log(e)
    }

    // Get Vendors
    try {
      const { data } = await this.$axios.get('mycrew/get-vendors')
      let vendor_name = ''
      for (const c of data.vendors) {
        if (c.first_name != null) {
          vendor_name = c.first_name + ' ' + c.last_name
        } else {
          vendor_name = c.label
        }

        this.vendorsList.push({
          text: vendor_name + (c.status == 'pending' ? ' (pending)' : ''),
          value: c.id,
        })
      }
    } catch (e) {
      console.log(e)
    }

    // Get Homes
    try {
      const { data } = await this.$axios.get('home/user/list/my?mode=minify&status=active')
      this.houses = data
      // this.houses.forEach(([key, value]) => { this.filter_by_items.push(value); });
      for (const v in this.houses) {
        this.homesList.push({
          text: this.houses[v].title,
          value: this.houses[v].id,
        })
      }

      this.jobForm.home = this.houses[0] !== undefined ? this.houses[0].id : null
    } catch (e) {
      console.log(e)
    }
  },

  methods: {
    validate() {
      this.form.validate()
    },
    reset() {
      this.form.reset()
    },
    resetValidation() {
      this.form.resetValidation()
    },
    cancel() {
      this.dialog = false
      this.selectedCrew = {} as MyCrew
      this.reset()
      this.resetValidation()
    },

    async create() {
      this.validate()

      if (!this.valid) return

      try {
        if (this.selectedCrew.id) {
          await this.$axios.put(`/service-providers/${this.selectedCrew.slug}`, this.selectedCrew)
        } else {
          await this.$axios.post('/mycrew/create-job', {
            form_data: this.jobForm,
          })
        }

        this.dialog = false
        // this.selectedCrew = {} as MyCrew
        this.reset()
        this.resetValidation()

        this.$refs.jobsList.getJobs()
        this.dashboardData.pending++

        this.toastSuccess('MyCrew job has been created successfully.')
      } catch (e) {
        console.log(e)
      }
    },

    async deleteCrew(crew: MyCrew) {
      this.selectedCrew = Object.assign({}, crew)

      this.deleteDialog = true
    },

    async confirmDeleteCrew() {
      try {
        await this.$axios.delete(`/service-providers/${this.selectedCrew.slug}`)

        this.deleteDialog = false

        const { data } = await this.$axios.get('/service-providers')

        this.crews = data
        this.selectedCrew = {} as MyCrew

        this.toastSuccess('Crew successfully deleted!')
      } catch (e) {
        console.log(e)
      }
    },

    cancelDeleteCrew() {
      this.deleteDialog = false
      this.selectedCrew = {} as MyCrew
    },

    selectCrew(crew: MyCrew) {
      this.selectedCrew = Object.assign({}, crew)
      this.dialog = true
    },
    formatDate(date) {
      if (!date) return ''
      const options = {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        timeZone: 'UTC',
      }
      return new Date(date).toLocaleDateString('en-US', options)
    },
    closeDueDateMenu() {
      this.formDueDateMenu = false
    },
    triggerUploader() {
      ;(this.$refs.imageUploader as any)?.triggerFileInput()
    },

    async uploadFiles(e: any) {
      const files = [...e]

      console.log('Files to upload:', files)

      for (let i = 0; i < files.length; i++) {
        const request = new FormData()
        request.append('file', files[i])

        try {
          await this.$axios.post('upload', request).then(response => {
            const uploadedFile: UploadedFile = response.data

            this.jobForm.photos.push(uploadedFile)
            console.log('Uploaded file:', uploadedFile)
          })
        } catch (error) {
          this.toastError('Please check the file type and size')

          // editHomeData.photos = []

          break
        }
      }
    },


    // Handle files selected
    handleFilesSelected(files: File[]) {
      if (!files || files.length === 0) {
        return
      }

      this.processingFiles = true
      console.log(`Processing ${files.length} files`)

      try {
        // Add images to store - this is a synchronous operation
        this.imageStore.addImages(files)
        this.uploadFiles(files);

        // Show success toast
        this.$toast.success(`Added ${files.length} image(s) for upload`)
        console.log('Image store:', this.imageStore.images)

        // Log success
        console.log(`Added ${files.length} files, total in store:`, this.imageStore.images.length)
      } catch (error) {
        console.error('Error adding images:', error)
        this.$toast.error('Failed to add images')
      } finally {
        this.processingFiles = false
      }
    },

    // Handle image deletion
    handleDeleteImage(imageId: string) {
      try {
        this.imageStore.deleteImage(imageId)
      } catch (error) {
        console.error('Error deleting image:', error)
        this.$toast.error('Failed to delete image')
      }
    },

    // Handle image reordering - IMPORTANT: Match the event name from ImagesUploader
    handleImagesReordered(reorderedImages) {
      try {
        console.log('Images reordered, new order:', reorderedImages)
        this.imageStore.reorderImages(reorderedImages)
        this.$toast.success('Image order updated')
      } catch (error) {
        console.error('Error reordering images:', error)
        this.$toast.error('Failed to reorder images')
      }
    },

    // Handle uploader errors
    handleUploadError(errorMessage: string) {
      this.$toast.error(errorMessage).goAway(3000)
    }


  },
})
</script>

<template>
  <div class="container-fluid">
    <!--    CREATE CREW DIALOG-->
    <v-dialog v-model="dialog" :max-width="isDesktop ? '80%' : '100%'">
      <good-card card-text-classes="tw-p-5">
        <div class="tw-text-3xl tw-font-semibold tw-text-left tw-tracking-wide">
          <span class="tw-text-zinc-500">New MyCrew </span
          ><span class="tw-text-purple-700 tw-tracking-wide"> Jobs </span>
        </div>

        <p class="tw-font-regular tw-text-base tw-mt-3 tw-mb-5 tw-text-zinc-500">
          Input the details for your new MyCrew Job below, and send directly to your Vendor!
        </p>

        <v-form ref="form" v-model="valid">
          <h3 class="tw-text-xl tw-font-semibold tw-text-zinc-500 tw-mb-3">Job Details</h3>

          <v-row>
            <v-col xs="12" sm="12" md="4" cols="12">
              <v-text-field
                v-model="jobForm.jobTitle"
                label="Job Title"
                :rules="[
                  v => !!v || 'Job title is required',
                  v => (v && v.length <= 200) || 'Job title must be less than 200 characters',
                ]"
                outlined
                hide-details
                class="tw-w-full tw-shadow-input custom-input"
              ></v-text-field>
            </v-col>
            <v-col xs="12" sm="12" md="4" cols="12">
              <v-select
                v-model="jobForm.category"
                :items="categories"
                label="Category"
                :rules="[v => !!v || 'Category is required']"
                class="tw-w-full tw-shadow-input tw-rounded-2xl custom-input"
                outlined
                hide-details
              >
              </v-select>
            </v-col>
          </v-row>

          <v-row>
            <v-col xs="12" sm="12" md="4" cols="12">
              <v-menu
                v-model="formDueDateMenu"
                :close-on-content-click="false"
                transition="scale-transition"
                offset-y
                min-width="290px"
              >
                <template #activator="{ on, attrs }">
                  <v-text-field
                    v-model="formattedDueDate"
                    label="Due Date"
                    :rules="[v => !!v || 'Due date is required']"
                    prepend-icon=""
                    readonly
                    v-bind="attrs"
                    outlined
                    hide-details
                    class="tw-w-full tw-shadow-input tw-rounded-2xl custom-input"
                    v-on="on"
                  ></v-text-field>
                </template>
                <v-date-picker
                  v-model="jobForm.dueDate"
                  no-title
                  :min="new Date().toISOString().substr(0, 10)"
                  @input="closeDueDateMenu"
                ></v-date-picker>
              </v-menu>
            </v-col>

            <v-col xs="12" sm="12" md="4" cols="12">
              <v-select
                v-model="jobForm.priorityLevel"
                :items="priorityList"
                label="Priority Level"
                required
                :rules="[v => !!v || 'Priority level is required']"
                class="tw-w-full tw-shadow-input tw-rounded-2xl custom-input"
                outlined
                hide-details
              >
              </v-select>
            </v-col>
          </v-row>

          <v-row>
            <v-col xs="12" sm="12" md="8" cols="12">
              <v-select
                v-model="jobForm.home"
                :items="homesList"
                label="Home"
                :rules="[v => !!v || 'Home is required']"
                required
                class="tw-w-full tw-shadow-input tw-rounded-2xl custom-input"
                clearable="true"
                outlined
                hide-details
              >
              </v-select>
            </v-col>
          </v-row>

          <v-row>
            <v-col xs="12" sm="12" md="8" cols="12">
              <v-textarea
                v-model="jobForm.details"
                label="Details"
                outlined
                hide-details
                class="tw-w-full tw-shadow-input tw-rounded-2xl custom-input"
              ></v-textarea>
            </v-col>
          </v-row>

          <v-row no-gutters>
            <v-col
              xs="12"
              sm="12"
              md="8"
              cols="12"
              class="tw-p-[2em] tw-mt-5 tw-mb-2 tw-rounded-2xl tw-shadow-input mycrew-upload-section"
            >
              <div class="tw-text-[13px] tw-text-purple-700 tw-font-semibold">
                Add Image (If applicable)
              </div>

              <!--<MyCrewImagesUploader
                ref="imageUploader"
                class="tw-min-h-[100px] xs:tw-min-h-[100px] tw-mt-5 tw-mb-5 tw-cursor-pointer"
                @files-uploaded="uploadFiles"
                @delete-file="deletePhoto"
                @set-as-cover-photo="setAsCoverPhoto"
                @images-reordered="imagesReordered"
              />-->
              <MyCrewImagesUploader
                  ref="imageUploader"
                  class="tw-min-h-[150px] imageUploaderContainer"
                  :images="displayImages"
                  :max-files="20"
                  :max-file-size="10"
                  :is-uploading="isUploading || processingFiles"
                  @files-selected="handleFilesSelected"
                  @delete-image="handleDeleteImage"
                  @images-reordered="handleImagesReordered"
                  @error="handleUploadError"
                />
            </v-col>
          </v-row>

          <h3 class="tw-text-xl tw-font-semibold tw-text-zinc-500 tw-mt-8 tw-mb-3">Payment</h3>

          <v-row>
            <v-col xs="12" sm="12" md="4" cols="12">
              <v-select
                v-model="jobForm.paymentType"
                :items="paymentTypeList"
                label="Payment Type"
                :rules="[v => !!v || 'Payment type is required']"
                required
                class="tw-w-full tw-shadow-input tw-rounded-2xl custom-input"
                outlined
                hide-details
              >
              </v-select>
            </v-col>
            <v-col xs="12" sm="12" md="4" cols="12">
              <v-text-field
                v-model="jobForm.paymentAmount"
                label="Payment Amount"
                required
                :rules="[v => !!v || 'Payment amount is required']"
                class="tw-w-full tw-shadow-input tw-rounded-2xl custom-input"
                clearable="true"
                outlined
                hide-details
                type="number"
              >
              </v-text-field>
            </v-col>
          </v-row>

          <h3 class="tw-text-xl tw-font-semibold tw-text-zinc-500 tw-mt-8">Complete Job</h3>
          <p class="tw-font-regular tw-text-base tw-mt-3 tw-mb-2 tw-text-zinc-500">
            Please select your vendor
          </p>

          <v-row>
            <v-col xs="12" sm="12" md="4" cols="12">
              <v-select
                v-model="jobForm.vendor"
                :items="vendorsList"
                label="Vendor"
                required
                class="tw-w-full tw-shadow-input tw-rounded-2xl custom-input"
                clearable="true"
                outlined
                hide-details
              >
              </v-select>
            </v-col>
          </v-row>

          <div v-if="jobForm.vendor == null">
            <p class="tw-font-regular tw-text-base tw-mt-3 tw-mb-2 tw-text-zinc-500">or</p>

            <p class="tw-font-regular tw-text-base tw-mt-3 tw-mb-2 tw-text-zinc-500">
              Please enter your Vendor’s Contact Details below.
            </p>
            <p class="tw-font-regular tw-text-base tw-mt-3 tw-mb-3 tw-text-zinc-500">
              They will be sent a direct link to create a Twimo Vendor account, where you can
              communicate, track job progress, and pay your vendor directly
            </p>

            <v-row>
              <v-col xs="12" sm="12" md="4" cols="12">
                <v-text-field
                  v-model="jobForm.vendorEmail"
                  label="Vendor Email"
                  outlined
                  :rules="[
                    v => (!!v && jobForm.vendor == null) || 'Vendor email is required',
                    v => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(v) || 'E-mail must be valid',
                  ]"
                  hide-details
                  type="email"
                  class="tw-w-full tw-shadow-input tw-rounded-2xl custom-input"
                ></v-text-field>
              </v-col>
              <v-col xs="12" sm="12" md="4" cols="12"> </v-col>
            </v-row>
          </div>

          <div class="tw-flex tw-justify-end tw-mb-10 tw-mt-3">
            <good-cancel-button @click="cancel" />

            <GoodButton @click="create"> Send Job </GoodButton>
          </div>
        </v-form>
      </good-card>
    </v-dialog>

    <v-row class="mycrew-dashboard-header">
      <v-col>
        <div
          class="md:tw-px-[10px] tw-px-4 tw-bg-center tw-bg-no-repeat tw-bg-cover tw-overflow-hidden"
        >
          <div
            class="tw-mt-[10px] tw-text-[1.4rem] md:tw-text-[2rem] tw-font-semibold tw-text-left tw-text-[#672093]"
          >
            MyCrew
          </div>

          <div
            class="tw-mt-[30px] tw-text-[1.3rem] md:tw-text-[1.5rem] tw-font-normal tw-text-left tw-text-[#5E5E5E]"
          >
            MyCrew Vendor Dashboard
          </div>

          <div
            class="tw-mt-[5px] tw-text-[1.1rem] md:tw-text-[1.2rem] tw-font-light tw-text-left tw-text-[#6C6C6C] md:tw-max-w-[90%]"
          >
            This month's snapshot of MyCrew Updates
          </div>

          <div
            class="tw-flex tw-mt-[30px] tw-mb-[50px] tw-overflow-auto tw-pl-[5px] tw-py-5 md:tw-flex-nowrap tw-flex-nowrap tw-gap-[1rem] md:tw-gap-[1.5rem] sm:tw-gap-[1rem]"
          >
            <MyCrewInfoThumbItem
              title="Pending"
              :value="dashboardData.pending"
              :description="`Response Needed`"
            />

            <MyCrewInfoThumbItem
              title="Calendar Events"
              :value="dashboardData.calendar"
              :description="`MyCrew Jobs`"
            />

            <MyCrewInfoThumbItem
              title="Messages"
              :value="dashboardData.messages"
              :description="`Unread`"
            />

            <MyCrewInfoThumbItem
              title="MyCrew Jobs"
              :value="dashboardData.jobs"
              :description="`Active`"
            />
          </div>
        </div>
      </v-col>
    </v-row>

    <div class="tw-pt-[0rem] md:tw-pt-[1rem]">
      <!-- PAGE TITLE-->
      <MyCrewDashboardTitle>
        <GoodButton class="tw-mt-5 md:tw-mt-0" @click="dialog = true">
          <v-icon>mdi-plus</v-icon>
          New MyCrew Job
        </GoodButton>
      </MyCrewDashboardTitle>
    </div>

    <v-row>
      <v-col>
        <div class="sort_mycrew md:tw-flex tw-text-[#858585] tw-max-w-[700px] tw-mb-10 tw-ml-3">
          <div class="sort_label tw-w-100% md:tw-w-[75%] tw-text-[1.1rem] tw-p-[10px]">
            Jobs are sorted by Date, resort by selecting additional filters
          </div>
          <div class="tw-w-[100%] md:tw-w-[25%] tw-text-right tw-px-5 tw-pt-[3px]">
            <v-select
              v-model="sortOption"
              :items="sortOptions"
              hide-details
              class="tw-p-0 no-outline"
              @change="handleSortOptions"
            ></v-select>
          </div>
        </div>
      </v-col>
    </v-row>

    <MyCrewJobsList ref="jobsList" type="host" :filter="sortOption" />
  </div>
</template>

<style scoped>
#crew-card:focus::before {
  opacity: 0 !important;
}

#delete-crew-icon:focus::after {
  opacity: 0 !important;
}

.sort_mycrew {
  border: 1px solid rgba(133, 133, 133, 0.2);
  box-shadow: 0px 2px 3px rgba(0, 0, 0, 0.25);
  border-radius: 10px;
}

.sort_mycrew > div:first-child {
  border-right: 1px solid rgba(133, 133, 133, 0.2);
  align-items: center;
  display: flex;
}

.custom-input {
  border-radius: 10px !important;
}

.custom-input >>> fieldset {
  border-color: #8585851a !important;
}

.error--text.custom-input >>> fieldset {
  border-color: #ff5252 !important;
}

.custom-input >>> .v-input__slot {
  min-height: 45px !important;
}

.custom-input >>> .v-label {
  top: 13px !important;
}

.custom-input >>> .v-label--active {
  top: 17px !important;
  color: #7c0cb1;
  font-weight: 600;
}

.custom-input >>> .v-input__append-inner {
  margin-top: 10px;
}

.custom-input >>> .v-select__selections {
  padding: 0px !important;
}

>>> .no-outline .v-input__slot:before {
  border: none !important;
  display: none !important;
}

>>> .mycrew-upload-section .col-md-4 {
  flex: 0 0 100%;
  max-width: 95%;
}

::v-deep .imageUploaderContainer p{
  display: none !important;
}

@media screen and (max-width: 640px) {
  .sort_mycrew > div:first-child {
    border-right: 0px;
    border-bottom: 1px solid rgba(133, 133, 133, 0.2);
    align-items: center;
    display: flex;
  }
  #drop-region {
    height: 300px;
  }
}
</style>
