<script lang="ts">
import { defineComponent } from '@nuxtjs/composition-api'

export default defineComponent({
  name: 'MyCrewOnboarding',
})
</script>

<template>
  <v-container>
    <v-row>
      <v-col cols="12" md="8" offset-md="2">
        <div class="tw-flex tw-flex-col tw-items-center">
          <div class="tw-text-3xl tw-font-bold tw-text-center tw-mb-8">
            Streamline Your Property Management Tasks
          </div>
          <div class="tw-w-full">
            <ul class="tw-pl-8 tw-text-xl tw-font-bold tw-list-none">
              <li class="tw-mb-4">
                <span class="tw-text-primary">🚀 Centralize Your Service Providers:</span>
                Say goodbye to the hassle of coordinating with multiple providers. Our Crews feature
                allows you to manage all your services in one place.
              </li>
              <li class="tw-mb-4">
                <span class="tw-text-primary">💬 Communicate Effectively:</span>
                Our platform enables seamless communication between you and your crew members,
                eliminating misunderstandings and ensuring that your property's maintenance is
                always up-to-date.
              </li>
              <li class="tw-mb-4">
                <span class="tw-text-primary">📅 Schedule Tasks with Ease:</span>
                Assign tasks to your crew members effortlessly, and receive notifications when
                they're completed. Our automated scheduling system ensures that your property's
                maintenance is always on track.
              </li>
              <li class="tw-mb-4">
                <span class="tw-text-primary">🤝 Build Long-Term Relationships:</span>
                With Crews, you can build trust and reliance with your service providers, ensuring
                that your property is always in good hands.
              </li>
              <li class="tw-mb-8">
                <span class="tw-text-primary">📊 Stay on Top of Maintenance:</span>
                Our comprehensive tracking system allows you to monitor your property's maintenance
                activities, giving you peace of mind and ensuring that everything runs smoothly.
              </li>
            </ul>
          </div>
          <div class="tw-text-center">
            <button
              class="tw-bg-primary hover:tw-bg-purple-950 tw-text-white tw-font-bold tw-py-4 tw-px-6 tw-rounded-full tw-shadow-lg tw-w-full tw-max-w-xs"
              @click="$router.push('/signup')"
            >
              Get Started Today! 🎉
            </button>
          </div>
        </div>
      </v-col>
    </v-row>
  </v-container>
</template>
