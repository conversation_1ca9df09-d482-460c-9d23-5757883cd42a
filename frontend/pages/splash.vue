<script lang="ts">
// @ts-nocheck
import { defineComponent } from '@nuxtjs/composition-api'

import ScreenCheckerMixins from '~/mixins/screenChecker.mixins'
import { openNewTab } from '~/composables/useCommon'

export default defineComponent({
  name: 'Index',

  mixins: [ScreenCheckerMixins],

  layout: 'index',

  setup() {
    return {
      openNewTab,
    }
  },

  data() {
    return {
      tab: 0,
    }
  },

  methods: {
    onImageLoad() {
      const mainImageHeight = this.$refs.image?.offsetHeight || 0
      const arrowIconHeight = this.$refs.arrow?.offsetHeight || 0
      const maxHeight = Math.max(mainImageHeight, arrowIconHeight)
      this.$refs.vCol.style.height = `${maxHeight}px`
    },

    onImage2Load() {
      const mainImageHeight = this.$refs.image2?.offsetHeight || 0
      const arrowIconHeight = this.$refs.arrow2?.offsetHeight || 0
      const maxHeight = Math.max(mainImageHeight, arrowIconHeight)
      this.$refs.vCol2.style.height = `${maxHeight}px`
    },

    onImage3Load() {
      const mainImageHeight = this.$refs.image3?.offsetHeight || 0
      const arrowIconHeight = this.$refs.arrow3?.offsetHeight || 0
      const maxHeight = Math.max(mainImageHeight, arrowIconHeight)
      this.$refs.vCol3.style.height = `${maxHeight}px`
    },

    onImage4Load() {
      const mainImageHeight = this.$refs.image4?.offsetHeight || 0
      const arrowIconHeight = this.$refs.arrow4?.offsetHeight || 0
      const maxHeight = Math.max(mainImageHeight, arrowIconHeight)
      this.$refs.vCol4.style.height = `${maxHeight}px`
    },

    learnMore() {
      openNewTab('https://learn.twimo.com')
    },

    explore() {
      this.$router.push('/home')
    },

    signup() {
      this.$router.push('/signup')
    },

    login() {
      this.$router.push('/login')
    },
  },
})
</script>

<template>
  <v-row no-gutters>
    <v-col cols="12">
      <h1 class="sm:tw-text-6xl tw-text-4xl tw-font-semibold">Welcome to Twimo</h1>

      <h2 class="md:tw-text-4xl tw-text-xl tw-mt-4">
        A Better Way to Share & Care for Your Vacation Home
      </h2>

      <v-tabs
        v-model="tab"
        background-color="transparent"
        dark
        fixed-tabs
        center-active
        class="tw-mt-16"
      >
        <v-tab class="tw-normal-case sm:tw-text-3xl"> Learn More </v-tab>
        <v-tab class="tw-normal-case sm:tw-text-3xl"> Explore Homes </v-tab>
        <v-tab class="tw-normal-case sm:tw-text-3xl"> Sign Up </v-tab>
        <v-tab class="tw-normal-case sm:tw-text-3xl"> Log In </v-tab>
      </v-tabs>

      <v-tabs-items v-model="tab" dark class="tw-bg-transparent">
        <v-tab-item>
          <v-row no-gutters>
            <v-col cols="6" class="tw-flex tw-flex-col">
              <h3 class="sm:tw-mt-16 tw-mt-8 sm:tw-text-3xl tw-text-lg tw-font-semibold">
                Don’t Let Your Vacation Home Own You
              </h3>

              <div class="sm:tw-mt-8 tw-mt-4 tw-font-thin sm:tw-text-3xl">
                Choose how you Share, Swap, Rent and Service your Home with Twimo.
              </div>

              <div class="sm:tw-mt-8 tw-mt-4 tw-font-thin sm:tw-text-3xl">
                Visit our website learn.twimo.com to get more info!
              </div>

              <div class="sm:tw-mt-8 tw-mt-4">
                <v-btn
                  class="tw-normal-case sm:tw-text-xl tw-tracking-normal tw-rounded-xl tw-font-semibold"
                  x-large
                  color="primary"
                  @click="learnMore"
                >
                  Learn More
                </v-btn>
              </div>
            </v-col>

            <v-col ref="vCol" cols="6" class="tw-relative">
              <img
                ref="image"
                :src="require('~/assets/IndexTab1Image.webp')"
                loading="lazy"
                alt="learn more"
                class="tw-absolute sm:tw-w-[90%] sm:tw-max-w-[90%] tw-w-[120%] tw-max-w-[120%] sm:tw-bottom-[5%] tw-bottom-[-12%] tw-right-[25%] sm:tw-right-[35%]"
                @load="onImageLoad"
              />

              <img
                ref="arrow"
                :src="require('~/assets/NextRightIcon.webp')"
                alt="right arrow"
                class="tw-absolute tw-w-[15%] tw-max-w-[15%] sm:tw-bottom-[45.5%] tw-bottom-[25%] tw-right-[10%] sm:tw-right-[17%] tw-cursor-pointer tw-z-50"
                @click="tab = 1"
              />

              <img
                :src="require('~/assets/IndexTab1Shadow.webp')"
                alt="shadow"
                class="tw-absolute tw-w-full sm:tw-top-[40%] tw-top-[70%]"
              />
            </v-col>
          </v-row>
        </v-tab-item>

        <v-tab-item>
          <v-row no-gutters>
            <v-col ref="vCol2" :cols="isDesktop ? 4 : 5" class="tw-relative">
              <img
                ref="image2"
                :src="require('~/assets/IndexTab2Image.webp')"
                alt="Swap, Stay or Both!"
                class="tw-absolute tw-w-[160%] tw-max-w-[160%] sm:tw-w-[150%] sm:tw-max-w-[150%] sm:tw-bottom-[10%] tw-bottom-[-12%] tw-left-[-10%]"
                @load="onImage2Load"
              />

              <img
                ref="arrow2"
                :src="require('~/assets/NextRightIcon.webp')"
                alt="right arrow"
                class="tw-absolute tw-w-[20%] tw-max-w-[20%] sm:tw-bottom-[45.5%] tw-bottom-[25%] sm:tw-right-[-172%] tw-right-[-138%] tw-cursor-pointer tw-z-50"
                @click="tab = 2"
              />

              <img
                :src="require('~/assets/IndexTab1Shadow.webp')"
                alt="shadow"
                class="tw-absolute tw-w-full sm:tw-top-[53%] tw-top-[75%]"
              />
            </v-col>

            <v-col cols="6" class="tw-flex tw-flex-col">
              <h3 class="sm:tw-mt-16 tw-mt-8 sm:tw-text-3xl tw-text-lg tw-font-semibold">
                Swap, Stay or Both!
              </h3>

              <div class="sm:tw-mt-8 tw-mt-4 tw-font-thin sm:tw-text-3xl">
                Twimo’s network of homeowners can swap homes with other Twimo Host and/or offer
                direct booking rentals.
              </div>

              <div class="sm:tw-mt-8 tw-mt-4 tw-font-thin sm:tw-text-3xl">
                Whether a Host or Guest, your next vacation is a click away.
              </div>

              <div class="sm:tw-mt-8 tw-mt-4">
                <v-btn
                  class="tw-normal-case sm:tw-text-xl tw-tracking-normal tw-rounded-xl tw-font-semibold"
                  x-large
                  color="primary"
                  @click="explore"
                >
                  Explore
                </v-btn>
              </div>
            </v-col>
          </v-row>
        </v-tab-item>

        <v-tab-item>
          <v-row no-gutters>
            <v-col cols="6" class="tw-flex tw-flex-col">
              <h3 class="sm:tw-mt-16 tw-mt-8 sm:tw-text-3xl tw-text-lg tw-font-semibold">
                Sign Up
              </h3>

              <div class="sm:tw-mt-8 tw-mt-4 tw-font-thin sm:tw-text-3xl">
                You are just a click away from simplifying your vacation home management, while
                accessing Home Swap opportunities.
              </div>

              <div class="sm:tw-mt-8 tw-mt-4 tw-font-thin sm:tw-text-3xl">
                Click below to get started now!
              </div>

              <div class="sm:tw-mt-8 tw-mt-4">
                <v-btn
                  class="tw-normal-case sm:tw-text-xl tw-tracking-normal tw-rounded-xl tw-font-semibold"
                  x-large
                  color="primary"
                  @click="signup"
                >
                  Sign Up
                </v-btn>
              </div>
            </v-col>

            <v-col ref="vCol3" cols="6" class="tw-relative">
              <img
                ref="image3"
                :src="require('~/assets/IndexTab3Image.webp')"
                alt="Sign Up"
                class="tw-absolute tw-w-[140%] tw-max-w-[140%] sm:tw-w-[115%] sm:tw-max-w-[115%] sm:tw-bottom-[12%] tw-bottom-[-4%] tw-right-[10%] sm:tw-right-[19%]"
                @load="onImage3Load"
              />

              <img
                ref="arrow3"
                :src="require('~/assets/NextRightIcon.webp')"
                alt="right arrow"
                class="tw-absolute tw-w-[15%] tw-max-w-[15%] sm:tw-bottom-[45.5%] tw-bottom-[25%] tw-right-[10%] sm:tw-right-[17%] tw-cursor-pointer tw-z-50"
                @click="tab = 3"
              />

              <img
                :src="require('~/assets/IndexTab1Shadow.webp')"
                alt="shadow"
                class="tw-absolute tw-w-full sm:tw-top-[40%] tw-top-[70%]"
              />
            </v-col>
          </v-row>
        </v-tab-item>

        <v-tab-item>
          <v-row no-gutters>
            <v-col ref="vCol4" :cols="isDesktop ? 4 : 5" class="tw-relative">
              <img
                ref="image4"
                :src="require('~/assets/IndexTab4Image.webp')"
                alt="Login"
                class="tw-absolute tw-w-[160%] tw-max-w-[160%] sm:tw-w-[150%] sm:tw-max-w-[150%] sm:tw-bottom-[10%] tw-bottom-[3%] tw-left-[-10%]"
                @load="onImage4Load"
              />

              <img
                ref="arrow4"
                :src="require('~/assets/NextRightIcon.webp')"
                alt="right arrow"
                class="tw-absolute tw-w-[20%] tw-max-w-[20%] sm:tw-bottom-[45.5%] tw-bottom-[25%] sm:tw-right-[-172%] tw-right-[-138%] tw-cursor-pointer tw-z-50"
                @click="tab = 0"
              />

              <img
                :src="require('~/assets/IndexTab1Shadow.webp')"
                alt=""
                class="tw-absolute tw-w-full sm:tw-top-[53%] tw-top-[60%]"
              />
            </v-col>

            <v-col cols="6" class="tw-flex tw-flex-col">
              <h3 class="sm:tw-mt-16 tw-mt-8 sm:tw-text-3xl tw-text-lg tw-font-semibold">Login</h3>

              <div class="sm:tw-mt-8 tw-mt-4 tw-font-thin sm:tw-text-3xl">
                Already a Twimo member? Welcome back! Login here.
              </div>

              <div class="sm:tw-mt-8 tw-mt-4">
                <v-btn
                  class="tw-normal-case sm:tw-text-xl tw-tracking-normal tw-rounded-xl tw-font-semibold"
                  x-large
                  color="primary"
                  @click="login"
                >
                  Login
                </v-btn>
              </div>
            </v-col>
          </v-row>
        </v-tab-item>
      </v-tabs-items>
    </v-col>
  </v-row>
</template>

<style lang="scss">
.v-slide-group__prev--disabled {
  display: none !important;
}
</style>
