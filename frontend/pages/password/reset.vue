<template>
  <div
    class="tw-min-h-screen tw-flex tw-items-center tw-justify-center tw-p-4 sm:tw-p-6 md:tw-py-16 md:tw-px-8"
  >
    <!-- Loading state -->
    <div v-if="showWait" class="tw-flex tw-flex-col tw-items-center tw-justify-center tw-p-4">
      <v-progress-circular indeterminate size="80" color="#7c0cb1" class="tw-mb-6" />
      <div class="tw-text-lg md:tw-text-xl tw-font-bold tw-text-center">
        Please wait while we verify your request
      </div>
    </div>

    <!-- Invalid token state -->
    <div
      v-if="showInvalid"
      class="tw-flex tw-flex-col tw-items-center tw-justify-center tw-text-center tw-p-4"
    >
      <v-icon size="80" color="red" class="tw-mb-4 md:tw-mb-6">mdi-account-off</v-icon>
      <div class="tw-text-xl md:tw-text-2xl tw-font-bold tw-mb-3 md:tw-mb-4">
        Invalid or Expired Token
      </div>
      <p class="tw-text-gray-600 tw-mb-6 md:tw-mb-8">
        The password reset link is invalid or has expired.
      </p>
      <good-button @click="$router.push('/login')"> Return to Login </good-button>
    </div>

    <!-- Reset password form -->
    <div v-if="showReset" class="tw-w-full tw-max-w-md tw-px-4">
      <div class="tw-flex tw-justify-center tw-mb-6 md:tw-mb-8">
        <img src="~/assets/plain_logo.webp" class="tw-w-32 md:tw-w-36 tw-h-auto" alt="Twimo" />
      </div>

      <div class="tw-bg-white tw-rounded-xl tw-shadow-lg tw-p-6 md:tw-p-8">
        <h1 class="tw-text-xl md:tw-text-2xl tw-font-bold tw-text-center tw-mb-6">
          Reset Your Password
        </h1>

        <v-form
          ref="formRef"
          v-model="valid"
          lazy-validation
          class="tw-flex tw-flex-col tw-gap-5"
          @submit.prevent="handleResetPassword"
        >
          <div class="tw-w-full">
            <v-text-field
              v-model="password"
              :rules="passwordRules"
              label="New Password"
              type="password"
              outlined
              hide-details="auto"
              class="tw-bg-white tw-rounded-lg"
            />

            <!-- Password Requirements Component -->
            <PasswordRequirements
              :password="password"
              :dark-mode="false"
              class="tw-mt-2"
            />
          </div>

          <v-text-field
            v-model="passwordConfirmation"
            :rules="passwordConfirmationRules"
            label="Confirm New Password"
            type="password"
            outlined
            hide-details="auto"
            class="tw-bg-white tw-rounded-lg"
          />

          <div class="tw-mt-2">
            <good-button type="submit" class="tw-w-full" :disabled="!valid" :loading="isSubmitting">
              {{ isSubmitting ? 'Resetting Password...' : 'Reset Password' }}
            </good-button>
          </div>

          <div class="tw-text-center tw-text-sm tw-mt-4">
            <a
              class="tw-cursor-pointer tw-text-purple-700 tw-font-semibold hover:tw-underline"
              @click="$router.push('/login')"
            >
              Back to Login
            </a>
          </div>
        </v-form>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
// @ts-nocheck

import { defineComponent, ref, useRoute, useRouter, onMounted } from '@nuxtjs/composition-api'

import { useAuthStore } from '~/composables/useAuthStore'
import { useToast } from '~/composables/useCommon'
import GoodButton from '~/components/GoodButton.vue'
import PasswordRequirements from '~/components/PasswordRequirements.vue'

export default defineComponent({
  name: 'PasswordResetPage',

  components: {
    GoodButton,
    PasswordRequirements,
  },

  setup() {
    const route = useRoute()
    const router = useRouter()
    const { verifyToken, resetPassword, isSubmitting } = useAuthStore()
    const toast = useToast()

    // UI states
    const showWait = ref(true)
    const showReset = ref(false)
    const showInvalid = ref(false)

    // Form data
    const formRef = ref(null)
    const password = ref('')
    const passwordConfirmation = ref('')
    const valid = ref(false)

    // Password validation rules
    const passwordRules = [
      v => !!v || 'Password is required',
      v => (v && v.length >= 8) || 'Password must be at least 8 characters',
      v => (v && v.length <= 24) || 'Password must not exceed 24 characters',
    ]

    const passwordConfirmationRules = [
      v => !!v || 'Password confirmation is required',
      v => v === password.value || 'Passwords do not match',
    ]

    const checkToken = async () => {
      const token = route.value.query.token

      if (!token) {
        router.push('/')
        return
      }

      try {
        const response = await verifyToken(token)

        if (response.is_valid_token) {
          showWait.value = false
          showInvalid.value = false
          showReset.value = true
        } else {
          showReset.value = false
          showWait.value = false
          showInvalid.value = true
        }
      } catch (error) {
        console.error('Token verification error:', error)
        showReset.value = false
        showWait.value = false
        showInvalid.value = true
      }
    }

    const handleResetPassword = async () => {
      if (!formRef.value?.validate()) return

      try {
        const response = await resetPassword({
          password: password.value.trim(),
          token: route.value.query.token,
          password_confirmation: passwordConfirmation.value.trim(),
        })

        if (response.success) {
          toast.success('Password reset successfully')

          // Redirect to login after successful reset
          setTimeout(() => {
            router.push('/login')
          }, 2000)
        } else {
          toast.error('Something went wrong')
        }
      } catch (error) {
        toast.error(error?.response?.data?.message || 'Failed to reset password')
      }
    }

    onMounted(() => {
      checkToken()
    })

    return {
      showWait,
      showReset,
      showInvalid,
      formRef,
      password,
      passwordConfirmation,
      valid,
      passwordRules,
      passwordConfirmationRules,
      handleResetPassword,
      isSubmitting,
    }
  },
})
</script>

<style scoped>
/* Add any additional custom styles here */
</style>
