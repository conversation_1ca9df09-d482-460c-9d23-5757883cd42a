<script lang="ts">
// @ts-nocheck
import { defineComponent } from '@nuxtjs/composition-api'

import AppButton from '~/components/AppButton.vue'
import { HomeTo, HostDashboardData, User } from '~/types'
import MyCalendar from '~/components/MyCalendar.vue'

export default defineComponent({
  name: 'Home',

  components: {
    MyCalendar,
    AppButton,
  },

  middleware: ['auth'],

  data() {
    return {
      loading: true,
      isGuestUser: false,
      serverLoaded: false,
      showCalendar: false, // Add a flag to control MyCalendar visibility
    }
  },

  computed: {},

  watch: {
    page() {
      window.scrollTo(0, 0)
    },
  },

  watchQuery: [],

  mounted() {
    window.scrollTo(0, 0)
    // Delay loading MyCalendar by 3 seconds
    setTimeout(() => {
      this.showCalendar = true
      this.loading = false
    }, 2000)
  },

  methods: {
    decidePage(page: string) {
      let path = '/signup'

      if (this.isUserLoggedIn) {
        path = page === 'profile' ? '/profile' : page === 'home' ? '/homes' : path
      }

      this.$router.push({ path })
    },
  },
})
</script>

<template>
  <v-row no-gutters class="tw-max-w-full">
    <template>
      <!-- HOMES LOADING SKELETON -->
      <div
        v-if="loading"
        class="tw-fixed tw-top-0 tw-inset-0 tw-z-5 tw-bg-gray-50 tw-bg-opacity-80 tw-flex tw-items-center tw-justify-center tw-backdrop-blur-sm"
      >
        <div class="tw-flex tw-flex-col tw-items-center tw-gap-3">
          <v-progress-circular indeterminate color="primary" size="36" />
          <span class="tw-text-primary tw-font-medium">Loading Calendar...</span>
        </div>
      </div>

      <MyCalendar v-if="showCalendar" />
    </template>
  </v-row>
</template>

<style lang="scss" scoped></style>
