export default function ({ $cookies, redirect, route }) {
  const token = $cookies.get('token')
  const user = $cookies.get('user')

  // If not logged in, redirect to login
  if (!token || !user) {
    $cookies.remove('user')
    $cookies.remove('token')
    return redirect('/login')
  }

  // Routes that are accessible without email verification
  const emailVerificationWhitelist = [
    '/verify-email',
    '/logout',
    '/password/reset',
    '/password/forgot'
  ]

  // Check if the current route is in the whitelist
  const isWhitelistedRoute = emailVerificationWhitelist.some(path => {
    // Exact match or starts with the path (for routes with parameters)
    return route.path === path || route.path.startsWith(`${path}/`)
  })

  // Allow access to whitelisted routes
  if (isWhitelistedRoute) {
    return
  }

  // For all other routes, check if email is verified
  if (!user.email_verified_at) {
    // Store the original URL in localStorage for potential redirect after verification
    if (process.client) {
      localStorage.setItem('redirectAfterVerification', route.fullPath)
    }
    return redirect(`/verify-email?email=${encodeURIComponent(user.email)}`)
  }
}
