export const monthDict: Record<string, string> = {
  '01': 'January',
  '02': 'February',
  '03': 'March',
  '04': 'April',
  '05': 'May',
  '06': 'June',
  '07': 'July',
  '08': 'August',
  '09': 'September',
  '10': 'October',
  '11': 'November',
  '12': 'December',
}

export const getMonthName = (monthNumber: string): string => {
  return monthDict[monthNumber]
}

export const getTodaysDate = (): string => {
  const dt = new Date().toLocaleDateString().split('/')
  return `${dt[2]}-${dt[1].padStart(2, '0')}-${dt[0].padStart(2, '0')}`
}

export const getYesterdaysDate = (): string => {
  const today = new Date()
  const yesterday = new Date(today)
  yesterday.setDate(yesterday.getDate() - 1)
  const dt = yesterday.toLocaleDateString().split('/')
  return `${dt[2]}-${dt[1].padStart(2, '0')}-${dt[0].padStart(2, '0')}`
}

export const formatDate = (dt: string, format: 'ymd-hm' | 'ymd-hms'): string => {
  switch (format) {
    case 'ymd-hm': {
      const date = `${dt.split('T')[0]} ${dt.split('T')[1].slice(0, 5)}`
      return date
    }
    case 'ymd-hms': {
      const date = `${dt.split('T')[0]} ${dt.split('T')[1].slice(0, 8)}`
      return date
    }
  }
}

export const isProduction = (): boolean => {
  return window.location.hostname === 'twimo.com'
}

export const parseAddress = (address: string): { country: string; state: string; city: string } => {
  const parsed = address.split(',').map(part => part.trim())

  // Handle common case of "City, State, Country" format
  if (parsed.length === 3) {
    return {
      country: parsed[2],
      state: parsed[1],
      city: parsed[0],
    }
  }

  // Handle other cases
  return {
    country: parsed.length >= 1 ? parsed[parsed.length - 1] : '',
    state: parsed.length >= 2 ? parsed[parsed.length - 2] : '',
    city: parsed.length >= 3 ? parsed[parsed.length - 3] : '',
  }
}

export const unParseAddress = (country: string, state: string, city: string): string => {
  let fullAddress = ''

  if (city) {
    fullAddress += city
  }

  if (state) {
    if (fullAddress) fullAddress += ', '
    fullAddress += state
  }

  if (country) {
    if (fullAddress) fullAddress += ', '
    fullAddress += country
  }

  return fullAddress
}

export const formatText = (text: string): string => {
  if (text && text.length > 200) {
    let result = ''
    for (let i = 0; i < text.length; i++) {
      if (text[i] === ' ' && text[i + 1] === ' ') {
        result += `${text[i]}`
      } else {
        result += text[i]
      }
    }
    return result
  } else {
    return text
  }
}

export const getDateRange = (start: string, end: string): string[] => {
  const arr: string[] = []

  // Parse dates in a timezone-safe way
  // The dates are in YYYY-MM-DD format
  const parseDate = (dateStr: string): Date => {
    const [year, month, day] = dateStr.split('-').map(Number)
    return new Date(Date.UTC(year, month - 1, day))
  }

  const startDate = parseDate(start)
  const endDate = parseDate(end)

  // Clone the start date to avoid modifying it
  const dt = new Date(startDate.getTime())

  while (dt <= endDate) {
    // Format as YYYY-MM-DD
    arr.push(dt.toISOString().slice(0, 10))
    // Add one day (in milliseconds)
    dt.setUTCDate(dt.getUTCDate() + 1)
  }

  return arr
}

export const getRandomId = (): string => {
  return '#' + Math.floor(1000 + Math.random() * 9000)
}

export const getRandomToken = (length = 22): string => {
  let result = ''
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  const charactersLength = characters.length
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength))
  }
  return result
}

export const hasGoldMembership = ({
  memberships,
}: {
  memberships: { membership_type: string }[]
}): boolean => {
  return !!memberships.find(el => el.membership_type === 'gold')
}

export const hasSilverMembership = ({
  memberships,
}: {
  memberships: { membership_type: string }[]
}): boolean => {
  return !!memberships?.find(el => el.membership_type === 'silver')
}

export const convertStringToDateWithoutTimezone = (dateString: string): Date => {
  // Parse the date in a timezone-safe way
  // The dateString is in YYYY-MM-DD format
  const [year, month, day] = dateString.split('-').map(Number)

  // Use UTC to avoid timezone shifts
  // This ensures dates are interpreted exactly as provided without timezone adjustments
  return new Date(Date.UTC(year, month - 1, day))
}

export const getDatePrefix = (startDate: string, endDate: string): string => {
  const start = convertStringToDateWithoutTimezone(startDate)
  const end = convertStringToDateWithoutTimezone(endDate)

  if (start.getTime() === end.getTime()) {
    return 'Date'
  } else {
    return 'Dates'
  }
}

export const formatDateRange = (startDate: string, endDate: string): string => {
  // Parse dates in a timezone-safe way using UTC
  const start = convertStringToDateWithoutTimezone(startDate)
  const end = convertStringToDateWithoutTimezone(endDate)

  // Format dates using UTC to ensure consistent display regardless of user's timezone
  const startMonth = start.toLocaleString('default', { month: 'short', timeZone: 'UTC' })
  const startDay = start.getUTCDate().toString().padStart(2, '0')
  const endMonth = end.toLocaleString('default', { month: 'short', timeZone: 'UTC' })
  const endDay = end.getUTCDate().toString().padStart(2, '0')
  const startYear = start.getUTCFullYear()
  const endYear = end.getUTCFullYear()

  if (start.getTime() === end.getTime()) {
    return `${startMonth} ${startDay}, ${startYear}`
  }

  if (startMonth === endMonth && startYear === endYear) {
    return `${startMonth} ${startDay}-${endDay}, ${startYear}`
  }

  let formattedDateRange = `${startMonth} ${startDay}-${endMonth} ${endDay}`

  if (startYear === endYear) {
    formattedDateRange += `, ${startYear}`
  } else {
    formattedDateRange = `${startMonth} ${startDay}, ${startYear}-${endMonth} ${endDay}, ${endYear}`
  }

  return formattedDateRange
}

export const roundInt = (value: number): number => {
  return Math.round(value)
}

export const formatDateToDayMonthYear = (date: string): string => {
  return new Date(date).toLocaleDateString('en-GB')
}

export const formatDateToMonthDayYear = (date: string): string => {
  return new Date(date).toLocaleDateString('en-US')
}

export const convertDateStringFromDatePicker = (dateString: any): string => {
  const [year, month, day] = dateString.split('-')

  const date = new Date(+year, parseInt(month) - 1, parseInt(day))

  const newMonth = date.getMonth() + 1
  const newDay = date.getDate()
  const newYear = date.getFullYear()

  return `${newMonth}/${newDay}/${newYear}`
}

export const formatDateTimeToMonthDayYear = (date: string | Date): string => {
  return new Date(date).toLocaleString('en-US')
}

export const formatDateTimeToDayMonthYear = (date: string): string => {
  return new Date(date).toLocaleString('en-GB')
}

export const formatNumberToDisplay = (number: any): string => {
  if (!number) return '0'

  return roundInt(number)
    .toString()
    .replace(/\D/g, '')
    .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}

export const getStripeUrl = (
  token: string,
  chosenPointsOption: string,
  isDefault = false
): string => {
  const baseUrl = 'https://buy.stripe.com/'

  const productIds = {
    '5$': '00g6rFcdW4Gp0WQ8wD',
    '10,000: $150': '14kaHV7XG0q9axqbIK',
    '50,000 + 1,500 Bonus Points: $750': 'bIYg2ffq82yhgVO4gj',
    '100,000 + 3,000 Bonus Points: $1,500': 'bIYcQ36TC2yh8pi3ci',
    '250,000 + 7,500 Bonus Points: $3,750': '5kAbLZcdW7SBbBufZ3',
  } as const

  const testProductId = !isDefault ? 'test_fZe9BCa989o44k89AA' : 'test_6oE4hi6WW43KbMAfYZ'

  const isLocal = ['localhost', 'qa.the48dots.com'].includes(window.location.hostname)

  const productId = isLocal ? testProductId : productIds[chosenPointsOption]

  return `${baseUrl}${productId}?utm_medium=${token}`
}

export const generateUuid = (): string => {
  const s4 = (): string => {
    return Math.floor((1 + Math.random()) * 0x10000)
      .toString(16)
      .substring(1)
  }

  return `${s4()}-${s4()}-${s4()}-${s4()}-${s4()}-${s4()}-${s4()}`
}

export const convertIcsToWebcal = (icsUrl: string): string => {
  return icsUrl.replace(/^http(s?):\/\//i, 'webcal://')
}

export const capitalizeFirstLetter = (string: string): string => {
  return string.charAt(0).toUpperCase() + string.slice(1).toLowerCase()
}

export const humanize = (str: string): string => {
  return str
    .replace(/([a-z])([A-Z])/g, '$1 $2')
    .replace(/_/g, ' ')
    .replace(/\b\w/g, char => char.toUpperCase())
}

export const deepClone = (obj: any): any => {
  return JSON.parse(JSON.stringify(obj))
}

export const slugify = (text: null | string): string => {
  if (!text) return ''
  return text
    .toString()
    .toLowerCase()
    .replace(/\s+/g, '-')
    .replace(/[^\w-]+/g, '')
    .replace(/--+/g, '-')
    .replace(/^-+/, '')
    .replace(/-+$/, '')
}

export const debounce = (func, delay) => {
  let timeoutId
  return (...args) => {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => func(...args), delay)
  }
}

export const checkExistingSlug = (str: string, slug: string): boolean => {
  return slugify(str) === slug
}

export const isValidAirbnbUrl = (url: string): boolean => {
  const pattern = /^(https?:\/\/)?(www\.)?airbnb\.(com|com\.[a-z]{2}|[a-z]{2}|ca|co\.[a-z]{2})(\/.*)?\/rooms\/\d+/i
  return pattern.test(url)
}

export const normalizeAirbnbUrl = (url: string): string => {
  const match = url.match(/airbnb\.(com|com\.[a-z]{2}|[a-z]{2}|ca|co\.[a-z]{2})\/rooms\/(\d+)/i)
  if (match && match[2]) {
    return `https://www.airbnb.com/rooms/${match[2]}`
  }
  return url
}
