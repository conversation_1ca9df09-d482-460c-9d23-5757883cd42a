{"name": "twimo.com", "version": "1.0.0", "private": true, "scripts": {"dev": "nuxt", "build": "nuxt build", "start": "nuxt start", "lint": "eslint --fix --ext .js,.vue --ignore-path .gitignore .", "format": "prettier --write \"**/*.{js,ts,vue,json,css,scss,md}\""}, "dependencies": {"@fullcalendar/core": "^6.1.17", "@fullcalendar/daygrid": "^6.1.17", "@fullcalendar/interaction": "^6.1.17", "@fullcalendar/multimonth": "^6.1.17", "@fullcalendar/timegrid": "^6.1.17", "@fullcalendar/vue": "^6.1.17", "@nuxtjs/axios": "^5.13.6", "@nuxtjs/composition-api": "^0.34.0", "@nuxtjs/recaptcha": "^1.1.2", "@nuxtjs/robots": "2.5.0", "@nuxtjs/sentry": "^8.0.8", "@nuxtjs/toast": "^3.3.1", "@pinia/nuxt": "^0.2.1", "@vueuse/core": "^11.3.0", "cookie-universal-nuxt": "^2.2.2", "nuxt": "^2.18.1", "nuxt-leaflet": "^0.0.27", "nuxt-stripe-module": "^3.2.0", "pinia": "^2.3.1", "qrcode.vue": "^1.7.0", "swiper": "^6.8.4", "vue-awesome-swiper": "^4.1.1"}, "devDependencies": {"@mdi/font": "^7.4.47", "@nuxt/types": "^2.18.1", "@nuxt/typescript-build": "^3.0.2", "@nuxtjs/eslint-config-typescript": "^12.1.0", "@nuxtjs/eslint-module": "^3.1.0", "@nuxtjs/sitemap": "2.4.0", "@nuxtjs/tailwindcss": "^6.14.0", "@nuxtjs/vuetify": "^1.12.3", "@typescript-eslint/eslint-plugin": "^8.33.1", "@typescript-eslint/parser": "^8.33.1", "eslint": "^8.57.1", "eslint-config-prettier": "^10.1.5", "eslint-import-resolver-typescript": "^3.10.1", "eslint-plugin-nuxt": "^4.0.0", "eslint-plugin-vue": "^10.1.0", "hard-source-webpack-plugin": "^0.13.1", "image-webpack-loader": "^8.1.0", "nuxt-compress": "^5.0.0", "prettier": "^3.5.3", "typescript": "^5.8.3"}}