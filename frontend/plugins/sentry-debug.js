// plugins/sentry-debug.js
export default ({ app, $sentry, route, store }) => {
  if (!$sentry) return

  // Add breadcrumbs for navigation
  app.router.beforeEach((to, from, next) => {
    if ($sentry) {
      $sentry.addBreadcrumb({
        category: 'navigation',
        message: `Navigation from ${from.fullPath} to ${to.fullPath}`,
        level: 'info',
      })
    }
    next()
  })

  // Track router errors
  app.router.onError(error => {
    if ($sentry) {
      $sentry.captureException(error, {
        tags: {
          source: 'router',
          route: route.fullPath,
        },
      })
    }
  })

  // Track Vuex mutations for better debugging context
  if (store) {
    store.subscribe(mutation => {
      if ($sentry && process.env.NODE_ENV !== 'production') {
        $sentry.addBreadcrumb({
          category: 'vuex',
          message: `Mutation: ${mutation.type}`,
          level: 'debug',
        })
      }
    })
  }

  // Enhance error reporting with additional context
  if (app.context.$logError) {
    const originalLogError = app.context.$logError

    app.context.$logError = (error, context = {}) => {
      // Call the original error logger
      originalLogError(error, context)

      // Add enhanced context to Sentry
      if ($sentry) {
        $sentry.withScope(scope => {
          // Add tags for filtering in Sentry
          if (context.tags) {
            Object.entries(context.tags).forEach(([key, value]) => {
              scope.setTag(key, value)
            })
          }

          // Add extra context data
          if (context.extra) {
            scope.setExtras(context.extra)
          }

          // Set error level
          if (context.level) {
            scope.setLevel(context.level)
          }

          // Send to Sentry
          $sentry.captureException(error)
        })
      }
    }
  }

  // Add a debug helper
  app.context.$debug = (message, data = {}) => {
    if (process.env.NODE_ENV === 'development' || process.env.DEBUG === 'true') {
      console.debug(`[DEBUG] ${message}`, data)

      // Add as breadcrumb in Sentry for tracing user actions
      if ($sentry) {
        $sentry.addBreadcrumb({
          category: 'debug',
          message,
          data,
          level: 'debug',
        })
      }
    }
  }
}
