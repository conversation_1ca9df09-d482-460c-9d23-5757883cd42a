// plugins/auth-persist.js
export default async function ({ store, app, $axios }) {
  // When app initializes
  const token = app.$cookies.get('token')

  // If we have token in cookie but not logged in in the store
  if (token && !store.state.auth.isLoggedIn) {
    try {
      // Set preliminary logged in state to ensure axios can use the token
      store.commit('auth/setLoggedIn', true)

      // Dispatch action to get fresh user data from API
      await store.dispatch('auth/getUserDetails')

      // The getUserDetails action will handle setting the user and confirming login state
      // If it fails, it will reset the auth state automatically
    } catch (error) {
      // If any error occurs, log out
      store.commit('auth/logoutWithoutRedirect')
      console.error('Error restoring auth state:', error)
    }
  }
  // If no token in cookies but we're logged in, log out
  else if (!token && store.state.auth.isLoggedIn) {
    store.commit('auth/logoutWithoutRedirect')
  }
}
