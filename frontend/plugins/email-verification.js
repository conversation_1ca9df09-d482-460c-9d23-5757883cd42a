// plugins/email-verification.js
export default function ({ app, store, redirect, route }) {
  // Add a global navigation guard
  app.router.beforeEach((to, from, next) => {
    // Get user from cookies
    const user = app.$cookies.get('user')
    const token = app.$cookies.get('token')

    // If not logged in, proceed normally (auth middleware will handle login redirects)
    if (!token || !user) {
      return next()
    }

    // Routes that are accessible without email verification
    const emailVerificationWhitelist = [
      '/verify-email',
      '/logout',
      '/password/reset',
      '/password/forgot',
      '/login',
      '/signup'
    ]

    // Check if the current route is in the whitelist
    const isWhitelistedRoute = emailVerificationWhitelist.some(path => {
      // Exact match or starts with the path (for routes with parameters)
      return to.path === path || to.path.startsWith(`${path}/`)
    })

    // Special handling for protected routes with sharable links
    const isProtectedRoute = to.path.includes('/protected') && to.query.sharable_link

    // If email is not verified and trying to access a protected route
    if (!user.email_verified_at && !isWhitelistedRoute && !isProtectedRoute) {
      // Store the original URL in localStorage for potential redirect after verification
      if (process.client) {
        // Special handling for booking pages
        if (to.path.includes('/bookings/')) {
          console.log('Email verification plugin: Detected booking flow, storing path with high priority:', to.fullPath)
          // Store in both localStorage keys for redundancy
          localStorage.setItem('redirectAfterVerification', to.fullPath)
          localStorage.setItem('twimo_auth_redirect', to.fullPath)
        } else {
          // For non-booking pages, just store normally
          localStorage.setItem('redirectAfterVerification', to.fullPath)
          localStorage.setItem('twimo_auth_redirect', to.fullPath)
          console.log('Email verification plugin: Setting redirect path:', to.fullPath)
        }
      }
      return next(`/verify-email?email=${encodeURIComponent(user.email)}`)
    }

    // Otherwise proceed normally
    return next()
  })
}
