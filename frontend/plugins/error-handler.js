// plugins/error-handler.js
import Vue from 'vue'

export default ({ app, $sentry }, inject) => {
  // Configure Vue error handling directly on the Vue object
  Vue.config.errorHandler = (err, vm, info) => {
    console.error('Vue Error Handler:', err, info)

    // Send to Sentry if available
    if (process.client && $sentry) {
      // Gather component information
      const componentData = {
        componentName: vm?.$options?.name || 'AnonymousComponent',
        lifecycleHook: info,
        propsData: vm?.$options?.propsData || {},
      }

      $sentry.withScope(scope => {
        // Add component context
        scope.setContext('vue', componentData)

        // Add component hierarchy for better debugging
        if (vm) {
          let componentTrace = ''
          let currentVm = vm
          while (currentVm) {
            const name = currentVm.$options?.name || 'AnonymousComponent'
            componentTrace += `\n-> <${name}>`
            currentVm = currentVm.$parent
          }
          scope.setContext('componentTrace', { value: componentTrace })
        }

        // Add route information
        if (vm?.$route) {
          scope.setTag('route', vm.$route.fullPath)
        }

        $sentry.captureException(err)
      })
    }
  }

  // Catch and log Vue warnings
  Vue.config.warnHandler = (msg, vm, trace) => {
    console.warn('Vue Warning:', msg)
    console.log(trace)

    // For important warnings, log them to Sentry as well
    if (
      $sentry &&
      (msg.includes('Failed to mount component') ||
        msg.includes('Unknown custom element') ||
        msg.includes('Invalid prop'))
    ) {
      $sentry.captureMessage(`Vue Warning: ${msg}`, 'warning')
    }
  }

  // Add enhanced error logging method
  inject('logError', (error, context = {}) => {
    console.error(`[App Error]: ${error.message || error}`, context)

    // Send to Sentry
    if ($sentry) {
      $sentry.withScope(scope => {
        // Add context information
        if (context.tags) {
          Object.entries(context.tags).forEach(([key, value]) => {
            scope.setTag(key, value)
          })
        }

        if (context.extra) {
          scope.setExtras(context.extra)
        }

        if (context.level) {
          scope.setLevel(context.level)
        }

        // Add user information if available
        if (app.$store?.state?.auth?.user) {
          const user = app.$store.state.auth.user
          scope.setUser({
            id: user.id,
            email: user.email,
            username: user.name || user.username,
          })
        }

        $sentry.captureException(error)
      })
    }
  })

  // Add debug helper
  inject('debug', (message, data = {}) => {
    if (process.env.NODE_ENV === 'development') {
      console.debug(`[DEBUG] ${message}`, data)

      // Add as breadcrumb in Sentry for tracing user actions
      if ($sentry) {
        $sentry.addBreadcrumb({
          category: 'debug',
          message,
          data,
          level: 'debug',
        })
      }
    }
  })
}
