export default function ({ app }, inject) {
  if (process.client) {
    // Leaflet is loaded via the nuxt-leaflet module
    // This is just a compatibility layer for components that expect googleMapsLoader
    const loadLeaflet = callback => {
      // Leaflet should already be available via the nuxt-leaflet module
      // Just call the callback directly
      callback()
    }

    inject('googleMapsLoader', loadLeaflet)
  }
}
