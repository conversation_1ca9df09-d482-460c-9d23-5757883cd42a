export default ({ $axios, $cookies, store }, inject) => {
  // Add axios default config to handle SSL certificate issues
  if (process.env.NODE_ENV === 'development') {
    $axios.defaults.httpsAgent = new (require('https').Agent)({
      rejectUnauthorized: false, // Only use this in development!
    })
  }

  // Set token on every request
  $axios.onRequest(() => {
    const token = $cookies.get('token')
    if (token) {
      $axios.setToken(token, 'Bearer')
    }
  })

  // Handle unauthorized responses
  $axios.onError(error => {
    if (error.response && error.response.status === 401) {
      // Token is invalid or expired
      store.commit('auth/logoutWithoutRedirect')
    }
    return Promise.reject(error)
  })

  inject('$axios', $axios)
}
