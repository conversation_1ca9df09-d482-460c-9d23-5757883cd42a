import { INACTIVITY_TIMEOUT } from '~/constants'

export default function ({ store, app }) {
  // Only run on client-side
  if (!process.client) return

  // Skip setup if we're in development mode
  if (process.env.NODE_ENV === 'development') return

  let inactivityTimeout

  // Simple function to reset the inactivity timer
  const resetInactivity = () => {
    // Clear any existing timeout
    clearTimeout(inactivityTimeout)

    // Set a new timeout
    inactivityTimeout = setTimeout(() => {
      // Only logout if we're still on a page that requires auth
      // This prevents logout during public browsing
      const currentPath = app.router.currentRoute.path
      const publicPaths = ['/login', '/signup', '/explore', '/', '/homes']

      // Skip logout if we're on a public page
      if (publicPaths.some(path => currentPath.startsWith(path))) {
        console.log('On public page, skipping auto-logout')
        resetInactivity() // Reset the timer instead
        return
      }

      console.log('Inactivity timeout reached, logging out')
      store.dispatch('auth/logout')
    }, INACTIVITY_TIMEOUT)
  }

  // Add event listeners for user activity
  // Using passive listeners for better performance
  ;['mousemove', 'keydown', 'scroll', 'click'].forEach(event => {
    window.addEventListener(event, resetInactivity, { passive: true })
  })

  // Start the inactivity timer
  resetInactivity()

  // Clean up when the app is destroyed
  window.addEventListener('beforeunload', () => {
    clearTimeout(inactivityTimeout)
  })
}
