@import '@mdi/font/css/materialdesignicons.css';
@tailwind base;
@tailwind components;
@tailwind utilities;

html,
body {
  overflow-x: clip;
}

/* Page transitions */
.page-enter-active,
.page-leave-active {
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.page-enter {
  opacity: 0;
  transform: translateY(-20px);
}

.page-leave-to {
  opacity: 0;
  transform: translateY(20px);
}

/* Layout transitions */
.layout-enter-active,
.layout-leave-active {
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.layout-enter,
.layout-leave-to {
  opacity: 0;
  transform: translateY(20px);
}

.zpcookie-message-bar {
  font-family: 'Figtree', sans-serif;
}

.zpcustom-layout-2.zpdark {
  background-color: rgb(0 0 0 / 90%) !important;
}

.zpcustom-layout-2 .zpcookie-action-buttons.zpbutton-type-primary {
  background: conic-gradient(
    from 90deg at 50% 50%,
    rgba(56, 11, 115, 0.9) -124.46deg,
    #7c0cb1 73.63deg,
    rgba(56, 11, 115, 0.9) 235.54deg,
    #7c0cb1 433.63deg
  );
  font-weight: bold;
}

#zpcookie-banner {
  max-width: 280px;
  right: 100px;
  left: auto;
  bottom: 2vh;
}

.msg-left-align p {
  flex-direction: column;
}

/* Toast container styling */
.toast-container {
  z-index: 9999;
  max-width: 420px;
  min-width: 300px;
  bottom: 80px !important;
  position: fixed;
}

/* Position adjustment for mobile devices */
@media (max-width: 768px) {
  .toast-container {
    bottom: 100px !important;
  }
}

/* Customize all toasts */
.toasted.my-custom-toast {
  font-family: 'Figtree', sans-serif;
  border-radius: 12px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.12);
  padding: 16px 20px;
  display: flex;
  align-items: center;
  gap: 12px;
  transition: all 0.3s ease;
  opacity: 0.98;
  margin-bottom: 10px;
  backdrop-filter: blur(10px);
  max-width: 100%;
  overflow: hidden;
  line-height: 1.5;
  font-size: 14px;
  font-weight: 500;
}

.toasted.my-custom-toast:hover {
  opacity: 1;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

/* Toast icons */
.toasted.my-custom-toast i {
  font-size: 20px;
  margin-right: 8px;
}

/* Customize success toasts */
.toasted.my-success-toast {
  background-color: rgba(46, 213, 115, 0.95) !important;
  border-left: 4px solid #2ed573;
  color: #fff;
}

/* Customize error toasts */
.toasted.my-error-toast {
  background-color: rgba(255, 71, 87, 0.95) !important;
  border-left: 4px solid #ff4757;
  color: #fff;
}

/* Customize info toasts */
.toasted.my-info-toast {
  background-color: rgba(48, 151, 255, 0.95) !important;
  border-left: 4px solid #3097ff;
  color: #fff;
}

/* Customize warning toasts */
.toasted.my-warning-toast {
  background-color: rgba(255, 168, 38, 0.95) !important;
  border-left: 4px solid #ffa826;
  color: #fff;
}

/* Match your brand color for primary toasts */
.toasted.toasted-primary:not(.my-success-toast):not(.my-error-toast):not(.my-info-toast):not(
    .my-warning-toast
  ) {
  background-color: rgba(112, 25, 153, 0.95) !important;
  border-left: 4px solid #9333ea;
  color: #fff;
}

/* Style the close action */
.toasted .action {
  color: rgba(255, 255, 255, 0.9);
  font-weight: 600;
  text-decoration: none;
  margin-left: 12px;
  background: rgba(255, 255, 255, 0.15);
  padding: 4px 10px;
  border-radius: 8px;
  transition: all 0.2s ease;
  font-size: 13px;
}

.toasted .action:hover {
  background: rgba(255, 255, 255, 0.25);
  color: #ffffff;
}

/* Toast animations */
@keyframes toast-in-right {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes toast-in-left {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.toasted.my-custom-toast {
  animation: toast-in-right 0.3s ease-out forwards;
}

/* Accessibility improvements */
.toasted.my-custom-toast {
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
}

.toasted.my-custom-toast .action:focus {
  outline: 2px solid white;
  outline-offset: 2px;
}
