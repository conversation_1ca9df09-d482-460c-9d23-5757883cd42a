<template>
  <v-app>
    <NoScript />

    <v-main class="">
      <PageHeader />

      <Nuxt />

      <PageFooter />
    </v-main>
  </v-app>
</template>

<script>
export default {
  components: {
    PageHeader: () => import('~/components/PageHeader.vue'),
    PageFooter: () => import('~/components/PageFooter.vue'),
    NoScript: () => import('~/components/NoScript.vue'),
  },

  computed: {},
}
</script>

<style scoped></style>
