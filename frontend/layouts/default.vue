<script lang="ts">
/// @ts-nocheck
import { defineComponent } from '@nuxtjs/composition-api'

import PageFooter from '~/components/PageFooter.vue'
import AppHeader from '~/components/AppHeader.vue'
import screenCheckerMixins from '~/mixins/screenChecker.mixins'
import { useAuthStore } from '~/composables/useAuthStore'

export default defineComponent({
  components: {
    AppHeader,
    PageFooter,
  },

  mixins: [screenCheckerMixins],

  setup() {
    const { isHostType } = useAuthStore()
    return {
      isHostType,
    }
  },

  computed: {
    isFullScreen() {
      return (this.isHostType && this.isAtHomePage) || this.isAtAuthPages
    },

    isAtHomePage() {
      return this.$route.path === '/' // this.$route.path === '/home' ||
    },

    isAtAuthPages() {
      return (
        this.$route.path === '/login' ||
        this.$route.path === '/signup' ||
        this.$route.path === '/vendor_signup' ||
        this.$route.path.includes('/protected')
      )
    },

    containerClass() {
      if (this.isFullScreen) {
        return 'tw-max-w-full tw-mt-0 tw-p-0'
      }

      return this.isDesktop ? 'tw-pt-[50px] container-class' : 'container-class'
    },
  },
})
</script>

<template>
  <v-app>
    <v-main>
      <app-header />

      <v-container fluid :class="containerClass" class="tw-min-h-[calc(100vh)]">
        <v-row>
          <v-col cols="12">
            <Nuxt />
          </v-col>
        </v-row>
      </v-container>

      <PageFooter />
    </v-main>
  </v-app>
</template>

<style scoped>
.container-class {
  padding-left: 24px !important;
  padding-right: 24px !important;
  max-width: 100% !important;
}

@media (min-width: 1440px) {
  .container-class {
    padding-left: 64px !important;
    padding-right: 64px !important;
  }
}

@media (min-width: 1600px) {
  .container-class {
    padding-left: 80px !important;
    padding-right: 80px !important;
  }
}

@media (min-width: 1800px) {
  .container-class {
    padding-left: 96px !important;
    padding-right: 96px !important;
  }
}

@media (min-width: 2000px) {
  .container-class {
    padding-left: 120px !important;
    padding-right: 120px !important;
    max-width: 2000px !important;
    margin-left: auto !important;
    margin-right: auto !important;
  }
}
</style>
