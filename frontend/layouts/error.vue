<template>
  <div
    class="tw-min-h-screen tw-flex tw-flex-col tw-items-center tw-justify-center tw-py-12 tw-px-4 tw-sm:px-6 tw-lg:px-8"
  >
    <!-- Error status code -->
    <h1
      v-if="statusCode === 404"
      class="tw-text-center tw-text-8xl tw-font-bold tw-text-purple-800"
    >
      404
    </h1>
    <h1 v-else class="tw-text-center tw-text-8xl tw-font-bold tw-text-purple-800">
      {{ statusCode }}
    </h1>

    <!-- Error animation -->
    <div
      v-if="statusCode === 404"
      class="tw-w-full tw-max-w-md tw-h-64 tw-bg-cover tw-bg-center tw-mt-6"
      style="
        background-image: url(https://cdn.dribbble.com/users/285475/screenshots/2083086/dribbble_1.gif);
      "
    ></div>

    <div v-else class="tw-w-full tw-max-w-md tw-my-8">
      <img
        src="/images/error.svg"
        alt="Error illustration"
        class="tw-w-full tw-h-auto"
        onerror="this.onerror=null; this.src='data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyMDAgMjAwIiBmaWxsPSIjZDFkNWRiIj48cGF0aCBkPSJNMTgwIDEwMGMwIDQ0LjEtMzUuOSA4MC04MCA4MHMtODAtMzUuOS04MC04MCAzNS45LTgwIDgwLTgwIDgwIDM1LjkgODAgODB6bS04MC03MGMtMzguNyAwLTcwIDMxLjMtNzAgNzBzMzEuMyA3MCA3MCA3MCA3MC0zMS4zIDcwLTcwLTMxLjMtNzAtNzAtNzB6bTIwIDkwaC00MGExMCAxMCAwIDAxMC0yMGg0MGExMCAxMCAwIDAxMCAyMHptLTQtNTBhMTUgMTUgMCAxMS0zMCAwIDE1IDE1IDAgMDEzMCAweiIvPjwvc3ZnPg=='"
      />
    </div>

    <!-- Error message -->
    <div class="tw-mt-6 tw-text-center">
      <h2 class="tw-text-2xl tw-font-medium tw-text-gray-900">
        {{ errorTitle }}
      </h2>
      <p class="tw-mt-2 tw-text-base tw-text-gray-600">
        {{ errorDescription }}
      </p>
    </div>

    <!-- Action button -->
    <div class="tw-mt-8">
      <a href="/" class="tw-no-underline">
        <app-button :large="true" button-text="Go Home" />
      </a>
    </div>

    <!-- Sentry Feedback (in production and if error is not 404) -->
    <div v-if="showFeedbackForm" class="tw-w-full tw-max-w-lg tw-mt-12">
      <v-expansion-panels>
        <v-expansion-panel>
          <v-expansion-panel-header class="tw-bg-purple-50">
            <div class="tw-flex tw-items-center">
              <v-icon color="purple darken-2" class="tw-mr-2">mdi-message-text-outline</v-icon>
              <span class="tw-font-medium tw-text-purple-800">Help us improve</span>
            </div>
          </v-expansion-panel-header>
          <v-expansion-panel-content class="tw-bg-purple-50 tw-bg-opacity-50">
            <v-form ref="form" v-model="valid" lazy-validation class="tw-mt-4">
              <p class="tw-text-gray-600 tw-mb-4">
                We'd appreciate if you could tell us what happened so we can fix it faster.
              </p>

              <v-textarea
                v-model="userFeedback"
                outlined
                label="What were you trying to do?"
                :rules="[v => !!v || 'Your feedback helps us improve']"
                class="tw-mb-4"
                rows="3"
                hide-details="auto"
                placeholder="I was trying to..."
              ></v-textarea>

              <v-text-field
                v-model="userEmail"
                outlined
                label="Your email (optional)"
                hint="We'll let you know when this is fixed"
                :rules="[v => !v || /.+@.+\..+/.test(v) || 'Email must be valid']"
                class="tw-mb-6"
                placeholder="<EMAIL>"
              ></v-text-field>

              <div class="tw-flex tw-justify-end">
                <v-btn
                  color="primary"
                  :disabled="!valid"
                  :loading="submitting"
                  class="tw-bg-purple-700 tw-text-white"
                  @click="submitFeedback"
                >
                  Submit Feedback
                </v-btn>
              </div>
            </v-form>
          </v-expansion-panel-content>
        </v-expansion-panel>
      </v-expansion-panels>
    </div>

    <!-- Technical details for development environment -->
    <div
      v-if="showErrorDetails"
      class="tw-w-full tw-max-w-lg tw-mt-8 tw-bg-gray-50 tw-rounded-lg tw-p-4 tw-shadow-sm"
    >
      <div class="tw-flex tw-justify-between tw-items-center">
        <h3 class="tw-text-sm tw-font-medium tw-text-gray-900">Technical Details</h3>
        <v-btn small text icon color="grey" @click="toggleDetails">
          <v-icon>{{ detailsExpanded ? 'mdi-chevron-up' : 'mdi-chevron-down' }}</v-icon>
        </v-btn>
      </div>

      <div v-if="detailsExpanded" class="tw-mt-2">
        <pre
          class="tw-bg-gray-800 tw-text-green-400 tw-p-4 tw-rounded tw-text-xs tw-overflow-x-auto"
          >{{ errorInfo }}</pre
        >

        <div class="tw-mt-4 tw-text-xs tw-text-gray-500">
          <p>Error ID: {{ sentryEventId || 'Not available' }}</p>
          <p>Time: {{ currentTime }}</p>
          <p>Path: {{ currentPath }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ErrorPage',
  layout: 'empty',
  props: {
    error: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      userFeedback: '',
      userEmail: '',
      valid: true,
      submitting: false,
      sentryEventId: null,
      pageNotFound: '404 Not Found',
      otherError: 'An error occurred',
      detailsExpanded: false,
      currentTime: new Date().toISOString(),
    }
  },
  head() {
    const title = this.statusCode === 404 ? this.pageNotFound : this.otherError
    return {
      title,
      meta: [{ name: 'robots', content: 'noindex, nofollow' }],
    }
  },
  computed: {
    statusCode() {
      return (this.error && this.error.statusCode) || 500
    },
    sentryEnabled() {
      return !!this.$sentry
    },
    errorTitle() {
      switch (this.statusCode) {
        case 404:
          return 'Page Not Found'
        case 403:
          return 'Access Forbidden'
        case 401:
          return 'Authentication Required'
        case 400:
          return 'Bad Request'
        default:
          return 'Something went wrong'
      }
    },
    errorDescription() {
      switch (this.statusCode) {
        case 404:
          return "Sorry, we couldn't find the page you're looking for."
        case 403:
          return "You don't have permission to access this page."
        case 401:
          return 'Please log in to access this page.'
        case 400:
          return "The request couldn't be processed. Please try again."
        default:
          return "We've encountered an unexpected error. Our team has been notified."
      }
    },
    showErrorDetails() {
      return process.env.NODE_ENV !== 'production' || (this.$config && this.$config.debug)
    },
    showFeedbackForm() {
      return this.sentryEnabled && this.statusCode !== 404 && process.client
    },
    currentPath() {
      return process.client ? window.location.pathname : this.$route ? this.$route.path : 'unknown'
    },
    errorInfo() {
      if (!this.error) return 'No error information available'

      const info = {
        statusCode: this.statusCode,
        message: this.error.message || 'No error message available',
        name: this.error.name,
        stack:
          process.env.NODE_ENV !== 'production' && this.error.stack
            ? this.error.stack.split('\n').slice(0, 8).join('\n')
            : undefined,
      }

      return JSON.stringify(info, null, 2)
    },
  },
  mounted() {
    if (this.error && this.$sentry && this.statusCode !== 404) {
      // Capture the error in Sentry if it hasn't been captured yet
      this.$sentry.withScope(scope => {
        scope.setLevel('error')
        scope.setTag('source', 'error-page')
        scope.setTag('statusCode', this.statusCode)
        scope.setTag('path', this.currentPath)

        // Capture the event and store the ID for user feedback
        this.sentryEventId = this.$sentry.captureException(this.error)
      })
    }
  },
  methods: {
    toggleDetails() {
      this.detailsExpanded = !this.detailsExpanded
    },
    submitFeedback() {
      if (!this.$refs.form.validate() || !this.$sentry || !this.sentryEventId) {
        return
      }

      this.submitting = true

      // Send user feedback to Sentry
      this.$sentry.showReportDialog({
        eventId: this.sentryEventId,
        user: {
          email: this.userEmail || undefined,
          name: undefined,
        },
        title: 'Tell us what happened',
        subtitle: 'Your feedback helps us improve our service',
        subtitle2: '',
        labelName: 'Name',
        labelEmail: 'Email',
        labelComments: 'What happened?',
        labelClose: 'Close',
        labelSubmit: 'Submit',
        errorGeneric: 'An unknown error occurred while submitting your report. Please try again.',
        errorFormEntry: 'Some fields were invalid. Please correct the errors and try again.',
        successMessage: 'Your feedback has been sent. Thank you!',
        onLoad: () => {
          // Hide the form once Sentry's dialog is shown
          this.submitting = false
          this.userFeedback = ''
          this.userEmail = ''
        },
      })
    },
  },
}
</script>

<style scoped>
/* Add any additional styles that Tailwind can't handle */
.v-expansion-panel::before {
  box-shadow: none !important;
}

.v-expansion-panels {
  box-shadow:
    0 4px 6px -1px rgba(112, 25, 153, 0.1),
    0 2px 4px -1px rgba(112, 25, 153, 0.06) !important;
  border-radius: 8px !important;
}
</style>
