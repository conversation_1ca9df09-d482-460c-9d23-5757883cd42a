export const state = () => ({
  isLoading: false,
  housesData: [],
  mainData: {},
  progress: null,
  tableHeaders: [
    {
      text: 'LISTINGS',
      align: 'start',
      sortable: false,
      filterable: false,
      value: 'home_image',
      cols: '2',
    },
    {
      text: 'AVAILABILITY',
      align: 'start',
      sortable: false,
      filterable: false,
      value: 'availability',
      cols: '6',
    },
    {
      text: 'AVAILABILITY',
      align: 'center',
      sortable: false,
      filterable: false,
      value: 'availability',
      cols: '2',
    },
    // {
    //   text: "DASHBOARD",
    //   align: "start",
    //   sortable: false,
    //   filterable: false,
    //   value: "actions",
    //   cols: "2"
    // }
  ],
})

export const mutations = {
  startLoading(state) {
    state.isLoading = true
  },
  stopLoading(state) {
    state.isLoading = false
  },
  setHousesData(state, payload) {
    state.housesData = payload
  },
  setProgress(state, payload) {
    state.progress = payload
  },
  addDate(state, payload) {
    state.housesData[payload.index].available_dates.push(payload.dateData)
  },
}

export const actions = {
  async getHouses({ commit }) {
    const { data } = await this.$axios.get('home/user/list/my')

    commit('setHousesData', data)

    commit('stopLoading')

    return data
  },

  async getCompletionProgress({ commit }) {
    commit('startLoading')
    const {
      data: { completion },
    } = await this.$axios.get('home/user/completion')
    commit('setProgress', completion)
    commit('stopLoading')
    return completion
  },
}

export const getters = {
  isLoading: state => state.isLoading,
  getHousesData: state => state.housesData,
  getTableHeaders: state => state.tableHeaders,
  getProgress: state => state.progress,
  getMainData: state => state.mainData,
}
