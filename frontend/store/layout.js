export const state = () => ({
  drawer: true,
  theme: false,
  mobileDrawer: false,
})

export const mutations = {
  toggleDrawer(state) {
    state.drawer = !state.drawer
  },
  toggleTheme(state) {
    state.theme = !state.theme
  },
  toggleMobileDrawer(state) {
    state.mobileDrawer = true
  },
}

export const actions = {
  TOGGLE_DRAWER({ commit }) {
    commit('toggleDrawer')
  },
  TOGGLE_THEME({ commit }) {
    commit('toggleTheme')
  },
}

export const getters = {
  DRAWER_STATE(state) {
    return state.drawer
  },
  THEME_STATE(state) {
    return state.theme
  },
  mobileDrawer: state => state.mobileDrawer,
}
