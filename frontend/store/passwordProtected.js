export const state = () => ({
  isPasswordValidated: false,
  currentSharableLink: null,
  validatedLinks: new Set(), // Track which links have been validated
})

export const mutations = {
  setPasswordValidated(state, value) {
    state.isPasswordValidated = value
  },

  setCurrentSharableLink(state, link) {
    state.currentSharableLink = link
  },

  addValidatedLink(state, link) {
    if (link) {
      state.validatedLinks.add(link)
    }
  },

  removeValidatedLink(state, link) {
    if (link) {
      state.validatedLinks.delete(link)
    }
  },

  clearValidatedLinks(state) {
    state.validatedLinks.clear()
  },

  resetState(state) {
    state.isPasswordValidated = false
    state.currentSharableLink = null
    state.validatedLinks.clear()
  },
}

export const getters = {
  isLinkValidated: (state) => (link) => {
    return state.validatedLinks.has(link)
  },
}
