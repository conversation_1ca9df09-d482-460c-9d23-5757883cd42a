import { FETCH_NOTIFICATIONS_INTERVAL, PROCESS_NOTIFICATIONS_INTERVAL } from '~/constants'

export const state = () => ({
  notifications: [],
  archivedNotifications: [],
  unreadCount: 0,
  loading: false,
  error: null,
  activeTab: 'all', // 'all', 'unread', 'archived'
})

export const mutations = {
  SET_NOTIFICATIONS(state, notifications) {
    // Always update to ensure we have the latest data
    state.notifications = notifications.filter(n => !n.archived_at)
    state.archivedNotifications = notifications.filter(n => n.archived_at)
    state.unreadCount = state.notifications.filter(n => !n.read_at).length
  },
  SET_LOADING(state, loading) {
    state.loading = loading
  },
  SET_ERROR(state, error) {
    state.error = error
  },
  SET_ACTIVE_TAB(state, tab) {
    state.activeTab = tab
  },
  MARK_AS_READ(state, notificationId) {
    const notification = state.notifications.find(n => n.id === notificationId)
    if (notification) {
      notification.read_at = new Date().toISOString()
      state.unreadCount = Math.max(0, state.unreadCount - 1)
    }
  },
  MARK_ALL_AS_READ(state) {
    state.notifications.forEach(n => {
      n.read_at = new Date().toISOString()
    })
    state.unreadCount = 0
  },
  ARCHIVE_NOTIFICATION(state, notificationId) {
    const notificationIndex = state.notifications.findIndex(n => n.id === notificationId)
    if (notificationIndex !== -1) {
      const notification = state.notifications[notificationIndex]
      notification.archived_at = new Date().toISOString()

      // Move to archived list
      state.archivedNotifications.push(notification)

      // Remove from main list
      state.notifications.splice(notificationIndex, 1)

      // Update unread count if needed
      if (!notification.read_at) {
        state.unreadCount = Math.max(0, state.unreadCount - 1)
      }
    }
  },
  UNARCHIVE_NOTIFICATION(state, notificationId) {
    const notificationIndex = state.archivedNotifications.findIndex(n => n.id === notificationId)
    if (notificationIndex !== -1) {
      const notification = state.archivedNotifications[notificationIndex]
      notification.archived_at = null

      // Move to main list
      state.notifications.push(notification)

      // Remove from archived list
      state.archivedNotifications.splice(notificationIndex, 1)

      // Update unread count if needed
      if (!notification.read_at) {
        state.unreadCount++
      }
    }
  },
  ARCHIVE_ALL_NOTIFICATIONS(state) {
    // Move all notifications to archived
    state.notifications.forEach(notification => {
      notification.archived_at = new Date().toISOString()
      state.archivedNotifications.push(notification)
    })

    // Clear main list and unread count
    state.notifications = []
    state.unreadCount = 0
  },
}

export const actions = {
  async fetchNotifications({ commit, state }) {
    try {
      const showLoading = state.notifications.length === 0 && state.archivedNotifications.length === 0
      if (showLoading) {
        commit('SET_LOADING', true)
      }

      // Fetch all notifications in one request
      const { data } = await this.$axios.get('/notifications', { params: { type: 'all' } })
      const allNotifications = Array.isArray(data) ? data : data.notifications
      commit('SET_NOTIFICATIONS', allNotifications)
    } catch (error) {
      commit('SET_ERROR', error.message)
    } finally {
      commit('SET_LOADING', false)
    }
  },

  setActiveTab({ commit }, tab) {
    commit('SET_ACTIVE_TAB', tab)
  },

  startPolling({ dispatch }) {
    // Clear any existing intervals
    if (this.fetchInterval) {
      clearInterval(this.fetchInterval)
    }
    if (this.processInterval) {
      clearInterval(this.processInterval)
    }

    // Initial fetches
    dispatch('fetchNotifications')
    dispatch('processCurrentUserNotifications')

    // Set up separate intervals for fetch and process
    this.fetchInterval = setInterval(() => {
      dispatch('fetchNotifications')
    }, FETCH_NOTIFICATIONS_INTERVAL)

    this.processInterval = setInterval(() => {
      dispatch('processCurrentUserNotifications')
    }, PROCESS_NOTIFICATIONS_INTERVAL)
  },

  stopPolling() {
    if (this.fetchInterval) {
      clearInterval(this.fetchInterval)
      this.fetchInterval = null
    }
    if (this.processInterval) {
      clearInterval(this.processInterval)
      this.processInterval = null
    }
  },

  async markAsRead({ commit }, notificationId) {
    try {
      await this.$axios.post(`/notifications/${notificationId}/read`)
      commit('MARK_AS_READ', notificationId)
    } catch (error) {
      commit('SET_ERROR', error.message)
    }
  },

  async markAllAsRead({ commit }) {
    try {
      await this.$axios.post('/notifications/mark-all-read')
      commit('MARK_ALL_AS_READ')
    } catch (error) {
      commit('SET_ERROR', error.message)
    }
  },

  async archiveNotification({ commit }, notificationId) {
    try {
      await this.$axios.post(`/notifications/${notificationId}/archive`)
      commit('ARCHIVE_NOTIFICATION', notificationId)
    } catch (error) {
      commit('SET_ERROR', error.message)
    }
  },

  async unarchiveNotification({ commit }, notificationId) {
    try {
      await this.$axios.post(`/notifications/${notificationId}/unarchive`)
      commit('UNARCHIVE_NOTIFICATION', notificationId)
    } catch (error) {
      commit('SET_ERROR', error.message)
    }
  },

  async archiveAllNotifications({ commit }) {
    try {
      await this.$axios.post('/notifications/archive-all')
      commit('ARCHIVE_ALL_NOTIFICATIONS')
    } catch (error) {
      commit('SET_ERROR', error.message)
    }
  },

  async processCurrentUserNotifications({ dispatch }) {
    try {
      await this.$axios.post('/notifications/process-current-user')
      // Don't need to fetch here since we have a separate fetch interval
    } catch (error) {
      console.error('Error processing notifications:', error)
    }
  },
}

export const getters = {
  unreadNotifications: state => state.notifications.filter(n => !n.read_at),
  readNotifications: state => state.notifications.filter(n => n.read_at),
  activeNotifications: state => {
    switch (state.activeTab) {
      case 'unread':
        return state.notifications.filter(n => !n.read_at)
      case 'archived':
        return state.archivedNotifications
      default: // 'all'
        return state.notifications
    }
  }
}
