import { cookieOptions } from '~/constants'

export const state = () => ({
  user: '',
  isLoggedIn: false,
})

export const mutations = {
  setUser(state, payload) {
    state.user = payload
  },

  setLoggedIn(state, payload) {
    state.isLoggedIn = payload
  },

  async logout(state) {
    state.isLoggedIn = false
    state.user = ''
    this.$cookies.remove('token')
    this.$cookies.remove('user')
    await this.$router.push({ path: '/login' })
  },

  logoutWithoutRedirect(state) {
    state.isLoggedIn = false
    state.user = ''
    this.$cookies.remove('token')
    this.$cookies.remove('user')
  },

  async login(state, { token, user, redirect = null }) {
    state.isLoggedIn = true
    state.user = user
    this.$cookies.set('token', token, cookieOptions)
    const { id, role, user_type, email_verified_at, email, created_at, is_host, is_subscribed } =
      user
    this.$cookies.set(
      'user',
      {
        id,
        role,
        user_type,
        email_verified_at,
        email,
        created_at,
        is_host,
        is_subscribed,
      },
      cookieOptions
    )

    if (redirect) {
      await this.$router.push({ path: redirect })
    }
  },

  updateUser(state, user) {
    state.user = user
  },
}

export const actions = {
  async getUserDetails({ commit, state }) {
    if (state.isLoggedIn && state.user) {
      return
    }

    const token = this.$cookies.get('token')
    const user = this.$cookies.get('user')

    if (!token || !user) {
      commit('logoutWithoutRedirect')
      return
    }

    try {
      const { data } = await this.$axios.get('/user')

      if (!data.id) {
        commit('logoutWithoutRedirect')
        return
      }

      commit('setUser', data)
      commit('setLoggedIn', true)
    } catch (e) {
      console.log(e)
      commit('logoutWithoutRedirect')
    }
  },

  async sendEmailToUser({ commit }, payload) {
    const { data } = await this.$axios.post('auth/reset/send', {
      email: payload,
    })

    return data
  },

  async verifyToken({ commit }, payload) {
    const { data } = await this.$axios.post('auth/reset/verifyToken', {
      token: payload,
    })

    return data
  },

  async resetPassword({ commit }, payload) {
    const { data } = await this.$axios.post('auth/reset/restorePassword', payload)

    return data
  },

  logout({ commit }) {
    commit('logout')
  },
}

export const getters = {
  getuser: state => state.user,
  isLoggedIn: state => state.isLoggedIn,
}
