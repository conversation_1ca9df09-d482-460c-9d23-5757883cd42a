const DEFAULT_LOGO = {
  href: 'https://twimo.com',
  src: require('~/assets/newhome/twimo-header-logo.png'),
  mobileSrc: require('~/assets/newhome/twimo-header-logo.png'),
  alt: 'twimo.com',
  height: 45,
  mobileHeight: 35,
  width: 200,
  mobileWidth: 150,
  isPoweredBy: false,
}

export const state = () => ({
  logo: { ...DEFAULT_LOGO },
  isMobile: false,
})

export const mutations = {
  SET_LOGO(state, logo) {
    const { href, src, alt, height, width, isPoweredBy } = logo

    state.logo = {
      href: href || DEFAULT_LOGO.href,
      src: src || (state.isMobile ? DEFAULT_LOGO.mobileSrc : DEFAULT_LOGO.src),
      alt: alt || DEFAULT_LOGO.alt,
      height: height || (state.isMobile ? DEFAULT_LOGO.mobileHeight : DEFAULT_LOGO.height),
      width: width || (state.isMobile ? DEFAULT_LOGO.mobileWidth : DEFAULT_LOGO.width),
      isPoweredBy: isPoweredBy || DEFAULT_LOGO.isPoweredBy,
    }
  },
  SET_IS_MOBILE(state, isMobile) {
    state.isMobile = isMobile
  },
}

export const actions = {
  async fetchLogo({ commit }, sharedUrl) {
    try {
      const {
        data: { host_logo: hostLogo, host_landing_page: hostLandingPage },
      } = await this.$axios.get(`users/shared-url/${sharedUrl}`)

      commit('SET_LOGO', {
        href: hostLandingPage,
        src: hostLogo,
        alt: hostLandingPage,
        isPoweredBy: true,
      })
    } catch (error) {
      console.error(error)
    }
  },
  setIsMobile({ commit, dispatch }, isMobile) {
    commit('SET_IS_MOBILE', isMobile)
    dispatch('resetLogo')
  },
  setIsPoweredBy({ commit, dispatch }, isPoweredBy) {
    commit('SET_LOGO', { isPoweredBy })
    dispatch('resetLogo')
  },
  resetLogo({ commit, state }) {
    const { isMobile } = state
    const { isPoweredBy, src, href, alt } = state.logo

    commit('SET_LOGO', {
      isPoweredBy,
      href: isPoweredBy ? href : DEFAULT_LOGO.href,
      alt: isPoweredBy ? alt : DEFAULT_LOGO.alt,
      src: isPoweredBy ? src : isMobile ? DEFAULT_LOGO.mobileSrc : DEFAULT_LOGO.src,
      height: isMobile ? DEFAULT_LOGO.mobileHeight : DEFAULT_LOGO.height,
      width: isMobile ? DEFAULT_LOGO.mobileWidth : DEFAULT_LOGO.width,
    })
  },
}

export const getters = {
  getLogo: state => state.logo,
  getDefaultLogo: () => DEFAULT_LOGO,
}
