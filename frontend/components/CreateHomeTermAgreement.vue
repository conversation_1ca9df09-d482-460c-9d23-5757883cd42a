<script lang="ts">
// @ts-nocheck

import { defineComponent, ref, watch } from '@nuxtjs/composition-api'

import BoldPurpleText from '~/components/BoldPurpleText.vue'
import { useCreateHomeProgressStore } from '~/composables/useCreateHomeStore'
import { additionalInfos } from '~/constants'

export default defineComponent({
  name: 'CreateHomeTermAgreement',

  components: {
    BoldPurpleText,
  },

  setup() {
    const store = useCreateHomeProgressStore()
    const {
      createHomeData,
      clickOnOfferAsSeasonalLease,
      isAdditionalInfoSelected,
      clickOnAdditionalInfo,
    } = store

    const termAgreement = ref(false)

    // Watch the local agreement and update the store
    watch(termAgreement, newValue => {
      createHomeData.termAgreement = newValue
    })

    return {
      createHomeData,
      additionalInfos,
      clickOnOfferAsSeasonalLease,
      isAdditionalInfoSelected,
      clickOnAdditionalInfo,
      termAgreement,
    }
  },
})
</script>

<template>
  <v-row>
    <v-col cols="12" class="tw-mt-8 tw-flex tw-flex-col tw-gap-4">
      <div class="tw-text-2xl tw-font-semibold tw-text-zinc-600">Home Security</div>

      <div class="tw-text-lg tw-font-medium tw-text-zinc-500">
        If you have any security items within or outside of your home, please make sure to be
        mindful of your guests privacy. Turn Indoor Cameras OFF, and/or notify guests of outdoor
        cameras, ring doorbells, etc.
      </div>
    </v-col>

    <v-col cols="12">
      <v-row>
        <v-col cols="12" md="8">
          <v-textarea
            v-model="createHomeData.additionalInfos.other"
            label="Home Security Details (include if needed):"
            placeholder="I have outdoor security cameras, I have a Ring doorbell, etc."
            outlined
            hide-details
            dense
            required
          />
        </v-col>
      </v-row>
    </v-col>

    <v-col cols="12" class="tw-mt-8">
      <div class="tw-text-2xl tw-font-semibold tw-text-zinc-600">
        <BoldPurpleText> Terms and Conditions*</BoldPurpleText>
      </div>

      <div class="tw-mt-4">
        <v-checkbox v-model="termAgreement" hide-details class="tw-mt-4" dense required>
          <template #label>
            <div class="tw-text-xl tw-text-primary">
              I agree to the Twimo Privacy Policy and Terms and Conditions
            </div>
          </template>
          >
        </v-checkbox>
      </div>
    </v-col>
  </v-row>
</template>

<style scoped></style>
