<script lang="ts">
import { defineComponent } from '@nuxtjs/composition-api'

import { JobInvitation } from '~/types'
import GoodCard from '~/components/GoodCard.vue'
import { formatDateTimeToMonthDayYear } from '~/helpers'

export default defineComponent({
  name: 'JobInvitationDetail',

  components: { GoodCard },

  props: {
    jobInvitation: {
      type: Object as () => JobInvitation,
      required: true,
    },
  },

  data() {
    return {}
  },

  computed: {
    getJobStatus() {
      const { jobInvitation } = this
      const { is_accepted, extra_info } = jobInvitation

      if (is_accepted && extra_info.crew_ready_at && extra_info.host_done_at) {
        return 'Complete'
      }

      if (is_accepted && extra_info.crew_ready_at) {
        return 'Ready to review'
      }

      if (is_accepted) {
        return 'Accepted'
      }

      if (extra_info.crew_declined_at) {
        return 'Declined'
      }

      return 'Pending'
    },
  },

  async mounted() {},

  methods: {
    formatDateTimeToMonthDayYear,

    getIconClass(mimeType: string) {
      switch (mimeType) {
        case 'image/jpeg':
        case 'image/jpg':
        case 'image/png':
          return 'mdi-file-image'
        case 'application/pdf':
          return 'mdi-file-pdf-box'
        default:
          return 'mdi-file-document'
      }
    },
  },
})
</script>

<template>
  <good-card card-text-classes="tw-p-3">
    <v-card-text class="tw-text-black">
      <div class="tw-flex tw-justify-between">
        <!--              Job title-->
        <div class="tw-font-bold tw-text-xl tw-flex tw-items-center">
          <v-icon color="black" dense small class="tw-mr-2"> mdi-briefcase-outline </v-icon>
          <div>{{ jobInvitation.extra_info.job_snapshot.title }}</div>
        </div>

        <!--              Job status-->
        <v-chip
          class="tw-text-sm tw-font-semibold tw-text-white"
          :color="
            getJobStatus === 'Pending'
              ? 'orange'
              : getJobStatus === 'Accepted'
                ? 'green'
                : getJobStatus === 'Ready to review'
                  ? 'blue'
                  : getJobStatus === 'Complete'
                    ? 'purple'
                    : 'red'
          "
          label
        >
          {{ getJobStatus }}
        </v-chip>
      </div>

      <!--              Job description-->
      <div class="tw-flex tw-items-center tw-ml-6 tw-mb-5">
        <span class="tw-text-ellipsis">{{
          jobInvitation.extra_info.job_snapshot.description
        }}</span>
      </div>

      <div class="tw-text-sm tw-text-slate-500 tw-flex tw-items-center tw-mt-2">
        <v-icon class="tw-mr-2" small color="black"> mdi-cash-multiple</v-icon>
        <span>${{ jobInvitation.alternate_price }}</span>
      </div>

      <div class="tw-text-sm tw-text-slate-500 tw-flex tw-items-center tw-mt-2">
        <v-icon class="tw-mr-2" small color="black"> mdi-map-marker-outline </v-icon>
        <span>{{ jobInvitation.extra_info.job_snapshot.home.title }}</span>
      </div>

      <div class="tw-text-sm tw-text-slate-500 tw-flex tw-items-center tw-mt-2">
        <v-icon class="tw-mr-2" small color="black"> mdi-calendar-clock-outline </v-icon>
        <span>{{ formatDateTimeToMonthDayYear(jobInvitation.alternate_start_time) }}</span>
      </div>

      <div class="tw-text-sm tw-text-slate-500 tw-flex tw-items-center tw-mt-2">
        <v-icon class="tw-mr-2" small color="black"> mdi-account-group-outline </v-icon>
        <span>{{ jobInvitation.extra_info.service_provider_snapshot.name }}</span>
      </div>

      <div class="tw-text-sm tw-text-slate-500 tw-flex tw-items-center tw-mt-2">
        <v-icon class="tw-mr-2" small color="black"> mdi-information-outline </v-icon>
      </div>

      <div class="tw-text-sm tw-text-slate-500 tw-flex tw-items-center tw-mt-2 tw-ml-6">
        <span class="tw-text-black tw-mr-1">Sent at:</span>
        {{ formatDateTimeToMonthDayYear(jobInvitation.created_at) }}
      </div>

      <div
        v-if="jobInvitation.extra_info.crew_viewed_at"
        class="tw-text-sm tw-text-slate-500 tw-flex tw-items-center tw-mt-2 tw-ml-6"
      >
        <span class="tw-text-black tw-mr-1">Crew last viewed at:</span>
        {{ formatDateTimeToMonthDayYear(jobInvitation.extra_info.crew_viewed_at) }}
      </div>

      <div
        v-if="jobInvitation.extra_info.crew_accepted_at"
        class="tw-text-sm tw-text-slate-500 tw-flex tw-items-center tw-mt-2 tw-ml-6"
      >
        <span class="tw-text-black tw-mr-1">Crew accepted at:</span>
        {{ formatDateTimeToMonthDayYear(jobInvitation.extra_info.crew_accepted_at) }}
      </div>

      <div
        v-if="jobInvitation.extra_info.crew_declined_at"
        class="tw-text-sm tw-text-slate-500 tw-flex tw-items-center tw-mt-2 tw-ml-6"
      >
        <span class="tw-text-black tw-mr-1">Crew declined at:</span>
        {{ formatDateTimeToMonthDayYear(jobInvitation.extra_info.crew_declined_at) }}
      </div>

      <div
        v-if="jobInvitation.extra_info.crew_decline_reason"
        class="tw-text-sm tw-text-slate-500 tw-flex tw-items-center tw-mt-2 tw-ml-6"
      >
        <span class="tw-text-black tw-mr-1">Decline reason:</span>
        {{ jobInvitation.extra_info.crew_decline_reason }}
      </div>

      <div
        v-if="jobInvitation.extra_info.host_done_at"
        class="tw-text-sm tw-text-slate-500 tw-flex tw-items-center tw-mt-2 tw-ml-6"
      >
        <span class="tw-text-black tw-mr-1"> Completed at: </span>
        {{ formatDateTimeToMonthDayYear(jobInvitation.extra_info.host_done_at) }}
      </div>

      <div
        v-if="jobInvitation.extra_info.crew_ready_at && jobInvitation.extra_info.ready_notes"
        class="tw-text-sm tw-text-slate-500 tw-flex tw-items-center tw-mt-2 tw-ml-6"
      >
        <span class="tw-text-black tw-mr-1">Review notes:</span>
        {{ jobInvitation.extra_info.ready_notes }}
      </div>

      <div
        v-if="jobInvitation.attachments && jobInvitation.attachments.length > 0"
        class="tw-text-sm tw-text-slate-500 tw-flex tw-mt-2 tw-ml-6"
      >
        <span class="tw-text-black">Review attachments:</span>
        <ul>
          <li
            v-for="(attachment, index) in jobInvitation.attachments"
            :key="index"
            class="tw-mb-2 tw-flex tw-items-center"
          >
            <v-icon small color="black" class="tw-mr-1">
              {{ getIconClass(attachment.mime_type) }}
            </v-icon>
            <a :href="attachment.url" target="_blank" class="tw-text-slate-500 tw-underline">{{
              attachment.name
            }}</a>
          </li>
        </ul>
      </div>

      <slot />
    </v-card-text>
  </good-card>
</template>
