<script lang="ts">
// @ts-nocheck
import { defineComponent, computed, ref, watch, onMounted, onUnmounted } from '@nuxtjs/composition-api'

import { useCreateHomeImageStore } from '~/composables/useCreateHomeImageStore'
import { useApi, useToast } from '~/composables/useCommon'

export default defineComponent({
  name: 'PhotoProcessingStatus',

  props: {
    homeId: {
      type: [Number, String],
      required: true
    }
  },

  emits: ['refresh-requested'],

  setup(props, { emit }) {
    const api = useApi()
    const toast = useToast()
    const imageStore = useCreateHomeImageStore()
    const processingStatus = ref(imageStore.processingStatus || 'pending')
    const isLoading = ref(true)
    const error = ref(null)
    const pollInterval = ref(null)
    const completedTimestamp = ref(null)

    const statusMessage = computed(() => {
      if (error.value) {
        return 'Error processing photos. Please try again later.'
      }

      if (isLoading.value && !processingStatus.value) {
        return 'Checking photo processing status...'
      }

      if (!processingStatus.value || processingStatus.value === 'pending') {
        return 'Your photos are waiting to be processed...'
      }

      if (processingStatus.value === 'queued') {
        return 'Your photos are in queue for processing...'
      }

      if (processingStatus.value === 'processing') {
        return 'Your photos are being processed...'
      }

      if (typeof processingStatus.value === 'string' && processingStatus.value.startsWith('processing:')) {
        const parts = processingStatus.value.split(':')[1].split('/')
        return `Processing photos: ${parts[0]} of ${parts[1]} complete...`
      }

      if (typeof processingStatus.value === 'string' && processingStatus.value.startsWith('completed:')) {
        const parts = processingStatus.value.split(':')[1].split('/')
        return `Photo processing complete! ${parts[0]} of ${parts[1]} photos uploaded successfully.`
      }

      if (processingStatus.value === 'completed') {
        return 'All photos have been processed successfully!'
      }

      if (processingStatus.value === 'failed') {
        return 'There was an error processing some photos. You can try again later.'
      }

      return 'Photo processing status: ' + processingStatus.value
    })

    const statusColor = computed(() => {
      if (error.value) return 'error'
      if (!processingStatus.value ||
          processingStatus.value === 'pending' ||
          processingStatus.value === 'queued' ||
          processingStatus.value === 'processing' ||
          (typeof processingStatus.value === 'string' && processingStatus.value.startsWith('processing:'))) {
        return 'primary'
      }
      if (processingStatus.value === 'completed' ||
          (typeof processingStatus.value === 'string' && processingStatus.value.startsWith('completed:'))) {
        return 'success'
      }
      if (processingStatus.value === 'failed') {
        return 'error'
      }
      return 'primary'
    })

    const isComplete = computed(() => {
      return processingStatus.value === 'completed' ||
             (typeof processingStatus.value === 'string' && processingStatus.value.startsWith('completed:'))
    })

    // Determine if we should show the status component at all
    const shouldShowStatus = computed(() => {
      // Only show for pending, queued, and processing states
      // Hide when completed or failed to provide a cleaner UI
      return (
        processingStatus.value === 'pending' ||
        processingStatus.value === 'queued' ||
        processingStatus.value === 'processing' ||
        (processingStatus.value && typeof processingStatus.value === 'string' && processingStatus.value.startsWith('processing:'))
      )
    })

    const progressValue = computed(() => {
      if (!processingStatus.value ||
          processingStatus.value === 'pending' ||
          processingStatus.value === 'queued') {
        return 0
      }

      if (processingStatus.value === 'processing') {
        return 50
      }

      if (typeof processingStatus.value === 'string' && processingStatus.value.startsWith('processing:')) {
        const parts = processingStatus.value.split(':')[1].split('/')
        return Math.round((parseInt(parts[0]) / parseInt(parts[1])) * 100)
      }

      if (processingStatus.value === 'completed' ||
          (typeof processingStatus.value === 'string' && processingStatus.value.startsWith('completed:'))) {
        return 100
      }

      if (processingStatus.value === 'failed') {
        return 100
      }

      return 0
    })

    const fetchStatus = async () => {
      if (!props.homeId) return

      try {
        isLoading.value = true
        // Use the dedicated endpoint for photo processing status
        const { data } = await api.get(`user/homes/${props.homeId}/photo-processing-status`)

        console.log(`[PhotoProcessingStatus] Fetched status for home ${props.homeId}:`, data)

        if (data) {
          // Check if status is changing from non-completed to completed
          const wasCompleted = processingStatus.value === 'completed' ||
                              (typeof processingStatus.value === 'string' && processingStatus.value.startsWith('completed:'))
          const isNowCompleted = data.status === 'completed' ||
                                (typeof data.status === 'string' && data.status.startsWith('completed:'))

          // Log the raw status from the API
          console.log(`[PhotoProcessingStatus] Raw status from API: '${data.status}'`)

          // Update the status
          processingStatus.value = data.status || null
          imageStore.processingStatus = data.status || null

          // Check if we should show the component based on the new status
          const willShow = processingStatus.value === 'processing' ||
                          (processingStatus.value && typeof processingStatus.value === 'string' && processingStatus.value.startsWith('processing:'))

          console.log(`[PhotoProcessingStatus] Updated status: '${processingStatus.value}', shouldShow: ${willShow}`)

          // If status just changed to completed, set the timestamp
          if (!wasCompleted && isNowCompleted) {
            completedTimestamp.value = Date.now()
            console.log(`[PhotoProcessingStatus] Status changed to completed, setting timestamp: ${completedTimestamp.value}`)
          }

          // If we have Airbnb info, update the store
          if (data.is_from_airbnb) {
            imageStore.isFromAirbnb = data.is_from_airbnb
          }
        } else {
          console.log(`[PhotoProcessingStatus] No data returned from API`)
          processingStatus.value = null
          imageStore.processingStatus = null
        }

        error.value = null
      } catch (err) {
        console.error('Error fetching photo processing status:', err)
        error.value = err.message || 'Failed to fetch status'
      } finally {
        isLoading.value = false
      }
    }

    const startPolling = () => {
      // Poll every 10 seconds
      pollInterval.value = setInterval(fetchStatus, 10000)
    }

    const stopPolling = () => {
      if (pollInterval.value) {
        clearInterval(pollInterval.value)
        pollInterval.value = null
      }
    }

    watch(() => processingStatus.value, (newStatus) => {
      // Check if we should show the component based on the new status
      const willShow = newStatus === 'pending' ||
                      newStatus === 'queued' ||
                      newStatus === 'processing' ||
                      (newStatus && typeof newStatus === 'string' && newStatus.startsWith('processing:'))

      console.log(`[PhotoProcessingStatus] Status changed to: ${newStatus}, shouldShow: ${willShow}`)

      // Only stop polling when processing is complete or failed
      // Continue polling for pending and queued states
      if (newStatus === 'completed' ||
          (newStatus && typeof newStatus === 'string' && newStatus.startsWith('completed:')) ||
          newStatus === 'failed') {
        stopPolling()
        console.log(`[PhotoProcessingStatus] Stopping polling as processing is complete or failed`)

        // If photos are completed, emit a refresh event to update the parent component
        if (newStatus === 'completed' ||
            (newStatus && typeof newStatus === 'string' && newStatus.startsWith('completed:'))) {
          // Wait a moment to ensure the backend has fully processed the photos
          setTimeout(() => {
            emit('refresh-requested')
            console.log(`[PhotoProcessingStatus] Emitting refresh-requested event after completion`)
          }, 1000)
        }
      } else if (newStatus === 'pending' || newStatus === 'queued') {
        // Make sure polling is active for pending and queued states
        if (!pollInterval.value) {
          startPolling()
          console.log(`[PhotoProcessingStatus] Starting polling for ${newStatus} state`)
        }
      }
    })

    onMounted(() => {
      console.log(`[PhotoProcessingStatus] Component mounted for home ${props.homeId}, initial status: '${processingStatus.value}'`)
      fetchStatus()
      startPolling()
    })

    onUnmounted(() => {
      stopPolling()
    })

    // Function to manually refresh the home data
    const refreshHomeData = async () => {
      try {
        isLoading.value = true
        console.log('[PhotoProcessingStatus] Manually refreshing home data')

        // First fetch the latest status
        await fetchStatus()

        // Then emit an event to the parent component to refresh the home data
        emit('refresh-requested')

        toast.info('Refreshing photos...').goAway(3000)
      } catch (error) {
        console.error('Error refreshing home data:', error)
      } finally {
        isLoading.value = false
      }
    }

    return {
      processingStatus,
      statusMessage,
      statusColor,
      isComplete,
      progressValue,
      isLoading,
      shouldShowStatus,
      refreshHomeData
    }
  }
})
</script>

<template>
  <!-- Show the component for all photo processing states -->
  <div v-if="shouldShowStatus" class="tw-mt-4 tw-mb-6">
    <v-card class="tw-p-4 tw-border tw-border-gray-200 tw-rounded-lg">
      <div class="tw-flex tw-flex-col tw-gap-2">
        <div class="tw-flex tw-items-center tw-gap-2 tw-flex-wrap">
          <!-- Show different icons based on status -->
          <v-icon v-if="isComplete" :color="statusColor">mdi-check-circle</v-icon>
          <v-icon v-else-if="processingStatus === 'pending' || processingStatus === 'queued'" :color="statusColor">mdi-clock-outline</v-icon>
          <v-progress-circular
            v-else
            indeterminate
            :color="statusColor"
            size="24"
          ></v-progress-circular>
          <span class="tw-text-lg tw-font-medium status-message" :class="`tw-text-${statusColor}`">{{ statusMessage }}</span>
        </div>

        <v-progress-linear
          :value="progressValue"
          :color="statusColor"
          height="8"
          rounded
          class="tw-mt-2"
        ></v-progress-linear>

        <div class="tw-mt-2 tw-text-sm tw-text-gray-600">
          <!-- Different messages based on status -->
          <p v-if="processingStatus === 'pending' || processingStatus === 'queued'">
            Your photos have been uploaded and are waiting to be processed. This usually takes a few moments.
            <strong>The page will automatically update</strong> when processing begins.
          </p>
          <p v-else-if="processingStatus === 'processing' || (processingStatus && processingStatus.startsWith('processing:'))">
            Your photos are being processed in the background. You can continue using the site while we handle this for you.
            <strong>There's no need to wait</strong> - you can navigate away from this page and the processing will continue.
          </p>
          <p v-else-if="isComplete">
            All your photos have been processed. You can view them on your home page.
            <strong>Refresh the page</strong> to see your updated photos.
          </p>

          <!-- Refresh button -->
          <div class="tw-mt-3 tw-flex tw-justify-end">
            <v-btn
              small
              outlined
              color="primary"
              :loading="isLoading"
              class="refresh-button"
              @click="refreshHomeData"
            >
              <v-icon small class="tw-mr-1">mdi-refresh</v-icon>
              <span class="button-text">Refresh Photos</span>
            </v-btn>
          </div>
        </div>
      </div>
    </v-card>
  </div>
</template>

<style scoped>
/* Mobile-specific styles */
@media (max-width: 600px) {
  .status-message {
    font-size: 1rem;
    line-height: 1.5rem;
    width: 100%;
    margin-top: 0.25rem;
  }

  .refresh-button {
    width: 100%;
    margin-top: 0.5rem;
  }

  .button-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  /* Ensure text doesn't overflow on small screens */
  p {
    word-break: break-word;
  }
}
</style>
