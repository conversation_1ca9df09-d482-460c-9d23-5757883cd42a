<script lang="ts">
// @ts-nocheck
import { defineComponent, ref, computed, watch, onMounted } from '@nuxtjs/composition-api'

export default defineComponent({
  name: 'CreateHomeMap',

  props: {
    location: {
      type: Object,
      default: null,
    },
    title: {
      type: String,
      default: 'Your Home',
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },

  setup(props) {
    // Map and marker references
    const map = ref(null)
    const markerRef = ref(null)
    const homeCircle = ref(null)

    // State management
    const mapLoaded = ref(false)
    const mapError = ref(false)
    const userZoomed = ref(false)
    const initialZoom = 13 // Lower zoom level to show more context
    const currentZoom = ref(initialZoom)
    const mapCenter = ref(null)

    // Format address for display
    const formattedAddress = computed(() => {
      if (!props.location) {
        return ''
      }

      // Format address or use fallback
      if (props.location.address_components) {
        const city =
          props.location.address_components.city || props.location.address_components.town
        const state = props.location.address_components.state
        const country = props.location.address_components.country

        const parts = [city, state, country].filter(Boolean)
        if (parts.length) {
          return parts.join(', ')
        }
      }

      return props.location.full_address || 'Selected location'
    })

    // Show shorter address for the popup
    const popupAddress = computed(() => {
      if (!props.location) {
        return ''
      }

      if (props.location.address_components) {
        const street =
          props.location.address_components.road || props.location.address_components.street
        const houseNumber = props.location.address_components.house_number
        const city =
          props.location.address_components.city || props.location.address_components.town

        const parts = []
        if (houseNumber && street) {
          parts.push(`${houseNumber} ${street}`)
        } else if (street) {
          parts.push(street)
        }
        if (city) {
          parts.push(city)
        }

        if (parts.length) {
          return parts.join(', ')
        }
      }

      // Limit to first 80 chars if full address is too long
      if (props.location.full_address && props.location.full_address.length > 80) {
        return props.location.full_address.substring(0, 80) + '...'
      }

      return props.location.full_address || 'Selected location'
    })

    // Monitor location changes
    watch(
      () => props.location,
      newLocation => {
        if (newLocation && newLocation.lat && newLocation.lng) {
          mapLoaded.value = true
          mapError.value = false
          mapCenter.value = [newLocation.lat, newLocation.lng]

          // Reset zoom if this is a new location
          if (!userZoomed.value) {
            currentZoom.value = initialZoom
          }
        } else {
          mapLoaded.value = false
          mapError.value = true
        }
      },
      { immediate: true }
    )

    // Map interaction handlers
    const handleMapZoom = e => {
      userZoomed.value = true
      currentZoom.value = e.target.getZoom()
    }

    const handleMapReady = e => {
      if (e && e.target) {
        e.target.on('zoomend', handleMapZoom)
      }
    }

    const resetView = () => {
      if (props.location && map.value && map.value.leafletObject) {
        map.value.leafletObject.setView([props.location.lat, props.location.lng], initialZoom)
        userZoomed.value = false
        currentZoom.value = initialZoom
      }
    }

    // Clean up on unmount
    onMounted(() => {
      if (props.location) {
        mapCenter.value = [props.location.lat, props.location.lng]
      }
    })

    return {
      map,
      markerRef,
      homeCircle,
      mapLoaded,
      mapError,
      formattedAddress,
      popupAddress,
      initialZoom,
      currentZoom,
      mapCenter,
      handleMapReady,
      resetView,
    }
  },
})
</script>

<template>
  <div
    class="create-home-map tw-w-full tw-rounded-xl tw-overflow-hidden tw-relative tw-border tw-border-gray-200 tw-shadow-md"
  >
    <div class="tw-relative" style="height: 400px">
      <!-- Loading state -->
      <div
        v-if="loading"
        class="tw-absolute tw-inset-0 tw-z-20 tw-bg-gray-50 tw-bg-opacity-80 tw-flex tw-items-center tw-justify-center tw-backdrop-blur-sm"
      >
        <div class="tw-flex tw-flex-col tw-items-center tw-gap-3">
          <v-progress-circular indeterminate color="primary" size="36" />
          <span class="tw-text-primary tw-font-medium">Finding your location...</span>
        </div>
      </div>

      <!-- Map display when location is available -->
      <client-only>
        <l-map
          v-if="location && location.lat && location.lng"
          ref="map"
          :zoom="currentZoom"
          :center="mapCenter"
          class="tw-h-full tw-w-full"
          @ready="handleMapReady"
        >
          <l-tile-layer
            url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
            attribution="&copy; OpenStreetMap contributors"
            :subdomains="['a', 'b', 'c']"
          />

          <l-marker ref="markerRef" :lat-lng="[location.lat, location.lng]">
            <l-popup>
              <div class="tw-p-1">
                <h3 class="tw-text-lg tw-font-semibold tw-text-primary tw-mb-1">
                  {{ title }}
                </h3>
                <p v-if="popupAddress" class="tw-text-sm tw-text-gray-600 tw-mb-0">
                  {{ popupAddress }}
                </p>
              </div>
            </l-popup>
          </l-marker>

          <!-- Privacy circle around the home location -->
          <l-circle
            v-if="location && location.lat && location.lng"
            ref="homeCircle"
            :lat-lng="[location.lat, location.lng]"
            :radius="300"
            color="#672093"
            :fill="true"
            fill-color="#672093"
            fill-opacity="0.15"
          />

          <!-- Map controls -->
          <l-control position="bottomright">
            <v-btn
              v-if="map && location"
              small
              fab
              color="white"
              class="tw-shadow-md tw-mr-1 tw-elevation-4"
              @click="resetView"
            >
              <v-icon small> mdi-home </v-icon>
            </v-btn>
          </l-control>
        </l-map>
      </client-only>

      <!-- Empty state when no location is provided -->
      <div
        v-if="!loading && (!location || !location.lat || !location.lng)"
        class="tw-absolute tw-inset-0 tw-bg-gray-50 tw-flex tw-items-center tw-justify-center tw-flex-col"
      >
        <div class="tw-text-center tw-px-4 tw-max-w-sm">
          <v-icon x-large color="primary" class="tw-opacity-60 tw-mb-4"> mdi-map-search </v-icon>
          <h3 class="tw-text-xl tw-font-medium tw-text-zinc-700 tw-mb-2">No location selected</h3>
          <p class="tw-text-zinc-500 tw-mb-3">
            Enter an address above to see your home's location on the map
          </p>

          <div
            v-if="mapError"
            class="tw-mt-2 tw-p-3 tw-bg-red-50 tw-text-red-600 tw-rounded-lg tw-text-sm"
          >
            <div class="tw-flex tw-items-center tw-gap-2">
              <v-icon small color="error"> mdi-alert-circle </v-icon>
              <span>We couldn't find this location. Please try a more specific address.</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Map overlay with additional information -->
      <div
        v-if="location && location.lat && location.lng"
        class="tw-absolute tw-left-3 tw-top-3 tw-z-10"
      >
        <div class="tw-bg-white tw-rounded-lg tw-shadow-md tw-p-3 tw-max-w-xs">
          <div class="tw-flex tw-items-center tw-gap-2 tw-mb-1">
            <v-icon small color="primary"> mdi-information-outline </v-icon>
            <span class="tw-text-sm tw-font-medium tw-text-zinc-700">Location Information</span>
          </div>
          <p v-if="formattedAddress" class="tw-text-xs tw-text-zinc-600 tw-mb-0">
            {{ formattedAddress }}
          </p>
          <p class="tw-text-xs tw-text-zinc-500 tw-mb-0 tw-mt-1">
            The circle shows the approximate area of your home. For privacy, exact location is only
            shared after booking.
          </p>
        </div>
      </div>
    </div>

    <!-- Information footer -->
    <div class="tw-p-4 tw-bg-gray-50 tw-border-t tw-border-gray-200">
      <div class="tw-flex tw-items-start tw-gap-3">
        <v-icon color="primary" class="tw-mt-0.5"> mdi-shield-home </v-icon>
        <div>
          <h4 class="tw-text-base tw-font-medium tw-text-zinc-700 tw-mb-1">Privacy Protected</h4>
          <p class="tw-text-sm tw-text-zinc-500 tw-mb-0">
            Your exact address will only be shared with guests after they book. The map shows an
            approximate area.
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* Custom styling for home marker icon */
:deep(.home-marker) {
  background-color: #672093; /* Using primary color */
  border-radius: 50%;
  border: 2px solid white;
  width: 24px !important;
  height: 24px !important;
  margin-left: -12px !important;
  margin-top: -12px !important;
  box-shadow: 0 0 0 2px rgba(112, 25, 153, 0.4);
}

:deep(.leaflet-popup-content-wrapper) {
  border-radius: 8px;
  padding: 0;
}

:deep(.leaflet-popup-content) {
  margin: 0;
  line-height: 1.4;
}

:deep(.leaflet-container) {
  font-family: 'Figtree', sans-serif;
}
</style>
