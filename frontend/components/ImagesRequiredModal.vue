<script lang="ts">
// @ts-nocheck
import { defineComponent, watch } from '@nuxtjs/composition-api'

import GoodButton from '~/components/GoodButton.vue'

export default defineComponent({
  name: 'ImagesRequiredModal',

  components: {
    GoodButton,
  },

  props: {
    show: {
      type: Boolean,
      default: false,
    },
    value: {
      type: Boolean,
      default: false,
    },
    selectedTypes: {
      type: Array,
      default: () => [],
    },
  },

  emits: ['close', 'go-to-photos', 'update:show', 'input'],

  setup(props, { emit }) {
    // Determine if modal should be shown (either from show prop or value prop)
    const shouldShow = () => props.show || props.value

    // Add debugging watch to see when visibility changes
    watch(
      [() => props.show, () => props.value],
      ([newShow, newValue]) => {
        console.log('ImagesRequiredModal visibility changed:', { show: newShow, value: newValue })
        console.log('Selected types:', props.selectedTypes)
      },
      { immediate: true }
    )

    const closeModal = () => {
      console.log('Modal close button clicked')
      emit('close')
      emit('update:show', false)
      emit('input', false)
    }

    const goToPhotos = () => {
      console.log('Go to photos button clicked')
      emit('go-to-photos')
      emit('update:show', false)
      emit('input', false)
    }

    return {
      closeModal,
      goToPhotos,
      shouldShow,
    }
  },
})
</script>

<template>
  <!-- Create a complete overlay to ensure the modal is visible -->
  <div v-if="shouldShow()" class="modal-overlay">
    <div class="modal-container">
      <div class="tw-bg-white tw-p-6 tw-rounded-lg tw-shadow-lg tw-max-w-lg tw-mx-auto">
        <div class="tw-flex tw-flex-col tw-gap-4">
          <div class="tw-text-2xl tw-font-semibold tw-text-zinc-600">Images Required</div>

          <div class="tw-text-zinc-600">
            You've selected booking types: {{ selectedTypes.join(', ') }}
          </div>

          <div class="tw-text-zinc-600">
            When publicly renting your home at least 3 images are required. In order to switch your
            home from draft to active all required elements must be completed.
          </div>

          <div class="tw-text-zinc-600">
            You can add these images now or later in the My Homes section.
          </div>

          <div class="tw-flex tw-justify-center tw-gap-4 tw-mt-4">
            <GoodButton @click="goToPhotos">Continue Without Images</GoodButton>
            <GoodButton color="primary" @click="closeModal">Add Images Now</GoodButton>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.modal-container {
  width: 100%;
  max-width: 500px;
  padding: 20px;
}
</style>
