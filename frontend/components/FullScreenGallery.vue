<template>
  <v-dialog
    v-model="dialog"
    scrollable
    fullscreen
    hide-overlay
    transition="dialog-bottom-transition"
  >
    <v-sheet :class="[!showMaximized ? 'sheet-one' : '']">
      <div class="pa-2 fixed-div">
        <v-icon id="back-icon" x-large color="black" @click="goBack"> mdi-chevron-left </v-icon>
      </div>

      <v-container class="pt-16">
        <v-row v-if="!showMaximized">
          <v-col v-for="(image, index) in houseInfo.photos" :key="index" cols="12" md="6">
            <img
              :id="image.src"
              :class="['rounded', 'darken-image-animation']"
              :src="image.src"
              width="100%"
              height="300px"
              loading="lazy"
              style="object-fit: cover"
              :alt="`twimo.com - ${houseInfo.title}`"
              @click="maximizeImage(index)"
            />
          </v-col>
        </v-row>

        <app-lightbox ref="lightbox" />
      </v-container>
    </v-sheet>
  </v-dialog>
</template>

<script>
import AppLightbox from '~/components/AppLightbox.vue'

export default {
  components: { AppLightbox },
  props: {
    houseInfo: {
      type: Object,
      required: true,
    },
  },
  data: () => ({
    dialog: false,
    showMaximized: false,
  }),
  methods: {
    goBack() {
      if (this.showMaximized) {
        this.showMaximized = false
      } else {
        this.toggleDialog()
      }
    },
    toggleDialog(photo) {
      this.dialog = !this.dialog

      if (photo && photo.src) {
        setTimeout(() => {
          const element = document.getElementById(photo.src)
          element.scrollIntoView({ behavior: 'smooth' })
          element.focus()
        }, 0)
      }
    },
    maximizeImage(index) {
      this.$refs.lightbox.toggleLightBox(
        this.houseInfo.photos,
        index,
        !this.$vuetify.breakpoint.mdAndUp
      )
    },
  },
}
</script>

<style scoped>
.sheet-one {
  height: 1000px;
  overflow: auto;
}

.fixed-div {
  position: fixed;
  width: 100%;
}

.max-image {
  margin-bottom: 200px;
}

.darken-image-animation:hover {
  filter: brightness(70%);
  transition: filter 0.5s ease-in;
  cursor: pointer;
}

.darken-image-animation:focus {
  filter: brightness(70%);
  transition: filter 0.5s ease-in;
  cursor: pointer;
}
</style>
