<script lang="ts">
import { defineComponent, computed } from '@nuxtjs/composition-api'

export default defineComponent({
  props: {
    currentStep: {
      type: Number,
      required: true,
    },
    totalSteps: {
      type: Number,
      required: true,
    },
  },
  setup(props) {
    const progressValue = computed(() => {
      return (props.currentStep / props.totalSteps) * 100
    })

    return {
      progressValue,
    }
  },
})
</script>

<template>
  <div class="tw-bg-gray-200 tw-rounded-full tw-w-full">
    <div
      class="tw-bg-primary tw-text-xs tw-font-medium tw-text-purple-100 tw-text-center tw-p-0.5 tw-leading-none tw-rounded-full"
      :style="{ width: `${progressValue}%` }"
    >
      {{ Math.round(progressValue) }}%
    </div>
  </div>
</template>
