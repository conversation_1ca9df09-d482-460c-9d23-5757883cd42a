<script lang="ts">
// @ts-nocheck
import { defineComponent, ref, onMounted, computed } from '@nuxtjs/composition-api'

import OpenStreetAutocomplete from '~/components/OpenStreetAutocomplete.vue'
import CreateHomeMap from '~/components/CreateHomeMap.vue'
import { useCreateHomeProgressStore } from '~/composables/useCreateHomeStore'

export default defineComponent({
  name: 'CreateHomeAddress',

  components: {
    OpenStreetAutocomplete,
    CreateHomeMap,
  },

  setup() {
    const store = useCreateHomeProgressStore()
    const { createHomeData, setCurrentStep, validateCreateHomeData } = store

    const mapLoading = ref(false)
    const locationData = ref(null)
    const addressVerified = computed(() =>
      Boolean(locationData.value?.lat && locationData.value?.lng && createHomeData.address)
    )

    // Handle address selection from autocomplete
    const handleAddressSelection = (addressText, locationDetails = null) => {
      createHomeData.address = addressText

      // If we receive location details directly from the autocomplete component
      if (locationDetails && locationDetails.lat && locationDetails.lng) {
        locationData.value = locationDetails
      } else if (addressText) {
        // Fallback geocoding if we only get the address text
        geocodeAddress(addressText)
      } else {
        // Clear location if address is cleared
        locationData.value = null
      }
    }

    // Geocode address using Nominatim
    const geocodeAddress = async address => {
      if (!address || address.trim().length < 3) {
        locationData.value = null
        return
      }

      mapLoading.value = true
      try {
        // First try with the full address
        const response = await fetch(
          `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(address)}&limit=1&addressdetails=1`
        )

        if (!response.ok) {
          throw new Error(`Error: ${response.status}`)
        }

        const data = await response.json()

        if (data && data.length > 0) {
          // Extract useful address components
          const addressComponents = data[0].address || {}

          // Store normalized data
          locationData.value = {
            lat: parseFloat(data[0].lat),
            lng: parseFloat(data[0].lon),
            full_address: data[0].display_name,
            address_components: addressComponents,
            // Add structured address components for future use
            country: addressComponents.country,
            state: addressComponents.state,
            city: addressComponents.city || addressComponents.town,
            street: addressComponents.road,
            house_number: addressComponents.house_number,
          }

          // Save a complete version of the address
          if (!createHomeData.formatted_address) {
            createHomeData.formatted_address = data[0].display_name
          }
        } else {
          // If no results, try a more permissive search
          const fallbackResponse = await fetch(
            `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(address)}&limit=5`
          )

          const fallbackData = await fallbackResponse.json()

          if (fallbackData && fallbackData.length > 0) {
            // Use the first result as a fallback
            locationData.value = {
              lat: parseFloat(fallbackData[0].lat),
              lng: parseFloat(fallbackData[0].lon),
              full_address: fallbackData[0].display_name,
              address_components: fallbackData[0].address || {},
            }
          } else {
            locationData.value = null
          }
        }
      } catch (error) {
        console.error('Error geocoding address:', error)
        locationData.value = null
      } finally {
        mapLoading.value = false
      }
    }

    // Handle navigation to next step
    const handleNext = () => {
      if (addressVerified.value) {
        // Store the location data for future use
        if (locationData.value) {
          createHomeData.location_data = locationData.value
        }
        setCurrentStep(2)
      }
    }

    // Initialize with existing address if available
    onMounted(() => {
      if (createHomeData.address && createHomeData.address.trim().length > 0) {
        if (createHomeData.location_data) {
          // If we already have location data stored, use it
          locationData.value = createHomeData.location_data
        } else {
          // Otherwise geocode the address
          geocodeAddress(createHomeData.address)
        }
      }
    })

    return {
      createHomeData,
      validateCreateHomeData,
      setCurrentStep,
      mapLoading,
      locationData,
      handleAddressSelection,
      addressVerified,
      handleNext,
    }
  },
})
</script>

<template>
  <div class="create-home-address tw-py-6">
    <!-- Header section -->
    <div class="tw-mb-8">
      <h1 class="tw-text-3xl tw-font-bold tw-text-zinc-800 tw-mb-2">
        Let's start with your address
      </h1>
      <p class="tw-text-lg tw-text-zinc-600">
        Enter the address of the home you want to list on our platform
      </p>
    </div>

    <v-row>
      <v-col cols="12" lg="6" class="tw-mb-6">
        <!-- Address input section -->
        <div class="tw-bg-white tw-rounded-xl tw-shadow-sm tw-p-6 tw-border tw-border-gray-200">
          <OpenStreetAutocomplete
            :address="createHomeData.address"
            placeholder="Enter your complete home address"
            label="Your Home Address"
            @address-selection="handleAddressSelection"
          />

          <!-- Privacy information -->
          <div class="tw-mt-6 tw-bg-gray-50 tw-rounded-lg tw-p-4 tw-border tw-border-gray-200">
            <div class="tw-flex tw-items-start tw-gap-3">
              <v-icon color="primary" class="tw-mt-0.5"> mdi-shield-lock </v-icon>
              <div>
                <h4 class="tw-text-base tw-font-medium tw-text-zinc-700 tw-mb-1">
                  Privacy Guarantee
                </h4>
                <p class="tw-text-sm tw-text-zinc-500 tw-mb-0">
                  Your full address is kept private and only shared with guests after they book. On
                  the public listing, we'll only show the general area.
                </p>
              </div>
            </div>
          </div>
        </div>
      </v-col>

      <v-col cols="12" lg="6">
        <!-- Map section -->
        <div v-if="createHomeData.address || mapLoading">
          <div class="tw-mb-5">
            <h3 class="tw-text-xl tw-font-semibold tw-text-zinc-700">
              <v-icon class="tw-mr-2" color="primary"> mdi-map-marker-check </v-icon>
              Verify Your Location
            </h3>
            <p class="tw-text-zinc-500">
              Please confirm this is the correct location for your property
            </p>
          </div>

          <CreateHomeMap :loading="mapLoading" :location="locationData" :title="'Your Home'" />

          <!-- Verification status -->
          <div
            v-if="addressVerified"
            class="tw-mt-4 tw-p-3 tw-bg-green-50 tw-text-green-700 tw-rounded-lg tw-flex tw-items-center tw-gap-2"
          >
            <v-icon color="success"> mdi-check-circle </v-icon>
            <span class="tw-font-medium">Address verified successfully</span>
          </div>

          <div
            v-else-if="createHomeData.address && !mapLoading"
            class="tw-mt-4 tw-p-3 tw-bg-amber-50 tw-text-amber-700 tw-rounded-lg tw-flex tw-items-center tw-gap-2"
          >
            <v-icon color="warning"> mdi-alert-circle </v-icon>
            <span class="tw-font-medium"
              >We couldn't verify this address. Please try entering it differently.</span
            >
          </div>

          <!-- Nominatim attribution -->
          <div class="tw-mt-3 tw-text-xs tw-text-gray-500 tw-text-right">
            <span>
              Address search powered by
              <a
                href="https://nominatim.org/"
                target="_blank"
                rel="noopener noreferrer"
                class="tw-text-primary"
              >
                Nominatim
              </a>
              /
              <a
                href="https://www.openstreetmap.org/copyright"
                target="_blank"
                rel="noopener noreferrer"
                class="tw-text-primary"
              >
                © OpenStreetMap
              </a>
            </span>
          </div>
        </div>

        <!-- Empty state when no address entered -->
        <div
          v-else
          class="tw-bg-gray-50 tw-rounded-xl tw-p-8 tw-text-center tw-border tw-border-gray-200 tw-flex tw-flex-col tw-items-center tw-min-h-[400px] tw-justify-center"
        >
          <v-icon x-large color="primary" class="tw-opacity-60 tw-mb-4"> mdi-home-search </v-icon>
          <h3 class="tw-text-xl tw-font-medium tw-text-zinc-700 tw-mb-2">No address entered yet</h3>
          <p class="tw-text-zinc-500 tw-max-w-sm tw-mx-auto">
            Enter your property's address to see it on the map and continue with listing your home.
          </p>
        </div>
      </v-col>
    </v-row>
  </div>
</template>

<style scoped>
.create-home-address {
  max-width: 1200px;
  margin: 0 auto;
}

:deep(.v-btn) {
  text-transform: none;
  letter-spacing: normal;
}
</style>
