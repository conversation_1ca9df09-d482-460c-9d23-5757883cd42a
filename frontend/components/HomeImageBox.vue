<script>
export default {
  props: ['image', 'typeText', 'title', 'description', 'animate_delay'],
}
</script>
<template>
  <div
    class="md:tw-mb-5 tw-flex tw-flex-col tw-pt-[2rem] tw-pb-[3rem] tw-px-[1rem] tw-items-center quick_info animate"
    :class="`animate-${animate_delay}`"
  >
    <div
      v-if="image"
      class="tw-mb-9 tw-bg-gradient-to-r tw-from-[#480A83] tw-to-[#672093] tw-rounded-full tw-size-[5.5rem] tw-flex tw-items-center tw-justify-center tw-p-3"
    >
      <p v-if="typeText" class="tw-text-white tw-mb-0 tw-font-extrabold tw-text-xl">
        {{ image }}
      </p>
      <img
        v-else
        :src="image"
        class="tw-object-cover tw-w-15 tw-aspect-square tw-transition-all tw-duration-300 hover:tw-scale-125 tw-cursor-pointer"
        :alt="title"
      />
    </div>
    <h3
      v-if="title"
      class="tw-text-[#360877] tw-text-xl md:tw-text-xl tw-mb-5 tw-font-semibold tw-text-center"
      v-html="title"
    ></h3>
    <div
      class="tw-text-[#360877] tw-text-base md:tw-text-base tw-leading-2 tw-text-center"
      v-html="description"
    ></div>
  </div>
</template>

<style scoped>
.quick_info {
  background: rgba(242, 242, 242, 0.71);
  box-shadow: 0px 2px 2px #360877;
  border-radius: 40px;
}

.quick_info > div:first-child {
  box-shadow: 0px 2px 2px #8b8b8b;
}

.fade-default {
  animation: none;
}

.animate-1 {
  transition-delay: 0.1s !important;
}

.animate-2 {
  transition-delay: 0.3s !important;
}

.animate-3 {
  transition-delay: 0.5s !important;
}
</style>
