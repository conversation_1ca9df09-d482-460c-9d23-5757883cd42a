<template>
  <div class="tw-flex tw-flex-col tw-gap-6">
    <div class="tw-flex tw-flex-col tw-gap-4">
      <div class="tw-text-2xl tw-font-semibold tw-text-zinc-600">
        {{ isHost ? 'Review Your Guest' : 'Review Your Stay' }}
      </div>

      <div class="tw-flex tw-flex-col tw-gap-2">
        <div class="tw-text-lg tw-font-medium tw-text-zinc-500">
          {{ isHost ? 'Guest Stay' : 'Your Stay' }}
        </div>
        <div class="tw-text-lg tw-text-zinc-500">
          {{ booking?.toHome?.title }}
        </div>
        <div class="tw-text-base tw-text-zinc-400">
          {{ booking?.toHome?.address }}
        </div>
        <div class="tw-text-base tw-text-zinc-400">
          {{ formatDateRange(booking?.start_at, booking?.end_at) }}
        </div>
      </div>

      <div class="tw-flex tw-flex-col tw-gap-2">
        <div class="tw-text-lg tw-font-medium tw-text-zinc-500">
          {{ isHost ? 'Rate your guest' : 'Rate your stay' }}
        </div>

        <div class="tw-flex tw-gap-2">
          <v-rating
            v-model="rating"
            color="primary"
            background-color="grey lighten-2"
            hover
            length="5"
            size="36"
          ></v-rating>
        </div>

        <v-textarea
          v-model="feedback"
          outlined
          dense
          placeholder="Type any additional feedback here"
          rows="4"
          class="tw-mt-2"
        ></v-textarea>
      </div>
    </div>

    <GoodButton class="tw-w-full" :disabled="!rating" @click="submitReview">
      Submit Rating
    </GoodButton>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, PropType } from '@nuxtjs/composition-api'

import { useApi } from '~/composables/useCommon'
import { formatDateRange } from '~/helpers'
import GoodButton from '~/components/GoodButton.vue'

export default defineComponent({
  components: {
    GoodButton,
  },
  props: {
    bookingId: {
      type: Number,
      required: true,
    },
    type: {
      type: String as PropType<'guest_review' | 'host_review'>,
      required: true,
    },
    booking: {
      type: Object,
      required: true,
    },
  },

  setup(props, { emit }) {
    const api = useApi()
    const rating = ref(0)
    const feedback = ref('')

    const isHost = computed(() => props.type === 'host_review')

    const submitReview = async () => {
      try {
        const { data } = await api.post(`/bookings/${props.bookingId}/reviews`, {
          rating: rating.value,
          feedback: feedback.value,
          type: props.type,
        })
        emit('review-submitted', data)
      } catch (error: any) {
        emit('review-error', error.response?.data?.message || 'Failed to submit review')
      }
    }

    return {
      rating,
      feedback,
      submitReview,
      formatDateRange,
      isHost,
    }
  },
})
</script>
