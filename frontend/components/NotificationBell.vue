<template>
  <div class="tw-relative notification-bell">
    <!-- Archive All Confirmation Dialog -->
    <ConfirmationDialog
      v-model="showArchiveAllConfirm"
      title="Archive All Notifications"
      message="Are you sure you want to archive all notifications? Archived notifications will not appear again, even if they are recurring notifications."
      confirm-text="Archive All"
      cancel-text="Cancel"
      @confirm="archiveAllNotifications"
    />

    <!-- Archive Single Notification Confirmation Dialog -->
    <ConfirmationDialog
      v-model="showArchiveConfirm"
      title="Archive Notification"
      message="Are you sure you want to archive this notification? Archived notifications will not appear again, even if they are recurring notifications."
      confirm-text="Archive"
      cancel-text="Cancel"
      @confirm="() => {
        if (notificationToArchive) {
          doArchiveNotification(notificationToArchive)
        }
      }"
    />
    <v-menu
      v-model="isOpen"
      :close-on-content-click="false"
      offset-y
      width="500"
      rounded="xl"
      transition="slide-y-transition"
      :eager="true"
    >
      <template #activator="{ on, attrs }">
        <button
          v-bind="attrs"
          class="tw-relative tw-p-2 tw-text-primary hover:tw-text-primary-dark focus:tw-outline-none"
          v-on="on"
          @click="fetchNotifications"
        >
          <svg class="tw-w-6 tw-h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"
            />
          </svg>
          <span
            v-if="unreadCount > 0"
            class="tw-absolute tw-top-[-4px] tw-right-[-4px] tw-inline-flex tw-items-center tw-justify-center tw-min-w-[18px] tw-h-[18px] tw-text-xs tw-font-bold tw-leading-none tw-text-white tw-bg-primary tw-rounded-full"
          >
            {{ unreadCount }}
          </span>
        </button>
      </template>

      <good-card class="tw-w-[500px] !tw-max-w-[500px]">
        <div class="tw-flex tw-flex-col">
          <!-- Tabs for All, Unread, Archived -->
          <div class="tw-flex tw-border-b">
            <button
              v-for="tab in tabs"
              :key="tab.value"
              class="tw-flex-1 tw-px-3 tw-py-3 tw-text-lg tw-font-medium tw-transition-colors"
              :class="{
                'tw-text-primary tw-border-b-2 tw-border-primary': activeTab === tab.value,
                'tw-text-gray-500 hover:tw-text-primary': activeTab !== tab.value
              }"
              @click="setActiveTab(tab.value)"
            >
              {{ tab.label }}
            </button>
          </div>

          <!-- Notification List -->
          <v-list class="tw-max-h-96 tw-overflow-y-auto">
            <v-skeleton-loader v-if="loading" type="list-item-three-line" :loading="loading" />

            <v-alert v-else-if="error" type="error" class="tw-mx-4 tw-my-2">
              {{ error }}
            </v-alert>

            <template v-else>
              <!-- Action Buttons -->
              <div v-if="activeNotifications.length > 0" class="tw-px-4 tw-py-2 tw-flex tw-justify-between tw-items-center">
                <div v-if="activeTab === 'archived'" class="tw-text-xs tw-text-gray-500">
                  Archived notifications won't reappear
                </div>
                <div v-else-if="activeTab === 'all' || activeTab === 'unread'" class="tw-text-xs tw-text-gray-500">
                  <span>{{ activeNotifications.length }} notification(s)</span>
                </div>

                <div class="tw-flex tw-gap-2">
                  <a
                    v-if="(activeTab === 'all' || activeTab === 'unread') && unreadCount > 0"
                    href="#"
                    class="tw-text-xs tw-text-primary hover:tw-text-primary-dark"
                    @click.prevent="markAllAsRead"
                  >
                    Mark all as read
                  </a>
                  <a
                    v-if="activeTab === 'all' && activeNotifications.length > 0"
                    href="#"
                    class="tw-text-xs tw-text-primary hover:tw-text-primary-dark tw-whitespace-nowrap"
                    @click.prevent="confirmArchiveAll"
                  >
                    Archive all
                  </a>
                </div>
              </div>
              <div v-if="activeTab === 'archived' && activeNotifications.length === 0" class="tw-px-4 tw-py-2 tw-text-xs tw-text-gray-500 tw-border-b">
                Archived notifications won't reappear
              </div>

              <!-- Notification Items -->
              <div v-for="notification in activeNotifications" :key="notification.id" class="tw-relative">
                <v-list-item
                  :to="notification.data.link"
                  :class="{ 'tw-bg-primary/5': !notification.read_at }"
                  class="notification-item tw-transition-colors hover:tw-bg-gray-50 tw-pr-12"
                  @click="!notification.read_at && markAsRead(notification.id)"
                >
                  <v-list-item-content>
                    <v-list-item-title class="tw-font-medium">
                      {{ notification.data.title }}
                    </v-list-item-title>
                    <v-list-item-subtitle class="tw-text-gray-600">
                      {{ notification.data.message }}
                    </v-list-item-subtitle>
                  </v-list-item-content>
                </v-list-item>

                <!-- Action buttons -->
                <div class="tw-absolute tw-right-2 tw-top-1/2 tw-transform tw--translate-y-1/2 tw-flex tw-gap-1">
                  <v-tooltip bottom>
                    <template #activator="{ on, attrs }">
                      <v-btn
                        v-bind="attrs"
                        icon
                        x-small
                        class="tw-text-gray-400 hover:tw-text-primary"
                        v-on="on"
                        @click.stop="activeTab === 'archived' ? unarchiveNotification(notification.id) : archiveNotification(notification.id)"
                      >
                        <v-icon v-if="activeTab === 'archived'">
                          mdi-archive-arrow-up
                        </v-icon>
                        <v-icon v-else>
                          mdi-archive
                        </v-icon>
                      </v-btn>
                    </template>
                    <span>{{ activeTab === 'archived' ? 'Unarchive' : 'Archive' }}</span>
                  </v-tooltip>
                </div>
              </div>

              <!-- Empty State -->
              <div v-if="activeNotifications.length === 0" class="tw-py-10 tw-px-4 tw-text-center tw-text-gray-500">
                <div class="tw-text-xl">
                  <span v-if="activeTab === 'archived'">No archived notifications</span>
                  <span v-else-if="activeTab === 'unread'">No unread notifications</span>
                  <span v-else>No notifications</span>
                </div>
              </div>
            </template>
          </v-list>
        </div>
      </good-card>
    </v-menu>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted, computed, useContext } from '@nuxtjs/composition-api'

import { useToast } from '~/composables/useCommon'
import ConfirmationDialog from '~/components/ConfirmationDialog.vue'
import { NOTIFICATION_TABS } from '~/constants/notifications'

export default {
  name: 'NotificationBell',

  components: {
    ConfirmationDialog,
  },

  setup() {
    const { store } = useContext()
    const toast = useToast()
    const isOpen = ref(false)
    const showArchiveAllConfirm = ref(false)
    const showArchiveConfirm = ref(false)
    const notificationToArchive = ref(null)

    const tabs = NOTIFICATION_TABS

    const loading = computed(() => store.state.notifications.loading)
    const error = computed(() => store.state.notifications.error)
    const notifications = computed(() => store.state.notifications.notifications)
    const archivedNotifications = computed(() => store.state.notifications.archivedNotifications)
    const unreadCount = computed(() => store.state.notifications.unreadCount)
    const activeTab = computed(() => store.state.notifications.activeTab)
    const activeNotifications = computed(() => store.getters['notifications/activeNotifications'])

    const fetchNotifications = () => {
      store.dispatch('notifications/fetchNotifications')
    }

    const setActiveTab = (tab) => {
      store.dispatch('notifications/setActiveTab', tab)
    }

    const markAsRead = id => {
      store.dispatch('notifications/markAsRead', id)
    }

    const markAllAsRead = () => {
      store.dispatch('notifications/markAllAsRead')
      toast.success('All notifications marked as read').goAway(3000)
    }

    const archiveNotification = id => {
      notificationToArchive.value = id
      showArchiveConfirm.value = true
    }

    const doArchiveNotification = id => {
      store.dispatch('notifications/archiveNotification', id)
      toast.success('Notification archived').goAway(3000)
      notificationToArchive.value = null
    }

    const unarchiveNotification = id => {
      store.dispatch('notifications/unarchiveNotification', id)
      toast.success('Notification unarchived').goAway(3000)
    }

    const confirmArchiveAll = () => {
      showArchiveAllConfirm.value = true
    }

    const archiveAllNotifications = () => {
      store.dispatch('notifications/archiveAllNotifications')
      toast.success('All notifications archived').goAway(3000)
    }

    onMounted(() => {
      // Start both polling intervals
      store.dispatch('notifications/startPolling')
    })

    onUnmounted(() => {
      // Clean up both polling intervals
      store.dispatch('notifications/stopPolling')
    })

    return {
      isOpen,
      loading,
      error,
      notifications,
      archivedNotifications,
      unreadCount,
      activeTab,
      activeNotifications,
      tabs,
      showArchiveAllConfirm,
      showArchiveConfirm,
      notificationToArchive,
      fetchNotifications,
      setActiveTab,
      markAsRead,
      markAllAsRead,
      archiveNotification,
      doArchiveNotification,
      unarchiveNotification,
      confirmArchiveAll,
      archiveAllNotifications,
    }
  },
}
</script>

<style lang="scss" scoped>
.notification-item {
  min-height: 40px !important;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  cursor: pointer;
  padding: 12px 16px !important;
  position: relative;

  &:last-child {
    border-bottom: none;
  }

  :deep(.v-list-item__title) {
    white-space: normal !important;
    -webkit-line-clamp: unset !important;
    -webkit-box-orient: unset !important;
    overflow: visible !important;
    text-overflow: unset !important;
    line-height: 1.4 !important;
    margin-bottom: 4px !important;
  }

  :deep(.v-list-item__subtitle) {
    white-space: normal !important;
    -webkit-line-clamp: unset !important;
    -webkit-box-orient: unset !important;
    overflow: visible !important;
    text-overflow: unset !important;
    line-height: 1.4 !important;
  }
}

.notification-bell {
  .v-menu__content {
    margin-top: 8px;
    overflow: hidden;
    border-radius: 16px;
    max-width: 500px !important;
  }
}

.v-list {
  padding: 0 !important;
}
</style>
