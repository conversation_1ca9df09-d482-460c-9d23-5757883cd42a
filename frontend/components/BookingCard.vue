<script lang="ts">
// @ts-nocheck

import { defineComponent, ref, computed } from '@nuxtjs/composition-api'

import { Booking } from '~/types'
import GoodCard2 from '~/components/GoodCard2.vue'
import GoodButton from '~/components/GoodButton.vue'
import { useToast, useApi } from '~/composables/useCommon'
import useAddressFormatter from '~/composables/useAddressFormatter'
import ReviewForm from '~/components/ReviewForm.vue'
import ReviewList from '~/components/ReviewList.vue'

export default defineComponent({
  components: {
    GoodCard2,
    GoodButton,
    ReviewForm,
    ReviewList,
  },

  props: {
    booking: {
      type: Object as () => Booking,
      required: true,
    },
    bookingType: {
      type: String,
      required: true,
    },
    isHost: {
      type: Function,
      required: true,
    },
    getBookingRole: {
      type: Function,
      required: true,
    },
    getHomeTitle: {
      type: Function,
      required: true,
    },
    getOtherHomeTitle: {
      type: Function,
      required: true,
    },
    formatDateRange: {
      type: Function,
      required: true,
    },
    onMessageClick: {
      type: Function,
      required: true,
    },
    onCancelClick: {
      type: Function,
      required: true,
    },
    onApproveClick: {
      type: Function,
      required: true,
    },
    onApproveIndefinitelyClick: {
      type: Function,
      required: true,
    },
    onDenyClick: {
      type: Function,
      required: true,
    },
    onConfirmVenmoPayment: {
      type: Function,
      required: true,
    },
  },

  setup(props) {
    const toast = useToast()
    const api = useApi()
    const { formatAddress } = useAddressFormatter()
    const showReviewForm = ref(false)
    const showReviews = ref(false)

    const currentUserReview = computed(() => {
      if (!props.booking?.reviews?.length) return null

      const isHost = props.isHost(props.booking)
      const reviewType = isHost ? 'host_review' : 'guest_review'

      return props.booking.reviews.find(review => review.type === reviewType)
    })

    const canLeaveReview = computed(() => {
      if (!props.booking) return false

      // Only allow reviews for completed bookings
      if (props.booking.status !== 'completed') return false

      // Check if booking end date is in the past
      const endDate = new Date(props.booking.end_at)
      const now = new Date()
      if (endDate > now) return false

      const isHost = props.isHost(props.booking)
      const reviewType = isHost ? 'host_review' : 'guest_review'

      // Check if the user has already left a review
      const hasReview = props.booking.reviews?.some(review => review.type === reviewType)

      return !hasReview
    })

    const hasReviews = computed(() => {
      return props.booking?.reviews && props.booking.reviews.length > 0
    })

    const handleReviewSubmitted = async () => {
      showReviewForm.value = false
      toast.success('Review submitted successfully')

      // Refresh the booking data to get updated reviews
      try {
        const { data } = await api.get(`/bookings/${props.booking.id}`)
        Object.assign(props.booking, data)
      } catch (error) {
        console.error('Error refreshing booking data:', error)
      }
    }

    const handleReviewError = (error: string) => {
      toast.error(error)
    }

    const homeAverageRating = computed(() => {
      const guestReviews =
        props.booking.toHome?.reviews?.filter(review => review.type === 'guest_review') || []
      const total = guestReviews.reduce((sum, review) => sum + review.rating, 0)
      return guestReviews.length ? (total / guestReviews.length).toFixed(1) : '0'
    })

    // Format address based on booking status and payment status
    const formattedAddress = computed(() => {
      const home = props.booking.toHome
      if (!home) return 'Address not available'

      return formatAddress(
        home.address,
        home.city_long,
        home.state_long,
        home.country_long,
        props.booking.status,
        props.booking.payment_status
      )
    })

    // Check if the booking is private (from a shareable link)
    const isPrivateBooking = computed(() => {
      return !!props.booking.from_sharable_link
    })

    // Check if the home is archived (deleted)
    const isHomeArchived = computed(() => {
      return props.booking.toHome?.status === 'archived' ||
             (props.booking.booking_type === 'SWAP' && props.booking.fromHome?.status === 'archived')
    })

    return {
      isPrivateBooking,
      isHomeArchived,
      showReviewForm,
      showReviews,
      canLeaveReview,
      hasReviews,
      currentUserReview,
      homeAverageRating,
      formattedAddress,
      handleReviewSubmitted,
      handleReviewError,
    }
  },
})
</script>

<template>
  <GoodCard2
    :class="[
      'tw-p-0 tw-overflow-hidden tw-rounded-xl tw-shadow-md',
      isPrivateBooking ? 'tw-border-2 tw-border-purple-300' : '',
      isHomeArchived ? 'tw-border-2 tw-border-red-300 tw-opacity-75' : ''
    ]"
  >
    <!-- Card Header with Status Badge -->
    <div
      :class="[
        'tw-p-4 sm:tw-p-6 tw-border-b tw-border-gray-100 tw-flex tw-justify-between tw-items-center',
        isPrivateBooking ? 'tw-bg-purple-50' : 'tw-bg-gray-50'
      ]"
    >
      <div class="tw-flex tw-items-center tw-gap-2">
        <span
          :class="[
            'tw-px-4 tw-py-1.5 tw-rounded-full tw-text-sm tw-font-semibold tw-inline-block tw-shadow-sm',
            isHost(booking) ? 'tw-bg-primary/10 tw-text-primary' : 'tw-bg-primary/10 tw-text-primary',
          ]"
        >
          {{ getBookingRole(booking) }}
        </span>

        <!-- Private/Public Badge -->
        <span
          :class="[
            'tw-px-3 tw-py-1 tw-rounded-full tw-text-xs tw-font-medium tw-inline-block tw-shadow-sm',
            isPrivateBooking
              ? 'tw-bg-purple-100 tw-text-purple-800 tw-border tw-border-purple-300'
              : 'tw-bg-blue-100 tw-text-blue-800 tw-border tw-border-blue-300'
          ]"
        >
          {{ isPrivateBooking
            ? (bookingType === 'SWAP' ? 'Private Swap' : 'Private Booking')
            : (bookingType === 'SWAP' ? 'Public Swap' : 'Public Booking') }}
        </span>
      </div>
    </div>

    <!-- Archived Home Warning -->
    <div v-if="isHomeArchived" class="tw-bg-red-50 tw-p-4 tw-border-b tw-border-red-100">
      <div class="tw-flex tw-items-start">
        <div class="tw-flex-shrink-0">
          <svg class="tw-h-5 tw-w-5 tw-text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="tw-ml-3">
          <h3 class="tw-text-sm tw-font-medium tw-text-red-800">This home has been removed from Twimo</h3>
          <div class="tw-mt-2 tw-text-sm tw-text-red-700">
            <p>This booking is for a home that is no longer available on Twimo. Explore other homes instead.</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Card Body -->
    <div class="tw-p-4 sm:tw-p-6">
      <div class="tw-grid tw-grid-cols-1 md:tw-grid-cols-12 tw-gap-6">
        <!-- Home Image(s) Section -->
        <div class="md:tw-col-span-3 tw-flex tw-justify-center md:tw-justify-start">
          <div class="tw-relative tw-w-32 tw-h-32 sm:tw-w-40 sm:tw-h-40 md:tw-w-44 md:tw-h-44">
            <div class="tw-absolute tw-inset-0 tw-z-10">
              <BeastImage
                :src="booking.toHome?.photos?.[0]?.src || ''"
                :alt="booking.toHome?.title || ''"
                class="tw-rounded-full tw-w-full tw-h-full tw-object-cover tw-border-4 tw-border-white tw-shadow-md hover:tw-brightness-105 tw-transition-all"
                @click="booking.toHome?.slug ? $router.push(isHost(booking) ?
                  `/${booking.toHome.slug}/edit` :
                  (isPrivateBooking && booking.from_sharable_link ?
                    `/${booking.toHome.slug}?sharable_link=${booking.from_sharable_link}` :
                    `/${booking.toHome.slug}`)) : null"
              />
            </div>
            <div
              v-if="bookingType === 'SWAP'"
              class="tw-absolute tw-inset-0 tw-z-0 tw-translate-x-1/3 tw-translate-y-1/3"
            >
              <BeastImage
                :src="booking.fromHome?.photos[0]?.src || ''"
                :alt="booking.fromHome?.title"
                class="tw-rounded-full tw-w-full tw-h-full tw-object-cover tw-border-4 tw-border-white tw-shadow-md tw-cursor-pointer hover:tw-brightness-105 tw-transition-all"
                @click="booking.fromHome?.slug ? $router.push(`/${booking.fromHome.slug}`) : null"
              />
            </div>
          </div>
        </div>

        <!-- Main Content Area -->
        <div class="md:tw-col-span-9">
          <!-- Primary Info - 3 column grid on desktop, single column on mobile -->
          <div
            class="tw-grid tw-grid-cols-1 sm:tw-grid-cols-2 lg:tw-grid-cols-3 tw-gap-x-8 tw-gap-y-5"
          >
            <!-- Home Title Section -->
            <div class="tw-border-l-4 tw-border-primary tw-pl-3">
              <h3 class="tw-text-gray-500 tw-text-xs tw-uppercase tw-tracking-wider tw-font-medium">
                {{ getHomeTitle(booking) }}
              </h3>
              <p
                class="tw-text-primary tw-font-bold tw-text-lg tw-mt-1 tw-cursor-pointer hover:tw-underline tw-truncate"
                @click="booking.toHome?.slug ? $router.push(isHost(booking) ?
                  `/${booking.toHome.slug}/edit` :
                  (isPrivateBooking && booking.from_sharable_link ?
                    `/${booking.toHome.slug}?sharable_link=${booking.from_sharable_link}` :
                    `/${booking.toHome.slug}`)) : null"
              >
                {{ booking.toHome?.title || 'Home' }}
              </p>
            </div>

            <!-- Other Home Section (only for SWAP) -->
            <template v-if="bookingType === 'SWAP'">
              <div class="tw-border-l-4 tw-border-primary tw-pl-3">
                <h3
                  class="tw-text-gray-500 tw-text-xs tw-uppercase tw-tracking-wider tw-font-medium"
                >
                  {{ getOtherHomeTitle(booking) }} for Swap
                </h3>
                <p
                  class="tw-text-primary tw-font-bold tw-text-lg tw-mt-1 tw-cursor-pointer hover:tw-underline tw-truncate"
                  @click="$router.push(`/${booking.fromHome?.slug}`)"
                >
                  {{ booking.fromHome?.title }}
                </p>
              </div>
            </template>

            <!-- Dates Section -->
            <div class="tw-border-l-4 tw-border-primary/70 tw-pl-3">
              <h3 class="tw-text-gray-500 tw-text-xs tw-uppercase tw-tracking-wider tw-font-medium">
                Dates
              </h3>
              <p class="tw-font-semibold tw-text-lg tw-mt-1 tw-truncate">
                {{ formatDateRange(booking.start_at, booking.end_at) }}
              </p>
            </div>

            <!-- Address Section -->
            <div class="tw-border-l-4 tw-border-primary/70 tw-pl-3">
              <h3 class="tw-text-gray-500 tw-text-xs tw-uppercase tw-tracking-wider tw-font-medium">
                Address
              </h3>
              <p class="tw-font-semibold tw-text-lg tw-mt-1 tw-truncate">
                {{ formattedAddress }}
              </p>
            </div>

            <!-- Payment Status (for non-SWAP) -->
            <div
v-if="bookingType !== 'SWAP'"
              :class="[
                'tw-border-l-4 tw-pl-3',
                isPrivateBooking ? 'tw-border-purple-400' : 'tw-border-primary/50'
              ]"
            >
              <div class="tw-flex tw-items-center tw-gap-2">
                <h3 class="tw-text-gray-500 tw-text-xs tw-uppercase tw-tracking-wider tw-font-medium">
                  Payment Status
                </h3>
                <span v-if="isPrivateBooking" class="tw-bg-purple-100 tw-text-purple-800 tw-text-xs tw-px-2 tw-py-0.5 tw-rounded">
                  Venmo Enabled
                </span>
              </div>
              <div class="tw-font-semibold tw-text-lg tw-mt-1">
                <template v-if="booking.status === 'completed'">
                  <span class="tw-text-green-600">
                    Payment Completed: ${{
                      booking.extra_info.priceInfo?.hostPaymentAmount ||
                      booking.extra_info.priceInfo?.getTotalMoney
                    }}
                  </span>
                </template>
                <template
                  v-else-if="booking.status === 'accepted' || booking.status === 'requested'"
                >
                  <template
                    v-if="
                      booking.extra_info?.venmo_payment &&
                      !booking.extra_info?.venmo_payment?.confirmed_at &&
                      isHost(booking)
                    "
                  >
                    <div
                      :class="[
                        'tw-mt-2 tw-p-3 tw-rounded-md tw-border',
                        isPrivateBooking
                          ? 'tw-bg-purple-50 tw-border-purple-200'
                          : 'tw-bg-primary/5 tw-border-primary/20'
                      ]"
                    >
                      <p
:class="[
                        'tw-text-base tw-font-medium',
                        isPrivateBooking ? 'tw-text-purple-700' : 'tw-text-primary'
                      ]">
                        Venmo Payment Marked as Sent: ${{ booking.extra_info.venmo_payment.amount }}
                      </p>
                      <GoodButton
                        class="tw-mt-3 tw-py-2 tw-text-sm tw-h-auto tw-w-full"
                        size="normal"
                        @click="onConfirmVenmoPayment(booking)"
                      >
                        Confirm Venmo Payment
                      </GoodButton>
                    </div>
                  </template>
                  <template v-else>
                    <span
:class="[
                      isPrivateBooking ? 'tw-text-purple-700' : 'tw-text-primary'
                    ]">
                      Payment Pending: ${{
                        booking.extra_info.priceInfo?.hostPaymentAmount ||
                        booking.extra_info.priceInfo?.getTotalMoney
                      }}
                    </span>
                  </template>
                </template>
                <template v-else>
                  <span class="tw-text-gray-500">Not Applicable</span>
                </template>
              </div>
            </div>

            <!-- Payment Status for SWAP bookings -->
            <div
v-if="bookingType === 'SWAP'"
              :class="[
                'tw-border-l-4 tw-pl-3',
                isPrivateBooking ? 'tw-border-purple-400' : 'tw-border-primary/50'
              ]"
            >
              <div class="tw-flex tw-items-center tw-gap-2">
                <h3 class="tw-text-gray-500 tw-text-xs tw-uppercase tw-tracking-wider tw-font-medium">
                  Swap Payment Status
                </h3>
              </div>
              <div class="tw-font-semibold tw-mt-1">
                <template v-if="booking.status === 'completed'">
                  <span class="tw-text-green-600 tw-text-base">
                    All Payments Completed
                  </span>
                </template>
                <template v-else-if="booking.status === 'accepted' || booking.status === 'requested'">
                  <!-- Display both host and guest payment status -->
                  <div class="tw-flex tw-flex-col tw-gap-2">
                    <!-- Host Home Cleaning Fee (paid by guest) -->
                    <div class="tw-flex tw-items-center tw-gap-2">
                      <span class="tw-text-sm tw-font-medium tw-text-gray-600">Host Home Cleaning Fee:</span>
                      <span v-if="booking.host_cleaning_fee_enabled" class="tw-text-sm">
                        <template v-if="booking.extra_info.priceInfo?.guestPaymentIntentCompleted">
                          <span class="tw-text-green-600 tw-flex tw-items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="tw-h-4 tw-w-4 tw-mr-1" viewBox="0 0 20 20" fill="currentColor">
                              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                            Paid (${{ booking.extra_info.priceInfo?.guestPaymentAmount || booking.toHome?.cleaning_fee || 0 }})
                          </span>
                        </template>
                        <template v-else>
                          <span class="tw-text-orange-500 tw-flex tw-items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="tw-h-4 tw-w-4 tw-mr-1" viewBox="0 0 20 20" fill="currentColor">
                              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd" />
                            </svg>
                            Pending (${{ booking.extra_info.priceInfo?.guestPaymentAmount || booking.toHome?.cleaning_fee || 0 }})
                          </span>
                          <!-- Only show payment button if status is accepted -->
                          <GoodButton
                            v-if="!isHost(booking) && booking.status === 'accepted'"
                            class="tw-mt-1 tw-py-1 tw-text-xs tw-h-auto"
                            size="small"
                            :disabled="isHomeArchived"
                            @click="$router.push(`/bookings/${booking.id}/payment`)"
                          >
                            Make Payment
                          </GoodButton>
                        </template>
                      </span>
                      <span v-else class="tw-text-sm tw-text-gray-500">Not Required</span>
                    </div>

                    <!-- Guest Home Cleaning Fee (paid by host) -->
                    <div class="tw-flex tw-items-center tw-gap-2">
                      <span class="tw-text-sm tw-font-medium tw-text-gray-600">Guest Home Cleaning Fee:</span>
                      <span v-if="booking.guest_cleaning_fee_enabled" class="tw-text-sm">
                        <template v-if="booking.extra_info.priceInfo?.hostPaymentIntentCompleted">
                          <span class="tw-text-green-600 tw-flex tw-items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="tw-h-4 tw-w-4 tw-mr-1" viewBox="0 0 20 20" fill="currentColor">
                              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                            Paid (${{ booking.extra_info.priceInfo?.hostPaymentAmount || booking.fromHome?.cleaning_fee || 0 }})
                          </span>
                        </template>
                        <template v-else>
                          <span class="tw-text-orange-500 tw-flex tw-items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="tw-h-4 tw-w-4 tw-mr-1" viewBox="0 0 20 20" fill="currentColor">
                              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd" />
                            </svg>
                            Pending (${{ booking.extra_info.priceInfo?.hostPaymentAmount || booking.fromHome?.cleaning_fee || 0 }})
                          </span>
                          <!-- Only show payment button if status is accepted -->
                          <GoodButton
                            v-if="isHost(booking) && booking.status === 'accepted'"
                            class="tw-mt-1 tw-py-1 tw-text-xs tw-h-auto"
                            size="small"
                            :disabled="isHomeArchived"
                            @click="$router.push(`/bookings/${booking.id}/payment`)"
                          >
                            Make Payment
                          </GoodButton>
                        </template>
                      </span>
                      <span v-else class="tw-text-sm tw-text-gray-500">Not Required</span>
                    </div>

                    <!-- Add note for requested/pending status -->
                    <div v-if="booking.status === 'requested'" class="tw-mt-2 tw-text-xs tw-text-gray-600 tw-bg-gray-50 tw-p-2 tw-rounded">
                      <span class="tw-flex tw-items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="tw-h-4 tw-w-4 tw-mr-1" viewBox="0 0 20 20" fill="currentColor">
                          <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                        </svg>
                        Payment options will be available after the swap is approved by both hosts.
                      </span>
                    </div>
                  </div>
                </template>
                <template v-else>
                  <span class="tw-text-gray-500">Not Applicable</span>
                </template>
              </div>
            </div>

            <!-- Guest Info -->
            <div class="tw-border-l-4 tw-border-primary/50 tw-pl-3">
              <h3 class="tw-text-gray-500 tw-text-xs tw-uppercase tw-tracking-wider tw-font-medium">
                Guest
              </h3>
              <p class="tw-font-semibold tw-text-lg tw-mt-1">
                {{ booking.user?.first_name || 'Guest' }}
              </p>
            </div>

            <!-- Confirmation Code -->
            <div class="tw-border-l-4 tw-border-gray-300 tw-pl-3">
              <h3 class="tw-text-gray-500 tw-text-xs tw-uppercase tw-tracking-wider tw-font-medium">
                Confirmation Code
              </h3>
              <p class="tw-font-semibold tw-text-lg tw-mt-1 tw-break-all">
                {{ booking.code }}
              </p>
            </div>

            <!-- Superhog Status if available -->
            <div
              v-if="isHost(booking) && booking.extra_info.superHogInfo?.verification"
              class="tw-border-l-4 tw-border-primary/40 tw-pl-3"
            >
              <h3 class="tw-text-gray-500 tw-text-xs tw-uppercase tw-tracking-wider tw-font-medium">
                Truvi Guest Verification Status
              </h3>
              <p class="tw-font-semibold tw-text-lg tw-mt-1">
                {{ booking.extra_info.superHogInfo?.verification.status }}
              </p>
            </div>

            <!-- Superhog Verification ID if available -->
            <div
              v-if="isHost(booking) && booking.extra_info.superHogInfo?.verification"
              class="tw-border-l-4 tw-border-primary/40 tw-pl-3"
            >
              <h3 class="tw-text-gray-500 tw-text-xs tw-uppercase tw-tracking-wider tw-font-medium">
                Truvi Verification ID
              </h3>
              <p class="tw-font-semibold tw-text-lg tw-mt-1 tw-truncate">
                {{ booking.extra_info.superHogInfo?.verification.verificationId }}
              </p>

              <a v-if="isHost(booking)" href="https://resolutions-web.azurewebsites.net/manual/login" class="tw-inline-block tw-px-4 tw-py-2 tw-border tw-border-[#672093] tw-text-sm tw-text-[#672093] tw-rounded-3xl hover:tw-bg-[#672093] hover:tw-text-white tw-transition tw-duration-200" target="_blank">Resolution Request</a>
            </div>

            <!-- Home Reviews if available -->
            <div
              v-if="booking.toHome?.reviews?.length"
              class="tw-cursor-pointer hover:tw-opacity-90 tw-border-l-4 tw-border-primary/60 tw-pl-3"
              @click="showReviews = true"
            >
              <h3 class="tw-text-gray-500 tw-text-xs tw-uppercase tw-tracking-wider tw-font-medium">
                Home Reviews
              </h3>
              <div class="tw-flex tw-flex-col tw-mt-1">
                <div class="tw-flex tw-items-center">
                  <v-rating
                    :value="homeAverageRating"
                    color="primary"
                    background-color="grey lighten-2"
                    readonly
                    dense
                    half-increments
                    size="18"
                  ></v-rating>
                  <span class="tw-ml-2 tw-font-semibold">{{ homeAverageRating }}</span>
                </div>
                <p class="tw-text-zinc-600 tw-text-sm tw-mt-1 tw-line-clamp-1">
                  Click to view
                  {{ booking.toHome?.reviews?.filter(r => r.type === 'guest_review').length }}
                  home reviews
                </p>
              </div>
            </div>

            <!-- User's Review if available -->
            <div v-if="hasReviews" class="tw-border-l-4 tw-border-primary/60 tw-pl-3">
              <h3 class="tw-text-gray-500 tw-text-xs tw-uppercase tw-tracking-wider tw-font-medium">
                Your Review for {{ isHost(booking) ? 'Guest' : 'Stay' }}
              </h3>
              <div class="tw-flex tw-flex-col tw-mt-1">
                <div class="tw-flex tw-items-center">
                  <v-rating
                    :value="currentUserReview?.rating || 0"
                    color="primary"
                    background-color="grey lighten-2"
                    readonly
                    dense
                    half-increments
                    size="18"
                  ></v-rating>
                  <span class="tw-ml-2 tw-font-semibold">{{ currentUserReview?.rating || 0 }}</span>
                </div>
                <p class="tw-text-zinc-600 tw-text-sm tw-mt-1 tw-line-clamp-2">
                  {{ currentUserReview?.feedback || 'No review text provided' }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Card Footer with Action Buttons -->
    <div
      :class="[
        'tw-border-t tw-border-gray-100 tw-p-4 sm:tw-p-6',
        isPrivateBooking ? 'tw-bg-purple-50' : 'tw-bg-gray-50'
      ]"
    >
      <div v-if="isPrivateBooking" class="tw-mb-3 tw-flex tw-items-center tw-gap-2">
        <span class="tw-bg-purple-100 tw-text-purple-800 tw-px-3 tw-py-1 tw-rounded-md tw-text-sm tw-font-medium tw-flex tw-items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="tw-h-4 tw-w-4 tw-mr-1" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd" />
          </svg>
          {{ bookingType === 'SWAP' ? 'Private Swap' : 'Private Booking' }}
        </span>
        <span class="tw-text-gray-600 tw-text-sm">This {{ bookingType === 'SWAP' ? 'swap' : 'booking' }} was created via a private {{ bookingType === 'SWAP' ? 'swap' : 'booking' }} link</span>
      </div>
      <div class="tw-flex tw-flex-col sm:tw-flex-row tw-flex-wrap tw-justify-between tw-gap-y-4">
        <!-- Communication Actions -->
        <div class="tw-flex-grow tw-flex tw-flex-wrap tw-gap-3">
          <button
            class="tw-inline-flex tw-items-center tw-justify-center tw-text-primary tw-font-semibold tw-text-base tw-bg-white tw-border-2 tw-border-primary tw-rounded-full tw-px-5 tw-py-3 hover:tw-bg-primary/5 tw-transition-colors tw-shadow-sm tw-min-w-[140px]"
            :disabled="isHomeArchived"
            :class="{ 'tw-opacity-50 tw-cursor-not-allowed': isHomeArchived }"
            @click="onMessageClick(booking)"
          >
            <span class="tw-w-5 tw-h-5 tw-mr-2 tw-flex tw-items-center tw-justify-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="currentColor"
                class="tw-w-5 tw-h-5"
              >
                <path
                  d="M1.5 8.67v8.58a3 3 0 003 3h15a3 3 0 003-3V8.67l-8.928 5.493a3 3 0 01-3.144 0L1.5 8.67z"
                />
                <path
                  d="M22.5 6.908V6.75a3 3 0 00-3-3h-15a3 3 0 00-3 3v.158l9.714 5.978a1.5 1.5 0 001.572 0L22.5 6.908z"
                />
              </svg>
            </span>
            Send Message
          </button>

          <button
            class="tw-inline-flex tw-items-center tw-justify-center tw-text-gray-600 tw-font-semibold tw-text-base tw-bg-white tw-border-2 tw-border-gray-300 tw-rounded-full tw-px-5 tw-py-3 hover:tw-bg-gray-50 tw-transition-colors tw-shadow-sm tw-min-w-[140px]"
            :disabled="isHomeArchived"
            :class="{ 'tw-opacity-50 tw-cursor-not-allowed': isHomeArchived }"
            @click="$router.push(`/bookings/${booking.id}`)"
          >
            <span class="tw-w-5 tw-h-5 tw-mr-2 tw-flex tw-items-center tw-justify-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="currentColor"
                class="tw-w-5 tw-h-5"
              >
                <path d="M11.625 16.5a1.875 1.875 0 100-3.75 1.875 1.875 0 000 3.75z" />
                <path
                  fill-rule="evenodd"
                  d="M5.625 1.5H9a3.75 3.75 0 013.75 3.75v1.875c0 1.036.84 1.875 1.875 1.875H16.5a3.75 3.75 0 013.75 3.75v7.875c0 1.035-.84 1.875-1.875 1.875H5.625a1.875 1.875 0 01-1.875-1.875V3.375c0-1.036.84-1.875 1.875-1.875zm6 16.5c.66 0 1.277-.19 1.797-.518l1.048 1.048a.75.75 0 001.06-1.06l-1.047-1.048A3.375 3.375 0 1011.625 18z"
                  clip-rule="evenodd"
                />
                <path
                  d="M14.25 5.25a5.23 5.23 0 00-1.279-3.434 9.768 9.768 0 016.963 6.963A5.23 5.23 0 0016.5 7.5h-1.875a.375.375 0 01-.375-.375V5.25z"
                />
              </svg>
            </span>
            View Details
          </button>
        </div>

        <!-- Host Action Buttons for Requested Status -->
        <div
          v-if="isHost(booking) && booking.status === 'requested'"
          class="tw-flex tw-flex-wrap tw-gap-3 tw-mt-2 sm:tw-mt-0"
        >
          <button
            class="tw-inline-flex tw-items-center tw-justify-center tw-bg-primary tw-text-white tw-font-semibold tw-text-base tw-rounded-full tw-px-5 tw-py-3 hover:tw-bg-primary/90 tw-transition-colors tw-shadow-sm tw-min-w-[140px]"
            :disabled="isHomeArchived"
            :class="{ 'tw-opacity-50 tw-cursor-not-allowed': isHomeArchived }"
            @click="onApproveClick(booking)"
          >
            <span class="tw-w-5 tw-h-5 tw-mr-2 tw-flex tw-items-center tw-justify-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="currentColor"
                class="tw-w-5 tw-h-5"
              >
                <path
                  fill-rule="evenodd"
                  d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12zm13.36-1.814a.75.75 0 10-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 00-1.06 1.06l2.25 2.25a.75.75 0 001.14-.094l3.75-5.25z"
                  clip-rule="evenodd"
                />
              </svg>
            </span>
            Approve
          </button>

          <button
            class="tw-inline-flex tw-items-center tw-justify-center tw-bg-primary/80 tw-text-white tw-font-semibold tw-text-base tw-rounded-full tw-px-5 tw-py-3 hover:tw-bg-primary/70 tw-transition-colors tw-shadow-sm tw-min-w-[140px]"
            :disabled="isHomeArchived"
            :class="{ 'tw-opacity-50 tw-cursor-not-allowed': isHomeArchived }"
            @click="onApproveIndefinitelyClick(booking)"
          >
            <span class="tw-w-5 tw-h-5 tw-mr-2 tw-flex tw-items-center tw-justify-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="currentColor"
                class="tw-w-5 tw-h-5"
              >
                <path
                  fill-rule="evenodd"
                  d="M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25zM12.75 9a.75.75 0 00-1.5 0v2.25H9a.75.75 0 000 1.5h2.25V15a.75.75 0 001.5 0v-2.25H15a.75.75 0 000-1.5h-2.25V9z"
                  clip-rule="evenodd"
                />
              </svg>
            </span>
            Approve Indefinitely
          </button>

          <button
            class="tw-inline-flex tw-items-center tw-justify-center tw-bg-gray-200 tw-text-gray-700 tw-font-semibold tw-text-base tw-rounded-full tw-px-5 tw-py-3 hover:tw-bg-gray-300 tw-transition-colors tw-shadow-sm tw-min-w-[140px]"
            :disabled="isHomeArchived"
            :class="{ 'tw-opacity-50 tw-cursor-not-allowed': isHomeArchived }"
            @click="onDenyClick(booking)"
          >
            <span class="tw-w-5 tw-h-5 tw-mr-2 tw-flex tw-items-center tw-justify-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="currentColor"
                class="tw-w-5 tw-h-5"
              >
                <path
                  fill-rule="evenodd"
                  d="M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25zm-1.72 6.97a.75.75 0 10-1.06 1.06L10.94 12l-1.72 1.72a.75.75 0 101.06 1.06L12 13.06l1.72 1.72a.75.75 0 101.06-1.06L13.06 12l1.72-1.72a.75.75 0 10-1.06-1.06L12 10.94l-1.72-1.72z"
                  clip-rule="evenodd"
                />
              </svg>
            </span>
            Deny
          </button>
        </div>

        <!-- Additional Action Buttons -->
        <div class="tw-flex tw-flex-wrap tw-gap-3 tw-mt-2 sm:tw-mt-0">
          <button
            v-if="['completed'].includes(booking.status)"
            class="tw-inline-flex tw-items-center tw-justify-center tw-text-gray-700 tw-font-semibold tw-text-base tw-bg-white tw-border-2 tw-border-gray-300 tw-rounded-full tw-px-5 tw-py-3 hover:tw-bg-gray-50 tw-transition-colors tw-shadow-sm tw-min-w-[140px]"
            :disabled="isHomeArchived"
            :class="{ 'tw-opacity-50 tw-cursor-not-allowed': isHomeArchived }"
            @click="onCancelClick(booking)"
          >
            <span class="tw-w-5 tw-h-5 tw-mr-2 tw-flex tw-items-center tw-justify-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="currentColor"
                class="tw-w-5 tw-h-5"
              >
                <path
                  fill-rule="evenodd"
                  d="M5.47 5.47a.75.75 0 011.06 0L12 10.94l5.47-5.47a.75.75 0 111.06 1.06L13.06 12l5.47 5.47a.75.75 0 11-1.06 1.06L12 13.06l-5.47 5.47a.75.75 0 01-1.06-1.06L10.94 12 5.47 6.53a.75.75 0 010-1.06z"
                  clip-rule="evenodd"
                />
              </svg>
            </span>
            Cancel {{ bookingType === 'SWAP' ? 'Home Swap' : 'Booking' }}
          </button>

          <button
            v-if="canLeaveReview"
            class="tw-inline-flex tw-items-center tw-justify-center tw-text-primary tw-font-semibold tw-text-base tw-bg-white tw-border-2 tw-border-primary tw-rounded-full tw-px-5 tw-py-3 hover:tw-bg-primary/5 tw-transition-colors tw-shadow-sm tw-min-w-[140px]"
            :disabled="isHomeArchived"
            :class="{ 'tw-opacity-50 tw-cursor-not-allowed': isHomeArchived }"
            @click="showReviewForm = true"
          >
            <span class="tw-w-5 tw-h-5 tw-mr-2 tw-flex tw-items-center tw-justify-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="currentColor"
                class="tw-w-5 tw-h-5"
              >
                <path
                  fill-rule="evenodd"
                  d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z"
                  clip-rule="evenodd"
                />
              </svg>
            </span>
            Leave Review
          </button>
        </div>
      </div>
    </div>

    <!-- Dialogs -->
    <v-dialog v-model="showReviewForm" max-width="500px">
      <GoodCard2 class="tw-p-0 tw-overflow-hidden tw-rounded-xl tw-shadow-lg">
        <div class="tw-bg-primary tw-text-white tw-py-4 tw-px-6 tw-font-bold tw-text-lg">
          Leave a Review
        </div>
        <div class="tw-p-6">
          <ReviewForm
            :booking-id="booking.id"
            :booking="booking"
            :type="isHost(booking) ? 'host_review' : 'guest_review'"
            @review-submitted="handleReviewSubmitted"
            @review-error="handleReviewError"
          />
        </div>
      </GoodCard2>
    </v-dialog>

    <v-dialog v-model="showReviews" max-width="700px">
      <GoodCard2 class="tw-p-0 tw-overflow-hidden tw-rounded-xl tw-shadow-lg">
        <div class="tw-bg-primary tw-text-white tw-py-4 tw-px-6 tw-font-bold tw-text-lg">
          Home Reviews
        </div>
        <div class="tw-p-6">
          <ReviewList :booking="booking" />
        </div>
      </GoodCard2>
    </v-dialog>
  </GoodCard2>
</template>
