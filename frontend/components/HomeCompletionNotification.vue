<script lang="ts">
// @ts-nocheck
import { defineComponent } from '@nuxtjs/composition-api'

export default defineComponent({
  name: 'HomeCompletionNotification',

  props: {
    isFromAirbnb: {
      type: Boolean,
      default: false
    },
    missingInfo: {
      type: Array,
      default: () => []
    },
    hasMissingInfo: {
      type: Boolean,
      required: true
    },
    // Total number of fields that should be completed
    totalFields: {
      type: Number,
      default: 10
    }
  },

  data() {
    return {
      dismissed: false,
      showAllMissing: false,
      maxVisibleItems: 5
    }
  },

  computed: {
    shouldShow() {
      return (this.hasMissingInfo || this.isFromAirbnb) && !this.dismissed
    },
    notificationTitle() {
      if (this.isFromAirbnb) {
        return 'This home was imported from Airbnb'
      }
      return 'Complete your home listing'
    },
    notificationDescription() {
      if (this.isFromAirbnb) {
        return 'We\'ve imported your listing details from Airbnb. Please review all sections to ensure everything is accurate and complete. Some information might be missing or need adjustments to match Twimo\'s format.'
      }
      return 'Your home listing is missing some important information. Complete these sections to improve your listing and attract more guests.'
    },
    completionPercentage() {
      if (this.totalFields === 0) return 100
      const completedFields = this.totalFields - this.missingInfo.length
      return Math.max(0, Math.min(100, Math.round((completedFields / this.totalFields) * 100)))
    },
    progressColor() {
      if (this.completionPercentage < 50) return 'error'
      if (this.completionPercentage < 80) return 'warning'
      return 'success'
    },
    visibleMissingInfo() {
      if (this.showAllMissing || this.missingInfo.length <= this.maxVisibleItems) {
        return this.missingInfo
      }
      return this.missingInfo.slice(0, this.maxVisibleItems)
    },
    hasMoreItems() {
      return this.missingInfo.length > this.maxVisibleItems
    }
  },

  methods: {
    dismissNotification() {
      this.dismissed = true
    },
    toggleShowAll() {
      this.showAllMissing = !this.showAllMissing
    }
  }
})
</script>

<template>
  <transition name="fade">
    <div v-if="shouldShow" class="tw-mt-4 tw-mb-6">
      <v-card
class="tw-p-5 tw-border tw-border-gray-200 tw-rounded-lg tw-shadow-sm home-notification"
             :class="[isFromAirbnb ? 'tw-bg-purple-50 tw-border-l-purple-400' : 'tw-bg-blue-50 tw-border-l-blue-400']">
      <div class="tw-flex tw-items-start tw-gap-4">
        <div class="tw-flex-shrink-0 tw-mt-1">
          <v-icon :color="isFromAirbnb ? 'purple darken-1' : 'blue darken-1'" size="28">{{ isFromAirbnb ? 'mdi-home-import-outline' : 'mdi-home-edit-outline' }}</v-icon>
        </div>
        <div class="tw-flex-grow">
          <div class="tw-flex tw-items-center tw-gap-2">
            <div class="tw-text-lg tw-font-medium tw-text-primary tw-mb-2">
              {{ notificationTitle }}
            </div>
            <v-chip v-if="missingInfo.length > 0" x-small :color="isFromAirbnb ? 'purple' : 'blue'" text-color="white" class="tw-mb-2">
              {{ missingInfo.length }}
            </v-chip>
          </div>
          <div class="tw-text-sm tw-text-gray-700">
            <p>
              {{ notificationDescription }}
            </p>
            <div class="tw-mt-3 tw-mb-2">
              <div class="tw-flex tw-justify-between tw-items-center tw-mb-1">
                <span class="tw-text-sm tw-font-medium">Listing completion</span>
                <span class="tw-text-sm tw-font-medium">{{ completionPercentage }}%</span>
              </div>
              <v-progress-linear
                :value="completionPercentage"
                :color="progressColor"
                height="8"
                rounded
              ></v-progress-linear>
            </div>
            <template v-if="missingInfo.length > 0">
              <p class="tw-mt-3">
                <strong>{{ isFromAirbnb ? 'Tip:' : 'Missing information:' }}</strong> {{ isFromAirbnb ? 'Pay special attention to these sections:' : 'Complete these sections to improve your listing:' }}
              </p>
              <ul class="tw-mt-1 tw-ml-5 tw-list-disc">
                <li v-for="(item, index) in visibleMissingInfo" :key="index" class="tw-mb-1">{{ item }}</li>
                <li v-if="hasMoreItems && !showAllMissing" class="tw-mb-1 tw-text-primary tw-cursor-pointer tw-font-medium" @click="toggleShowAll">
                  + {{ missingInfo.length - maxVisibleItems }} more items... (click to view all)
                </li>
                <li v-if="showAllMissing" class="tw-mb-1 tw-text-primary tw-cursor-pointer tw-font-medium" @click="toggleShowAll">
                  Show less
                </li>
              </ul>
            </template>
            <div class="tw-mt-3 tw-flex tw-justify-end">
              <v-btn :color="isFromAirbnb ? 'purple darken-1' : 'blue darken-1'" small text @click="dismissNotification">
                <v-icon small class="tw-mr-1">mdi-check</v-icon>
                {{ isFromAirbnb ? 'I understand' : 'Got it' }}
              </v-btn>
            </div>
          </div>
        </div>
      </div>
      </v-card>
    </div>
  </transition>
</template>

<style scoped>
.home-notification {
  transition: all 0.3s ease;
  border-left-width: 4px;
}

.fade-enter-active, .fade-leave-active {
  transition: opacity 0.5s, transform 0.5s;
}

.fade-enter, .fade-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}
</style>
