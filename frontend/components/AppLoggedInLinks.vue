<script lang="ts">
// @ts-nocheck

import { computed, defineComponent, ref, useContext, useRoute } from '@nuxtjs/composition-api'
import { storeToRefs } from 'pinia'

import { cookieOptions } from '~/constants'
import NotificationBell from '~/components/NotificationBell.vue'
import BeastImage from '~/components/BeastImage.vue'
import { useAuthStore } from '~/composables/useAuthStore'
import { useScreenSize } from '~/composables/useScreenSize'
import { useToast } from '~/composables/useCommon'

export default defineComponent({
  name: 'AppLoggedInLinks',

  components: {
    BeastImage,
    NotificationBell,
  },

  setup() {
    const authStore = useAuthStore()

    const { app } = useContext()
    const toast = useToast()
    const route = useRoute();

    const { isHostType, isVendorType, isTravelerType, currentUser } = storeToRefs(authStore)

    const { logout } = authStore

    const { isDesktop } = useScreenSize()

    const showSignUpModal = ref(false)

    const userAvatar = computed(() => {
      if (currentUser.value?.avatar) {
        return currentUser.value.avatar
      }

      return ''
    })

    const upgradeToHostAccount = () => {
      const request = new FormData()
      request.append('role_id', '1')
      request.append('user_type', "host")

      app.$axios
        .post('user/update_account_type', request, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        })
        .then(response => {
          const user = app.$cookies.get('user');
          user.user_type = 'host';
          user.is_host = true;
          app.$cookies.set('user', user, cookieOptions);

          toast.success('Profile successfully updated. Please wait..').goAway(3000)
            setTimeout(() => {
              window.location.reload();
            }, 1000);
        })
        .catch(err => {
          const errorMessage = err.response?.data?.message || 'Error updating profile'
          toast.error(errorMessage).goAway(3000)
        })
    }

    return {
      isHostType,
      isVendorType,
      isTravelerType,
      isDesktop,
      currentUser,
      userAvatar,
      upgradeToHostAccount,
      logout,
      showSignUpModal
    }
  },
})
</script>

<template>

  <v-col cols="6" md="8" class="tw-flex tw-justify-end tw-items-center pr-0 tw-z-20">
    <v-dialog v-model="showSignUpModal" max-width="500px" @click:outside="showSignUpModal = false">
        <v-card class="tw-bg-cover tw-bg-center tw-border-[10px]  tw-py-2 tw-px-2">
          <!--<div class="tw-absolute tw-inset-0 tw-bg-black tw-bg-opacity-60 tw-z-5"></div>-->
          <v-card-title class="headline tw-text-black tw-z-10 tw-relative">Become a Twimo Host</v-card-title>
          <v-card-text class="tw-text-black tw-z-10 tw-relative">
            <p>You want to start hosting on Twimo! As a Twimo Host you can share your vacation home privately with friends &amp; family, rent your home publicly, and/or swap homes with other Twimo hosts globally. </p>

            <p>Once you select upgrade you will be asked to Add Your Home. Payment will be required once you set your home from draft to active. After payment you can share, rent, and/or swap!</p>
          </v-card-text>
          <v-card-actions class="tw-pb-5 tw-block">
            <v-spacer></v-spacer>
            <GoodButton color="primary" class="tw-px-5" @click="upgradeToHostAccount">Upgrade to Host Account</GoodButton>
            <v-btn class="tw-normal-case tw-text-black" text @click="showSignUpModal = false">Cancel</v-btn>
          </v-card-actions>
        </v-card>
    </v-dialog>

    <div v-if="isDesktop && isHostType" class="tw-w-1/2 tw-flex tw-justify-center tw-mr-auto">
      <NuxtLink to="/homes" class="tw-text-zinc-500 tw-px-3 tw-py-2"> Homes </NuxtLink>
      <NuxtLink to="/bookings" class="tw-text-zinc-500 tw-px-3 tw-py-2"> Bookings </NuxtLink>
      <NuxtLink to="/my-calendar" class="tw-text-zinc-500 tw-px-3 tw-py-2"> Calendar </NuxtLink>
      <NuxtLink to="/messages" class="tw-text-zinc-500 tw-px-3 tw-py-2"> Messages </NuxtLink>
      <NuxtLink to="/my-crews" class="tw-text-zinc-500 tw-px-3 tw-py-2"> MyCrew </NuxtLink>
    </div>

    <div v-if="isDesktop && isVendorType" class="tw-w-1/2 tw-flex tw-justify-center tw-mr-auto">
      <NuxtLink to="/vendor/my-crew-jobs" class="tw-text-zinc-500 tw-px-3 tw-py-2">
        MyCrew
      </NuxtLink>
      <NuxtLink to="/my-calendar" class="tw-text-zinc-500 tw-px-3 tw-py-2"> Calendar </NuxtLink>
      <NuxtLink to="/messages" class="tw-text-zinc-500 tw-px-3 tw-py-2"> Messages </NuxtLink>
    </div>

    <div
      v-if="isDesktop && isTravelerType"
      class="tw-flex tw-items-center tw-cursor-pointer tw-font-semibold tw-px-3 tw-py-2 hover:tw-bg-purple-50 hover:tw-rounded-full tw-text-[#000]"
      @click="showSignUpModal = true"
    >
      Become a Host
    </div>

    <div
      v-if="isDesktop && (isHostType || isTravelerType)"
      class="tw-flex tw-items-center tw-cursor-pointer tw-font-semibold tw-px-3 tw-py-2 hover:tw-bg-purple-50 hover:tw-rounded-full tw-text-primary"
      @click="$router.push('/explore')"
    >
      Explore Homes
    </div>

    <NotificationBell class="tw-mx-2" />

    <!-- User Menu -->
    <v-menu bottom right transition="slide-y-transition" offset-y rounded="xl">
      <template #activator="{ on, attrs }">
        <v-btn icon v-bind="attrs" class="user-menu-btn tw-transition-all" v-on="on">
          <v-avatar size="32" class="user-avatar">
            <BeastImage :src="userAvatar" />
          </v-avatar>
        </v-btn>
      </template>

      <v-list class="tw-min-w-64" dense>
        <!-- Mobile Nav Items -->
        <template v-if="!isDesktop">
          <!-- Host Mobile Nav Items -->
          <template v-if="isHostType">
            <v-list-item class="tw-font-medium tw-text-zinc-500" @click="$router.push('/homes')">
              Homes
            </v-list-item>
            <v-list-item class="tw-font-medium tw-text-zinc-500" @click="$router.push('/bookings')">
              Bookings
            </v-list-item>
            <v-list-item
              class="tw-font-medium tw-text-zinc-500"
              @click="$router.push('/my-calendar')"
            >
              Calendar
            </v-list-item>
            <v-list-item class="tw-font-medium tw-text-zinc-500" @click="$router.push('/messages')">
              Messages
            </v-list-item>
            <v-list-item class="tw-font-medium tw-text-zinc-500" @click="$router.push('/my-crews')">
              MyCrew
            </v-list-item>
            <v-divider />
            <v-list-item class="tw-font-medium tw-text-zinc-500" @click="$router.push('/explore')">
              Explore Homes
            </v-list-item>
            <v-divider />
          </template>

          <!-- Vendor Menu Items -->
          <template v-if="isVendorType">
            <v-list-item
              class="tw-font-medium tw-text-zinc-500"
              @click="$router.push('/vendor/my-crew-jobs')"
            >
              MyCrew
            </v-list-item>
            <v-list-item
              class="tw-font-medium tw-text-zinc-500"
              @click="$router.push('/my-calendar')"
            >
              Calendar
            </v-list-item>
            <v-list-item class="tw-font-medium tw-text-zinc-500" @click="$router.push('/messages')">
              Messages
            </v-list-item>
          </template>
        </template>

        <!-- Host Menu Items -->
        <template v-if="isHostType">
          <v-list-item class="tw-font-medium tw-text-zinc-500" @click="$router.push('/home')">
            Dashboard
          </v-list-item>
          <v-list-item class="tw-font-medium tw-text-zinc-500" @click="$router.push('/create')">
            Add Home
          </v-list-item>
          <v-list-item
            class="tw-font-medium tw-text-zinc-500"
            @click="$router.push('/bookings?booking_type=swap')"
          >
            Swaps
          </v-list-item>
          <v-list-item class="tw-font-medium tw-text-zinc-500" @click="$router.push('/guests')">
            Guest List
          </v-list-item>
          <v-list-item
            class="tw-font-medium tw-text-zinc-500"
            @click="$router.push('/saved-homes')"
          >
            Saved Homes
          </v-list-item>
        </template>

        <!-- Guest Menu Items -->
        <template v-if="isTravelerType">
          <v-list-item class="tw-font-medium tw-text-zinc-500" @click="$router.push('/bookings')">
            My Stays
          </v-list-item>
          <v-list-item class="tw-font-medium tw-text-zinc-500" @click="$router.push('/messages')">
            Messages
          </v-list-item>
          <v-list-item
            class="tw-font-medium tw-text-zinc-500"
            @click="$router.push('/saved-homes')"
          >
            Saved Homes
          </v-list-item>
        </template>

        <!-- Vendor Menu Items -->
        <template v-if="isVendorType">
          <v-list-item
            class="tw-font-medium tw-text-zinc-500"
            @click="$router.push('/vendor/my-crew-jobs')"
          >
            MyCrew
          </v-list-item>
          <v-list-item
            class="tw-font-medium tw-text-zinc-500"
            @click="$router.push('/my-calendar')"
          >
            Calendar
          </v-list-item>
          <v-list-item class="tw-font-medium tw-text-zinc-500" @click="$router.push('/messages')">
            Messages
          </v-list-item>
          <v-list-item
            class="tw-font-medium tw-text-zinc-500"
            @click="$router.push('/saved-homes')"
          >
            Saved Homes
          </v-list-item>
        </template>

        <v-divider />

        <!-- Common Menu Items -->
        <v-list-item class="tw-font-medium tw-text-zinc-500" @click="$router.push('/profile')">
          Profile
        </v-list-item>

        <v-divider />

        <v-list-item class="tw-font-medium tw-text-zinc-500">
          <a href="https://twimo.hiverkb.com/" target="_blank" class="tw-text-zinc-500">Help Center</a>
        </v-list-item>
        <v-list-item class="tw-font-medium tw-text-zinc-500" @click="logout"> Log Out </v-list-item>
      </v-list>
    </v-menu>
  </v-col>
</template>

<style lang="scss">
.active-link {
  border-bottom: 1px solid black;
}

.logo {
  display: flex;
  justify-content: center;
  align-items: center;

  img {
    max-height: 40px !important;
  }
}

.user-name {
  font-weight: 600 !important;
  text-align: center;
}

.profile-fill {
  font-size: 12px;
}

.nav-link {
  color: black !important;
  transition: 0.2s all;
}

.nav-link:hover {
  text-decoration: underline;
}

.signup-button {
  background: #df32d7;
  padding: 0.5em 2em;
  border-radius: 999px;
  color: white;
  font-weight: bold;
  transition: 0.2s all;
}

.signup-button:hover {
  background: #f74fee;
}

.backdrop {
  background: rgba(0, 0, 0, 0.8);
  height: calc(100vh - 4rem);
  top: 4rem;
}

.show {
  left: -75vw;
}

.hideBackdrop {
  background: rgba(0, 0, 0, 0);
  pointer-events: none;
}

.v-list-item__title > .v-badge {
  margin-top: 0 !important;
}

.v-list-item {
  min-height: 40px !important;
}

.v-list-item:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.v-divider {
  margin: 8px 0;
}

.user-menu-btn {
  background: transparent !important;
  transition: all 0.2s ease;

  &:hover {
    transform: scale(1.02);
  }

  .user-avatar {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
  }

  &:hover .user-avatar {
    box-shadow: 0 3px 12px rgba(124, 12, 177, 0.2);
  }
}
</style>
