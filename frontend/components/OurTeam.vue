<script>
export default {
  props: {
    src: String,
    alt: String,
    heading: String,
    subHeading: String,
    description: String,
    order: {
      type: Number,
      default: 'ASC',
    },
  },
}
</script>
<template>
  <div
    class="tw-flex tw-gap-5 tw-flex-wrap tw-mt-5 tw-mb-5 sm:tw-flex-nowrap sm:tw-mb-20 sm:tw-gap-16 tw-justify-between"
  >
    <div
      class="tw-w-full sm:tw-w-1/3 tw-order-none"
      :class="order == 'DESC' ? 'sm:tw-order-1' : 'sm:tw-order-none'"
    >
      <img
        :src="src"
        :alt="alt"
        :class="order == 'DESC' ? 'sm:tw-ml-auto sm:tw-block' : 'sm:tw-inline'"
        class="tw-aspect-square tw-w-full tw-block tw-object-contain tw-max-w-[450px] tw-h-auto tw-rounded-theme tw-shadow-theme"
      />
      <!-- <NuxtImg :src="src" :alt="alt" /> -->
    </div>
    <div class="tw-w-full sm:tw-w-2/3">
      <div
        v-if="heading"
        class="tw-text-[#7B7B7B] tw-font-extrabold tw-text-2xl tw-mb-1 tw-leading-5"
      >
        {{ heading }}
      </div>
      <h4 v-if="subHeading" class="tw-text-xl tw-mb-5">
        {{ subHeading }}
      </h4>
      <div v-if="description" class="tw-text-base tw-leading-5">
        {{ description }}
      </div>
    </div>
  </div>
</template>
<style scoped>
img {
  border-radius: 25px;
  box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.25);
}

/* .image__box{
    width: 30%;
    text-align: center;
}
.image__box--image{
    margin-bottom: 25px;
}
.image__box--image img{
    width: 100%;
    height: auto;
    object-fit: cover;
    border-radius: 25px;
    box-shadow: 0 4px 4px 0 rgba(0, 0, 0, .25);
    aspect-ratio: 1/1;
}
.image__box--heading{
    font-size: 25px;
    color: #7B7B7B;
    font-weight: 800;
    margin-bottom: 5px;
}
.image__box--subheading{
    font-size: 20px;
    color: #7B7B7B;
    font-weight: 300;
    margin-bottom: 25px;
}
.image__box--description{
    font-size: 14px;
    line-height: 16px;
    color: #7B7B7B;
}
@media screen and (max-width:1024px) and (min-width:768px) {
    .image__box{
        width: 46%;
    }
}
@media screen and (max-width:767px) {
    .image__box{
        width: 100%;
    }
} */
</style>
