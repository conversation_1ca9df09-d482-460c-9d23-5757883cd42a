<script lang="ts">
// @ts-nocheck
import {
  computed,
  defineComponent,
  onMounted,
  reactive,
  ref,
  useContext,
  useRoute,
  useRouter,
} from '@nuxtjs/composition-api'

import { formatNumberToDisplay, getDateRange } from '~/helpers'
import AppDateRangePicker from '~/components/AppDateRangePicker.vue'
import {
  petFriendlyNumbers,
  petFriendlyOptions,
  petFriendlyTypes,
  SwapPaymentTypeEnum,
  TOAST_DURATION,
} from '~/constants'
import { useBookingCalculator } from '~/composables/useCommon'

export default defineComponent({
  name: 'SwapBox',

  components: {
    AppDateRangePicker,
  },

  props: {
    houseInfo: {
      type: Object,
      required: true,
    },
    forceSwapMode: {
      type: Boolean,
      default: false,
    },
    isUserHost: {
      type: Boolean,
      default: false,
    },
  },

  setup(props) {
    const { $axios, $toast } = useContext()
    const router = useRouter()
    const route = useRoute()

    const pickerRef = ref(null)
    const startDateSelected = ref(null)
    const endDateSelected = ref(null)
    const blockedDates = ref([])
    const datesHashMap = ref({})
    const startDateRed = ref(false)
    const endDateRed = ref(false)
    const showSwapForm = ref(false)
    const localSwapType = ref(null)
    const localHome = ref(null)
    const localComment = ref('')
    const chosenRadioOption = ref(SwapPaymentTypeEnum.MONEY)
    const cardRef = ref(null)
    const isExpanded = ref(false)

    const swapTypeItems = [
      { key: 'Swap at the same time', value: 'sim' },
      { key: 'Swap at different times', value: 'non-sim' },
    ]

    const isPrivateBooking = computed(() => {
      return !!route.value.query.sharable_link
    })

    // Determine if we're in swap mode based on props or URL
    const isSwapMode = computed(() => {
      // First check the forceSwapMode prop
      if (props.forceSwapMode) {
        return true
      }
      // Then check the URL query parameter
      return route.value.query.booking_type === 'swap'
    })

    const homeRef = ref(props.houseInfo)

    const {
      nightly_rate,
      getDiffsInDays,
      getRawTotalMoney,
      getCleaningFee,
      getLiabilityProtection,
      getTaxRate,
      getTax,
      getServiceFee,
      getTotalMoney,
      averageNightlyRateOnTotal,
      customRates,
      isCustomRatesLoaded,
      hasAdvancedPricing,
      baseNightlyRate,
    } = useBookingCalculator(
      homeRef,
      startDateSelected,
      endDateSelected,
      isPrivateBooking,
      isSwapMode
    )

    const canBeFullyPaidOnSite = computed(
      () =>
        startDateSelected.value &&
        endDateSelected.value &&
        SwapPaymentTypeEnum.MONEY === chosenRadioOption.value &&
        nightly_rate.value
    )

    const getRadioOptions = computed(() => {
      const options = [
        { label: 'Rent', value: SwapPaymentTypeEnum.MONEY },
        { label: 'Swap', value: SwapPaymentTypeEnum.FREE },
      ]

      return options.filter(option => {
        // Don't show money option if no nightly rate
        if (!nightly_rate.value && option.value === SwapPaymentTypeEnum.MONEY) {
          return false
        }
        // Don't show swap option if swaps not allowed
        if (!props.houseInfo.allow_swaps && option.value === SwapPaymentTypeEnum.FREE) {
          return false
        }
        // Don't show booking option for non-hosts if home is swap-only
        if (!props.isUserHost && !props.houseInfo.allow_booking && option.value === SwapPaymentTypeEnum.MONEY) {
          return false
        }
        return true
      })
    })

    const handleDateSelection = dates => {
      console.log('dates', dates)
      if (!dates) return

      if (startDateSelected.value === dates.startDate && endDateSelected.value === dates.endDate) {
        return
      }

      startDateSelected.value = dates.startDate
      endDateSelected.value = dates.endDate

      if (dates.startDate) {
        startDateRed.value = false
      }
      if (dates.endDate) {
        endDateRed.value = false
      }

      if (dates.startDate && dates.endDate) {
        testSwapValidation()
      } else {
        swapValidated.value = false
      }
    }

    const handleSelectSwapBox = item => {
      chosenRadioOption.value = item.value
    }

    const createBooking = async () => {

      // Validate guest count
      if (editAmountDialogData.guests < 1) {
        $toast.error('Minimum 1 guest required').goAway(TOAST_DURATION)
        return
      }

      if (editAmountDialogData.guests > props.houseInfo.guests) {
        $toast.error(`Maximum ${props.houseInfo.guests} guests allowed`).goAway(TOAST_DURATION)
        return
      }

      // Validate pets
      if (editAmountDialogData.pets.enabled === 'yes') {
        if (!isPetFriendly.value) {
          $toast.error('This home does not allow pets').goAway(TOAST_DURATION)
          return
        }
      }

      const testResult = await testSwapValidation()

      if (testResult !== true) {
        $toast.error(testResult).goAway(TOAST_DURATION)
        return
      }

      const requestData = {
        start_at: startDateSelected.value,
        end_at: endDateSelected.value,
        user_home: null,
        request_user_home: props.houseInfo.id,
        from_sharable_link: route.value.query.sharable_link || null,
        booking_type: isSwapMode.value ? 'SWAP' : 'RENT',
        extra_info: {
          guests: editAmountDialogData.guests,
          pets: editAmountDialogData.pets,
          priceInfo: {
            // Always include cleaning fee for both swap and regular bookings
            getCleaningFee: getCleaningFee.value,
            ...(isSwapMode.value
              ? {
                  // For swap bookings, total money equals cleaning fee
                  getTotalMoney: getCleaningFee.value,
                }
              : {
                  nightly_rate: nightly_rate.value,
                  getDiffsInDays: getDiffsInDays.value,
                  getRawTotalMoney: getRawTotalMoney.value,
                  getLiabilityProtection: getLiabilityProtection.value,
                  getTaxRate: getTaxRate.value,
                  getTax: getTax.value,
                  getServiceFee: getServiceFee.value,
                  getTotalMoney: getTotalMoney.value,
                  averageNightlyRateOnTotal: averageNightlyRateOnTotal.value,
                }),
          },
        },
      }

      try {
        const { data: booking } = await $axios.post(
          `/bookings?sharable_link=${route.value.query.sharable_link}`,
          requestData
        )

        if (booking) {
          resetForm()
          router.push({ path: `/bookings/${booking.id}` })
        }
      } catch (error) {
        console.error(error)
        $toast.error('Failed to create booking request. Please try again.').goAway(TOAST_DURATION)
      }
    }

    const goBackForMobile = () => {
      resetForm()
      isExpanded.value = false
    }

    const toggleExpand = () => {
      isExpanded.value = !isExpanded.value
    }

    const resetForm = () => {
      startDateSelected.value = null
      endDateSelected.value = null
      showSwapForm.value = false
      localSwapType.value = null
      localHome.value = null
      localComment.value = ''
      if (pickerRef.value) {
        pickerRef.value.clearDates()
      }
    }

    const formatPrice = (price: number) => formatNumberToDisplay(price)

    const swapValidated = ref(false)

    const testSwapValidation = async () => {
      try {
        await $axios.post(`/bookings/validate?sharable_link=${route.value.query.sharable_link}`, {
          start_at: startDateSelected.value,
          end_at: endDateSelected.value,
          isSwap: isSwapMode.value,
          request_home_id: props.houseInfo.id,
        })

        swapValidated.value = true
        return true
      } catch (err) {
        $toast
          .error(
            err.response.data.message || 'Booking is not available. Please check the booking info'
          )
          .goAway(TOAST_DURATION)

        swapValidated.value = false
        return (
          err.response.data.message || 'Booking is not available. Please check the booking info'
        )
      }
    }

    const getBlockedDates = async () => {
      const { data } = await $axios.get(`ical/allDates?home_id=${props.houseInfo.id}`)
      const blocked = []
      const hashMap = {}
      data.forEach(el => {
        if (el.type === 'accepted_swaps') {
          getDateRange(el.start_at.slice(0, 10), el.end_at.slice(0, 10)).forEach(el => {
            blocked.push({ date: el, type: 'swap' })
            hashMap[el] = 'swap'
          })
        } else {
          getDateRange(el.start_at.slice(0, 10), el.end_at.slice(0, 10)).forEach(el => {
            blocked.push({ date: el, type: 'swap' })
            hashMap[el] = 'swap'
          })
        }
      })
      
      blockedDates.value = blocked
      datesHashMap.value = hashMap
    }

    const editAmountDialogData = reactive({
      guests: 1,
      pets: {
        enabled: 'no',
      },
    })

    // Add computed properties for validation
    const maxGuests = computed(() => props.houseInfo.guests)
    const isPetFriendly = computed(() => {
      const petSetting = props.houseInfo.extra_info?.pets?.enabled
      return petSetting === 'yes' || petSetting === 'service animal only'
    })
    const isServiceAnimalOnly = computed(
      () => props.houseInfo.extra_info?.pets?.enabled === 'service animal only'
    )
    const displayPetOptions = computed(() => {
      if (!isPetFriendly.value) {
        return false
      }
      return true
    })

    const simplePetOptions = [
      { text: 'Yes', value: 'yes' },
      { text: 'No', value: 'no' },
    ]

    onMounted(() => {
      getBlockedDates()
      // Initialize with valid values
      editAmountDialogData.guests = 1
      editAmountDialogData.pets.enabled = 'no'
    })

    // Determine if there are any price items to display for swap bookings
    const hasSwapPriceItems = computed(() => {
      if (!isSwapMode.value) return true; // Always show for non-swap bookings

      // For swap bookings, only show if there's a cleaning fee
      return getCleaningFee.value > 0;
    });

    return {
      pickerRef,
      cardRef,
      startDateSelected,
      endDateSelected,
      isExpanded,
      toggleExpand,
      blockedDates,
      datesHashMap,
      startDateRed,
      endDateRed,
      showSwapForm,
      localSwapType,
      localHome,
      localComment,
      chosenRadioOption,
      swapTypeItems,
      nightly_rate,
      getDiffsInDays,
      getRawTotalMoney,
      getCleaningFee,
      getLiabilityProtection,
      getTaxRate,
      getTax,
      getServiceFee,
      getTotalMoney,
      canBeFullyPaidOnSite,
      getRadioOptions,
      customRates,
      isCustomRatesLoaded,
      handleDateSelection,
      handleSelectSwapBox,
      createBooking,
      goBackForMobile,
      formatPrice,
      SwapPaymentTypeEnum,
      petFriendlyOptions,
      petFriendlyTypes,
      petFriendlyNumbers,
      editAmountDialogData,
      testSwapValidation,
      swapValidated,
      averageNightlyRateOnTotal,
      isSwapMode,
      TOAST_DURATION,
      maxGuests,
      isPetFriendly,
      isServiceAnimalOnly,
      displayPetOptions,
      simplePetOptions,
      isPrivateBooking,
      baseNightlyRate,
      hasAdvancedPricing,
      hasSwapPriceItems,
    }
  },
})
</script>

<template>
  <div
    :class="['booking-container', $vuetify.breakpoint.mdAndDown ? 'booking-container-mobile' : '']"
  >
    <v-card
      ref="cardRef"
      class="booking-card"
      :class="[
        $vuetify.breakpoint.mdAndDown ? 'booking-card-mobile' : '',
        $vuetify.breakpoint.mdAndDown && isExpanded ? 'booking-card-expanded' : '',
        $vuetify.breakpoint.mdAndDown && !isExpanded ? 'booking-card-compact' : '',
        'has-chat-bubble',
      ]"
      :elevation="$vuetify.breakpoint.mdAndDown ? 4 : 2"
      :style="{
        position: $vuetify.breakpoint.mdAndUp ? 'sticky' : 'fixed',
        top: $vuetify.breakpoint.mdAndUp ? '20px' : 'auto',
        bottom: $vuetify.breakpoint.mdAndUp ? 'auto' : '0',
      }"
    >
      <!-- Pull indicator for mobile -->
      <div
        v-if="$vuetify.breakpoint.mdAndDown"
        :class="['pull-indicator', !isExpanded ? 'compact-mode' : '']"
        @click="toggleExpand"
      >
        <div class="pull-indicator-bar"></div>
      </div>

      <!-- Mobile back button shown when expanded -->
      <div
        v-if="$vuetify.breakpoint.mdAndDown && isExpanded"
        class="mobile-back-button"
        @click="isExpanded = false"
      >
        <v-icon color="primary">mdi-chevron-left</v-icon>
        <span>Back</span>
      </div>

      <!-- Compact header for mobile when not expanded -->
      <div
        v-if="$vuetify.breakpoint.mdAndDown && !isExpanded"
        class="booking-compact-header"
        @click="toggleExpand"
      >
        <div class="booking-title">
          <template v-if="!isSwapMode">
            <h2>Book this home</h2>
          </template>
          <template v-else>
            <h2>Swap this home</h2>
          </template>
        </div>

        <div class="booking-price">
          <template v-if="!isSwapMode">
            <div class="price-value">${{ averageNightlyRateOnTotal || nightly_rate }}</div>
            <p>per night</p>
            <p v-if="hasAdvancedPricing" class="tw-text-xs tw-text-green-600 tw-font-semibold">
              Advanced pricing applied
            </p>
          </template>
          <template v-else>
            <div class="price-value">${{ formatPrice(getCleaningFee) }}</div>
            <p>cleaning fee</p>
          </template>
        </div>
      </div>

      <!-- Full content (only shown when not in compact mode) -->
      <div v-if="!$vuetify.breakpoint.mdAndDown || isExpanded">
        <!-- Header Section -->
        <div class="booking-header">
          <div class="booking-title">
            <template v-if="!isSwapMode">
              <h2>Book this home</h2>
              <p v-if="houseInfo.minimum_stay_rentals">
                Minimum {{ houseInfo.minimum_stay_rentals }} night stay
              </p>
            </template>
            <template v-else>
              <h2>Swap this home</h2>
            </template>
          </div>

          <div class="booking-price">
            <template v-if="!isSwapMode">
              <div class="price-value">${{ averageNightlyRateOnTotal || nightly_rate }}</div>
              <p>per night</p>
              <p v-if="hasAdvancedPricing" class="tw-text-xs tw-text-green-600 tw-font-semibold">
                Advanced pricing applied
              </p>
            </template>
            <template v-else>
              <div class="price-value">${{ formatPrice(getCleaningFee) }}</div>
              <p>cleaning fee</p>
            </template>
          </div>
        </div>

        <!-- Date Picker Section -->
        <div class="date-picker-section">
          <AppDateRangePicker
            ref="pickerRef"
            width="100%"
            :blocked-dates="blockedDates"
            :dates-hash-map="datesHashMap"
            :start-date-red="startDateRed"
            :end-date-red="endDateRed"
            :prop-dates="{
              startDate: startDateSelected,
              endDate: endDateSelected,
            }"
            :fixed-to-bottom="!$vuetify.breakpoint.mdAndUp"
            @selected-dates="handleDateSelection"
            @show-booked-prompt="
              $toast.error('These dates are not available').goAway(TOAST_DURATION)
            "
          />
        </div>

        <!-- Guest and Pet Selection Section -->
        <div v-if="startDateSelected && endDateSelected" class="booking-details-section">
          <!-- Guests Section -->
          <div class="booking-detail-item">
            <div class="detail-label">
              <span>Guests</span>
              <span class="detail-sublabel">Max: {{ maxGuests }}</span>
            </div>
            <div class="guest-counter">
              <v-btn
                icon
                small
                class="counter-btn"
                :disabled="editAmountDialogData.guests <= 1"
                @click="editAmountDialogData.guests > 1 ? editAmountDialogData.guests-- : 1"
              >
                <v-icon>mdi-minus</v-icon>
              </v-btn>
              <span class="guest-count">{{ editAmountDialogData.guests }}</span>
              <v-btn
                icon
                small
                class="counter-btn"
                :disabled="editAmountDialogData.guests >= maxGuests"
                @click="
                  editAmountDialogData.guests < maxGuests
                    ? editAmountDialogData.guests++
                    : maxGuests
                "
              >
                <v-icon>mdi-plus</v-icon>
              </v-btn>
            </div>
          </div>

          <!-- Pets Section -->
          <div v-if="displayPetOptions" class="booking-detail-item">
            <div class="detail-label">
              <span>Pets</span>
              <span v-if="isServiceAnimalOnly" class="detail-sublabel">Service animals only</span>
            </div>
            <div class="pet-selector">
              <v-btn-toggle
                v-model="editAmountDialogData.pets.enabled"
                mandatory
                class="pet-toggle"
              >
                <v-btn small :value="'no'" class="pet-toggle-btn">No</v-btn>
                <v-btn small :value="'yes'" class="pet-toggle-btn">Yes</v-btn>
              </v-btn-toggle>
            </div>
          </div>
          <div v-else class="booking-detail-item pets-not-allowed">
            <v-icon small color="error" class="mr-2">mdi-close-circle</v-icon>
            <span>This home does not allow pets</span>
          </div>
        </div>

        <!-- Validation Button -->
        <div v-if="startDateSelected && endDateSelected && !swapValidated" class="booking-action">
          <v-btn
            color="primary"
            block
            x-large
            elevation="1"
            class="validation-btn"
            @click="testSwapValidation"
          >
            {{ isSwapMode ? 'Check Swap Availability' : 'Check Availability' }}
          </v-btn>
        </div>

        <!-- Booking Fee Section (shown after validation) -->
        <div v-if="startDateSelected && endDateSelected && swapValidated" class="booking-summary">
          <!-- Only show Price Breakdown section if there are items to display -->
          <template v-if="!isSwapMode || (isSwapMode && hasSwapPriceItems)">
            <h3 class="summary-title">Price Breakdown</h3>

            <div class="price-breakdown">
              <template v-if="!isSwapMode">
                <div class="breakdown-item">
                  <span>
                    ${{ nightly_rate }} × {{ getDiffsInDays }} nights
                    <span v-if="hasAdvancedPricing" class="tw-text-xs tw-text-green-600 tw-font-semibold">
                      (Advanced pricing applied)
                    </span>
                  </span>
                  <span>${{ formatPrice(getRawTotalMoney) }}</span>
                </div>
                <div v-if="isPetFriendly && editAmountDialogData.pets.enabled === 'yes'" class="breakdown-item">
                  <span>Pet fee</span>
                  <span>${{ formatPrice(houseInfo.pet_fee || 0) }}</span>
                </div>
                <div class="breakdown-item">
                  <span>Cleaning fee</span>
                  <span>${{ formatPrice(getCleaningFee) }}</span>
                </div>

                <div class="breakdown-item">
                  <span>Taxes</span>
                  <span>${{ formatPrice(getTax) }}</span>
                </div>
              </template>

              <!-- For swap mode, only show cleaning fee if it exists -->
              <template v-else-if="isSwapMode && getCleaningFee > 0">
                <div class="breakdown-item">
                  <span>Cleaning fee</span>
                  <span>${{ formatPrice(getCleaningFee) }}</span>
                </div>
              </template>

              <div v-if="!isSwapMode && !isPrivateBooking" class="breakdown-item">
                  <span>Liability Protection</span>
                  <span>${{ formatPrice(getLiabilityProtection) }}</span>
              </div>

              <div v-if="!isSwapMode" class="breakdown-total">
                <span>Total <span v-if="!isSwapBooking && !isPrivateBooking" class="tw-font-normal tw-text-sm">(Excluding Liability Protection)</span></span>
                <span>${{ formatPrice(getTotalMoney) }}</span>
              </div>
            </div>
          </template>

          <v-btn
            color="primary"
            block
            x-large
            elevation="2"
            class="booking-btn"
            @click="createBooking"
          >
            {{ isSwapMode ? 'Request to Swap' : 'Reserve Now' }}
          </v-btn>

          <p class="booking-note">
            <v-icon small class="mr-1">mdi-information-outline</v-icon>
            You won't be charged yet
          </p>
        </div>
      </div>
    </v-card>
  </div>
</template>

<style scoped>
.booking-container {
  width: 100%;
  display: flex;
  justify-content: flex-end;
}

.booking-container-mobile {
  position: relative;
  z-index: 990;
}

.booking-card {
  width: 90%;
  background: white;
  border-radius: 16px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
  transition: all 0.3s ease;
}

.booking-card-mobile {
  width: 100%;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  border-radius: 16px 16px 0 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1000;
  box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.1);
  padding-bottom: env(safe-area-inset-bottom, 16px);
}

.booking-card-mobile.has-chat-bubble {
  padding-right: 80px;
}

.booking-card-compact {
  max-height: 82px;
}

.booking-card-expanded {
  max-height: 85vh;
  overflow-y: auto;
  box-shadow: 0 -8px 24px rgba(0, 0, 0, 0.15);
}

.booking-card-expanded.has-chat-bubble {
  padding-right: 0;
}

.booking-card-compact.has-chat-bubble {
  padding-right: 80px;
}

.pull-indicator {
  width: 100%;
  display: flex;
  justify-content: center;
  padding: 8px 0 0;
}

.pull-indicator-bar {
  width: 36px;
  height: 4px;
  background-color: #e0e0e0;
  border-radius: 4px;
}

.pull-indicator.compact-mode {
  padding: 4px 0;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: 5;
}

.mobile-back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  cursor: pointer;
  font-weight: 500;
  color: var(--v-primary-base);
  background-color: rgba(var(--v-primary-base), 0.05);
  position: sticky;
  top: 0;
  z-index: 3;
}

.booking-header {
  padding: 16px 20px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  border-bottom: 1px solid #f3f4f6;
}

.booking-title h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--v-primary-base);
  margin-bottom: 4px;
}

.booking-title p {
  font-size: 0.875rem;
  color: #6b7280;
}

.booking-price {
  text-align: right;
}

.price-value {
  font-size: 1.5rem;
  font-weight: 600;
  color: #111827;
}

.booking-price p {
  font-size: 0.875rem;
  color: #6b7280;
}

.date-picker-section {
  padding: 16px;
  border-bottom: 1px solid #f3f4f6;
  position: relative;
}

.booking-details-section {
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  border-bottom: 1px solid #f3f4f6;
}

.booking-detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detail-label {
  display: flex;
  flex-direction: column;
}

.detail-label span:first-child {
  font-weight: 500;
  font-size: 1rem;
  color: #111827;
}

.detail-sublabel {
  font-size: 0.8rem;
  color: #6b7280;
}

.guest-counter,
.pet-selector {
  display: flex;
  align-items: center;
  gap: 12px;
}

.counter-btn {
  background-color: #f3f4f6 !important;
  box-shadow: none !important;
}

.guest-count {
  font-size: 1rem;
  font-weight: 500;
  min-width: 24px;
  text-align: center;
}

.pet-toggle {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: none !important;
  background-color: #f3f4f6;
}

.pet-toggle-btn {
  min-width: 50px;
  letter-spacing: 0;
}

.pets-not-allowed {
  color: #ef4444;
  font-size: 0.875rem;
}

.booking-action,
.booking-summary {
  padding: 16px;
}

.validation-btn,
.booking-btn {
  text-transform: none;
  font-weight: 600;
  font-size: 1rem;
  height: 48px !important;
  border-radius: 8px;
}

.summary-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 16px;
}

.price-breakdown {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 20px;
}

.breakdown-item {
  display: flex;
  justify-content: space-between;
  font-size: 0.95rem;
  color: #4b5563;
}

.breakdown-total {
  display: flex;
  justify-content: space-between;
  padding-top: 12px;
  margin-top: 8px;
  border-top: 1px solid #f3f4f6;
  font-weight: 600;
  font-size: 1.05rem;
  color: #111827;
}

.booking-note {
  margin-top: 12px;
  font-size: 0.875rem;
  color: #6b7280;
  text-align: center;
}

@media (max-width: 600px) {
  .booking-header {
    padding: 12px 16px;
  }

  .booking-title h2 {
    font-size: 1.25rem;
  }

  .price-value {
    font-size: 1.25rem;
  }

  .date-picker-section,
  .booking-details-section,
  .booking-action,
  .booking-summary {
    padding: 12px;
  }

  .validation-btn,
  .booking-btn {
    height: 44px !important;
  }

  .summary-title {
    font-size: 1.1rem;
  }

  .booking-details-section {
    gap: 12px;
  }

  .breakdown-item {
    font-size: 0.9rem;
  }

  .booking-card-compact.has-chat-bubble {
    padding-right: 70px; /* Slightly less on smaller screens */
  }
}

@media (max-width: 360px) {
  .booking-card-compact.has-chat-bubble {
    padding-right: 60px; /* Even less on very small screens */
  }
}

.booking-container-mobile::before {
  content: '';
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.booking-container-mobile:has(.booking-card-expanded)::before {
  display: block;
  opacity: 1;
}

.booking-compact-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px 16px;
  cursor: pointer;
  position: relative;
}
</style>

<!-- Component-specific global styles -->
<style>
@media (max-width: 960px) {
  .container {
    padding-bottom: 100px !important;
  }

  .leaflet-control-container,
  .leaflet-top,
  .leaflet-bottom,
  .leaflet-control,
  .leaflet-pane,
  .leaflet-control-zoom,
  .leaflet-control-attribution {
    z-index: 800 !important;
  }
}
</style>
