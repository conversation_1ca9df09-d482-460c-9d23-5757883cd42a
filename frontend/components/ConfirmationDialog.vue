<template>
  <v-dialog v-model="dialog" max-width="500" persistent>
    <v-card class="tw-p-8">
      <v-card-title class="tw-text-3xl tw-font-bold tw-text-primary tw-px-0">
        {{ title }}
      </v-card-title>
      <v-card-text class="tw-py-6 tw-px-0">
        <p class="tw-text-zinc-600 tw-text-lg">{{ message }}</p>
      </v-card-text>
      <v-card-actions class="tw-flex tw-justify-end tw-gap-4 tw-px-0">
        <v-btn
          outlined
          class="tw-normal-case tw-rounded-full tw-border-primary tw-text-primary tw-font-medium"
          @click="cancel"
        >
          {{ cancelText }}
        </v-btn>
        <v-btn
          class="tw-normal-case tw-rounded-full tw-font-medium tw-bg-primary"
          dark
          @click="confirm"
        >
          {{ confirmText }}
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script lang="ts">
// @ts-nocheck
import { defineComponent, ref } from '@nuxtjs/composition-api'

import GoodButton from '~/components/GoodButton.vue'
import GoodButtonReverted from '~/components/GoodButtonReverted.vue'

export default defineComponent({
  name: 'ConfirmationDialog',

  components: {
    GoodButton,
    GoodButtonReverted,
  },

  props: {
    value: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: 'Confirm Action',
    },
    message: {
      type: String,
      required: true,
    },
    confirmText: {
      type: String,
      default: 'Confirm',
    },
    cancelText: {
      type: String,
      default: 'Cancel',
    },
  },

  setup(props, { emit }) {
    const dialog = ref(props.value)

    const confirm = () => {
      emit('confirm')
      emit('input', false)
    }

    const cancel = () => {
      emit('cancel')
      emit('input', false)
    }

    return {
      dialog,
      confirm,
      cancel,
    }
  },

  watch: {
    value(newVal) {
      this.dialog = newVal
    },
    dialog(newVal) {
      this.$emit('input', newVal)
    },
  },
})
</script>
