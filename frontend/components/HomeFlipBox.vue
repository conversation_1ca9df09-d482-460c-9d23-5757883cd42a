<script>
export default {
  props: ['default_image', 'title', 'description', 'hover_description', 'animate_delay'],
}
</script>
<template>
  <div class="flip-box animate" :class="`animate-${animate_delay}`">
    <div class="flip-box-inner">
      <div class="flip-box-front">
        <div class="front-inner">
          <h3>{{ title }}</h3>
          <p>{{ description }}</p>
          <div class="image_container">
            <span><img :src="default_image" /></span>
          </div>
        </div>
      </div>
      <div class="flip-box-back">
        <div class="back-inner">
          <div class="back_image_container">
            <img :src="default_image" />
          </div>
          <p>{{ hover_description }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.quick_info > div:first-child {
  box-shadow: 0px 2px 2px #8b8b8b;
}

.flip-box {
  background-color: transparent;
  width: 300px;
  height: 320px;
  perspective: 1000px;
}

.flip-box-inner {
  position: relative;
  width: 100%;
  height: 100%;
  text-align: center;
  transition: transform 0.8s;
  transform-style: preserve-3d;
}

.flip-box:hover .flip-box-inner {
  transform: rotateY(-180deg);
}

.flip-box-front,
.flip-box-back {
  position: absolute;
  width: 100%;
  height: 100%;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
}

.flip-box-front {
  background-color: #bbb;
  color: black;
  box-shadow: 0px 3px 3px #360877;
  border-radius: 15px;
  background: conic-gradient(
    from 180deg at 50% 50%,
    #32076e 0deg,
    #5d0d82 176.4deg,
    #32076e 360deg
  );
  -webkit-transform-style: preserve-3d;
  transform-style: preserve-3d;
}

.flip-box-front h3 {
  color: #fff;
  font-size: 11px;
  padding-top: 30px;
  padding-bottom: 15px;
  font-weight: bold;
}

.flip-box-front p {
  color: #fff;
  font-size: 1.2rem;
  padding-bottom: 5px;
  min-height: 7rem;
  padding-left: 1rem;
  padding-right: 1rem;
  font-weight: 500;
}

.flip-box-front .image_container,
.back_image_container {
  text-align: center;
}

.flip-box-front .image_container img {
  max-width: 90px;
  display: inline;
  mix-blend-mode: luminosity;
}

.flip-box-front .image_container span {
  width: 90px;
  height: 90px;
  overflow: hidden;
  background-color: #380771;
  display: inline-block;
  border-radius: 45px;
}

.flip-box-back .back_image_container img {
  max-width: 70px;
  display: inline;
  margin-top: 1.2rem;
  margin-bottom: 1rem;
}

.flip-box-back {
  background: rgb(242, 242, 242);
  color: white;
  transform: rotateY(180deg) translateZ(10px);
  box-shadow: 0px 3px 3px #360877;
  border-radius: 15px;
  -webkit-transform-style: preserve-3d;
  transform-style: preserve-3d;
}

.flip-box-back p {
  color: #360877;
  padding-left: 1rem;
  padding-right: 1rem;
}

.front-inner,
.back-inner {
  -webkit-perspective: inherit;
  perspective: inherit;
  z-index: 2;
  transform: translateY(0%) translateZ(40px) scale(0.92);
  -webkit-transform: translateY(0%) translateZ(40px) scale(0.92);
}

.fade-default {
  animation: none;
}

.animate-1 {
  transition-delay: 0.1s !important;
}

.animate-2 {
  transition-delay: 0.3s !important;
}

.animate-3 {
  transition-delay: 0.5s !important;
}
</style>
