<template>
  <v-dialog :value="isOpen" max-width="600" persistent @input="$emit('update:is-open', $event)" @click:outside="close">
    <GoodCard>
      <v-card-title class="tw-text-2xl tw-font-bold tw-mb-4">New Message</v-card-title>
      <v-card-text>
        <v-select
          v-if="!isBookingMessage"
          v-model="receiverId"
          :items="messageablePeople"
          item-text="name"
          item-value="id"
          label="To"
          outlined
          dense
          class="tw-mb-4"
          :disabled="!!preSelectedReceiverId"
        ></v-select>
        <v-text-field
          v-if="!isBookingMessage"
          v-model="subject"
          label="Subject"
          outlined
          dense
          class="tw-mb-4"
        ></v-text-field>
        <v-textarea v-model="body" label="Message" outlined dense rows="4"></v-textarea>
      </v-card-text>
      <v-card-actions class="tw-mt-4">
        <v-spacer></v-spacer>
        <GoodButton class="tw-mr-2" @click="close">Cancel</GoodButton>
        <GoodButton color="primary" @click="send">Send</GoodButton>
      </v-card-actions>
    </GoodCard>
  </v-dialog>
</template>

<script lang="ts">
import { defineComponent, ref, watch, computed } from '@nuxtjs/composition-api'

import GoodButton from '~/components/GoodButton.vue'
import GoodCard from '~/components/GoodCard.vue'
import { useApi } from '~/composables/useCommon'
import { Booking } from '~/types'

export default defineComponent({
  name: 'NewMessageModal',
  components: {
    GoodButton,
    GoodCard,
  },
  props: {
    isOpen: {
      type: Boolean,
      required: true,
    },
    messageablePeople: {
      type: Array,
      required: false,
      default: () => [],
    },
    preSelectedReceiverId: {
      type: Number,
      default: null,
    },
    booking: {
      type: Object as () => Booking | null,
      default: null,
    },
  },
  emits: ['update:is-open', 'messageSent'],
  setup(props, { emit }) {
    const api = useApi()
    const receiverId = ref<number | null>(null)
    const body = ref('')
    const subject = ref('') // New ref for subject

    const isBookingMessage = computed(() => !!props.booking)

    watch(
      () => props.preSelectedReceiverId,
      newValue => {
        if (newValue) {
          receiverId.value = newValue
          body.value = ''
        }
      },
      { immediate: true }
    )

    const close = () => {
      emit('update:is-open', false)
      resetForm()
    }

    const resetForm = () => {
      receiverId.value = props.preSelectedReceiverId || null
      body.value = ''
      subject.value = '' // Reset subject
    }

    const send = async () => {
      if (!receiverId.value || !body.value.trim()) return

      try {
        const messageData: any = {
          receiver_id: receiverId.value,
          body: body.value,
        }

        if (isBookingMessage.value && props.booking) {
          messageData.subject = null
          messageData.messageable_id = props.booking.id
          messageData.messageable_type = 'App\\Models\\Booking'
        } else {
          messageData.subject = subject.value || 'New Message' // Use subject input or default
        }

        await api.post('/messages-v2', messageData)

        emit('messageSent')
        close()
      } catch (error) {
        console.error('Error sending new message:', error)
      }
    }

    return {
      receiverId,
      body,
      subject, // Add subject to returned object
      close,
      send,
      isBookingMessage,
    }
  },
})
</script>
