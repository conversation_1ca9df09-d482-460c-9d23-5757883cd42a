<script lang="ts">
// @ts-nocheck
import { defineComponent, ref, watch } from '@nuxtjs/composition-api'
import { storeToRefs } from 'pinia'

import { useCreateHomeProgressStore } from '~/composables/useCreateHomeStore'
import { normalizeAirbnbUrl, isValidAirbnbUrl } from '~/helpers'

export default defineComponent({
  name: 'CreateHomeAirBnbUrl',

  setup() {
    const store = useCreateHomeProgressStore()

    const { airbnbUrl } = storeToRefs(store)

    const validationError = ref('')

    const validateUrl = (value: string) => {
      if (!value) return true
      if (!isValidAirbnbUrl(value)) {
        validationError.value = 'Please enter a valid Airbnb listing URL'
        return false
      }
      validationError.value = ''
      return true
    }

    watch(airbnbUrl, newVal => {
      if (newVal) {
        airbnbUrl.value = normalizeAirbnbUrl(newVal)
      }
    })

    return {
      airbnbUrl,
      validationError,
      validateUrl,
    }
  },
})
</script>

<template>
  <v-row>
    <v-col cols="12" md="8" class="tw-mt-8 tw-flex tw-flex-col tw-gap-4">
      <div class="tw-text-2xl tw-font-semibold tw-text-zinc-600">Do You Have Airbnb Listing?</div>

      <div class="tw-text-lg tw-font-medium tw-text-zinc-500">
        Skip this step if not applicable by selecting next
      </div>

      <div class="tw-text-zinc-500">Paste your Airbnb URL below:</div>

      <v-text-field
        v-model="airbnbUrl"
        :error-messages="validationError"
        :rules="[validateUrl]"
        outlined
        hide-details
        dense
        required
        placeholder="https://www.airbnb.com/rooms/..."
      />
    </v-col>
  </v-row>
</template>

<style scoped></style>
