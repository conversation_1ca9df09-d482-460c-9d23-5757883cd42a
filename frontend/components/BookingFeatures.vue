<script>
export default {
  props: {
    features: {
      type: Array,
      required: true,
    },
  },
}
</script>
<template>
  <div>
    <div
      v-for="(item, i) in features"
      :key="i"
      class="tw-flex tw-items-center tw-justify-center tw-p-4 tw-mb-[6px] tw-h-[60px] tw-max-h-[60px]"
    >
      <h3
        class="tw-text-[#FFF] tw-text-xl tw-mb-0 tw-drop-shadow tw-font-regular"
        v-html="item"
      ></h3>
    </div>
  </div>
</template>
