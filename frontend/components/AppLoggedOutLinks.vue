<script lang="ts">
// @ts-nocheck
import { defineComponent } from '@nuxtjs/composition-api'

export default defineComponent({
  methods: {
    openExternalLink(url: string) {
      window.open(url, '_blank')
    },
  },
})
</script>

<template>
  <v-col md="8" sm="6" class="d-flex align-center justify-end pr-0 tw-z-20">
    <v-menu bottom right transition="slide-y-transition" offset-y rounded="xl">
      <template #activator="{ on, attrs }">
        <v-btn
          icon
          v-bind="attrs"
          class="menu-btn sm:tw-border sm:tw-border-solid sm:tw-border-gray-200 sm:tw-p-6 tw-transition-all hover:tw-border-primary hover:tw-shadow-md"
          v-on="on"
        >
          <v-icon large class="menu-icon">mdi-menu</v-icon>
        </v-btn>
      </template>
      <v-list class="tw-min-w-64" dense>
        <v-list-item
          class="tw-font-medium tw-text-zinc-500"
          @click="$router.push('/signup')"
        >
          Sign Up
        </v-list-item>
        <v-list-item class="tw-font-medium tw-text-zinc-500" @click="$router.push('/login')">
          Log In
        </v-list-item>
        <v-divider />
        <v-list-item class="tw-font-medium tw-text-zinc-500" @click="$router.push('/explore')">
          Explore
        </v-list-item>
        <v-divider />
        <v-list-item
          class="tw-font-medium tw-text-zinc-500"
          @click="openExternalLink('https://twimo.com')"
        >
          Learn More
        </v-list-item>
        <v-list-item class="tw-font-medium tw-text-zinc-500" @click="openExternalLink('/faqs')">
          FAQs
        </v-list-item>
        <v-list-item class="tw-font-medium tw-text-zinc-500" @click="openExternalLink('/about-us')">
          About Us
        </v-list-item>
      </v-list>
    </v-menu>
  </v-col>
</template>

<style scoped lang="scss">
.header-link {
  text-decoration: none;
  color: black;
}

.active-link {
  border-bottom: 1px solid black;
}

.menu-btn {
  background: white !important;
  border-radius: 50px !important;
  transition: all 0.2s ease;

  &:hover {
    transform: scale(1.02);

    .menu-icon {
      color: #7c0cb1 !important;
    }
  }
}
</style>
