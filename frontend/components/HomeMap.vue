<script lang="ts">
// @ts-nocheck
import { defineComponent, onMounted, ref, computed, watch } from '@nuxtjs/composition-api'
import { PropType } from 'vue'

import { HomeTo } from '~/types'
import useAddressFormatter from '~/composables/useAddressFormatter'

export default defineComponent({
  name: 'HomeMap',

  props: {
    houseInfo: {
      type: Object as PropType<HomeTo>,
      required: true,
    },
  },

  setup(props) {
    const mapLoaded = ref(false)
    const mapError = ref(false)
    const showDirectionsPanel = ref(false)
    const showNearbyPanel = ref(false)
    const userLocation = ref('')
    const coordinates = ref(null)
    const isGeocoding = ref(false)
    const loadingNearby = ref(false)
    const nearbyPlaces = ref([])
    const map = ref(null)
    const markerRef = ref(null)
    const homeCircle = ref(null)
    let nearbyMarkers = []

    const poiCategories = ref([
      {
        id: 'restaurant',
        label: 'Restaurants',
        icon: 'mdi-silverware-fork-knife',
        color: '#FF5722',
        selected: false,
      },
      {
        id: 'cafe',
        label: 'Cafes',
        icon: 'mdi-coffee',
        color: '#795548',
        selected: false,
      },
      {
        id: 'bar',
        label: 'Bars',
        icon: 'mdi-glass-cocktail',
        color: '#9C27B0',
        selected: false,
      },
      {
        id: 'supermarket',
        label: 'Supermarkets',
        icon: 'mdi-cart',
        color: '#4CAF50',
        selected: false,
      },
      {
        id: 'attraction',
        label: 'Attractions',
        icon: 'mdi-star',
        color: '#FFC107',
        selected: false,
      },
      {
        id: 'park',
        label: 'Parks',
        icon: 'mdi-tree',
        color: '#8BC34A',
        selected: false,
      },
      {
        id: 'pharmacy',
        label: 'Pharmacies',
        icon: 'mdi-medical-bag',
        color: '#F44336',
        selected: false,
      },
      {
        id: 'bus_station',
        label: 'Transit',
        icon: 'mdi-bus',
        color: '#2196F3',
        selected: false,
      },
    ])

    // Use address formatter
    const { formatAddress } = useAddressFormatter()

    // Format address based on booking status and payment status
    // For map display, we always show the partial address
    const formattedAddress = computed(() => {
      return formatAddress(
        props.houseInfo.address,
        props.houseInfo.city_long,
        props.houseInfo.state_long,
        props.houseInfo.country_long,
        null, // No booking status - always show partial
        null  // No payment status - always show partial
      )
    })

    const getCoordinates = async () => {
      try {
        if (!props.houseInfo.address) return null

        isGeocoding.value = true

        // Construct a readable address for geocoding
        const addressComponents = [
          props.houseInfo.street,
          props.houseInfo.city_long,
          props.houseInfo.state_long,
          props.houseInfo.country_long,
        ].filter(Boolean)

        const address = encodeURIComponent(addressComponents.join(', '))

        // Using Nominatim geocoding service (OpenStreetMap)
        const response = await fetch(
          `https://nominatim.openstreetmap.org/search?format=json&q=${address}&limit=1`
        )
        const data = await response.json()

        if (data && data.length > 0) {
          coordinates.value = {
            lat: parseFloat(data[0].lat),
            lng: parseFloat(data[0].lon),
          }
          return coordinates.value
        }

        // Fallback to using just city, state, country if exact address doesn't give results
        if (addressComponents.length > 2) {
          const cityStateCountry = [
            props.houseInfo.city_long,
            props.houseInfo.state_long,
            props.houseInfo.country_long,
          ].filter(Boolean)

          const fallbackAddress = encodeURIComponent(cityStateCountry.join(', '))
          const fallbackResponse = await fetch(
            `https://nominatim.openstreetmap.org/search?format=json&q=${fallbackAddress}&limit=1`
          )
          const fallbackData = await fallbackResponse.json()

          if (fallbackData && fallbackData.length > 0) {
            coordinates.value = {
              lat: parseFloat(fallbackData[0].lat),
              lng: parseFloat(fallbackData[0].lon),
            }
            return coordinates.value
          }
        }

        throw new Error('Could not geocode address')
      } catch (error) {
        console.error('Error fetching coordinates:', error)
        mapError.value = true
        return null
      } finally {
        isGeocoding.value = false
      }
    }

    const getDirectionsUrl = () => {
      const destination = encodeURIComponent(formattedAddress.value)
      let origin = encodeURIComponent(userLocation.value || '')

      // If we have coordinates instead of a user-entered location
      if (userLocation.value && userLocation.value.includes(',')) {
        origin = userLocation.value
      }

      return `https://www.google.com/maps/dir/?api=1&origin=${origin}&destination=${destination}`
    }

    const retryLoadingMap = async () => {
      mapError.value = false
      mapLoaded.value = false

      // Small delay before retrying to avoid too quick consecutive retries
      await getCoordinates()
      mapLoaded.value = true
    }

    const toggleNearbyPanel = () => {
      if (showNearbyPanel.value) {
        showNearbyPanel.value = false
        return
      }

      showDirectionsPanel.value = false
      showNearbyPanel.value = true
    }

    const clearNearbyMarkers = () => {
      // Since we're using nuxt-leaflet components, we'll handle this differently
      nearbyMarkers = []
    }

    const togglePoiCategory = async category => {
      category.selected = !category.selected

      clearNearbyMarkers()

      const selectedCategories = poiCategories.value.filter(cat => cat.selected)

      if (selectedCategories.length === 0 || !coordinates.value) {
        return
      }

      await showNearbyPlaces(selectedCategories.map(cat => cat.id))
    }

    const showNearbyPlaces = async categories => {
      if (!coordinates.value || categories.length === 0) return

      try {
        loadingNearby.value = true

        // Use OpenStreetMap Overpass API to find nearby POIs
        const radius = 2000 // 2km radius
        const lat = coordinates.value.lat
        const lng = coordinates.value.lng

        // Build Overpass query for selected categories
        const categoryQueries = categories
          .map(category => {
            return `node["amenity"="${category}"](around:${radius},${lat},${lng});
						way["amenity"="${category}"](around:${radius},${lat},${lng});
						relation["amenity"="${category}"](around:${radius},${lat},${lng});`
          })
          .join('')

        const overpassQuery = `
						[out:json];
						(
							${categoryQueries}
						);
						out center 50;
					`

        const response = await fetch('https://overpass-api.de/api/interpreter', {
          method: 'POST',
          body: overpassQuery,
        })

        const data = await response.json()
        nearbyPlaces.value = data.elements

        // Store marker details for rendering with l-marker components
        if (data.elements.length) {
          data.elements.forEach(element => {
            let placePosition

            // Get position based on element type
            if (element.type === 'node') {
              placePosition = [element.lat, element.lon]
            } else {
              placePosition = [element.center.lat, element.center.lon]
            }

            // Find the category for this place
            const placeCat = poiCategories.value.find(cat => cat.id === element.tags?.amenity)

            if (!placeCat) return

            // Add to nearby markers array
            nearbyMarkers.push({
              position: placePosition,
              color: placeCat.color,
              icon: placeCat.icon,
              title: element.tags?.name || placeCat.label,
              category: placeCat.label,
            })
          })
        }
      } catch (error) {
        console.error('Error fetching nearby places:', error)
      } finally {
        loadingNearby.value = false
      }
    }

    const toggleDirectionsPanel = () => {
      if (showDirectionsPanel.value) {
        showDirectionsPanel.value = false
        return
      }

      showNearbyPanel.value = false
      showDirectionsPanel.value = true

      if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
          position => {
            userLocation.value = `${position.coords.latitude},${position.coords.longitude}`
          },
          error => {
            console.error('Geolocation error:', error)
            // User denied geolocation or it's not available
            userLocation.value = ''
          }
        )
      }
    }

    onMounted(async () => {
      await getCoordinates()
      mapLoaded.value = true
    })

    watch(
      () => props.houseInfo,
      async () => {
        await getCoordinates()
      },
      { deep: true }
    )

    // Create custom divIcon styles
    const homeIconHtml =
      '<div class="home-marker-icon"><i class="v-icon mdi mdi-home-variant" style="color: #3F51B5;"></i></div>'
    const createPoiIconHtml = (color, icon) =>
      `<div class="poi-marker-icon" style="background-color: ${color};"><i class="v-icon mdi ${icon}" style="color: white;"></i></div>`

    return {
      map,
      markerRef,
      homeCircle,
      mapLoaded,
      mapError,
      isGeocoding,
      formattedAddress,
      showDirectionsPanel,
      showNearbyPanel,
      userLocation,
      toggleDirectionsPanel,
      toggleNearbyPanel,
      getDirectionsUrl,
      retryLoadingMap,
      poiCategories,
      togglePoiCategory,
      loadingNearby,
      nearbyPlaces,
      coordinates,
      nearbyMarkers,
      homeIconHtml,
      createPoiIconHtml,
    }
  },
})
</script>

<template>
  <div class="tw-flex tw-flex-col tw-gap-5">
    <div class="tw-flex tw-justify-between tw-items-center">
      <h2 class="tw-text-zinc-700 tw-font-bold tw-text-2xl tw-mb-0">Location</h2>
      <div class="tw-flex tw-items-center tw-gap-2">
        <v-btn
          small
          color="primary"
          outlined
          class="tw-h-9 tw-font-medium tw-shadow-sm hover:tw-shadow"
          :class="{ 'tw-bg-primary tw-text-white': showNearbyPanel }"
          :disabled="mapError"
          @click="toggleNearbyPanel"
        >
          <v-icon small class="mr-1">mdi-map-search</v-icon>
          <span class="tw-hidden sm:tw-inline">Nearby</span>
        </v-btn>
        <v-btn
          small
          color="primary"
          outlined
          class="tw-h-9 tw-font-medium tw-shadow-sm hover:tw-shadow"
          :class="{ 'tw-bg-primary tw-text-white': showDirectionsPanel }"
          :disabled="mapError"
          @click="toggleDirectionsPanel"
        >
          <v-icon small class="mr-1">mdi-directions</v-icon>
          <span class="tw-hidden sm:tw-inline">Directions</span>
        </v-btn>
      </div>
    </div>

    <!-- Directions panel -->
    <transition name="panel-fade">
      <div
        v-if="showDirectionsPanel"
        class="tw-bg-gray-50 tw-p-4 md:tw-p-5 tw-rounded-lg tw-border tw-border-gray-200 tw-mb-2 tw-shadow-sm"
      >
        <div class="tw-flex tw-flex-col tw-gap-3">
          <div class="tw-text-zinc-700 tw-font-medium tw-flex tw-items-center tw-gap-2">
            <v-icon small color="primary">mdi-map-marker-radius</v-icon>
            Get directions to {{ houseInfo.title }}
          </div>

          <div class="tw-flex tw-flex-col sm:tw-flex-row tw-gap-3">
            <v-text-field
              v-model="userLocation"
              label="Starting point"
              placeholder="Enter your location"
              dense
              hide-details
              outlined
              class="tw-flex-grow"
              prepend-inner-icon="mdi-map-marker"
            ></v-text-field>

            <a
              :href="getDirectionsUrl()"
              target="_blank"
              rel="noopener noreferrer"
              class="tw-no-underline"
            >
              <v-btn
                color="primary"
                class="tw-h-10 sm:tw-h-full tw-font-medium"
                :disabled="!userLocation"
              >
                <v-icon small class="mr-1">mdi-map-marker-path</v-icon>
                Get Directions
              </v-btn>
            </a>
          </div>

          <div
            class="tw-flex tw-items-center tw-text-sm tw-text-gray-500 tw-bg-gray-100 tw-p-2 tw-rounded"
          >
            <v-icon x-small class="mr-1">mdi-information-outline</v-icon>
            <span>Directions are approximate. Exact location provided after booking.</span>
          </div>
        </div>
      </div>
    </transition>

    <!-- Nearby places panel -->
    <transition name="panel-fade">
      <div
        v-if="showNearbyPanel"
        class="tw-bg-gray-50 tw-p-4 md:tw-p-5 tw-rounded-lg tw-border tw-border-gray-200 tw-mb-2 tw-shadow-sm"
      >
        <div class="tw-flex tw-flex-col tw-gap-3">
          <div class="tw-text-zinc-700 tw-font-medium tw-flex tw-items-center tw-gap-2">
            <v-icon small color="primary">mdi-map-search</v-icon>
            Explore what's nearby
          </div>

          <div class="tw-grid tw-grid-cols-2 sm:tw-grid-cols-4 tw-gap-2">
            <v-btn
              v-for="category in poiCategories.value"
              :key="category.id"
              small
              outlined
              :color="category.selected ? 'white' : category.color"
              :style="{
                backgroundColor: category.selected ? category.color : 'transparent',
              }"
              class="tw-text-left tw-justify-start tw-h-10 tw-text-sm tw-shadow-sm hover:tw-shadow"
              @click="togglePoiCategory(category)"
            >
              <v-icon
                small
                class="mr-1"
                :style="{ color: category.selected ? 'white' : category.color }"
              >
                {{ category.icon }}
              </v-icon>
              <span :style="{ color: category.selected ? 'white' : 'inherit' }">{{
                category.label
              }}</span>
            </v-btn>
          </div>

          <div v-if="loadingNearby" class="tw-flex tw-justify-center tw-py-2">
            <v-progress-circular indeterminate size="24" color="primary"></v-progress-circular>
          </div>

          <div
            class="tw-flex tw-items-center tw-text-sm tw-text-gray-500 tw-bg-gray-100 tw-p-2 tw-rounded"
          >
            <v-icon x-small class="mr-1">mdi-information-outline</v-icon>
            <span>Select categories to see points of interest near this home.</span>
          </div>
        </div>
      </div>
    </transition>

    <div class="tw-rounded-lg tw-overflow-hidden tw-shadow-md tw-border tw-border-gray-200">
      <div class="tw-w-full tw-h-80 tw-overflow-hidden tw-relative">
        <!-- Loading state -->
        <div
          v-if="!mapLoaded && !mapError"
          class="tw-w-full tw-h-full tw-flex tw-items-center tw-justify-center tw-bg-gray-100"
        >
          <div class="tw-flex tw-flex-col tw-items-center tw-gap-2">
            <v-progress-circular indeterminate color="primary" size="36"></v-progress-circular>
            <span class="tw-text-zinc-500">
              {{ isGeocoding ? 'Finding location...' : 'Loading map...' }}
            </span>
          </div>
        </div>

        <!-- Error state -->
        <div
          v-if="mapError"
          class="tw-w-full tw-h-full tw-flex tw-items-center tw-justify-center tw-bg-gray-100"
        >
          <div
            class="tw-flex tw-flex-col tw-items-center tw-gap-3 tw-p-6 tw-text-center tw-max-w-sm"
          >
            <v-icon color="error" size="48">mdi-map-marker-off</v-icon>
            <h3 class="tw-font-medium tw-text-zinc-700">Unable to load map</h3>
            <p class="tw-text-zinc-500 tw-mb-3">We couldn't display the map for this location.</p>
            <v-btn small color="primary" class="tw-font-medium" @click="retryLoadingMap">
              <v-icon small class="mr-1">mdi-refresh</v-icon>
              Retry
            </v-btn>
          </div>
        </div>

        <!-- Map -->
        <client-only>
          <l-map
            v-if="coordinates && mapLoaded && !mapError"
            ref="map"
            :zoom="14"
            :center="[coordinates.lat, coordinates.lng]"
          >
            <l-tile-layer
              url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
              :attribution="'&copy; <a href=\'https://www.openstreetmap.org/copyright\'>OpenStreetMap</a> contributors'"
            ></l-tile-layer>

            <!-- Home marker -->
            <l-marker
              v-if="coordinates"
              ref="markerRef"
              :lat-lng="[coordinates.lat, coordinates.lng]"
            >
              <l-popup>
                <b>{{ houseInfo.title }}</b
                ><br />{{ formattedAddress }}
              </l-popup>
            </l-marker>

            <!-- Privacy circle -->
            <l-circle
              v-if="coordinates"
              ref="homeCircle"
              :lat-lng="[coordinates.lat, coordinates.lng]"
              :radius="500"
              :color="'rgba(63, 81, 181, 0.4)'"
              :fill-color="'rgba(63, 81, 181, 0.1)'"
              :fill-opacity="0.5"
            />

            <!-- Nearby place markers -->
            <l-marker
              v-for="(marker, index) in nearbyMarkers"
              :key="'marker-' + index"
              :lat-lng="marker.position"
            >
              <l-popup>
                <b>{{ marker.title }}</b
                ><br />{{ marker.category }}
              </l-popup>
            </l-marker>
          </l-map>
        </client-only>
      </div>
    </div>

    <div
      class="tw-flex tw-items-start tw-gap-2 tw-bg-primary tw-bg-opacity-5 tw-p-3 tw-rounded-lg tw-text-zinc-600 tw-text-sm"
    >
      <v-icon small color="primary" class="tw-mt-0.5">mdi-map-marker-alert-outline</v-icon>
      <div>
        <p class="tw-m-0">
          The location shown is approximate to protect the home's exact location.
        </p>
        <p class="tw-m-0">
          Full address and check-in details will be provided after booking confirmation.
        </p>
      </div>
    </div>

    <div class="tw-flex tw-items-center tw-gap-2 tw-mb-2">
      <div class="tw-bg-primary tw-bg-opacity-10 tw-p-2 tw-rounded-full">
        <v-icon color="primary">mdi-map-marker</v-icon>
      </div>
      <span class="tw-font-medium tw-text-zinc-700">{{ formattedAddress }}</span>
    </div>
  </div>
</template>

<style scoped>
.home-marker-icon {
  background-color: white;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.poi-marker-icon {
  border-radius: 50%;
  width: 25px;
  height: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.v-btn--outlined {
  border: 1px solid currentColor !important;
}

.panel-fade-enter-active,
.panel-fade-leave-active {
  transition: all 0.3s ease;
}

.panel-fade-enter,
.panel-fade-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}
</style>
