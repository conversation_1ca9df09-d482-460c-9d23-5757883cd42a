<script lang="ts">
// @ts-nocheck
import { defineComponent, toRefs, onMounted, ref } from '@nuxtjs/composition-api'

import AppLoggedInLinks from '~/components/AppLoggedInLinks.vue'
import AppLoggedOutLinks from '~/components/AppLoggedOutLinks.vue'
import { useAuthStore } from '~/composables/useAuthStore'

export default defineComponent({
  components: {
    AppLoggedInLinks,
    AppLoggedOutLinks,
  },

  setup() {
    const { isLoggedIn } = toRefs(useAuthStore())
    const isMounted = ref(false)

    onMounted(() => {
      isMounted.value = true
    })

    return {
      isLoggedIn,
      isMounted
    }
  },
})
</script>

<template>
  <v-row no-gutters>
    <v-app-bar height="70" app elevate-on-scroll color="white" class="header-app">
      <v-toolbar-title class="pa-0 d-flex align-center">
        <nuxt-link :to="!isLoggedIn ? '/' : (isVendorType ? '/vendor' : (isTravelerType ? '/explore' : '/home'))" class="d-flex align-center mr-2">
          <v-img
            :src="require('~/assets/newhome/twimo-header-logo.png')"
            alt="Twimo Logo"
            :height="35"
            :width="150"
            class="rounded-lg tw-z-[100]"
            loading="lazy"
          />
        </nuxt-link>
      </v-toolbar-title>

      <v-spacer />

      <app-logged-in-links v-if="isLoggedIn" />

      <app-logged-out-links v-else />
    </v-app-bar>
  </v-row>
</template>
