<template>
  <div>
    <!-- Host Activation Tracker - Only shown when not fully activated -->
    <div v-if="!isFullyActivated" class="tw-mb-6 tw-px-0">
      <div
        :class="{
          'tw-bg-white tw-rounded-xl tw-shadow-sm tw-p-4 sm:tw-p-6 tw-border tw-border-gray-200 activation-wrapper': !isFullyActivated,
          'tw-bg-green-50 tw-rounded-xl tw-shadow-sm tw-p-4 sm:tw-p-6 tw-border tw-border-green-200 activation-wrapper': isFullyActivated
        }"
      >
        <!-- Header with icon - always visible -->
        <div class="tw-flex tw-flex-col sm:tw-flex-row sm:tw-justify-between sm:tw-items-center tw-mb-4 tw-gap-3">
          <div class="tw-flex tw-items-center tw-gap-3 tw-flex-wrap">
            <div
              :class="{
                'tw-bg-primary tw-bg-opacity-10 tw-p-2 tw-rounded-full tw-flex-shrink-0': !isFullyActivated,
                'tw-bg-green-100 tw-p-2 tw-rounded-full tw-flex-shrink-0': isFullyActivated
              }"
            >
              <v-icon v-if="!isFullyActivated" color="primary">mdi-home-account</v-icon>
              <v-icon v-else color="success">mdi-check-circle</v-icon>
            </div>
            <h2
              :class="{
                'tw-text-lg sm:tw-text-xl tw-font-semibold tw-text-zinc-800 activation-title': !isFullyActivated,
                'tw-text-lg sm:tw-text-xl tw-font-semibold tw-text-green-800 activation-title': isFullyActivated
              }"
            >
              {{ isFullyActivated ? 'Host Activation Complete' : 'Host Activation Required' }}
            </h2>
          </div>
          <GoodButton
            v-if="!isFullyActivated"
            class="tw-w-full sm:tw-w-auto activation-button"
            @click="openActivationModal"
          >
            Complete Setup
          </GoodButton>
          <GoodButton
            v-else
            color="success"
            class="tw-w-full sm:tw-w-auto activation-button"
            @click="openActivationModal"
          >
            View Details
          </GoodButton>
        </div>

        <!-- Message -->
        <p
          :class="{
            'tw-text-zinc-600 tw-mb-4 tw-text-sm sm:tw-text-base activation-message': !isFullyActivated,
            'tw-text-green-700 tw-mb-4 tw-text-sm sm:tw-text-base activation-message': isFullyActivated
          }"
        >
          {{ isFullyActivated
            ? 'Your host account is fully activated. You can now manage your home and receive bookings.'
            : 'To fully activate your home and make it available for bookings, please complete the following steps:'
          }}
        </p>

        <!-- Activation tracker -->
        <HostActivationTracker compact @navigate="$router.push($event)" />
      </div>
    </div>

    <!-- Public Rental Images Warning -->
    <div v-if="showPublicRentalImagesWarning" class="tw-mb-6 tw-px-0">
      <div class="tw-bg-amber-50 tw-rounded-xl tw-shadow-sm tw-p-4 sm:tw-p-6 tw-border tw-border-amber-200 warning-wrapper">
        <!-- Header with icon - always visible -->
        <div class="tw-flex tw-items-center tw-gap-3 tw-mb-3 tw-flex-wrap">
          <div class="tw-bg-amber-100 tw-p-2 tw-rounded-full tw-flex-shrink-0">
            <v-icon color="amber darken-2">mdi-image-multiple-outline</v-icon>
          </div>
          <h2 class="tw-text-lg sm:tw-text-xl tw-font-semibold tw-text-amber-800 warning-title">Images Required for Public Rental</h2>
        </div>

        <!-- Message -->
        <p class="tw-text-amber-700 tw-mb-4 tw-text-sm sm:tw-text-base warning-message">
          Your home has public rental enabled but needs at least 3 images to be activated.
          Please add more images to make your home available for public bookings.
        </p>

        <!-- Action buttons - stacked on mobile, side by side on larger screens -->
        <div class="tw-flex tw-flex-col sm:tw-flex-row tw-gap-3 sm:tw-gap-4">
          <GoodButton
            color="warning"
            class="tw-w-full sm:tw-w-auto warning-button"
            @click="scrollToPhotoGallery"
          >
            Add Images
          </GoodButton>
          <GoodButtonReverted
            color="warning"
            class="tw-w-full sm:tw-w-auto warning-button"
            @click="disablePublicRental"
          >
            Disable Public Rental
          </GoodButtonReverted>
        </div>
      </div>
    </div>

    <!-- Original Edit Home Component -->
    <EditHome ref="editHomeComponent" v-bind="$attrs" v-on="$listeners" />

    <!-- Host Activation Modal -->
    <HostActivationModal />
  </div>
</template>

<script lang="ts">
import { defineComponent, computed, onMounted, ref, watch } from '@nuxtjs/composition-api'

import EditHome from '~/components/EditHome.vue'
import HostActivationTracker from '~/components/HostActivationTracker.vue'
import HostActivationModal from '~/components/HostActivationModal.vue'
import GoodButton from '~/components/GoodButton.vue'
import GoodButtonReverted from '~/components/GoodButtonReverted.vue'
import { useHostActivationStore } from '~/composables/useHostActivationStore'
import { useEditHomeStore } from '~/composables/useEditHomeStore'
import { useApi, useToast } from '~/composables/useCommon'

export default defineComponent({
  name: 'HomeEditActivationWrapper',

  components: {
    EditHome,
    HostActivationTracker,
    HostActivationModal,
    GoodButton,
    GoodButtonReverted
  },

  inheritAttrs: false,

  setup(props) {
    const hostActivationStore = useHostActivationStore()
    const editHomeStore = useEditHomeStore()
    const api = useApi()
    const toast = useToast()
    const editHomeComponent = ref(null)

    // Add a flag to track if data is fully loaded
    const isDataFullyLoaded = ref(false)
    // Add a flag to track if initial check has been performed
    const initialCheckPerformed = ref(false)

    const isFullyActivated = computed(() => hostActivationStore.isFullyActivated)

    // Check if the home has public rental enabled but fewer than 3 images
    const showPublicRentalImagesWarning = computed(() => {
      const { editHomeData } = editHomeStore
      const hasPublicRental = editHomeData.isBooking || editHomeData.offerAsSeasonalLease
      const hasEnoughImages = editHomeData.photos && editHomeData.photos.length >= 3

      return hasPublicRental && !hasEnoughImages
    })

    // Watch for changes in the home data that might affect the warning banner
    watch(
      () => [editHomeStore.editHomeData.isBooking, editHomeStore.editHomeData.offerAsSeasonalLease, editHomeStore.editHomeData.photos?.length],
      () => {
        // This will trigger a re-evaluation of the showPublicRentalImagesWarning computed property
      }
    )

    // Watch for changes in activation status or home data that might affect activation conditions
    watch(
      () => [
        hostActivationStore.isFullyActivated,
        editHomeStore.editHomeData.isBooking,
        editHomeStore.editHomeData.offerAsSeasonalLease,
        editHomeStore.editHomeData.photos?.length
      ],
      () => {
        // Only run this check if the home is active AND data is fully loaded
        if (editHomeStore.editHomeData.status === 'active' && isDataFullyLoaded.value) {
          console.log('[HomeEditActivationWrapper] Detected change in activation conditions, scheduling check')
          // Add a small delay to avoid rapid consecutive updates
          setTimeout(() => {
            checkHomeActivationConditions()
          }, 500)
        }
      }
    )

    // Function to check if the home meets all activation conditions
    // Only deactivates homes that don't meet requirements, never auto-activates
    const checkHomeActivationConditions = async () => {
      // Check if the home is active
      const isActive = editHomeStore.editHomeData.status === 'active'
      if (!isActive) return // Only proceed if the home is active

      // Log the current state for debugging
      console.log('[HomeEditActivationWrapper] Checking home activation conditions:')
      console.log(`- Home ID: ${editHomeStore.editHomeData.id}`)
      console.log(`- Home Status: ${editHomeStore.editHomeData.status}`)
      console.log(`- Host Fully Activated: ${hostActivationStore.isFullyActivated}`)
      console.log(`- Has Public Rental: ${editHomeStore.editHomeData.isBooking || editHomeStore.editHomeData.offerAsSeasonalLease}`)
      console.log(`- Photo Count: ${editHomeStore.editHomeData.photos?.length || 0}`)

      // Check host activation requirements
      const isHostFullyActivated = hostActivationStore.isFullyActivated

      // Check image requirements for public rental
      const hasPublicRental = editHomeStore.editHomeData.isBooking || editHomeStore.editHomeData.offerAsSeasonalLease
      const hasEnoughImages = editHomeStore.editHomeData.photos && editHomeStore.editHomeData.photos.length >= 3
      const meetsImageRequirements = !hasPublicRental || (hasPublicRental && hasEnoughImages)

      console.log(`- Meets Image Requirements: ${meetsImageRequirements}`)

      // If any condition is not met, silently set the home to draft status
      if (!isHostFullyActivated || !meetsImageRequirements) {
        console.log('[HomeEditActivationWrapper] Home does not meet activation conditions, silently setting to draft')

        try {
          // Update the home status in the store
          editHomeStore.editHomeData.status = 'draft'

          // Update the home status in the backend
          await api.patch(`user/homes/${editHomeStore.editHomeData.id}`, {
            status: 'draft',
          })

          // No toast message - silently update in the background

          // Don't automatically open the activation modal either
        } catch (error) {
          console.error('Error updating home status:', error)
          // No error toast - keep it silent
        }
      } else {
        console.log('[HomeEditActivationWrapper] Home meets all activation conditions, remaining active')
      }
    }

    onMounted(async () => {
      console.log('[HomeEditActivationWrapper] Component mounted')
      // Set the flag to prevent auto-opening of the modal
      hostActivationStore.setPreventAutoOpen(true)

      // Check activation status but don't open the modal automatically
      console.log('[HomeEditActivationWrapper] Checking activation status')
      await hostActivationStore.checkActivationStatus(api)
      console.log(`[HomeEditActivationWrapper] Activation status checked, isFullyActivated: ${hostActivationStore.isFullyActivated}`)

      // Ensure the modal is closed on initial load
      hostActivationStore.closeActivationModal()

      // Wait for a longer delay to ensure all data is properly loaded
      // This is crucial to avoid premature status checks
      console.log('[HomeEditActivationWrapper] Scheduling initial activation check with delay')
      setTimeout(() => {
        console.log('[HomeEditActivationWrapper] Performing initial activation check')
        isDataFullyLoaded.value = true
        initialCheckPerformed.value = true
        checkHomeActivationConditions()
      }, 3000) // Increased delay to ensure all data is loaded
    })

    const openActivationModal = () => {
      hostActivationStore.openActivationModal()
    }

    // Scroll to the photo gallery section
    const scrollToPhotoGallery = () => {
      // Find the photo gallery element and scroll to it
      if (editHomeComponent.value) {
        // Look for the GoodCard2 that contains the photo gallery
        const photoGalleryCards = editHomeComponent.value.$el.querySelectorAll('.tw-text-xl.tw-font-semibold.tw-text-zinc-600')

        // Find the element that contains "Photo Gallery" text
        let photoGalleryElement = null
        photoGalleryCards.forEach(element => {
          if (element.textContent.includes('Photo Gallery')) {
            photoGalleryElement = element
          }
        })

        if (photoGalleryElement) {
          // Find the parent card element to scroll to
          const parentCard = photoGalleryElement.closest('.tw-cursor-pointer')
          if (parentCard) {
            parentCard.scrollIntoView({ behavior: 'smooth', block: 'start' })
            return
          }

          // Fallback to the element itself
          photoGalleryElement.scrollIntoView({ behavior: 'smooth', block: 'start' })
        } else {
          // Fallback: just scroll to the EditHome component
          editHomeComponent.value.$el.scrollIntoView({ behavior: 'smooth', block: 'start' })
        }
      }
    }

    // Disable public rental
    const disablePublicRental = async () => {
      try {
        // Update the editHomeData
        editHomeStore.editHomeData.isBooking = false

        // Call the API to update the setting
        await editHomeStore.updateSettingField(api, 'allow_booking', false)

        toast.success('Public rental has been disabled').goAway(3000)
      } catch (error) {
        console.error('Error disabling public rental:', error)
        toast.error('Failed to disable public rental').goAway(3000)
      }
    }

    return {
      isFullyActivated,
      showPublicRentalImagesWarning,
      openActivationModal,
      scrollToPhotoGallery,
      disablePublicRental,
      editHomeComponent,
      isDataFullyLoaded,
      initialCheckPerformed
    }
  }
})
</script>

<style scoped>
/* Mobile-specific styles */
@media (max-width: 600px) {
  /* Ensure text doesn't overflow on small screens */
  .activation-title, .warning-title {
    font-size: 1.125rem;
    line-height: 1.5rem;
    width: 100%;
    margin-top: 0.25rem;
  }

  .activation-message, .warning-message {
    font-size: 0.875rem;
    line-height: 1.25rem;
    word-break: break-word;
  }

  .activation-button, .warning-button {
    width: 100%;
    margin-top: 0.5rem;
  }

  .activation-wrapper, .warning-wrapper {
    padding: 1rem;
    border-radius: 0.75rem;
    overflow-x: hidden;
  }

  /* Ensure proper spacing for the host activation tracker */
  :deep(.host-activation-tracker) {
    padding: 1rem !important;
    overflow-x: hidden;
  }

  /* Ensure proper spacing for the tracker steps */
  :deep(.host-activation-tracker .tw-flex.tw-items-center) {
    flex-wrap: wrap;
  }

  /* Ensure proper button sizing in the tracker */
  :deep(.host-activation-tracker .v-btn) {
    width: 100%;
    margin-top: 0.5rem;
  }
}
</style>
