<script lang="ts">
import { defineComponent } from '@nuxtjs/composition-api'

import GoodButton from '~/components/GoodButton.vue'

export default defineComponent({
  name: 'PageFooter',

  components: {
    GoodButton,
  },

  props: {},

  data() {
    return {
      email: '',
      errors: [],
      invalid: false,
      valid: true,
      success: false,
    }
  },

  computed: {
    form(): any {
      return this.$refs.email_subscriber
    },
  },

  mounted() {},

  methods: {
    validate_email() {},

    async handleFormSubmit() {
      const isValid = this.form.validate()
      if (!isValid) return

      if (!this.valid) return

      this.errors = []

      try {
        const { email } = this

        const { data } = await this.$axios.post('/forms/emailSubscription', {
          email: email.trim(),
        })

        if (data.success == true) {
          this.form.reset()
          this.success = true
        }
      } catch (error) {
      } finally {
      }
    },
  },
})
</script>

<template>
  <footer class="tw-bg-white">
    <div class="footer__inner tw-container tw-mx-auto">
      <div class="footer__logo">
        <NuxtLink to="/">
          <img src="~/assets/newhome/twimo-header-logo.png" alt="Twimo Logo" />
        </NuxtLink>
      </div>
      <div class="footer__main--section">
        <div class="footer__column">
          <h3 class="footer__form--title">
            Find home management articles, vacation rental tools and more —delivered straight to
            your inbox!
          </h3>
          <p class="footer__form--subtitle">
            sign up with your email address to received news and updates from Twimo
          </p>

          <v-form
            ref="email_subscriber"
            v-model="valid"
            class="tw-flex tw-flex-row tw-gap-3 tw-align-top tw-max-w-[500px] animate"
          >
            <div class="tw-flex tw-flex-col tw-w-4/6 tw-justify-center tw-items-top tw-gap-4">
              <v-text-field
                id="email"
                v-model="email"
                :rules="[v => !!v || 'Email is required']"
                type="email"
                hide-details
                outlined
                label="Email Address"
                class="tw-bg-white tw-rounded-full tw-w-full tw-shadow-custom footer-email"
                @focus="invalid = false"
                @keyup.enter.prevent="handleFormSubmit"
              />

              <span
                v-for="(error, index) in errors"
                :key="index"
                class="tw-text-red-500 tw-text-center tw-mx-auto tw-font-semibold"
                >{{ error }}</span
              >
              <span
                v-if="success"
                class="tw-text-green-500 tw-text-center tw-mx-auto tw-font-semibold"
                >Thank you for subscribing.</span
              >
            </div>

            <div class="tw-flex tw-flex-col tw-w-2/6 tw-justify-center tw-items-top tw-gap-4">
              <good-button
                class="tw-text-base tw-w-full tw-font-bold tw-py-[1.7rem] footer-button"
                :disabled="!valid"
                @click.prevent="handleFormSubmit"
              >
                Sign Up
              </good-button>
            </div>
          </v-form>
        </div>

        <div class="footer__column links_column">
          <p class="footer__column--title">For the Hosts</p>
          <ul>
            <li>
              <NuxtLink class="footer__link" to="/#features">Features</NuxtLink>
            </li>
            <li>
              <NuxtLink class="footer__link" to="/#testimonials">Testimonials</NuxtLink>
            </li>
            <li>
              <NuxtLink class="footer__link" to="/#why_twimo">Why Twimo</NuxtLink>
            </li>
            <li>
              <NuxtLink class="footer__link" to="/#pricing">Pricing</NuxtLink>
            </li>
          </ul>
        </div>

        <div class="footer__column links_column">
          <p class="footer__column--title">The Basics</p>
          <ul>
            <li>
              <NuxtLink class="footer__link" to="/about-us">About Us</NuxtLink>
            </li>
            <li><NuxtLink class="footer__link" to="/faqs">FAQs</NuxtLink></li>
            <li>
              <NuxtLink class="footer__link" to="/faqs#contact">Contact</NuxtLink>
            </li>
            <li><NuxtLink class="footer__link" to="/blog">Blog</NuxtLink></li>
            <li>
              <a class="footer__link" href="https://twimo.hiverkb.com/" target="_blank">Help Section</a>
            </li>
          </ul>
        </div>

        <div class="footer__column">
          <p class="footer__column--title">For the Travelers</p>
          <ul>
            <li>
              <NuxtLink class="footer__link" to="/explore">Explore Vacation Rentals</NuxtLink>
            </li>
          </ul>
        </div>
      </div>
      <div class="footer__bottom">
        <p class="tw-text-center tw-flex tw-justify-center tw-items-center tw-gap-2 tw-mb-5">
          <a href="https://www.linkedin.com/company/*********/admin/dashboard/" target="_blank"
            ><img src="~/assets/icon-linkedin.png" alt="LinkedIn Icon" class="tw-size-8"
          /></a>
          &nbsp;
          <a href="https://www.instagram.com/twimohomes?igsh=N2lrMHVxNmtobGNm&utm_source=qr" target="_blank"
            ><img src="~/assets/icon-instagram.png" alt="Instagram Icon" class="tw-size-8"
          /></a>
          &nbsp;
          <a href="https://www.facebook.com/people/Twimo/61571779242929/" target="_blank"
            ><img src="~/assets/icon-facebook.png" alt="Facebook Icon" class="tw-size-8"
          /></a>
        </p>
        <p>
          © {{ new Date().getFullYear() }} Twimo.com, Inc.
          <span class="tw-hidden md:tw-inline">|</span>
          <br class="tw-block md:tw-hidden" />
          <NuxtLink class="footer__link" to="/terms-and-service">Terms and Service </NuxtLink>
          |
          <NuxtLink class="footer__link" to="/privacy-policy">Privacy Policy</NuxtLink>
        </p>
      </div>
    </div>
  </footer>
</template>
<style scoped>
footer {
  border-top: 1px solid #7b7b7b;
}

.footer__inner {
  padding-top: 50px;
  padding-bottom: 50px;
}
.footer__logo {
  margin: 20px 0;
}
.footer__logo img {
  width: 150px;
  height: auto;
  object-fit: contain;
}
.footer__main--section {
  display: flex;
  gap: 20px;
}
.footer__column:first-of-type {
  width: 70%;
  padding-right: 5%;
}
.footer__column {
  width: 20%;
  display: flex;
  flex-direction: column;
}
.footer__form--title {
  font-size: 20px;
  font-weight: bold;
  color: #360877;
}
.footer__column--title {
  color: #360877;
  font-weight: 800;
  margin-bottom: 20px;
  font-size: 1.2rem;
}
.footer__form--subtitle {
  color: #7b7b7b;
}

.tw-container {
  padding-left: 20px;
  padding-right: 20px;
}

input[type='email'] {
  border: none;
  padding: 12px 35px;
  display: block;
  font-size: 16px;
  background-color: white;
  border-radius: 30px;
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
  color: #000;
}
input[type='submit'] {
  border: none;
  font-size: 16px;
  padding: 12px 40px;
  border-radius: 30px;
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
  background: conic-gradient(
    from 90deg at 50% 50%,
    rgba(56, 11, 115, 0.9) -124.46deg,
    #7c0cb1 73.63deg,
    rgba(56, 11, 115, 0.9) 235.54deg,
    #7c0cb1 433.63deg
  );
  color: white;
  font-weight: 800;
  transition: all 300ms ease;
}
input[type='submit']:hover {
  background-color: white;
  color: #fff;
  cursor: pointer;
}
input:focus {
  outline-style: solid;
  outline-color: #7c0cb1;
  outline-width: 2px;
}

form {
  display: flex;
  gap: 10px;
}

.footer__link {
  display: inline-block;
  margin-bottom: 20px;
  text-decoration: none;
  color: #020202;
  border-bottom: 1px solid #020202;
}
.footer__link:hover {
  color: #360877;
  border-bottom: 1px solid #7a4abf;
}
.footer__bottom {
  margin-top: 50px;
  text-align: center;
  color: #360877;
}
.footer__bottom p {
  font-size: 15px;
}

ul,
li {
  margin: 0px !important;
  padding: 0px !important;
}

>>> .footer-email .v-input__control {
  border: 0px !important;
}

>>> .footer-button .v-btn__content {
  font-weight: bold !important;
}

@media screen and (min-width: 768px) and (max-width: 1024px) {
  .footer__form--title {
    font-size: 35px;
  }
}

@media screen and (max-width: 767px) {
  .footer__main--section {
    /*flex-direction: column;*/
    flex-wrap: wrap;
  }
  .footer__column {
    width: 100% !important;
  }
  .links_column {
    width: 46% !important;
  }
  .footer__column:first-of-type {
    margin-bottom: 20px;
  }
  .footer__bottom {
    margin-top: 30px;
  }
  .footer__form--title {
    font-size: 20px;
  }
  input[type='email'] {
    width: 100%;
    box-sizing: border-box;
    max-width: 400px;
  }
}
</style>
