<template>
  <v-dialog v-model="show" max-width="600px" persistent>
    <GoodCard2 class="tw-flex tw-flex-col tw-gap-4 tw-p-6">
      <div class="tw-text-3xl tw-font-semibold tw-text-primary tw-mb-4">
        Additional Home Details
      </div>

      <div class="tw-mb-4 tw-flex tw-flex-col tw-gap-4 tw-text-lg tw-text-zinc-500">
        <div>
          <strong>Home Details Editing:</strong> With the additional features of Twimo there might
          be some Home Details that weren't included before. If you need to add or edit any details
          you can do so on this page!
        </div>
        <div>
          <strong>Booking Settings and Pricing:</strong> You currently have Private Booking and
          Swaps turned ON. To add other booking types, remove booking types, or adjust pricing you
          can do so in this section.
        </div>
      </div>

      <GoodButton class="tw-w-fit tw-mx-auto" @click="closePopup"> Got it! </GoodButton>
    </GoodCard2>
  </v-dialog>
</template>

<script>
import { mapState } from 'vuex'

import GoodCard2 from '~/components/GoodCard2.vue'
import GoodButton from '~/components/GoodButton.vue'

export default {
  name: 'LegacyHomeDetails',

  components: {
    GoodCard2,
    GoodButton,
  },

  data() {
    return {
      show: false,
    }
  },

  computed: {
    ...mapState('auth', ['user']),
  },

  watch: {
    user: {
      immediate: true,
      handler(user) {
        if (user) {
          const hasSeenDetails = localStorage.getItem('twimo_legacy_details_seen')
          if (new Date(user.created_at) < new Date('2025-01-31') && !hasSeenDetails) {
            this.show = true
          }
        }
      },
    },
  },

  methods: {
    closePopup() {
      this.show = false
      localStorage.setItem('twimo_legacy_details_seen', 'true')
    },
  },
}
</script>
