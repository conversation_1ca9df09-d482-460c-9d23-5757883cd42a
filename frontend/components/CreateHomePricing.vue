<script lang="ts">
// @ts-nocheck
import { defineComponent, onMounted } from '@nuxtjs/composition-api'

import BoldPurpleText from '~/components/BoldPurpleText.vue'
import { useCreateHomeProgressStore } from '~/composables/useCreateHomeStore'

export default defineComponent({
  name: 'CreateHomePricing',

  components: {
    BoldPurpleText,
  },

  setup() {
    const store = useCreateHomeProgressStore()

    const { createHomeData, clickOnOfferAsSeasonalLease, isSeasonalLeaseSelected } = store

    const handleNumericInput = (event: KeyboardEvent, field: string) => {
      // Prevent non-numeric keys (allow only numbers and control keys)
      if (
        !/^\d$/.test(event.key) &&
        !['Backspace', 'Delete', 'Tab', 'ArrowLeft', 'ArrowRight'].includes(event.key)
      ) {
        event.preventDefault()
        return
      }

      const target = event.target as HTMLInputElement
      const value = target.value
      const cleanValue = value.replace(/[^0-9]/g, '').replace(/^0+/, '')

      // For nightlyRate, ensure we don't set it to null (which would bypass validation)
      if (field === 'nightlyRate') {
        // If the field is empty or only contains zeros, set to 0 (valid) instead of null (invalid)
        createHomeData[field] = cleanValue ? Number(cleanValue) : 0
      } else {
        // For other fields, maintain the existing behavior
        createHomeData[field] = cleanValue ? Number(cleanValue) : null
      }
    }

    const rules = {
      nightlyRate: [(v) => {
        // Handle both string and number types
        if (typeof v === 'string') {
          return v.trim() !== '' || 'Nightly Rate is required'
        }
        return (v !== null && v !== undefined && !isNaN(v)) || 'Nightly Rate is required'
      }],
      taxRate: [(v: number) => (v >= 0 && v <= 100) || 'Tax rate must be between 0 and 100%'],
      minimumStay: [(v: number) => v >= 1 || 'Minimum stay must be at least 1 night'],
    }

    onMounted(() => {
      if (isSeasonalLeaseSelected) {
        createHomeData.offerAsSeasonalLease = true
      }
    })

    return {
      createHomeData,
      clickOnOfferAsSeasonalLease,
      handleNumericInput,
      rules,
    }
  },
})
</script>

<template>
  <v-row>
    <v-col cols="12" md="10" lg="8" class="tw-mt-8 tw-flex tw-flex-col tw-gap-4">
      <div class="tw-text-2xl tw-font-semibold tw-text-zinc-600">Now Let's Set Up Your Pricing</div>

      <div class="tw-text-lg tw-font-medium tw-text-zinc-500">
        Twimo Pricing is flexible based on your needs - Friends & Family, Swap, or Rent. Set your
        Default Rates below.
      </div>

      <div class="tw-italic tw-text-zinc-500 tw-mt-8">
        You can input Adjustable Rates, discounting your fees when sharing with Friends & Family or
        add Advanced Pricing Rates to increase fees for Weekends, Holidays, etc within your Booking
        Settings once your home is complete.
      </div>
    </v-col>

    <v-col cols="12" md="10" lg="8">
      <v-row>
        <v-col cols="12" sm="6">
          <v-text-field
            v-model.number="createHomeData.nightlyRate"
            type="text"
            label="Nightly Rate*"
            prefix="$"
            outlined
            dense
            class="tw-mt-8"
            required
            validate-on-blur
            :rules="rules.nightlyRate"
            @keydown="e => handleNumericInput(e, 'nightlyRate')"
            @blur="createHomeData.nightlyRate === '' ? createHomeData.nightlyRate = 0 : null"
          />
        </v-col>

        <v-col cols="12" sm="6">
          <v-text-field
            v-model.number="createHomeData.petFee"
            type="text"
            label="Pet Fee (if applicable for entire stay)"
            prefix="$"
            outlined
            hide-details
            dense
            class="tw-mt-8"
            required
            @keydown="e => handleNumericInput(e, 'petFee')"
          />
        </v-col>

        <v-col cols="12" sm="6">
          <v-text-field
            v-model.number="createHomeData.cleaningFee"
            type="text"
            label="Cleaning Fee"
            prefix="$"
            outlined
            hide-details
            dense
            class="tw-mt-8"
            required
            @keydown="e => handleNumericInput(e, 'cleaningFee')"
          />
        </v-col>

        <v-col cols="12" sm="6">
          <v-text-field
            v-model.number="createHomeData.taxRate"
            type="text"
            label="Taxes"
            prefix="%"
            outlined
            hide-details
            dense
            class="tw-mt-8"
            required
            :rules="rules.taxRate"
            @keydown="e => handleNumericInput(e, 'taxRate')"
          />
        </v-col>

        <v-col cols="12" sm="6">
          <v-text-field
            v-model.number="createHomeData.minimumStay"
            type="text"
            label="Minimum Nightly Stay"
            suffix="nights"
            outlined
            hide-details
            dense
            class="tw-mt-8"
            required
            :rules="rules.minimumStay"
            @keydown="e => handleNumericInput(e, 'minimumStay')"
          />
        </v-col>
      </v-row>
    </v-col>
  </v-row>
</template>

<style scoped>
/* Add responsive padding for mobile */
@media (max-width: 639px) {
  :deep(.v-text-field) {
    width: 100%;
  }
  
  :deep(.v-input__slot) {
    min-height: 48px;
  }
  
  :deep(.v-label) {
    font-size: 14px;
  }
  
  :deep(.v-text-field__prefix) {
    padding-right: 4px;
  }
  
  :deep(.v-text-field__suffix) {
    padding-left: 4px;
  }
}
</style>
