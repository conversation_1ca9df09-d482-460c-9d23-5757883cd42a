<script lang="ts">
// @ts-nocheck
import { defineComponent, ref, watch } from '@nuxtjs/composition-api'

import { useCreateHomeProgressStore } from '~/composables/useCreateHomeStore'
import { amenities, MANDATORY_SAFETY_AMENITIES } from '~/constants'
import Amenity from '~/components/Amenity.vue'

export default defineComponent({
  name: 'CreateHomeAmenity',

  components: {
    Amenity,
  },

  setup() {
    const store = useCreateHomeProgressStore()

    const isSafetyAmenity = (amenity: string) => {
      return MANDATORY_SAFETY_AMENITIES.includes(amenity)
    }

    const {
      createHomeData,
      setCurrentStep,
      validateCreateHomeData,
      clickOnRentalPlan,
      decreaseBedrooms,
      decreaseBeds,
      decreaseBathrooms,
      decreaseGuests,
      decreasePets,
      isAmenitySelected,
      clickOnAmenity,
    } = store

    const amenityCategories = [
      { title: "Let's Start with the Exterior", key: 'exterior' },
      { title: 'How About the Interior?', key: 'interior' },
      { title: 'And the Kitchen', key: 'kitchen' },
      { title: 'What About Additional Security? ', key: 'security' },
      { title: 'What About the Entertainment?', key: 'entertainment' },
      { title: 'Kids & Fur Friends', key: 'kidsAndPets' },
      { title: 'Additional Home Details', key: 'additional' },
      { title: 'Essential Safety Items', key: 'safety' },
    ]

    const amenityAgreement = ref(false)

    // Watch the local agreement and update the store
    watch(amenityAgreement, newValue => {
      createHomeData.agreement = newValue
    })

    return {
      createHomeData,
      validateCreateHomeData,
      setCurrentStep,
      clickOnRentalPlan,
      decreaseBedrooms,
      decreaseBeds,
      decreaseBathrooms,
      decreasePets,
      decreaseGuests,
      amenities,
      isAmenitySelected,
      clickOnAmenity,
      amenityCategories,
      amenityAgreement,
      MANDATORY_SAFETY_AMENITIES,
      isSafetyAmenity,
    }
  },
})
</script>

<template>
  <v-row>
    <v-col cols="12" class="tw-mt-8 tw-flex tw-flex-col tw-gap-4">
      <div class="tw-text-2xl tw-font-semibold tw-text-zinc-600">Let's Add Amentities</div>

      <div class="tw-text-zinc-500 tw-text-lg">Check all applicable amenities:</div>

      <div class="tw-text-zinc-500">
        At any point, you can Skip a step if not applicable by selecting Next
      </div>
    </v-col>

    <template v-for="category in amenityCategories">
      <v-col :key="category.key" cols="12" class="tw-mt-4">
        <div class="tw-text-xl tw-font-semibold tw-text-primary">
          {{ category.title }}
        </div>
        <div v-if="category.key === 'safety'" class="tw-text-zinc-500 tw-mt-2">
          Please make sure you have all of these items in your home, they are required by default.
        </div>
      </v-col>
      <v-col
        v-for="amenity in amenities[category.key]"
        :key="amenity"
        cols="6"
        md="2"
        @click="clickOnAmenity(amenity)"
      >
        <Amenity
          :amenity="amenity"
          :disabled="isSafetyAmenity(amenity)"
          :is-activated="isAmenitySelected(amenity)"
        />
      </v-col>
    </template>

    <v-col cols="12">
      <div>
        <v-checkbox v-model="amenityAgreement" hide-details dense required>
          <template #label>
            <div class="tw-text-xl tw-text-primary">
              I agree to the Twimo Privacy Policy and Terms and Conditions
            </div>
          </template>
          >
        </v-checkbox>
      </div>
    </v-col>
  </v-row>
</template>

<style scoped></style>
