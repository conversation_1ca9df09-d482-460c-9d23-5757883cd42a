<script lang="ts">
// @ts-nocheck
import { defineComponent } from '@nuxtjs/composition-api'
import QrcodeVue from 'qrcode.vue'

import MyCrewImagesUploader from '~/components/MyCrewImagesUploader.vue'
import { useCreateHomeImageStore } from '~/composables/useCreateHomeImageStore'
import GoodButton from '~/components/GoodButton.vue'

export default defineComponent({
  name: 'MyCrewJobItem',

  components: {
    MyCrewImagesUploader,
    GoodButton,
    QrcodeVue,
  },

  props: {
    item: {
      type: Object,
      required: true,
    },
  },

  data() {
    return {
      isExpanded: false,
      user: {
        role_id: null,
      },
      media: [],
      original_url: '',
      markCompleteDialog: false,
      sendMessageDialog: false,
      paymentCompleteDialog: false,
      confirmList: [
        { text: `Yes`, value: 'yes' },
        { text: `No`, value: 'no' },
      ],
      jobForm: {
        confirm_status: null,
        details: null,
        photos: [],
      },
      messageForm: {
        body: null,
        sender_id: null,
        receiver_id: null,
        subject: null
      },
      qrUrl: `https://venmo.com/code?user_id=${this.item.venmo_username}&txn=pay&amount=${this.item.payment_amount}&note=MyCrew%20Job`,
      imageStore: useCreateHomeImageStore(),
    }
  },

  computed: {
    userInfo() {
      return this.$store.getters['auth/getuser']
    },
    displayImages() {
      // Safely access the images array with fallback to empty array
      const storeImages = this.imageStore.images || []

      return storeImages.map(img => ({
        ...img,
        id: img.id,
        name: img.name || (img.file ? img.file.name : 'Image'),
        size: img.size || (img.file ? img.file.size : 0),
      }))
    }
  },

  async mounted() {
    // Get user details
    await this.$store.dispatch('auth/getUserDetails')
    this.userInfo = this.$store.getters['auth/getuser']
    this.messageForm.sender_id = this.userInfo.id
    this.messageForm.subject = 'MyCrew Job - ' + this.item.job_title

    if (this.userInfo.role_id == 4) 
      this.messageForm.receiver_id = this.item.user_id
    else
      this.messageForm.receiver_id = this.item.vendor_user_id

    if (
      Object.values(this.item.home_featured_image).filter(value => typeof value === 'object')
        .length != 0
    )
      this.original_url = Object.values(this.item.home_featured_image)[0].original_url
    else
      this.original_url =
        'https://ik.imagekit.io/hweihwang/677/conversions/Screen-Shot-2021-05-29-at-6.08.49-AM-thumb.jpg?f=webp'
  },

  methods: {
    async toggleExpanded() {
      this.isExpanded = !this.isExpanded

      if (this.isExpanded == true) {
        const { data } = await this.$axios.get(`/mycrew/get-media?job_id=${this.item.job_id}`)
        this.media = data.media
      }
    },
    formatDate(date) {
      if (!date) return ''
      const options = {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        timeZone: 'UTC',
      }
      return new Date(date).toLocaleDateString('en-US', options)
    },
    async updateJobStatus(status) {
      try {
        if (status == 'completed') {
          this.markCompleteDialog = true

          return
        }

        const { data } = await this.$axios.put('mycrew/job-status-update/' + this.item.job_id, {
          form_data: { status },
        })
        if (data.status == 'success') {
          this.$emit('call-get-jobs')

          let message = ''

          if (status == 'accepted') message = 'Job has been accepted successfully.'
          else if (status == 'rejected') message = 'Job has been rejected successfully.'
          else if (status == 'completed') message = 'Job has been completed successfully.'

          this.$toast.success(message).goAway(3000)
        }
      } catch (err) {
        return this.$toast.error('Error while updating the job.').goAway(this.TOAST_DURATION)
      } finally {
      }
    },

    async cancelJob() {
      const c = confirm('Are you sure, do you want to cancel this job?')

      if (c == true) {
        try {
          const { data } = await this.$axios.put('mycrew/cancel-job/' + this.item.job_id, {
            form_data: this.createForm,
          })
          if (data.status == 'success') {
            this.$emit('call-get-jobs')
          }
        } catch (err) {
          return this.$toast.error('Error while updating the job.').goAway(this.TOAST_DURATION)
        } finally {
        }
      }
    },

    async uploadFiles(e: any) {
      const files = [...e]

      console.log('Files to upload:', files)

      for (let i = 0; i < files.length; i++) {
        const request = new FormData()
        request.append('file', files[i])

        try {
          await this.$axios.post('upload', request).then(response => {
            const uploadedFile: UploadedFile = response.data

            this.jobForm.photos.push(uploadedFile)
            console.log('Uploaded file:', uploadedFile)
          })
        } catch (error) {
          this.toastError('Please check the file type and size')

          // editHomeData.photos = []

          break
        }
      }
    },


    // Handle files selected
    handleFilesSelected(files: File[]) {
      if (!files || files.length === 0) {
        return
      }

      this.processingFiles = true
      console.log(`Processing ${files.length} files`)

      try {
        // Add images to store - this is a synchronous operation
        this.imageStore.addImages(files)
        this.uploadFiles(files);

        // Show success toast
        this.$toast.success(`Added ${files.length} image(s) for upload`)
        console.log('Image store:', this.imageStore.images)

        // Log success
        console.log(`Added ${files.length} files, total in store:`, this.imageStore.images.length)
      } catch (error) {
        console.error('Error adding images:', error)
        this.$toast.error('Failed to add images')
      } finally {
        this.processingFiles = false
      }
    },

    // Handle image deletion
    handleDeleteImage(imageId: string) {
      try {
        this.imageStore.deleteImage(imageId)
      } catch (error) {
        console.error('Error deleting image:', error)
        this.$toast.error('Failed to delete image')
      }
    },

    // Handle image reordering - IMPORTANT: Match the event name from ImagesUploader
    handleImagesReordered(reorderedImages) {
      try {
        console.log('Images reordered, new order:', reorderedImages)
        this.imageStore.reorderImages(reorderedImages)
        this.$toast.success('Image order updated')
      } catch (error) {
        console.error('Error reordering images:', error)
        this.$toast.error('Failed to reorder images')
      }
    },

    // Handle uploader errors
    handleUploadError(errorMessage: string) {
      this.$toast.error(errorMessage).goAway(3000)
    },



    async markAsCompleted() {
      try {
        if (this.jobForm.confirm_status == null) {
          alert('Please select the status.')
          return false
        }

        const { data } = await this.$axios.put('mycrew/update-job/' + this.item.job_id, {
          form_data: this.jobForm,
        })
        if (data.status == 'success') {
          this.markCompleteDialog = false

          this.$emit('call-get-jobs')

          this.$toast.success('Job has been completed successfully.').goAway(3000)
        }
      } catch (err) {
        return this.$toast.error('Error while updating the job.').goAway(this.TOAST_DURATION)
      } finally {
      }
    },

    async markAsPaid() {
      try {
        const { data } = await this.$axios.put('mycrew/mark-as-paid/' + this.item.job_id, {
          form_data: this.jobForm,
        })
        if (data.status == 'success') {
          this.paymentCompleteDialog = false

          this.$emit('call-get-jobs')

          this.$toast.success('Job has been completed successfully.').goAway(3000)
        }
      } catch (err) {
        return this.$toast.error('Error while updating the job.').goAway(this.TOAST_DURATION)
      } finally {
      }
    },

    formatDate(fullDate) {
      const date = new Date(fullDate)

      const month = (date.getMonth() + 1).toString().padStart(2, '0') // Month is 0-indexed, so add 1
      const day = date.getDate().toString().padStart(2, '0')
      const year = date.getFullYear().toString().slice(2) // Extract last 2 digits of the year

      // Return in MM/DD/YY format
      return `${month}/${day}/${year}`
    },

    cancel() {
      this.markCompleteDialog = false
      this.paymentCompleteDialog = false
      this.sendMessageDialog = false
    },

    sendMessage() {
      if (this.messageForm.body == null) {
        return this.$toast.error('Please enter the message.').goAway(this.TOAST_DURATION)
      }
      
      this.$axios
        .post('messages-v2', this.messageForm)
        .then(response => {
          this.sendMessageDialog = false
          this.$toast.success('Message has been sent successfully.').goAway(3000)
        })

    },
  },
})
</script>

<template>
  <div class="tw-items-center mycrew-item md:tw-p-[3em]">
    <!--   VENDOR - JOB COMPLETE DIALOG-->
    <v-dialog v-model="markCompleteDialog" :max-width="'80%'">
      <good-card card-text-classes="tw-p-5">
        <div class="tw-text-3xl tw-font-semibold tw-text-left tw-tracking-wide">
          <span class="tw-text-zinc-500">MyCrew Job Report</span>
        </div>

        <p class="tw-font-regular tw-text-base tw-mt-3 tw-mb-5 tw-text-zinc-500">
          Input the details for your new MyCrew Job below and send!
        </p>

        <v-form ref="form" v-model="valid">
          <v-row>
            <v-col xs="12" sm="12" md="8" cols="12">
              <v-select
                v-model="jobForm.confirm_status"
                :items="confirmList"
                label="Have all requested job tasks been completed?"
                required
                :rules="[v => !!v || 'Status is required']"
                class="tw-w-full tw-shadow-input tw-rounded-2xl custom-input"
                outlined
                hide-details
              >
              </v-select>
            </v-col>
          </v-row>

          <v-row>
            <v-col xs="12" sm="12" md="8" cols="12">
              <v-textarea
                v-model="jobForm.details"
                label="Any notes? Complications, changes, etc? "
                outlined
                hide-details
                class="tw-w-full tw-shadow-input tw-rounded-2xl custom-input"
              ></v-textarea>
            </v-col>
          </v-row>

          <v-row no-gutters>
            <v-col
              xs="12"
              sm="12"
              md="8"
              cols="12"
              class="tw-p-[2em] tw-mt-5 tw-mb-2 tw-rounded-2xl tw-shadow-input mycrew-upload-section"
            >
              <div class="tw-text-[13px] tw-text-purple-700 tw-font-semibold">
                Add Image (If applicable)
              </div>

              <MyCrewImagesUploader
                  ref="imageUploader"
                  class="tw-min-h-[150px] imageUploaderContainer"
                  :images="displayImages"
                  :max-files="20"
                  :max-file-size="10"
                  :is-uploading="isUploading || processingFiles"
                  @files-selected="handleFilesSelected"
                  @delete-image="handleDeleteImage"
                  @images-reordered="handleImagesReordered"
                  @error="handleUploadError"
                />              
            </v-col>
          </v-row>

          <div class="tw-flex tw-justify-end tw-mb-10 tw-mt-3">
            <good-cancel-button @click="cancel" />

            <GoodButton @click="markAsCompleted"> Mark as Completed </GoodButton>
          </div>
        </v-form>
      </good-card>
    </v-dialog>



    <!--   SEND MESSAGES  -->
    <v-dialog v-model="sendMessageDialog" :max-width="'80%'">
      <good-card card-text-classes="tw-p-5">
        <div class="tw-text-3xl tw-font-semibold tw-text-left tw-tracking-wide tw-mb-7">
          <span class="tw-text-zinc-500">Send Message</span>
        </div>

        <v-form ref="form" v-model="valid">
          
          <v-row>
            <v-col xs="12" sm="12" md="8" cols="12">
              <v-textarea
                v-model="messageForm.body"
                label="Enter the message you would like to send"
                outlined
                hide-details
                class="tw-w-full tw-shadow-input tw-rounded-2xl custom-input"
              ></v-textarea>
            </v-col>
          </v-row>

          <div class="tw-flex tw-justify-start tw-mb-10 tw-mt-5">
            <GoodButton @click="sendMessage"> Send Message </GoodButton>
            <good-cancel-button @click="cancel" />
          </div>
        </v-form>
      </good-card>
    </v-dialog>



    <!-- HOST - PAYMENT COMPLETE DIALOG -->
    <v-dialog v-model="paymentCompleteDialog" :max-width="'80%'">
      <good-card card-text-classes="tw-p-5">
        <div class="tw-text-3xl tw-font-semibold tw-text-left tw-tracking-wide">
          <span class="tw-text-zinc-500">MyCrew Job Report</span>
        </div>

        <p class="tw-font-regular tw-text-base tw-mt-3 tw-mb-5 tw-text-zinc-500">
          Review the below report from your vendor, if all looks good approve and initiate payment
        </p>

        <div
          id="job_details"
          class="tw-border-t-[1px] tw-border-b-[1px] tw-border-[#e0e0e0] tw-mt-[1.2rem] tw-pt-[1.2rem] tw-pb-[1.2rem] tw-mb-[1.2rem]"
        >
          <h3 class="tw-text-[#6C6C6C] tw-text-xl tw-font-semibold">
            {{ item.job_title }}
          </h3>
          <h4 class="tw-text-base">
            {{ item.vendor_first_name }} {{ item.vendor_last_name }},
            {{ item.category_name }}
          </h4>
          <h5>
            Due {{ formatDate(item.due_date) }} &nbsp; &nbsp; Amount Owed ${{ item.payment_amount }}
          </h5>
        </div>

        <v-form ref="form" v-model="valid">
          <h3 class="tw-text-[#6C6C6C] tw-text-xl tw-font-semibold tw-mb-[1rem]">Job Details</h3>

          <v-row>
            <v-col xs="12" sm="12" md="8" cols="12">
              <v-select
                v-model="jobForm.confirm_status"
                :items="confirmList"
                label="Have all requested job tasks been completed?"
                required
                :rules="[v => !!v || 'Status is required']"
                class="tw-w-full tw-shadow-input tw-rounded-2xl custom-input"
                outlined
                hide-details
              >
              </v-select>
            </v-col>
          </v-row>

          <v-row>
            <v-col xs="12" sm="12" md="8" cols="12">
              <v-textarea
                v-model="jobForm.details"
                label="Any notes? Complications, changes, etc? "
                outlined
                hide-details
                class="tw-w-full tw-shadow-input tw-rounded-2xl custom-input"
              ></v-textarea>
            </v-col>
          </v-row>

          <v-row no-gutters>
            <v-col
              xs="12"
              sm="12"
              md="8"
              cols="12"
              class="tw-p-[2em] tw-mt-5 tw-mb-2 tw-rounded-2xl tw-shadow-input"
            >
              <div class="tw-text-base tw-text-purple-700 tw-font-semibold tw-mb-5">
                Image of completed job
              </div>

              <div class="images-container">
                <v-row>
                  <v-col
                    v-for="(image, key) in item.vendor_uploaded_images"
                    :key="key"
                    xs="12"
                    sm="12"
                    md="6"
                    cols="12"
                  >
                    <a :href="image.original_url" target="_blank"
                      ><img :src="image.original_url" width="100%"
                    /></a>
                  </v-col>
                </v-row>
              </div>
            </v-col>
          </v-row>

          <v-row>
            <v-col xs="12" sm="12" md="8" cols="12">
              <h3
                v-if="item.payment_type == 'venmo'"
                class="tw-text-[#6C6C6C] tw-text-xl tw-font-semibold tw-mb-[1rem]"
              >
                Venmo Account
              </h3>
              <h4 v-if="item.payment_type == 'venmo'" class="tw-text-base">
                Your Vendor’s Venmo payment information is below, please scan the Venmo QR Code to
                finalize payment
              </h4>

              <h3 class="tw-text-[#6C6C6C] tw-text-xl tw-font-semibold tw-mt-[1.5rem] tw-mb-[1rem]">
                Payment Due
              </h3>
              <div class="tw-text-base tw-font-bold tw-text-purple-700">
                ${{ item.payment_amount }} via {{ item.payment_type }}
              </div>
              <div class="tw-text-[13px] tw-mt-5 tw-text-purple-700 tw-font-semibold">
                Scan Venmo QR Code
              </div>
              <div class="qr_code tw-mt-5 tw-mb-5">
                <qrcode-vue :value="qrUrl" :size="200" level="H" render-as="svg" />
              </div>
            </v-col>
          </v-row>

          <div class="tw-flex tw-justify-end tw-mb-10 tw-mt-3">
            <good-cancel-button class="tw-mt-[0.2rem] tw-mr-[1rem]" @click="cancel" />

            <GoodButton class="tw-mr-5" @click="markAsPaid"> Mark as Completed </GoodButton>
            <v-btn
              class="tw-bg-white tw-normal-case tw-text-[#000] tw-rounded-full"
              @click="$router.push({ path: '/messages' })"
            >
              Send Message to Vendor
            </v-btn>
          </div>
        </v-form>
      </good-card>
    </v-dialog>

    <div
      v-if="
        item.job_status == `pending` ||
        (item.job_status == `completed` && item.payment_status == `pending`)
      "
      class="tw-text-purple-700 tw-pt-1 tw-pb-1 tw-pl-4 tw-pr-4 tw-rounded-lg tw-elevation-4 mycrew-item-status"
    >
      {{ item.job_status == 'pending' ? 'Pending' : 'Payment Due' }}
    </div>

    <div
      v-if="item.job_status == `accepted`"
      class="tw-text-purple-700 tw-pt-1 tw-pb-1 tw-pl-4 tw-pr-4 tw-rounded-lg tw-elevation-4 mycrew-item-status"
    >
      In Progress
    </div>

    <div
      v-if="item.job_status == `rejected`"
      class="tw-text-purple-700 tw-pt-1 tw-pb-1 tw-pl-4 tw-pr-4 tw-rounded-lg tw-elevation-4 mycrew-item-status"
    >
      Rejected
    </div>

    <v-row>
      <v-col md="2" xs="12" sm="12" cols="12" class="tw-text-center">
        <div class="home_image_container">
          <img :src="original_url" />
        </div>
      </v-col>
      <v-col md="10" class="md:tw-flex tw-flex-wrap">
        <div class="mc_title md:tw-w-[25%] xs:tw-w-[50%] tw-mb-6">
          <h4 class="tw-text-[1.2rem] tw-font-semibold tw-mb-1 tw-text-gray-500">Job Title</h4>
          <h5 class="tw-font-xl tw-font-semibold tw-text-gray-500">
            {{ item.job_title }}
          </h5>
        </div>
        <div class="mc_home md:tw-w-[25%] xs:tw-w-[50%] tw-mb-6">
          <h4 class="tw-text-[1.2rem] tw-font-semibold tw-mb-1 tw-text-gray-500">Home</h4>
          <h5 class="tw-font-xl tw-text-gray-500">{{ item.home_title }}</h5>
        </div>
        <div class="mc_due_date md:tw-w-[25%] xs:tw-w-[50%] tw-mb-6">
          <h4 class="tw-text-[1.2rem] tw-font-semibold tw-mb-1 tw-text-gray-500">Due Date</h4>
          <h5 class="tw-font-xl tw-text-gray-500">
            {{ formatDate(item.due_date) }}
          </h5>
        </div>
        <div class="mc-priority md:tw-w-[25%] xs:tw-w-[50%] tw-mb-6">
          <h4 class="tw-text-[1.2rem] tw-font-semibold tw-mb-1 tw-text-gray-500">Priority</h4>
          <h5 class="tw-font-xl tw-text-gray-500" style="text-transform: capitalize">
            {{ item.priority_level }}
          </h5>
        </div>
        <div
          v-if="isExpanded && userInfo.role_id != 4"
          class="mc-priority md:tw-w-[25%] xs:tw-w-[50%] tw-mb-6"
        >
          <h4 class="tw-text-[1.2rem] tw-font-semibold tw-mb-1 tw-text-gray-500">Vendor</h4>
          <h5 class="tw-font-xl tw-text-gray-500">
            {{
              item.vendor_first_name != null
                ? item.vendor_first_name + ' ' + item.vendor_last_name
                : item.vendor_email
            }}
          </h5>
        </div>
        <div
          v-if="isExpanded && userInfo.role_id == 4"
          class="mc-priority md:tw-w-[25%] xs:tw-w-[50%] tw-mb-6"
        >
          <h4 class="tw-text-[1.2rem] tw-font-semibold tw-mb-1 tw-text-gray-500">Requested By</h4>
          <h5 class="tw-font-xl tw-text-gray-500">
            {{ item.request_first_name + ' ' + item.request_last_name }}
          </h5>
        </div>
        <div v-if="isExpanded" class="mc-priority md:tw-w-[25%] xs:tw-w-[50%] tw-mb-6">
          <h4 class="tw-text-[1.2rem] tw-font-semibold tw-mb-1 tw-text-gray-500">Category</h4>
          <h5 class="tw-font-xl tw-text-gray-500">{{ item.category_name }}</h5>
        </div>
        <div v-if="isExpanded" class="mc-priority md:tw-w-[25%] xs:tw-w-[50%] tw-mb-6">
          <h4 class="tw-text-[1.2rem] tw-font-semibold tw-mb-1 tw-text-gray-500">Home Address</h4>
          <h5 class="tw-font-xl tw-text-gray-500">{{ item.address }}</h5>
        </div>
        <div v-if="isExpanded" class="mc-priority md:tw-w-[25%] xs:tw-w-[50%] tw-mb-6">
          <h4 class="tw-text-[1.2rem] tw-font-semibold tw-mb-1 tw-text-gray-500">Payment Due</h4>
          <h5 class="tw-font-xl tw-text-gray-500">${{ item.payment_amount }}</h5>
        </div>
        <div v-if="isExpanded" class="mc-priority md:tw-w-[25%] xs:tw-w-[50%] tw-mb-6">
          <h4 class="tw-text-[1.2rem] tw-font-semibold tw-mb-1 tw-text-gray-500">Payment Type</h4>
          <h5 class="tw-font-xl tw-text-gray-500" style="text-transform: capitalize">
            {{ item.payment_type }}
          </h5>
        </div>
        <div v-if="isExpanded" class="mc-priority md:tw-w-[25%] xs:tw-w-[50%] tw-mb-6">
          <h4 class="tw-text-[1.2rem] tw-font-semibold tw-mb-1 tw-text-gray-500">Details</h4>
          <h5 class="tw-font-xl tw-text-gray-500">{{ item.details }}</h5>
        </div>
        <div v-if="isExpanded && Object.keys(media).length > 0" class="mc-priority tw-w-[100%]">
          <h4 class="tw-text-[1.2rem] tw-font-semibold tw-mb-1 tw-text-gray-500">Images</h4>
          <div class="tw-flex tw-gap-5">
            <div v-for="m in media">
              <img :src="m.original_url" class="tw-max-w-[250px]" />
            </div>
          </div>
        </div>

        <div v-if="item.vendor_task_status != NULL" class="tw-w-[100%] md:tw-flex tw-flex-wrap">
          <div v-if="isExpanded" class="mc-priority md:tw-w-[100%] xs:tw-w-[100%] tw-mb-1 tw-mt-10">
            <h4 class="tw-text-[1.2rem] tw-font-semibold tw-mb-1 tw-text-gray-500">
              Vendor Job Report
            </h4>
          </div>

          <div
            v-if="isExpanded"
            class="mc-priority md:tw-w-[50%] xs:tw-w-[50%] tw-mb-6 tw-mt-[0.5rem]"
          >
            <h4 class="tw-text-[1rem] tw-font-semibold tw-mb-1 tw-text-gray-500">
              Have all requested job tasks completed?
            </h4>
            <h5 class="tw-font-xl tw-text-gray-500 tw-capitalize">
              {{ item.vendor_task_status }}
            </h5>
          </div>

          <div
            v-if="isExpanded"
            class="mc-priority md:tw-w-[50%] xs:tw-w-[50%] tw-mb-6 tw-mt-[0.5rem]"
          >
            <h4 class="tw-text-[1rem] tw-font-semibold tw-mb-1 tw-text-gray-500">Description</h4>
            <h5 class="tw-font-xl tw-text-gray-500">
              {{ item.vendor_task_description }}
            </h5>
          </div>

          <div
            v-if="isExpanded"
            class="tw-text-[1rem] tw-w-[100%] tw-font-semibold tw-mb-1 tw-text-gray-500"
          >
            Images of completed job
          </div>

          <div v-if="isExpanded" class="images-container">
            <v-row>
              <v-col
                v-for="(image, key) in item.vendor_uploaded_images"
                :key="key"
                xs="12"
                sm="12"
                md="4"
                cols="12"
              >
                <a :href="image.original_url" target="_blank"
                  ><img :src="image.original_url" width="100%"
                /></a>
              </v-col>
            </v-row>
          </div>
        </div>

        <div v-if="item.host_task_status != NULL" class="tw-w-[100%] md:tw-flex tw-flex-wrap">
          <div v-if="isExpanded" class="mc-priority md:tw-w-[100%] xs:tw-w-[100%] tw-mb-1 tw-mt-10">
            <h4 class="tw-text-[1.2rem] tw-font-semibold tw-mb-1 tw-text-gray-500">
              Host Job Report
            </h4>
          </div>

          <div
            v-if="isExpanded"
            class="mc-priority md:tw-w-[50%] xs:tw-w-[50%] tw-mb-6 tw-mt-[0.5rem]"
          >
            <h4 class="tw-text-[1rem] tw-font-semibold tw-mb-1 tw-text-gray-500">
              Have all requested job tasks completed?
            </h4>
            <h5 class="tw-font-xl tw-text-gray-500 tw-capitalize">
              {{ item.host_task_status }}
            </h5>
          </div>

          <div
            v-if="isExpanded"
            class="mc-priority md:tw-w-[50%] xs:tw-w-[50%] tw-mb-6 tw-mt-[0.5rem]"
          >
            <h4 class="tw-text-[1rem] tw-font-semibold tw-mb-1 tw-text-gray-500">Description</h4>
            <h5 class="tw-font-xl tw-text-gray-500">
              {{ item.host_task_description }}
            </h5>
          </div>
        </div>

        <div
          v-if="isExpanded && item.job_status == 'pending' && userInfo.role_id == 4"
          class="mc-priority tw-w-[100%] tw-mt-10"
        >
          <v-btn
            class="tw-normal-case tw-rounded-xl tw-position-absolute tw-font-semibold tw-text-sm tw-pt-2 tw-pb-2 primary tw-mr-[10px] tw-mt-5 tw-tracking-normal"
            style=""
            @click="updateJobStatus('accepted')"
          >
            Accept
          </v-btn>
          &nbsp;
          <v-btn
            class="tw-normal-case tw-rounded-xl tw-position-absolute tw-font-semibold tw-text-sm tw-pt-2 tw-pb-2 tw-text-purple-700 tw-mr-[10px] tw-mt-5 tw-tracking-normal"
            @click="updateJobStatus('rejected')"
          >
            Decline
          </v-btn>
        </div>
      </v-col>
    </v-row>

    <v-row class="tw-p-0 tw-m-0">
      <v-col class="tw-w-[100%] tw-text-right">
        <div class="actions-container">
          <v-btn
            v-if="
              userInfo.role_id != 4 &&
              item.job_status == 'completed' &&
              item.payment_status == 'pending'
            "
            class="tw-text-purple-700 tw-bg-white tw-shadow-none tw-h-auto tw-normal-case tw-p-1 tw-pl-3 tw-pr-3 tw-mt-5 md:tw-mt-0 tw-tracking-normal"
            @click="paymentCompleteDialog = true"
          >
            Pay Vendor
          </v-btn>

          <v-btn
            v-if="userInfo.role_id == 4 && item.job_status == 'accepted'"
            class="tw-text-purple-700 tw-bg-white tw-shadow-none tw-h-auto tw-normal-case tw-p-1 tw-pl-3 tw-pr-3 tw-mt-5 md:tw-mt-0 tw-tracking-normal"
            @click="updateJobStatus('completed')"
          >
            Mark Job Complete
          </v-btn>

          <v-btn
            class="tw-normal-case tw-bg-white tw-h-auto tw-text-[#6C6B6B] tw-shadow-none tw-p-0 tw-p-1 tw-pl-3 tw-pr-3 tw-mt-5 md:tw-mt-0 tw-tracking-normal"
            @click="sendMessageDialog = true"
          >
            Send Message
          </v-btn>
          <v-btn
            v-if="item.job_status == 'pending'"
            class="tw-normal-case tw-bg-white tw-h-auto tw-text-[#6C6B6B] tw-shadow-none tw-p-0 tw-p-1 tw-pl-3 tw-pr-3 tw-mt-5 md:tw-mt-0 tw-tracking-normal"
            @click="cancelJob"
          >
            Cancel Job
          </v-btn>
          <v-btn
            class="tw-text-[#6C6B6B] tw-bg-white tw-mt-5 tw-shadow-none tw-p-0 md:tw-mt-0 tw-tracking-normal"
            @click="toggleExpanded"
          >
            <v-icon class="tw-text-[18px]">mdi-arrow-expand</v-icon>
          </v-btn>
        </div>
      </v-col>
    </v-row>
  </div>
</template>

<style scoped>
.mycrew-item {
  border: 1px solid rgba(133, 133, 133, 0.2);
  box-shadow: 0px 2px 3px rgba(0, 0, 0, 0.25);
  border-radius: 10px;
  margin-bottom: 25px;
  position: relative;
}
.mycrew-item {
  padding: 10px;
}

.mycrew-item-status {
  box-shadow: 0px 2px 3px rgba(0, 0, 0, 0.25);
  background: #fff;
  position: absolute;
  top: 15px;
  left: 15px;
  font-weight: 700;
}

.home_image_container img {
  max-width: 150px;
  border-radius: 50px;
  width: 100px;
  height: 100px;
  object-fit: cover;
}

.actions-container {
  position: absolute;
  bottom: 10px;
  right: 10px;
}

.primary {
  background: conic-gradient(
    from 90deg at 50% 50%,
    rgba(56, 11, 115, 0.9) -124.46deg,
    #7c0cb1 73.63deg,
    rgba(56, 11, 115, 0.9) 235.54deg,
    #7c0cb1 433.63deg
  ) !important;
}

.custom-input {
  border-radius: 10px !important;
}

.custom-input >>> fieldset {
  border-color: #8585851a !important;
}

.error--text.custom-input >>> fieldset {
  border-color: #ff5252 !important;
}

.custom-input >>> .v-input__slot {
  min-height: 45px !important;
}

.custom-input >>> .v-label {
  top: 13px !important;
}

.custom-input >>> .v-label--active {
  top: 17px !important;
  color: #7c0cb1;
  font-weight: 600;
}

.custom-input >>> .v-input__append-inner {
  margin-top: 10px;
}

.custom-input >>> .v-select__selections {
  padding: 0px !important;
}

>>> .mycrew-upload-section .col-md-4 {
  flex: 0 0 100%;
  max-width: 95%;
}

>>> .mycrew-upload-section #drop-region {
  height: 250px;
}

@media screen and (max-width: 640px) {
  .actions-container {
    position: relative;
  }
}
</style>
