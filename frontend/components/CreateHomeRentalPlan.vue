<script lang="ts">
// @ts-nocheck
import { defineComponent } from '@nuxtjs/composition-api'

import RentalPlanV2 from '~/components/RentalPlanV2.vue'
import { useCreateHomeProgressStore } from '~/composables/useCreateHomeStore'
import { HOME_STATUS_RENTAL_PLAN_MAPPING, HOME_STATUSES, rentalPlans } from '~/constants'

export default defineComponent({
  name: 'CreateHomeRentalPlan',
  components: { RentalPlanV2 },

  setup() {
    const store = useCreateHomeProgressStore()

    const { createHomeData } = store

    return {
      createHomeData,
      rentalPlans,
      HOME_STATUSES,
      HOME_STATUS_RENTAL_PLAN_MAPPING,
    }
  },
})
</script>

<template>
  <v-row>
    <v-col cols="12" md="9" class="tw-mt-8 tw-flex tw-flex-col tw-gap-8">
      <div class="tw-text-2xl tw-font-semibold tw-text-zinc-600">
        Let’s Add Your Booking Settings
      </div>

      <div class="tw-flex tw-flex-col tw-gap-4">
        <div class="tw-text-lg tw-font-medium tw-text-zinc-500">Private Booking Settings</div>

        <div class="tw-text-zinc-500">
          All Twimo Homes have Private Booking automatically turned ON.
        </div>

        <RentalPlanV2
          :rental-plan="HOME_STATUS_RENTAL_PLAN_MAPPING[HOME_STATUSES.PRIVATE]"
          :is-activated="true"
        />
      </div>

      <div class="tw-flex tw-flex-col tw-gap-4">
        <div class="tw-text-lg tw-font-medium tw-text-zinc-500">Additional Booking Settings</div>

        <div class="tw-text-zinc-500">
          To turn on additional Booking Types, click on which types you would like turned ON. Click
          on one, none or all!
        </div>

        <RentalPlanV2
          :rental-plan="HOME_STATUS_RENTAL_PLAN_MAPPING[HOME_STATUSES.SWAP]"
          :is-activated="createHomeData.isSwap"
          switchable
          @switch="createHomeData.isSwap = $event"
        />

        <RentalPlanV2
          :rental-plan="HOME_STATUS_RENTAL_PLAN_MAPPING[HOME_STATUSES.RENT]"
          :is-activated="createHomeData.isBooking"
          switchable
          @switch="createHomeData.isBooking = $event"
        />

        <RentalPlanV2
          :rental-plan="HOME_STATUS_RENTAL_PLAN_MAPPING[HOME_STATUSES.SEASONAL_RENTAL]"
          :is-activated="createHomeData.offerAsSeasonalLease"
          switchable
          @switch="createHomeData.offerAsSeasonalLease = $event"
        />
      </div>
    </v-col>
  </v-row>
</template>

<style scoped></style>
