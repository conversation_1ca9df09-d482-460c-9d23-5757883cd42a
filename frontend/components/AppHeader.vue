<script lang="ts">
// @ts-nocheck
import { defineComponent, onMounted, ref, useContext } from '@nuxtjs/composition-api'

import AppWebHeader from '~/components/AppWebHeader.vue'
import AppMobileHeader from '~/components/AppMobileHeader.vue'
import { useScreenSize } from '~/composables/useScreenSize'

export default defineComponent({
  components: {
    AppWebHeader,
    AppMobileHeader,
  },

  setup() {
    const { isDesktop } = useScreenSize()
    const isClient = process.client
    const isMounted = ref(false)

    // Use a ref to control what's rendered during SSR
    const showMobileHeader = ref(false)
    const showWebHeader = ref(false)

    onMounted(() => {
      isMounted.value = true
      // Set the correct header to show after mounting
      showWebHeader.value = isDesktop.value
      showMobileHeader.value = !isDesktop.value
    })

    return {
      isDesktop,
      isClient,
      isMounted,
      showMobileHeader,
      showWebHeader
    }
  },
})
</script>

<template>
  <client-only>
    <AppWebHeader v-if="showWebHeader" />
    <AppMobileHeader v-else-if="showMobileHeader" />
    <!-- Fallback during SSR - will be replaced by client-side rendering -->
    <div v-else></div>
  </client-only>
</template>
