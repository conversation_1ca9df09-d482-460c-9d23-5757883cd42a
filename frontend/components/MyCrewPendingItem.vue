<script lang="ts">
// @ts-nocheck
import { defineComponent } from '@nuxtjs/composition-api'

export default defineComponent({
  name: 'MyCrewPendingItem',

  props: {
    item: {
      type: Object,
      required: true,
    },
  },

  data() {
    return {
      original_url: '',
    }
  },

  async mounted() {
    if (
      Object.values(this.item.home_featured_image).filter(value => typeof value === 'object')
        .length != 0
    ) {
      this.original_url = Object.values(this.item.home_featured_image)[0].original_url
    } else {
      this.original_url =
        'https://ik.imagekit.io/hweihwang/677/conversions/Screen-Shot-2021-05-29-at-6.08.49-AM-thumb.jpg?f=webp'
    }
  },

  methods: {
    formatDate(date) {
      if (!date) {
        return ''
      }
      const options = {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        timeZone: 'UTC',
      }
      return new Date(date).toLocaleDateString('en-US', options)
    },
  },
})
</script>

<template>
  <div class="tw-items-center mycrew-item md:tw-p-[2em]">
    <v-row>
      <v-col md="4" xs="12" sm="12" cols="12" class="tw-text-center">
        <div class="image_container">
          <img :src="original_url" />
        </div>
      </v-col>
      <v-col md="8" class="">
        <h2 class="tw-text-lg tw-font-semibold tw-text-gray-500">MyCrew Job Request</h2>
        <h3 class="tw-text-xl tw-font-semibold">Job Title: {{ item.job_title }}</h3>

        <div class="tw-mt-6 tw-text-gray-700">
          Requested By: {{ item.first_name + ' ' + item.last_name }}<br />
          Due Date: {{ formatDate(item.due_date) }}<br />
          Home: {{ item.home_title }}<br />
          Payment: ${{ item.payment_amount }} via
          {{ item.payment_type.charAt(0).toUpperCase() + item.payment_type.slice(1) }}
        </div>
      </v-col>
    </v-row>
  </div>
</template>

<style scoped>
.mycrew-item {
  border: 1px solid rgba(133, 133, 133, 0.2);
  box-shadow: 0px 2px 3px rgba(0, 0, 0, 0.25);
  border-radius: 10px;
  margin-bottom: 25px;
  position: relative;
  width: 40%;
  margin-right: 25px;
}
.mycrew-item {
  padding: 10px;
}

.mycrew-item img {
  max-width: 150px;
  border-radius: 50px;
  width: 100px;
  height: 100px;
  object-fit: cover;
}

.actions-container {
  position: absolute;
  bottom: 10px;
  right: 10px;
}

@media screen and (max-width: 640px) {
  .actions-container {
    position: relative;
  }
  .mycrew-item {
    width: 100%;
    padding: 20px;
  }
}
</style>
