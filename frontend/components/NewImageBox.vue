<script setup>
defineProps(['logo', 'heading', 'description', 'animate_delay', 'alt'])
</script>

<template>
  <div
    class="tw-p-0 tw-rounded-2xl tw-w-full sm:tw-max-w-xs animate tw-flex tw-flex-row"
    :class="`animate-${animate_delay}`"
  >
    <div class="tw-text-left tw-w-[20%]">
      <img
        :src="logo"
        class="tw-size-[3rem] md:tw-size-13 tw-rounded-full tw-inline md:tw-block"
        :style="{ boxShadow: '0 3px 4px #00000040' }"
        :alt="alt"
      />
    </div>
    <div class="tw-px-0 tw-pb-5 tw-pt-0 tw-w-[80%]">
      <h4 class="tw-text-[#360877] tw-text-[15px] tw-text-left tw-mb-2 tw-font-[Poppins] tw-font-semibold">
        {{ heading }}
      </h4>
      <p class="tw-text-[#360877] tw-text-[13px] tw-font-light tw-mb-0 tw-text-left  tw-font-[Poppins] tw-pr-[10px]">
        {{ description }}
      </p>
    </div>
  </div>
</template>
<style scoped>
.animate-1 {
  transition-delay: 0.1s !important;
}

.animate-2 {
  transition-delay: 0.3s !important;
}

.animate-3 {
  transition-delay: 0.5s !important;
}
</style>
