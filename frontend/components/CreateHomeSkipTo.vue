<script lang="ts">
// @ts-nocheck
import { defineComponent } from '@nuxtjs/composition-api'
import { storeToRefs } from 'pinia'

import GoodButtonReverted from '~/components/GoodButtonReverted.vue'
import GoodButton from '~/components/GoodButton.vue'
import BoldPurpleText from '~/components/BoldPurpleText.vue'
import BoldText from '~/components/BoldText.vue'
import { useCreateHomeProgressStore } from '~/composables/useCreateHomeStore'

export default defineComponent({
  name: 'CreateHomeSkipTo',

  components: {
    BoldText,
    BoldPurpleText,
    GoodButton,
    GoodButtonReverted,
  },

  setup() {
    const store = useCreateHomeProgressStore()

    const { createHomeData, next, setCurrentStep } = store

    const { CREATE_HOME_STEPS } = storeToRefs(store)

    return {
      createHomeData,
      next,
      CREATE_HOME_STEPS,
      setCurrentStep,
    }
  },
})
</script>

<template>
  <v-row>
    <v-col cols="12" class="tw-mt-8 tw-flex tw-flex-col tw-gap-4">
      <div class="tw-text-2xl tw-font-semibold tw-text-zinc-600">Next Steps</div>

      <div class="tw-text-lg tw-font-medium tw-text-zinc-500">
        Your Home Status is
        <BoldPurpleText>Private</BoldPurpleText>
        and you turned
        <BoldPurpleText>
          {{ createHomeData.isSwap ? 'ON' : 'OFF' }}
        </BoldPurpleText>
        Twimo’s Home Swap
      </div>

      <div class="tw-text-zinc-500">
        To continue building your home’s profile, there are a few additional details to fill out
        -address, images, guest count, etc.
      </div>

      <div class="tw-text-zinc-500">
        As part of our Home Swap network, we recommend filling out these details so other Twimo Host
        can see basic profile info when searching for Swap Matches!
      </div>

      <div class="tw-text-zinc-500">
        If you don’t feel these are applicable for your needs, click
        <BoldText text="Move to Pricing" />
      </div>

      <div class="tw-text-zinc-500">
        To finish your Home Details, click
        <BoldText text="Fill out Details, or Next" />
      </div>
    </v-col>

    <div class="tw-w-full sm:tw-w-4/5 tw-flex sm:tw-flex-row tw-gap-4 tw-mt-4 tw-mb-4 tw-flex-col">
      <template v-if="!createHomeData.isSwap">
        <GoodButton
          class="sm:tw-w-1/2 tw-w-full sm:tw-text-xl tw-text-lg sm:tw-py-8 tw-py-6 tw-justify-start"
          @click="setCurrentStep(CREATE_HOME_STEPS.PRICING)"
        >
          Move to Pricing
        </GoodButton>
        <GoodButtonReverted
          class="sm:tw-w-1/2 tw-w-full sm:tw-text-xl tw-text-lg sm:tw-py-8 tw-py-6 tw-justify-start"
          @click="next"
        >
          Fill out Details
        </GoodButtonReverted>
      </template>

      <template v-else>
        <GoodButtonReverted
          class="sm:tw-w-1/2 tw-w-full sm:tw-text-xl tw-text-lg sm:tw-py-8 tw-py-6 tw-justify-start"
          @click="setCurrentStep(CREATE_HOME_STEPS.PRICING)"
        >
          Move to Pricing
        </GoodButtonReverted>

        <GoodButton
          class="sm:tw-w-1/2 tw-w-full sm:tw-text-xl tw-text-lg sm:tw-py-8 tw-py-6 tw-justify-start"
          @click="next"
        >
          Fill out Details
        </GoodButton>
      </template>
    </div>

    <v-col cols="12" class="tw-text-zinc-500">
      You can always decline Swap proposals, or change these Settings in the future
    </v-col>
  </v-row>
</template>

<style scoped></style>
