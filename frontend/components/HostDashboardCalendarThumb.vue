<script lang="ts">
// @ts-nocheck
import { defineComponent } from '@nuxtjs/composition-api'

export default defineComponent({
  name: 'HostDashboardCalendarThumb',

  props: {
    title: {
      type: String,
      default: '',
    },
    description: {
      type: [String, Number],
      default: '',
    },
    button_text: {
      type: String,
      default: '',
    },
    bold_text: {
      type: String,
      default: '',
    },
    link: {
      type: String,
      default: '',
    },
  },
})
</script>

<template>
  <div
    class="indiv_item tw-max-w-[300px] tw-min-h-[180px] tw-pt-5 tw-pb-1 tw-pl-5 tw-pr-2 tw-relative tw-cursor-pointer hover:tw-bg-[#F1F1F1] tw-text-[#6C6C6C] hover:tw-text-[#000] tw-transition-all"
    @click="$router.push(link)"
  >
    <div class="tw-text-xl tw-font-semibold tw-text-left">
      {{ title }}
    </div>
    <div class="tw-text-base tw-font-light tw-mt-3 tw-pr-3">
      {{ description }}
    </div>
    <div
      class="tw-text-sm tw-font-light tw-mt-8 tw-text-right tw-absolute tw-bottom-[5px] tw-right-[5px]"
    >
      <strong class="tw-font-bold">{{ bold_text }}</strong> {{ button_text }}
      <v-icon color="#6C6C6C" size="20" class="tw-mr-2"> mdi-arrow-right </v-icon>
    </div>
  </div>
</template>

<style scoped>
.indiv_item {
  background: #fff;
  box-shadow:
    0px 2px 3px rgba(0, 0, 0, 0.25),
    0px 4px 4px rgba(0, 0, 0, 0.25);
  border-radius: 15px;
}
</style>
