<script lang="ts">
// @ts-nocheck
import { defineComponent } from '@nuxtjs/composition-api'

export default defineComponent({
  name: 'HostBookingMetricItem',

  props: {
    title: {
      type: String,
      default: '',
    },
    value: {
      type: [String, Number],
      default: '',
    },
    description: {
      type: String,
      default: '',
    },
  },
})
</script>

<template>
  <div
    class="tw-w-[293px] md:tw-w-[24%] tw-min-w-[293px] tw-h-[320px] tw-bg-zinc-300 tw-bg-opacity-30 tw-rounded-[54px] tw-px-[28px] tw-py-[63px]"
    style="box-shadow: 0 4px 4px 0 #00000040"
  >
    <div class="tw-text-zinc-500 tw-text-2xl tw-font-semibold">
      {{ title }}
    </div>
    <div class="tw-text-zinc-500 tw-text-6xl tw-font-bold tw-mt-8">
      {{ value }}
    </div>
    <div class="tw-text-zinc-500 tw-text-xl tw-font-semibold tw-mt-8">
      {{ description }}
    </div>
  </div>
</template>

<style scoped></style>
