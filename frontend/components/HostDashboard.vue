<script lang="ts">
// @ts-nocheck
import { defineComponent } from '@nuxtjs/composition-api'

import HostDashboardCalendar from '~/components/HostDashboardCalendar.vue'
import WhatsNew from '~/components/WhatsNew.vue'
import GoodButton from '~/components/GoodButton.vue'
import HostActivationTracker from '~/components/HostActivationTracker.vue'
import HostActivationModal from '~/components/HostActivationModal.vue'
import screenCheckerMixins from '~/mixins/screenChecker.mixins'
import { HostDashboardData } from '~/types'
import { useHostActivationStore } from '~/composables/useHostActivationStore'

import Resources from './Resources.vue'

export default defineComponent({
  name: 'HostDashboard',

  components: {
    GoodButton,
    WhatsNew,
    Resources,
    HostDashboardCalendar,
    HostActivationTracker,
    HostActivationModal,
  },

  mixins: [screenCheckerMixins],

  props: {
    hostDashBoardData: {
      type: Object as () => HostDashboardData,
      default: () => ({}),
    },
    hostData: {
      type: Object,
      default: () => ({}),
    },
  },

  data() {
    return {
      localDashboardData: this.hostDashBoardData,
    }
  },

  computed: {
    userInfo(): User {
      return this.$store.getters['auth/getuser']
    },
    showActivationTracker() {
      return this.localDashboardData?.extraInfo?.registration_progress !== 3
    },
    profileProgress() {
      const hostActivationStore = useHostActivationStore()
      return hostActivationStore.activationProgress
    },
  },

  watch: {

  },

  mounted() {
    this.getDashboardData()

    // Initialize the host activation store
    const hostActivationStore = useHostActivationStore()
    hostActivationStore.checkActivationStatus(this.$axios)
  },

  methods: {
    async getDashboardData() {
      const { data } = await this.$axios.get('/dashboard/host')
      this.localDashboardData = data
      this.localDashboardData.profileProgress = this.profileProgress
    },
  },
})
</script>

<template>
  <v-col cols="12" class="tw-flex tw-flex-col tw-justify-center">
    <div
      class="tw-bg-center tw-bg-no-repeat tw-bg-cover tw-overflow-hidden tw-absolute tw-w-[100%] tw-left-0 tw-top-[1rem] md:tw-top-[5rem]"
      :style="{
        backgroundImage: `url(${require('~/assets/dashboard-bg.jpg')})`,
        backgroundSize: 'cover',
      }"
    >
      <div style="background-color: #00000012" class="md:tw-px-[55px] tw-px-4">
        <div class="tw-pt-[80px] tw-text-3xl tw-text-left tw-text-white">
          Welcome {{ userInfo.first_name }}!
        </div>

        <div
          v-if="hostDashBoardData.extraInfo?.registration_progress !== 3"
          class="tw-mt-[5px] tw-pb-[50px] md:tw-text-xl tw-font-normal tw-text-left tw-text-white md:tw-max-w-[90%] tw-flex tw-flex-col tw-gap-4"
        >
          <div>
            <!--<div class="tw-mb-[20px]">
							We are here to customize your Twimo experience, and want to<br> begin by
							learning more about <strong>you</strong>!
						</div class="tw-mb-[20px]">
						<div>Finish your New User Tour and Registration to get started!</div>-->
            <div class="tw-mb-[15px] tw-font-thin">
              Welcome to Twimo! To get started let's add your home
            </div>
          </div>
          <GoodButton
            class="tw-w-fit tw-mt-[10px] tw-rounded-full"
            @click="$router.push('/create')"
          >
            Add Your Home
          </GoodButton>
        </div>

        <div
          v-else
          class="tw-mt-[37px] tw-text-2xl tw-font-normal tw-text-left tw-text-[#6C6C6C] md:tw-max-w-[90%]"
        >
          If you're here to list, swap or travel we have the tools to help you manage your listings,
          while keeping track of your goals
        </div>
      </div>
    </div>
    <div class="tw-h-[20rem] md:tw-h-[18rem]"></div>

    <!-- Host Activation Tracker -->
    <div v-if="showActivationTracker" class="md:tw-px-[55px] tw-px-4 tw-mb-8">
      <HostActivationTracker @navigate="$router.push($event)" />
    </div>

    <WhatsNew v-if="localDashboardData != null" :host-data="localDashboardData" :profile-progress="profileProgress" />

    <!-- Bookings on the Calendar  -->
    <HostDashboardCalendar v-if="localDashboardData != null" :host-data="localDashboardData" />

    <Resources />

    <!-- Host Activation Modal -->
    <HostActivationModal />
  </v-col>
</template>

<style scoped></style>
