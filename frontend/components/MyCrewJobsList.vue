<script lang="ts">
// @ts-nocheck
import { co } from '@fullcalendar/core/internal-common'
import { defineComponent } from '@nuxtjs/composition-api'

import MyCrewJobItem from '~/components/MyCrewJobItem.vue'

export default defineComponent({
  name: 'MyCrewJobsList',
  components: {
    MyCrewJobItem,
  },

  props: {
    type: {
      type: String,
      default: '',
    },
    filter: {
      type: String,
      default: '',
    },
  },

  data() {
    return {
      tab: 0,
      currentJobs: [],
      completedJobs: [],
      cancelledJobs: [],
    }
  },

  watch: {
    filter: {
      immediate: true,
      handler(newValue) {
        this.getJobs()
      },
    },
  },

  async mounted() {
    this.getJobs()
  },

  methods: {
    async getJobs() {
      // Get Jobs
      try {
        const { data } = await this.$axios.get('mycrew/get-jobs/?filter=' + this.filter)
        this.currentJobs = data.jobs.filter(
          job =>
            job.job_status === 'accepted' ||
            job.job_status === 'pending' ||
            (job.job_status === 'completed' && job.payment_status == 'pending')
        )
        this.completedJobs = data.jobs.filter(
          job => job.job_status === 'completed' && job.payment_status != 'pending'
        )
        this.cancelledJobs = data.jobs.filter(job => job.job_status === 'rejected' || job.job_status === 'cancelled')
      } catch (e) {
        console.log(e)
      }
    },
  },
})
</script>

<template>
  <v-row>
    <!-- Tabs Title -->
    <v-col cols="12" class="tw-mb-4">
      <v-tabs v-model="tab" center-active show-arrows>
        <v-tab class="tw-normal-case sm:tw-text-lg"> Current Jobs </v-tab>
        <v-tab class="tw-normal-case sm:tw-text-lg"> Completed Jobs </v-tab>
        <v-tab class="tw-normal-case sm:tw-text-lg"> Cancelled Jobs </v-tab>
      </v-tabs>
    </v-col>

    <!-- MyCrew Tabs -->
    <v-col cols="12">
      <v-tabs-items v-model="tab">
        <!--  MyCrew list item -->
        <v-tab-item class="tw-p-[1em]">
          <MyCrewJobItem
            v-for="(item, index) in currentJobs"
            :key="index"
            :item="item"
            @call-get-jobs="getJobs"
          />
          <div v-if="currentJobs.length == 0" class="tw-p-3">No current jobs found.</div>
        </v-tab-item>
        <v-tab-item>
          <MyCrewJobItem
            v-for="(item, index) in completedJobs"
            :key="index"
            :item="item"
            @call-get-jobs="getJobs"
          />
          <div v-if="completedJobs.length == 0" class="tw-p-3">No completed jobs found.</div>
        </v-tab-item>
        <v-tab-item>
          <MyCrewJobItem
            v-for="(item, index) in cancelledJobs"
            :key="index"
            :item="item"
            @call-get-jobs="getJobs"
          />
          <div v-if="cancelledJobs.length == 0" class="tw-p-3">No cancelled jobs found.</div>
        </v-tab-item>
      </v-tabs-items>
    </v-col>
  </v-row>
</template>

<style scoped>
.mycrew-item {
  border: 1px solid rgba(133, 133, 133, 0.2);
  box-shadow: 0px 2px 3px rgba(0, 0, 0, 0.25);
  border-radius: 10px;
}
.mycrew-item {
  padding: 10px;
}

.mycrew-item img {
  max-width: 150px;
  border-radius: 50px;
  width: 100px;
  height: 100px;
}
</style>
