<template>
  <v-dialog v-model="show" max-width="600px" @click:outside="closeWelcome">
    <GoodCard2 class="tw-flex tw-flex-col tw-gap-4 tw-p-6">
      <div class="tw-text-3xl tw-font-semibold tw-text-primary tw-mb-4">
        Welcome to Twimo Legacy User!
      </div>

      <div class="tw-mb-4 tw-flex tw-flex-col tw-gap-4 tw-text-lg tw-text-zinc-500">
        <div>
          As a valued member of the Twimo community you have access to a FREE Twimo account for one
          year!
        </div>
        <div>
          Your home has already been transferred from 48dots, and you will be able to see your home
          in the Homes section.
        </div>
        <div>
          In the Homes section you can edit details, adjust pricing, setup your calendar and Share
          Your Private Home Link!
        </div>
      </div>

      <GoodButton class="tw-w-fit tw-mx-auto" @click="goToHomes"> My Homes </GoodButton>
    </GoodCard2>
  </v-dialog>
</template>

<script>
import { mapState } from 'vuex'

import GoodCard2 from '~/components/GoodCard2.vue'
import GoodButton from '~/components/GoodButton.vue'
import { cookieOptions } from '~/constants'

export default {
  name: 'LegacyUserWelcome',

  components: {
    GoodCard2,
    GoodButton,
  },

  data() {
    return {
      show: false,
    }
  },

  computed: {
    ...mapState('auth', ['user']),
  },

  watch: {
    user: {
      immediate: true,
      handler(user) {
        if (user) {
          // Check if the user has seen the welcome message using cookies instead of localStorage
          const hasSeenWelcome = this.$cookies.get('twimo_legacy_welcome_seen')
          // Also check if we've already shown the welcome message in this session
          const hasShownWelcomeThisSession = sessionStorage.getItem('twimo_legacy_welcome_shown_this_session')

          if (new Date(user.created_at) < new Date('2025-01-31') && !hasSeenWelcome && !hasShownWelcomeThisSession) {
            this.show = true
            // Mark that we've shown the welcome message in this session
            sessionStorage.setItem('twimo_legacy_welcome_shown_this_session', 'true')
          }
        }
      },
    },
  },

  methods: {
    goToHomes() {
      this.show = false

      // Set a cookie with a long expiration (5 years) to ensure it persists
      this.setWelcomeCookie()

      // Also clear the session storage flag
      sessionStorage.removeItem('twimo_legacy_welcome_shown_this_session')

      // Navigate to the homes page
      this.$router.push('/homes')
    },

    closeWelcome() {
      this.show = false

      // Set a cookie to remember that the user has seen the welcome message
      this.setWelcomeCookie()

      // Also clear the session storage flag
      sessionStorage.removeItem('twimo_legacy_welcome_shown_this_session')
    },

    setWelcomeCookie() {
      const cookieOpts = {
        ...cookieOptions,
        maxAge: 60 * 60 * 24 * 365 * 5, // 5 years in seconds
        path: '/',
      }
      this.$cookies.set('twimo_legacy_welcome_seen', 'true', cookieOpts)
    },
  },
}
</script>
