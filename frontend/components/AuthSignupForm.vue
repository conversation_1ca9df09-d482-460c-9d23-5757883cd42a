<script lang="ts">
// @ts-nocheck

import {
  computed,
  defineComponent,
  onMounted,
  reactive,
  toRefs,
  useContext,
} from '@nuxtjs/composition-api'

import { useAuthStore } from '~/composables/useAuthStore'
import GoodButton from '~/components/GoodButton.vue'
import PasswordRequirements from '~/components/PasswordRequirements.vue'

export default defineComponent({
  name: 'AuthSignupForm',

  components: { GoodButton, PasswordRequirements },

  props: {
    dense: {
      type: Boolean,
      default: false,
    },
    showLoginLink: {
      type: Boolean,
      default: true,
    },
    customClass: {
      type: String,
      default: '',
    },
    userType: {
      type: String,
      default: 'traveler',
    },
    darkMode: {
      type: Boolean,
      default: true,
    },
    isSignupPage: {
      type: Boolean,
      default: false,
    },
  },

  setup(props, { emit }) {
    const { app } = useContext()
    const authStore = useAuthStore()

    const state = reactive({
      formRef: null as any,
      firstName: '',
      lastName: '',
      email: '',
      password: '',
      countryCode: '+1', // Fixed to US country code
      phoneNumber: '',
      userType: props.userType,
      optedInToEmail: false,
      optedInToSms: false,
      valid: true,
      errors: [] as string[],
      formRules: {
        firstName: [v => !!v || 'First Name is required'],
        lastName: [v => !!v || 'Last Name is required'],
        countryCode: [
          v => !!v || 'Country code is required',
          v => v.startsWith('+') || 'Country code must start with +',
          v => /^\+[0-9]{1,4}$/.test(v) || 'Invalid country code format',
        ],
        phone: [
          v => !!v || 'Phone number is required',
          v => v.replace(/\D/g, '').length === 10 || 'Phone number must be exactly 10 digits',
          v => /^[0-9]+$/.test(v.replace(/\D/g, '')) || 'Phone number should contain only digits',
        ],
        email: [v => !!v || 'Email is required', v => /.+@.+\..+/.test(v) || 'Email must be valid'],
        password: [
          v => !!v || 'Password is required',
          v => v.length >= 8 || 'Password must be at least 8 characters',
          v => v.length <= 24 || 'Password must not exceed 24 characters',
        ],
        optInEmail: [v => v || 'You must agree to receive emails'],
        optInSms: [v => v || 'You must agree to receive SMS'],
      },
    })

    onMounted(async () => {
      // Ensure that the reCAPTCHA client is fully initialized
      try {
        await app.$recaptcha.init()
      } catch (error) {
        console.error('reCAPTCHA initialization error:', error)
      }

      // Prefill email field if email is available in the URL query parameters
      if (process.client) {
        const emailFromQuery = app.router?.currentRoute.query.email
        if (emailFromQuery && typeof emailFromQuery === 'string') {
          state.email = emailFromQuery
        }
      }
    })

    const formatPhoneNumber = () => {
      // Clean the phone number to contain only digits
      const phone = state.phoneNumber.replace(/\D/g, '')
      // Limit to exactly 10 digits
      state.phoneNumber = phone.slice(0, 10)
    }

    const formatCountryCode = () => {
      // Always set to US country code (+1)
      state.countryCode = '+1'
    }

    const executeRecaptcha = async () => {
      try {
        // Try to execute reCAPTCHA
        const token = await app.$recaptcha.execute('register')
        if (!token) {
          throw new Error('No reCAPTCHA token received')
        }
        return token
      } catch (error) {
        console.error('reCAPTCHA execution error:', error)
      }
    }

    const handleSignup = async () => {
      if (!state.formRef?.validate()) return

      try {
        let token: string | undefined

        if (app.$recaptcha) {
          token = await executeRecaptcha()
        }

        // Check if we're in a booking flow and store the path
        if (process.client) {
          const currentPath = app.router?.currentRoute.fullPath
          if (currentPath && currentPath.includes('/bookings/')) {
            console.log('AuthSignupForm: Detected booking flow, storing path:', currentPath)
            localStorage.setItem('redirectAfterVerification', currentPath)
            // Also store in auth store
            authStore.setRedirectPath(currentPath)
          }
        }

        const result = await authStore.signup({
          first_name: state.firstName,
          last_name: state.lastName,
          email: state.email,
          phone_number: state.countryCode + state.phoneNumber.replace(/\D/g, ''),
          password: state.password,
          user_type: state.userType,
          opted_in_to_email: state.optedInToEmail,
          opted_in_to_sms: state.optedInToSms,
          recaptcha_response: token,
        })

        if (result.success) {
          emit('success', result.data)
        } else {
          state.errors = [
            result.error?.response?.data?.message || 'Registration failed. Please try again.',
          ]
        }
      } catch (error) {
        console.error('Signup error:', error)
        state.errors = ['An unexpected error occurred']
      }
    }

    const canSubmit = computed(() => {
      return (
        state.valid &&
        state.optedInToEmail &&
        state.optedInToSms &&
        !authStore.isSubmitting &&
        state.firstName &&
        state.lastName &&
        state.email &&
        state.password &&
        state.countryCode === '+1' && state.phoneNumber.replace(/\D/g, '').length === 10
      )
    })

    return {
      ...toRefs(state),
      isSubmitting: computed(() => authStore.isSubmitting),
      formatPhoneNumber,
      formatCountryCode,
      handleSignup,
      canSubmit,
    }
  },
})
</script>

<template>
  <v-form
    ref="formRef"
    v-model="valid"
    lazy-validation
    :class="customClass"
    class="tw-flex tw-flex-col tw-gap-3"
  >
    <v-text-field
      v-model="firstName"
      :rules="formRules.firstName"
      outlined
      hide-details
      label="First Name"
      class="tw-bg-white tw-rounded-xl tw-shadow-md"
      :dense="dense"
    />

    <v-text-field
      v-model="lastName"
      :rules="formRules.lastName"
      outlined
      label="Last Name"
      hide-details
      class="tw-bg-white tw-rounded-xl tw-shadow-md"
      :dense="dense"
    />

    <div class="tw-flex tw-gap-2">
      <v-text-field
        v-model="countryCode"
        :rules="formRules.countryCode"
        outlined
        label="Country Code"
        placeholder="+1"
        hide-details
        disabled
        class="tw-bg-white tw-rounded-xl tw-shadow-md country-code-field"
        :dense="dense"
      />
      <v-text-field
        v-model="phoneNumber"
        :rules="formRules.phone"
        outlined
        label="Phone Number"
        placeholder="1234567890"
        hide-details
        class="tw-bg-white tw-rounded-xl tw-shadow-md"
        :dense="dense"
        @input="formatPhoneNumber"
      />
    </div>

    <v-text-field
      v-model="email"
      :rules="formRules.email"
      outlined
      label="Email"
      hide-details
      class="tw-bg-white tw-rounded-xl tw-shadow-md"
      :dense="dense"
    />

    <div class="tw-w-full">
      <v-text-field
        v-model="password"
        :rules="formRules.password"
        outlined
        hide-details
        type="password"
        label="Password"
        class="tw-bg-white tw-rounded-xl tw-shadow-md"
        :dense="dense"
      />

      <!-- Password Requirements Component -->
      <PasswordRequirements
        :password="password"
        :dark-mode="darkMode"
        class="tw-mt-2"
      />
    </div>

    <div :class="`tw-text-${darkMode ? 'white' : 'black'} tw-mt-3`">
      <h3 :class="`tw-font-medium tw-text-${darkMode ? 'white' : 'black'} tw-text-base`">
        Sign up for SMS:
      </h3>
      <v-checkbox
        v-model="optedInToSms"
        :rules="formRules.optInSms"
        :class="[`tw-text-${darkMode ? 'white' : 'black'}`, { 'white-checkbox': isSignupPage }, 'checkbox-fix']"
        hide-details="auto"
        required
        :color="isSignupPage ? 'white' : 'primary'"
      >
        <template #label>
          <div :class="`tw-text-${darkMode ? 'white' : 'black'} tw-text-xs`">
            I agree to receive notification text messages from Twimo. By subscribing, I consent to
            receive automated and non-automated messages, and updates. Message and data rates may
            apply. Reply STOP to unsubscribe anytime. Message frequency may vary. Standard Message
            and Data Rates may apply. Reply STOP to opt out. Reply Help for help. Your mobile
            information will not be sold or shared with third parties for promotional or marketing
            purposes (<a
              href="/privacy-policy"
              target="_blank"
              :class="`tw-text-${darkMode ? 'white' : 'black'} tw-underline hover:tw-text-gray-200`"
              @click.stop
              >Privacy Policy</a
            >
            &
            <a
              href="/terms-and-service"
              target="_blank"
              :class="`tw-text-${darkMode ? 'white' : 'black'} tw-underline hover:tw-text-gray-200`"
              @click.stop
              >Terms of Service</a
            >
            apply.)
          </div>
        </template>
      </v-checkbox>
    </div>

    <div :class="`tw-text-${darkMode ? 'white' : 'black'} tw-mt-3`">
      <h3 :class="`tw-font-medium tw-text-${darkMode ? 'white' : 'black'} tw-text-base`">
        Sign up for Email:
      </h3>
      <v-checkbox
        v-model="optedInToEmail"
        :rules="formRules.optInEmail"
        :class="[`tw-text-${darkMode ? 'white' : 'black'}`, { 'white-checkbox': isSignupPage }, 'checkbox-fix']"
        hide-details="auto"
        required
        :color="isSignupPage ? 'white' : 'primary'"
      >
        <template #label>
          <div :class="`tw-text-${darkMode ? 'white' : 'black'} tw-text-xs`">
            I consent to receive marketing emails from Twimo about new features, promotions, and
            company updates. I acknowledge that my information will be handled according to the
            Privacy Policy and that I can unsubscribe at any time. (<a
              href="/privacy-policy"
              target="_blank"
              :class="`tw-text-${darkMode ? 'white' : 'black'} tw-underline hover:tw-text-gray-200`"
              @click.stop
              >Privacy Policy</a
            >
            &
            <a
              href="/terms-and-service"
              target="_blank"
              :class="`tw-text-${darkMode ? 'white' : 'black'} tw-underline hover:tw-text-gray-200`"
              @click.stop
              >Terms of Service</a
            >
            apply.)
          </div>
        </template>
      </v-checkbox>
    </div>

    <span
      v-for="(error, index) in errors"
      :key="index"
      class="tw-text-red-500 tw-text-center tw-mx-auto tw-font-semibold"
      >{{ error }}</span
    >

    <div class="tw-flex tw-flex-col tw-justify-center tw-items-center tw-gap-3 tw-mt-3">
      <good-button
        class="tw-text-xl sm:tw-w-1/2 tw-w-full signUpButton"
        :disabled="!canSubmit"
        :loading="isSubmitting"
        @click.prevent="handleSignup"
      >
        {{ isSubmitting ? 'Signing up...' : 'Sign up' }}
      </good-button>
    </div>

    <div v-if="showLoginLink" :class="`tw-text-${darkMode ? 'white' : 'black'} tw-text-center tw-text-lg`">
      Already have an account?
      <a :class="`tw-font-semibold tw-text-${darkMode ? 'white' : 'primary'} tw-underline`" @click="$router.push('/login')"
        >Log in</a
      >
    </div>
  </v-form>
</template>

<style scoped>
.tw-text-xs {
  line-height: 1.3;
}

:deep(.v-input--selection-controls__input) {
  margin-right: 6px;
}

:deep(.v-input--selection-controls) {
  margin-top: 0;
  padding-top: 0;
}

:deep(.v-messages) {
  min-height: 12px;
}

:deep(.white--text .v-icon) {
  color: white !important;
}

:deep(.v-input--selection-controls__ripple) {
  display: none;
}

/* For signup page */
:deep(.v-input--is-label-active .v-icon) {
  color: white !important;
}

:deep(.v-input--is-dirty .v-icon) {
  color: white !important;
}

.white-checkbox :deep(.v-icon) {
  color: white !important;
}

.white-checkbox :deep(.v-input--selection-controls__ripple:before) {
  background-color: white !important;
}

.white-checkbox :deep(.v-input--selection-controls__input .mdi-checkbox-marked) {
  color: white !important;
}

.white-checkbox :deep(.v-input--selection-controls__input .mdi-checkbox-blank-outline) {
  color: white !important;
}

:deep(.v-text-field) {
  margin-bottom: 8px;
}

.country-code-field {
  max-width: 120px;
  min-width: 100px;
}

/* Fix for checkboxes in both dark and light modes */
.checkbox-fix :deep(.v-input--selection-controls__input) {
  opacity: 1 !important;
  visibility: visible !important;
  display: flex !important;
  z-index: 1;
}

.checkbox-fix :deep(.v-input--selection-controls__input .v-icon) {
  opacity: 1 !important;
  visibility: visible !important;
  display: inline-flex !important;
}

.checkbox-fix.v-input--is-label-active :deep(.v-input--selection-controls__input) {
  opacity: 1 !important;
  visibility: visible !important;
}

.checkbox-fix.v-input--is-dirty :deep(.v-input--selection-controls__input) {
  opacity: 1 !important;
  visibility: visible !important;
}

.checkbox-fix :deep(.mdi-checkbox-marked) {
  opacity: 1 !important;
  visibility: visible !important;
}

.checkbox-fix :deep(.mdi-checkbox-blank-outline) {
  opacity: 1 !important;
  visibility: visible !important;
}

/* Ensure checkbox label remains visible */
.checkbox-fix :deep(.v-label) {
  opacity: 1 !important;
  visibility: visible !important;
  position: relative;
  z-index: 0;
}

/* Fix for light mode */
.tw-text-black .checkbox-fix :deep(.v-icon) {
  color: #672093 !important; /* Primary color */
}

.tw-text-black .checkbox-fix.v-input--is-dirty :deep(.v-icon) {
  color: #672093 !important;
}

/* Ensure validation messages are visible */
.checkbox-fix :deep(.v-messages) {
  min-height: 12px !important;
  visibility: visible !important;
  opacity: 1 !important;
  display: block !important;
  color: #ff5252 !important; /* Error color */
}

.checkbox-fix :deep(.error--text) {
  color: #ff5252 !important;
}

.checkbox-fix :deep(.v-messages__message) {
  line-height: 12px;
  font-size: 12px;
  color: #ff5252 !important;
  visibility: visible !important;
  opacity: 1 !important;
  margin-top: 4px;
  margin-bottom: 4px;
  display: block;
}

/* Ensure error state is visible */
.checkbox-fix.error--text :deep(.v-icon) {
  color: #ff5252 !important;
}

.checkbox-fix.error--text :deep(.v-label) {
  color: #ff5252 !important;
}
</style>
