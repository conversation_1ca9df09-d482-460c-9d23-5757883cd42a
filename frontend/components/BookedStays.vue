<script lang="ts">
// @ts-nocheck
import { defineComponent } from '@nuxtjs/composition-api'

import GoodButton from '~/components/GoodButton.vue'

export default defineComponent({
  name: 'BookedStays',

  components: {
    GoodButton,
  },

  props: {},

  data() {
    return {
      houses: null,
      selected: null,
      pending_booking: 0,
      family_booking: 0,
      public_booking: 0,
    }
  },

  watch: {
    hostDashboardData(newValue) {},
  },

  mounted() {
    // const { data } = await this.$axios.get('/dashboard/host')
    // this.bookingData = data;
    this.getDashboardData()
  },

  methods: {
    async getDashboardData() {
      const { data } = await this.$axios.get('home/user/list/my?status=active')
      this.houses = data
    },

    async setSelected(index) {
      this.selected = index

      const { data } = await this.$axios.get('home/user/list/bookings/' + this.houses[index].id)
      const bookingInfo = data
      this.pending_booking = bookingInfo.pending_booking
      this.family_booking = bookingInfo.family_booking
      this.public_booking = bookingInfo.public_booking
    },
  },
})
</script>

<template>
  <!--  BOOKED STAYS -->
  <v-row>
    <v-col cols="12" class="tw-mt-2 tw-mb-[0.5rem] tw-ml-0 tw-flex tw-gap-5 tw-overflow-x-auto">
      <div
        v-for="(house, index) in houses"
        :key="index"
        class="tw-bg-[#F7F7F7] tw-cursor-pointer hover:tw-bg-[#E4E4E4] tw-rounded-xl individual-home tw-relative tw-overflow-hidden tw-shadow-custom tw-min-w-[460px] tw-max-w-[460px]"
        :class="
          selected === index || selected == null ? 'selected_property' : 'unselected_property'
        "
        @click="setSelected(index)"
      >
        <v-row class="booked_homes">
          <v-col
            cols="7"
            lg="7"
            md="7"
            xl="7"
            class="tw-mb-5 tw-p-5 tw-pl-7 tw-flex tw-flex-wrap tw-justify-center tw-items-center"
          >
            <div class="tw-w-[100%]">
              <h4 class="tw-text-gray-700 tw-text-[1rem] tw-mt-1 tw-mb-1">
                {{ house.title }}
              </h4>
              <h5 class="tw-text-gray-500 tw-text-[0.8rem] tw-mb-4">
                {{
                  (house.city_long == null ? '' : house.city_long + ', ') +
                  (house.state_long == null ? '' : house.state_long)
                }}
              </h5>

              <v-row class="">
                <v-col class="tw-flex tw-max-w-[100px]" col="3">
                  <div class="text--gray tw-text-xs tw-mr-1">{{ house.beds }} Beds</div>
                  <img src="../assets/icon-bed.svg" alt="Beds" style="max-height: 20px" />
                </v-col>
                <v-col class="tw-flex">
                  <div class="text--gray tw-text-xs tw-mr-1">{{ house.baths }} Bathrooms</div>
                  <img src="../assets/icon-bathroom.svg" alt="Bathrooms" style="max-height: 15px" />
                </v-col>
              </v-row>
            </div>
          </v-col>
          <v-col cols="5" class="tw-p-0">
            <img
              v-if="house.photos.length > 0"
              :src="house.photos[0].src"
              alt=""
              class="tw-h-[190px] tw-w-[100%] tw-object-cover"
            />
            <v-btn
              class="tw-normal-case tw-rounded-base tw-absolute tw-font-semibold tw-text-xs tw-pt-1 tw-pb-1 tw-bottom-2 tw-right-2 primary tw-h-8 tw-pl-1 tw-pr-1"
              @click="$router.push('/' + house.slug + '/edit')"
            >
              Share
            </v-btn>
          </v-col>
        </v-row>
      </div>
    </v-col>

    <div
      v-if="selected != null"
      class="tw-flex tw-flex-wrap tw-gap-[20px] tw-pt-2 tw-pb-5 tw-pl-3 tw-mt-[1rem]"
    >
      <HostDashboardCalendarThumb
        v-if="pending_booking == 0"
        title="Pending Bookings"
        description="You have no pending bookings. Want to Share Your Home? "
        button_text="to homes"
        bold_text="go"
        :link="'/' + houses[selected].slug + '/edit'"
      />

      <HostDashboardCalendarThumb
        v-if="pending_booking != 0"
        title="Pending Bookings"
        :description="
          'You have ' + pending_booking + ' pending booking! Please approve or deny the request'
        "
        button_text="to bookings"
        bold_text="go"
        :link="'/bookings'"
      />

      <HostDashboardCalendarThumb
        v-if="family_booking == 0"
        title="Friends & Family Bookings"
        description="Currently, you have no bookings scheduled. Want to Share Your Home? "
        button_text="private link"
        bold_text="share"
        :link="'/' + houses[selected].slug + '/edit'"
      />

      <HostDashboardCalendarThumb
        v-if="family_booking != 0"
        title="Friends & Family Bookings"
        :description="
          'Currently, you have ' + family_booking + ' Friends & Family bookings scheduled!'
        "
        button_text="to bookings"
        bold_text="go"
        :link="'/bookings'"
      />

      <HostDashboardCalendarThumb
        v-if="public_booking == 0"
        title="Public Bookings"
        description="Currently, you have no bookings scheduled. Want to Share Your Home? "
        button_text="public link"
        bold_text="share"
        :link="'/' + houses[selected].slug + '/edit'"
      />

      <HostDashboardCalendarThumb
        v-if="public_booking != 0"
        title="Public Bookings"
        :description="'Currently, you have ' + public_booking + ' Public Bookings scheduled!'"
        button_text="to bookings"
        bold_text="go"
        :link="'/bookings'"
      />
    </div>

    <v-col col="12" class="tw-mb-12 tw-pl-3 tw-mt-2 col-12">
      <GoodButton class="tw-w-fit tw-mt-[10px] tw-rounded-full" @click="$router.push('/bookings')">
        go to bookings
      </GoodButton>
    </v-col>
  </v-row>
</template>

<style scoped>
.unselected_property {
  opacity: 0.4;
}
</style>
