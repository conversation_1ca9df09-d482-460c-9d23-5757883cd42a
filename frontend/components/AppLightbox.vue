<template>
  <div v-if="showLightbox">
    <div id="lightbox" class="pa-2">
      <v-row>
        <v-col cols="12" class="d-flex justify-end">
          <v-icon
            large
            color="white"
            @click="
              showLightbox = false
              showImage = false
            "
          >
            mdi-close
          </v-icon>
        </v-col>

        <v-row>
          <v-col
            cols="12"
            class="d-flex align-center flex-column justify-center px-2"
            :style="{ height: $vuetify.breakpoint.smAndDown ? '90vh' : '85vh' }"
          >
            <!-- WILL SHOW ONLY ON MOBILE -->
            <div v-if="isMobile" class="d-flex justify-end mb-3 w-full">
              <div>
                <v-icon
                  v-if="showLeft"
                  color="white"
                  :size="isMobile ? 50 : 60"
                  :class="[isMobile ? '' : 'mr-4']"
                  @click="goLeft"
                >
                  mdi-chevron-left
                </v-icon>
              </div>
              <v-icon
                v-if="showRight"
                color="white"
                :size="isMobile ? 50 : 60"
                :class="[isMobile ? '' : 'ml-4']"
                @click="goRight"
              >
                mdi-chevron-right
              </v-icon>
            </div>

            <div class="d-flex align-center">
              <div v-if="!isMobile">
                <v-icon
                  v-if="showLeft"
                  color="white"
                  :size="isMobile ? 20 : 60"
                  :class="[isMobile ? '' : 'mr-4']"
                  @click="goLeft"
                >
                  mdi-chevron-left
                </v-icon>
              </div>
              <img
                v-if="showImage"
                :src="url"
                :width="$vuetify.breakpoint.smAndDown ? '360' : '800'"
                :height="$vuetify.breakpoint.smAndDown ? '300' : '600'"
                class="rounded"
                style="object-fit: contain"
              />
              <div v-if="!isMobile">
                <v-icon
                  v-if="showRight"
                  color="white"
                  :size="isMobile ? 20 : 60"
                  :class="[isMobile ? '' : 'ml-4']"
                  @click="goRight"
                >
                  mdi-chevron-right
                </v-icon>
              </div>
            </div>
          </v-col>
        </v-row>
      </v-row>
    </div>
  </div>
</template>

<script>
export default {
  data: () => ({
    showLightbox: false,
    photos: [],
    url: '',
    showImage: false,
    isMobile: false,
    currentIndex: '',
    showLeft: true,
    showRight: true,
  }),
  watch: {
    currentIndex: function (val) {
      if (val === 0) {
        this.showLeft = false
      } else {
        this.showLeft = true
      }

      if (val === this.photos.length - 1) {
        this.showRight = false
      } else {
        this.showRight = true
      }
    },
  },
  methods: {
    toggleLightBox(photos, index, isMobile = false) {
      this.photos = photos
      this.url = photos[index].src
      this.currentIndex = index
      this.showLightbox = true
      this.showImage = true
      this.isMobile = isMobile
    },
    goLeft() {
      this.currentIndex--
      this.url = this.photos[this.currentIndex].src
    },
    goRight() {
      this.currentIndex++
      this.url = this.photos[this.currentIndex].src
    },
  },
}
</script>

<style lang="scss" scoped>
#lightbox {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 1);
  z-index: 10;
}
</style>
