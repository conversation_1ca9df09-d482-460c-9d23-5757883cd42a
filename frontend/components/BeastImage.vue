<script lang="ts">
// @ts-nocheck
import { defineComponent, ref, computed } from '@nuxtjs/composition-api'

export default defineComponent({
  name: 'BeastImage',

  props: {
    src: {
      type: null,
      required: true,
    },

    lazySrc: {
      type: String,
      default: require('~/assets/home.webp'),
    },

    alt: {
      type: String,
      default: 'Beautiful home',
    },

    title: {
      type: String,
      default: 'Beautiful home',
    },

    isAvatar: {
      type: Boolean,
      default: false,
    },

    avatarSize: {
      type: [Number, String],
      default: 40,
    },
  },

  setup(props) {
    const imgSrc = ref(props.src || props.lazySrc)

    const onError = () => {
      imgSrc.value = props.lazySrc
    }

    const avatarStyle = computed(() => {
      if (!props.isAvatar) return {}

      const size = typeof props.avatarSize === 'number'
        ? `${props.avatarSize}px`
        : props.avatarSize

      return {
        width: size,
        height: size,
        objectFit: 'cover',
        borderRadius: '50%',
        overflow: 'hidden',
      }
    })

    return {
      imgSrc,
      onError,
      avatarStyle,
    }
  },
})
</script>

<template>
  <div v-if="isAvatar" :style="avatarStyle" class="tw-overflow-hidden">
    <v-img
      :src="imgSrc"
      :lazy-src="lazySrc"
      :alt="alt"
      :title="title"
      loading="lazy"
      height="100%"
      width="100%"
      cover
      @error="onError"
    >
      <template #placeholder>
        <v-row class="fill-height ma-0" align="center" justify="center">
          <v-progress-circular indeterminate color="primary" />
        </v-row>
      </template>
    </v-img>
  </div>
  <v-img
    v-else
    :src="imgSrc"
    :lazy-src="lazySrc"
    :alt="alt"
    :title="title"
    loading="lazy"
    @error="onError"
  >
    <template #placeholder>
      <v-row class="fill-height ma-0" align="center" justify="center">
        <v-progress-circular indeterminate color="primary" />
      </v-row>
    </template>
  </v-img>
</template>

<style scoped></style>
