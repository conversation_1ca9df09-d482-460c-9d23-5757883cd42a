<script lang="ts">
// @ts-nocheck

import { defineComponent, ref, computed, watch } from '@nuxtjs/composition-api'

import GoodCard2 from '~/components/GoodCard2.vue'
import { getDateRange } from '~/helpers'

export default defineComponent({
  name: 'AppDateRangePicker',

  components: {
    GoodCard2,
  },

  props: {
    parentClasses: {
      type: String,
      default: '',
    },
    opens: {
      type: String,
      default: 'left',
    },
    bigButton: {
      type: Boolean,
      default: false,
    },
    startDateRed: {
      type: Boolean,
      default: false,
    },
    endDateRed: {
      type: Boolean,
      default: false,
    },
    propDates: {
      type: Object,
      default: () => ({}),
    },
    width: {
      type: String,
      default: '40%',
    },
    blockedDates: {
      type: Array,
      default: () => [],
    },
    datesHashMap: {
      type: Object,
      default: () => ({}),
    },
    showDefaultSlot: {
      type: Boolean,
      default: true,
    },
    showClearDates: {
      type: Boolean,
      default: false,
    },
    buttonColor: {
      type: String,
      default: 'black',
    },
    isIframe: {
      type: Boolean,
      default: false,
    },
    dateIcon: {
      type: String,
      default: null,
    },
    fixedToBottom: {
      type: Boolean,
      default: false,
    },
    menuAttach: {
      type: Boolean,
      default: false,
    },
  },

  setup(props, { emit }) {
    // State
    const showPicker = ref(false)
    const dates = ref([])
    const isFinalSelected = ref(false)
    const isStartSelected = ref(false)
    const houseId = ref(null)
    const locationText = ref('Anytime')

    // Computed
    const formatDate = (dateStr) => {
      // Parse the date in a timezone-safe way
      // The dateStr is in YYYY-MM-DD format from the v-date-picker
      const [year, month, day] = dateStr.split('-').map(Number)

      // Create a date using UTC to avoid timezone shifts
      // This ensures dates are interpreted exactly as provided without timezone adjustments
      const date = new Date(Date.UTC(year, month - 1, day))

      // Format the date using UTC to ensure consistent display regardless of user's timezone
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric',
        timeZone: 'UTC' // Use UTC to ensure consistent display
      })
    }

    const formattedStartDate = computed(() => (dates.value[0] ? formatDate(dates.value[0]) : ''))
    const formattedEndDate = computed(() => (dates.value[1] ? formatDate(dates.value[1]) : ''))

    const dateButtonText = computed(() => {
      if (!dates.value.length) return locationText.value
      return isFinalSelected.value && dates.value.length === 2
        ? `${formattedStartDate.value} to ${formattedEndDate.value}`
        : locationText.value
    })

    const allowedDates = computed(() => {
      const blockedDatesSet = new Set(props.blockedDates.map(el => el.date))

      // Get today's date in UTC to ensure consistent comparison
      const now = new Date()
      const today = new Date(Date.UTC(
        now.getFullYear(),
        now.getMonth(),
        now.getDate()
      ))

      return (date) => {
        // The date parameter is already in YYYY-MM-DD format
        // We need to ensure consistent timezone handling
        const [year, month, day] = date.split('-').map(Number)

        // Create a date at midnight UTC for consistent comparison
        const dateObj = new Date(Date.UTC(year, month - 1, day))

        // Format for comparison with blocked dates
        const formatted = dateObj.toISOString().slice(0, 10)

        return !blockedDatesSet.has(formatted) && dateObj >= today
      }
    })

    const menuConfig = computed(() => ({
      closeOnContentClick: false,
      transition: 'scale-transition',
      offsetY: true,
      minWidth: 'auto',
      attach: props.menuAttach,
      returnValue: true,
      closeOnClick: true,
      retainFocus: false,
      contentClass: 'date-picker-menu-content',
    }))

    const datePickerConfig = computed(() => ({
      range: true,
      noTitle: true,
      scrollable: true,
      showAdjacentMonths: true,
      firstDayOfWeek: 1,
      color: 'primary',
      elevation: 0,
      events: [],
      eventColor: null,
      width: 380,
    }))

    const getDiffsInDays = computed(() => {
      if (dates.value.length !== 2) return 0

      // Parse dates in a timezone-safe way using UTC
      const parseDate = (dateStr) => {
        const [year, month, day] = dateStr.split('-').map(Number)
        // Create date at midnight UTC to ensure consistent calculation
        return new Date(Date.UTC(year, month - 1, day))
      }

      const [start, end] = dates.value.map(parseDate)

      // If same day, count as 1 night
      if (start.getTime() === end.getTime()) return 1

      // Calculate difference in days using UTC dates to avoid timezone issues
      return Math.round(Math.abs(end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24))
    })

    // Methods
    const resetSelection = () => {
      dates.value = []
      isStartSelected.value = false
      isFinalSelected.value = false
    }

    const emitDateSelection = (finalSelection = false) => {
      const [startDate, endDate] = [dates.value[0] || null, dates.value[1] || null]

      // Ensure we're passing the dates in ISO format (YYYY-MM-DD)
      // This ensures consistent handling regardless of timezone
      emit('selected-dates', {
        startDate,
        endDate,
        startDateFull: startDate,
        endDateFull: endDate,
        isFinalSelected: finalSelection || isFinalSelected.value,
        isStartSelected: isStartSelected.value,
        houseId: houseId.value,
      })
    }

    const handleFinishSelection = () => {
      if (!dates.value.length) return
      isStartSelected.value = true
      isFinalSelected.value = true
      emitDateSelection()
    }

    const isRangeBlocked = (selectedDates) => {
      // Use our timezone-safe date range function
      // The selectedDates are already in YYYY-MM-DD format from v-date-picker
      return getDateRange(selectedDates[0], selectedDates[1]).some(date => props.datesHashMap[date])
    }

    const handleSingleDateSelection = (selectedDates) => {
      dates.value = selectedDates
      isStartSelected.value = true
      emitDateSelection()
    }

    const closePicker = () => {
      showPicker.value = false

      if (dates.value.length === 2) {
        emitDateSelection(true)
      }
    }

    const handleRangeSelection = (selectedDates) => {
      if (isRangeBlocked(selectedDates)) {
        resetSelection()
        emit('show-booked-prompt')
        return
      }

      dates.value = selectedDates
      handleFinishSelection()
      emitDateSelection(true)
      closePicker()
    }

    const handleDateChange = (selectedDates) => {
      if (!selectedDates?.length) return

      if (selectedDates.length === 1) {
        handleSingleDateSelection(selectedDates)
      } else {
        handleRangeSelection(selectedDates)
      }
    }

    const clearDates = () => {
      if (isFinalSelected.value && isStartSelected.value) {
        resetSelection()
        emit('handle-date-clear')
      }
    }

    const setData = (data) => {
      if (!data?.[0]?.startDate || !data?.[0]?.endDate) return
      dates.value = [data[0].startDate, data[0].endDate]
      handleFinishSelection()
    }

    // Watch
    watch(
      () => props.propDates,
      newDates => {
        if (newDates?.startDate && newDates?.endDate) {
          setData([newDates])
        }
        if (newDates?.id) {
          houseId.value = newDates.id
        }
      },
      { immediate: true }
    )

    return {
      showPicker,
      dates,
      isFinalSelected,
      isStartSelected,
      houseId,
      locationText,
      formattedStartDate,
      formattedEndDate,
      dateButtonText,
      allowedDates,
      menuConfig,
      datePickerConfig,
      getDiffsInDays,
      handleDateChange,
      clearDates,
      setData,
      closePicker,
    }
  },
})
</script>

<template>
  <div :class="['d-flex justify-space-between tw-z-50', parentClasses]">
    <GoodCard2
      v-if="showDefaultSlot"
      class="tw-flex tw-justify-around tw-m-1 tw-w-full tw-p-6 tw-rounded-xl tw-bg-white/80 tw-backdrop-blur-sm"
    >
      <div class="tw-w-full">
        <div class="tw-w-full tw-flex tw-flex-col tw-gap-4">
          <div class="tw-flex tw-justify-between tw-items-center">
            <div class="tw-flex tw-flex-col tw-gap-1">
              <div class="tw-text-zinc-800 tw-font-bold tw-text-base tw-tracking-wide tw-uppercase">
                Check-in • Check-out
              </div>
              <div class="tw-text-zinc-500 tw-text-sm tw-font-medium">Add your travel dates</div>
            </div>
          </div>

          <v-menu
            v-model="showPicker"
            :close-on-content-click="false"
            :close-on-click="true"
            :retain-focus="false"
            transition="scale-transition"
            offset-y
            min-width="auto"
            :attach="menuAttach"
          >
            <template #activator="{ on, attrs }">
              <div
                class="tw-flex tw-items-center tw-gap-3 tw-text-zinc-700 tw-text-lg tw-cursor-pointer tw-p-4 tw-rounded-xl hover:tw-bg-gray-50/80 tw-transition-all tw-border tw-border-gray-200"
                v-bind="attrs"
                v-on="on"
              >
                <v-icon color="primary" size="20">mdi-calendar</v-icon>
                <div class="tw-flex tw-flex-col tw-gap-1">
                  <span
                    :class="[
                      'tw-text-base tw-font-medium',
                      dates.length ? 'tw-text-zinc-800' : 'tw-text-zinc-400',
                    ]"
                  >
                    {{ dateButtonText }}
                  </span>
                  <span v-if="dates.length === 2" class="tw-text-sm tw-text-zinc-500">
                    {{ getDiffsInDays }} night{{ getDiffsInDays > 1 ? 's' : '' }}
                  </span>
                </div>
              </div>
            </template>
            <v-date-picker
              v-model="dates"
              v-bind="datePickerConfig"
              :allowed-dates="allowedDates"
              class="date-picker-custom"
              @input="handleDateChange"
              @change="closePicker"
            />
          </v-menu>
        </div>
      </div>
    </GoodCard2>

    <v-menu v-else v-model="showPicker" v-bind="menuConfig">
      <template #activator="{ on, attrs }">
        <v-btn
          v-bind="attrs"
          :color="buttonColor"
          block
          :font-size="isIframe ? '27px' : $vuetify.breakpoint.smAndDown ? '16px' : '20px'"
          outlined
          lowercased
          text
          class="tw-font-bold tw-tracking-tight tw-normal-case tw-h-full tw-border tw-border-gray-200 tw-overflow-hidden tw-rounded-xl tw-transition-all hover:tw-bg-gray-50/80"
          style="justify-content: left"
          v-on="on"
        >
          <div class="tw-flex tw-flex-row tw-justify-start tw-items-center tw-gap-3 tw-p-2">
            <v-icon v-if="dateIcon" color="primary" size="20">
              {{ dateIcon }}
            </v-icon>
            <v-icon v-else color="primary" size="20">mdi-calendar</v-icon>
            <div class="tw-flex tw-flex-col tw-items-start tw-gap-1">
              <span
                :class="[
                  'tw-text-base tw-font-medium',
                  isStartSelected && isFinalSelected ? 'tw-text-zinc-800' : 'tw-text-zinc-400',
                ]"
              >
                {{
                  isStartSelected && isFinalSelected
                    ? isIframe
                      ? 'Given dates...'
                      : dateButtonText
                    : locationText
                }}
              </span>
              <span v-if="isStartSelected && isFinalSelected" class="tw-text-sm tw-text-zinc-500">
                {{ getDiffsInDays }} night{{ getDiffsInDays > 1 ? 's' : '' }}
              </span>
            </div>
          </div>
        </v-btn>
      </template>
      <v-date-picker
        v-model="dates"
        v-bind="datePickerConfig"
        :allowed-dates="allowedDates"
        class="date-picker-custom"
        @input="handleDateChange"
        @change="closePicker"
      />
    </v-menu>

    <template v-if="showClearDates">
      <div class="tw-absolute tw-right-2 tw-top-2">
        <v-btn
          x-small
          text
          class="tw-text-zinc-500 hover:tw-bg-gray-50/80 tw-transition-all tw-rounded-lg"
          @click="clearDates"
        >
          <v-icon size="16" class="tw-mr-1">mdi-close</v-icon>
          Clear
        </v-btn>
      </div>
    </template>
  </div>
</template>

<style scoped>
.v-menu__content {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  border-radius: 16px;
  overflow: hidden;
  border: 1px solid #e5e7eb;
  z-index: 1000 !important;
}

.date-picker-menu-content {
  z-index: 10000 !important;
  position: relative !important;
}

.date-picker-custom {
  border-radius: 16px;
  overflow: hidden;
  background: white;
  position: relative;
  z-index: 1001 !important;
}

.date-picker-custom .v-date-picker-header {
  padding: 20px 24px;
  background: white;
  border-bottom: 1px solid #f3f4f6;
}

.date-picker-custom .v-date-picker-header button {
  color: var(--v-primary-base) !important;
  width: 32px;
  height: 32px;
  border-radius: 8px;
}

.date-picker-custom .v-date-picker-header button:hover {
  background: #f3f4f6 !important;
}

.date-picker-custom .v-date-picker-header__value {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1f2937;
}

.date-picker-custom .v-date-picker-table {
  height: auto;
  padding: 16px;
}

.date-picker-custom .v-btn {
  height: 40px;
  width: 40px;
  font-size: 0.95rem;
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.date-picker-custom .v-btn--active {
  background-color: var(--v-primary-base) !important;
  color: white !important;
  transform: scale(1.05);
}

.date-picker-custom .v-btn--outlined {
  border: 2px solid var(--v-primary-base) !important;
  color: var(--v-primary-base) !important;
}

.date-picker-custom .v-date-picker-table__events {
  display: none;
}

.date-picker-custom .v-btn--disabled {
  opacity: 0.4 !important;
  background-color: #f3f4f6 !important;
  color: #9ca3af !important;
  text-decoration: line-through;
}

.date-picker-custom
  .v-date-picker-table
  button.v-btn:not(.v-btn--disabled):not(.v-btn--active):hover {
  background-color: #f3f4f6 !important;
  color: var(--v-primary-base) !important;
  transform: scale(1.05);
}

.v-picker--date {
  width: 380px;
  background: white;
}

/* Range selection styles */
.date-picker-custom .v-btn--active + .v-btn:not(.v-btn--active):not(.v-btn--disabled),
.date-picker-custom .v-btn--active:not(:last-child) {
  background-color: var(--v-primary-base) !important;
  opacity: 0.1;
}

/* Hover effects */
.v-btn:not(.v-btn--disabled):hover {
  opacity: 0.9;
}

/* Transitions */
.v-menu-transition-enter-active,
.v-menu-transition-leave-active {
  transition:
    opacity 0.3s ease,
    transform 0.3s ease;
}

.v-menu-transition-enter,
.v-menu-transition-leave-to {
  opacity: 0;
  transform: translateY(-12px);
}

/* Calendar day names */
.date-picker-custom .v-date-picker-table th {
  font-size: 0.85rem;
  font-weight: 600;
  color: #6b7280;
  padding: 8px 0;
}

/* Today's date */
.date-picker-custom .v-btn--active.v-btn--outlined {
  background-color: var(--v-primary-base) !important;
  color: white !important;
  border: none !important;
}

.date-picker-menu {
  display: block !important;
  top: 100% !important;
  left: 0 !important;
  transform-origin: top left !important;
  max-width: none !important;
}

/* Add these new styles */
.v-menu__content {
  max-height: none !important;
}

.v-picker.v-card {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12) !important;
  border: 1px solid #e5e7eb !important;
}

/* Ensure the date picker is always visible */
.v-picker--date.v-card {
  display: block !important;
  opacity: 1 !important;
  z-index: 1002 !important;
}

/* Mobile specific styles */
@media (max-width: 600px) {
  .v-menu__content,
  .date-picker-menu-content,
  .date-picker-custom,
  .v-picker--date.v-card {
    z-index: 10000 !important;
  }
}
</style>
