<script lang="ts">
// @ts-nocheck

import { defineComponent, PropType } from '@nuxtjs/composition-api'

import { HomeTo } from '~/types'

export default defineComponent({
  name: 'HomeFirstBasicInfo',

  props: {
    data: {
      type: Object as PropType<HomeTo>,
      required: true,
    },
    card: {
      type: Boolean,
      default: true,
    },
  },
})
</script>

<template>
  <div
    :class="
      card
        ? 'tw-flex tw-flex-row tw-items-center tw-w-fit'
        : 'tw-flex tw-flex-col tw-w-fit tw-items-center'
    "
  >
    <div :class="card ? 'tw-mr-2' : ''">
      <v-tooltip bottom color="primary">
        <template #activator="{ on, attrs }">
          <div v-bind="attrs" v-on="on">
            <div class="tw-flex tw-flex-row info-text">
              <div :class="card ? 'text--gray tw-text-sm tw-mr-1' : 'tw-text-base tw-mr-2'">
                {{ data.beds }} Beds
              </div>
              <img src="../assets/icon-bed.svg" alt="Beds" style="max-height: 20px" />
            </div>
          </div>
        </template>
        <span class="tw-text-xs">Number of beds</span>
      </v-tooltip>
    </div>

    <div :class="card ? 'tw-mx-2' : 'tw-mt-4'">
      <v-tooltip bottom color="primary">
        <template #activator="{ on, attrs }">
          <div v-bind="attrs" v-on="on">
            <div class="tw-flex tw-text-[1rem] tw-flex-row info-text">
              <div :class="card ? 'text--gray tw-text-sm tw-mr-1' : 'tw-text-base tw-mr-2'">
                {{ Math.floor(data.baths) }} Bathrooms
              </div>
              <img src="../assets/icon-bathroom.svg" alt="Bathrooms" style="max-height: 15px" />
            </div>
          </div>
        </template>
        <span class="tw-text-xs">Number of baths</span>
      </v-tooltip>
    </div>
  </div>
</template>
<style scoped>
.info-text {
  color: #858585;
}

.info-text > div {
  font-size: 0.9rem !important;
  /* font-family: "Proxima Nova"; */
  font-weight: 400;
  color: #858585;
}
</style>
