<script lang="ts">
// @ts-nocheck
import { defineComponent } from '@nuxtjs/composition-api'

import WhatsNewItem from '~/components/WhatsNewItem.vue'

export default defineComponent({
  name: 'WhatsNew',

  components: {
    WhatsNewItem,
  },

  props: {
    hostData: {
      type: Object,
      default: null,
    },
    profileProgress: {
      type: Number,
      default: 0,
    },
  },

  mounted() {
    this.getWhatsNewData()
  },

  methods: {
    getWhatsNewData() {},
  },
})
</script>

<template>
  <div
    v-if="
      hostData.totalPendingBookings != 0 || hostData.totalListings == 0 || hostData.totalVendors == 0 || hostData.totalCalendarEvents == 0 || (hostData.hasDraftHome > 0 && hostData.hasActiveHome == 0) || profileProgress < 100
    "
    class="md:tw-px-[55px] tw-py-[40px] tw-px-4 tw-bg-center tw-bg-no-repeat tw-bg-cover tw-overflow-hidden"
  >
    <div class="tw-text-3xl tw-mt-5 tw-text-left tw-text-[#5E5E5E]">What's New</div>

    <div
      class="tw-mt-[10px] tw-text-xl tw-font-normal tw-text-left tw-text-[#5E5E5E] md:tw-max-w-[90%] tw-flex tw-flex-col tw-gap-4"
    >
      <div>
        <div>The below notifications need your attention</div>
      </div>
    </div>

    <div
      class="tw-gap-[20px] tw-block md:tw-flex tw-overflow-auto tw-pt-5 tw-pl-[0.5rem] tw-pr-[0.5rem] tw-pb-5"
    >
    <WhatsNewItem
        v-if="hostData.totalPendingBookings != 0"
        title="Pending Bookings"
        :description="'You have ' + hostData.totalPendingBookings + ' pending bookings! Please approve or deny the request'"
        button_text="<strong class='tw-font-bold'>go to</strong> bookings"
        link="/bookings"
      />
      <WhatsNewItem
        v-if="hostData.totalListings == 0"
        title="Add Your Home"
        description="You need to Add your home to begin sharing or renting!"
        button_text="<strong class='tw-font-bold'>add</strong> home"
        link="/create"
      />
      <WhatsNewItem
        v-if="hostData.hasDraftHome > 0 && hostData.hasActiveHome == 0"
        title="Finish Your Home"
        description="Looks like your home is still a Draft. Finish your home so you can begin sharing"
        button_text="<strong class='tw-font-bold'>go to</strong> homes"
        link="/create"
      />
      <WhatsNewItem
        v-if="profileProgress < 100"
        title="Finish Profile"
        description="Finish your profile and verify your identity to list your home"
        button_text="<strong class='tw-font-bold'>go to</strong> profile"
        link="/profile"
      />
      <WhatsNewItem
        v-if="hostData.totalVendors == 0"
        title="Add Your Vendors"
        description="Add your first vendor to ease the back and forth and make payments directly in Twimo"
        button_text="<strong class='tw-font-bold'>go</strong> to mycrew"
        link="/my-crews"
      />
      <WhatsNewItem
        v-if="hostData.totalCalendarEvents == 0"
        title="Add Your Calendar Dates"
        description="You can add your personal calendar by simply syncing your iCal, Gmail, etc."
        button_text="<strong class='tw-font-bold'>add</strong> to calendar"
        link="/my-calendar"
      />
    </div>
  </div>
</template>
