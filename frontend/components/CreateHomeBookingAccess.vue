<script lang="ts">
// @ts-nocheck
import { defineComponent } from '@nuxtjs/composition-api'
import { storeToRefs } from 'pinia'

import GoodButtonReverted from '~/components/GoodButtonReverted.vue'
import GoodButton from '~/components/GoodButton.vue'
import BoldPurpleText from '~/components/BoldPurpleText.vue'
import { rentalPlans } from '~/constants'
import { useCreateHomeProgressStore } from '~/composables/useCreateHomeStore'

export default defineComponent({
  name: 'CreateHomeBookingAccess',

  components: { BoldPurpleText, GoodButton, GoodButtonReverted },

  setup() {
    const store = useCreateHomeProgressStore()

    const { createHomeData, validateCreateHomeData, clickOnRentalPlan } = store

    const { selectedRentalPlansString } = storeToRefs(store)

    return {
      createHomeData,
      validateCreateHomeData,
      rentalPlans,
      clickOnRentalPlan,
      selectedRentalPlansString,
    }
  },
})
</script>

<template>
  <v-row>
    <v-col cols="12" class="tw-mt-8 tw-flex tw-flex-col tw-gap-4">
      <div class="tw-text-2xl tw-font-semibold tw-text-zinc-600">Booking Access</div>

      <div class="tw-text-lg tw-font-medium tw-text-zinc-500">
        You selected {{ selectedRentalPlansString || 'nothing' }}.
        {{
          !createHomeData.isPrivate
            ? 'This indicates you would like for guests to be able to Book your Home.'
            : 'This indicates you would like to keep your home Private.'
        }}
      </div>

      <div v-if="!createHomeData.isPrivate" class="tw-text-zinc-500">
        In order to do so, your Booking access will need to be turned
        <BoldPurpleText>On</BoldPurpleText>
        . To do so, please turn on below:
      </div>

      <div v-else class="tw-text-zinc-500">
        When your home is Private, your Booking Access is turned
        <BoldPurpleText>Off</BoldPurpleText>
        . To invite guests to book your home, they can only access via private booking link. You
        will be able to access this direct booking link once your home profile is complete, and
        share directly with your guests if you choose!
      </div>
    </v-col>

    <v-col cols="12" md="8">
      <GoodButton
        v-if="createHomeData.isBooking"
        class="tw-w-full tw-text-2xl tw-py-8 tw-justify-start"
        @click="createHomeData.isBooking = !createHomeData.isBooking"
      >
        Yes, Turn On
      </GoodButton>

      <GoodButtonReverted
        v-else
        class="tw-w-full tw-text-2xl tw-py-8 tw-justify-start"
        @click="createHomeData.isBooking = !createHomeData.isBooking"
      >
        Yes, Turn On
      </GoodButtonReverted>
    </v-col>

    <v-col cols="12" md="8">
      <GoodButton
        v-if="!createHomeData.isBooking"
        class="tw-w-full tw-text-2xl tw-py-8 tw-justify-start"
        @click="createHomeData.isBooking = !createHomeData.isBooking"
      >
        No, not right now
      </GoodButton>

      <GoodButtonReverted
        v-else
        class="tw-w-full tw-text-2xl tw-py-8 tw-justify-start"
        @click="createHomeData.isBooking = !createHomeData.isBooking"
      >
        No, not right now
      </GoodButtonReverted>
    </v-col>

    <v-col cols="12" class="tw-text-zinc-500">
      You will have to approve or decline Booking request as they come in. If you would ever like to
      edit the status of this in the future, you can do so in the Homes section
    </v-col>
  </v-row>
</template>

<style scoped></style>
