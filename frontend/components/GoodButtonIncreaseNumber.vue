<script lang="ts">
// @ts-nocheck
import { defineComponent } from '@nuxtjs/composition-api'

export default defineComponent({
  name: 'GoodButtonIncreaseNumber',

  props: {
    value: {
      type: Number,
      required: true,
    },
  },
})
</script>

<template>
  <v-btn
    class="tw-py-6 tw-normal-case tw-tracking-normal tw-rounded-xl tw-text-primary tw-cursor-default"
    plain
    retain-focus-on-click
    outlined
  >
    <div class="tw-flex tw-flex-row tw-items-center tw-justify-around tw-w-full tw-h-full">
      <img
        :src="require('~/assets/CreateHomeDecrease.png')"
        alt="decrease"
        class="tw-h-8 tw-cursor-pointer"
        @click="$emit('decrease')"
      />
      <span> {{ value }} </span>
      <img
        :src="require('~/assets/CreateHomeIncrease.png')"
        alt="increase"
        class="tw-h-8 tw-cursor-pointer"
        @click="$emit('increase')"
      />
    </div>
    <slot></slot>
  </v-btn>
</template>

<style scoped></style>
