<template>
  <v-dialog
    :value="isOpen"
    max-width="900"
    content-class="paywall-dialog"
    :retain-focus="false"
    @input="isOpen = $event"
    @click:outside="closeModal"
  >
    <v-card class="tw-p-8 tw-grid md:tw-grid-cols-2 tw-gap-8 tw-max-h-[90vh] tw-overflow-y-auto">
      <div>
        <!-- Header Section -->
        <div class="tw-text-3xl tw-font-bold tw-text-primary mb-2">Payment Required</div>
        <div class="tw-text-zinc-500 tw-text-lg mb-8">
          In order to share your home publicly within our Twimo network you must upgrade your
          Twimo subscription and add your preferred payment method. Please Select Your Payment
          Option
        </div>

        <!-- Payment Options -->
        <div class="tw-flex tw-flex-col tw-gap-6 mb-8">
          <div v-for="plan in store.SUBSCRIPTION_PLANS" :key="plan.id">
            <GoodButton
              v-if="selectedPlan === plan.id"
              class="tw-w-full tw-h-fit tw-py-1"
              tabindex="0"
              @click.stop="handlePlanSelect(plan.id)"
            >
              <span class="tw-text-lg tw-flex tw-flex-col tw-items-center">
                <span>{{ plan.description }}</span>
                <strong> ${{ plan.price }}/{{ plan.interval }} </strong>
              </span>
            </GoodButton>
            <GoodButtonReverted
              v-else
              class="tw-w-full tw-h-fit tw-py-1"
              tabindex="0"
              @click.stop="handlePlanSelect(plan.id)"
            >
              <span class="tw-text-lg tw-flex tw-flex-col tw-items-center">
                <span>
                  {{ plan.description }}
                </span>
                <strong> ${{ plan.price }}/{{ plan.interval }} </strong>
              </span>
            </GoodButtonReverted>
          </div>
        </div>

        <!-- Promo Code Section -->
        <div v-if="selectedPlan" class="tw-mb-6 tw-mt-4 tw-p-4 tw-border tw-border-gray-200 tw-rounded-lg tw-bg-gray-50">
          <div class="tw-flex tw-flex-col tw-gap-3">
            <label for="promo-code" class="tw-text-base tw-font-medium tw-text-gray-700">
              Have a promo code?
            </label>
            <div class="tw-flex tw-gap-2">
              <input
                id="promo-code"
                v-model="promoCode"
                type="text"
                placeholder="Enter your code"
                class="tw-flex-grow tw-px-4 tw-py-2 tw-border tw-border-gray-300 tw-rounded-md tw-shadow-sm focus:tw-outline-none focus:tw-ring-primary focus:tw-border-primary"
                :disabled="loading"
              />
              <GoodButton
                :loading="validatingPromoCode"
                :disabled="!promoCode || loading"
                class="tw-whitespace-nowrap tw-bg-primary tw-text-white"
                @click.stop="validatePromo"
              >
                Apply
              </GoodButton>
            </div>

            <!-- Promo code validation message -->
            <div v-if="store.promoCodeValidation" class="tw-mt-1">
              <div
                v-if="store.promoCodeValidation.valid"
                class="tw-text-green-600 tw-text-sm tw-flex tw-items-center tw-bg-green-50 tw-p-2 tw-rounded"
              >
                <v-icon color="green" small class="mr-2">mdi-check-circle</v-icon>
                <div class="tw-flex tw-flex-col tw-gap-1">
                  <span>
                    <span class="tw-font-semibold">Discount applied:</span>
                    <span class="tw-font-semibold">
                      ${{ store.promoCodeValidation.discounted_price }}/year
                    </span>
                    <span class="tw-line-through tw-text-gray-500 tw-ml-1">
                      ${{ store.promoCodeValidation.original_price }}/year
                    </span>
                    <!-- Tooltip for TWIMO30 promo code -->
                    <v-tooltip v-if="promoCode.toUpperCase() === 'TWIMO30'" bottom>
                      <template #activator="{ on, attrs }">
                        <v-icon
                          color="info"
                          small
                          class="tw-ml-1 tw-cursor-help"
                          v-bind="attrs"
                          v-on="on"
                        >
                          mdi-information-outline
                        </v-icon>
                      </template>
                      <span class="tw-text-xs">
                        Promo applies to your first year only. Your plan will renew at the standard yearly price of $144 after the first year.
                      </span>
                    </v-tooltip>
                  </span>
                </div>
              </div>
              <div
                v-else
                class="tw-text-red-600 tw-text-sm tw-flex tw-items-center"
              >
                <v-icon color="red" small class="mr-2">mdi-alert-circle</v-icon>
                Invalid promo code
              </div>
            </div>
          </div>
        </div>

        <!-- Payment Form -->
        <div v-show="selectedPlan" class="tw-mt-6">
          <div class="tw-mb-4 tw-text-base tw-font-medium tw-text-gray-700">Payment Information</div>
          <div :id="uniqueCardElementId" class="card-element-container tw-mb-6"></div>
          <GoodButton
            block
            x-large
            :loading="loading"
            :disabled="!selectedPlan"
            class="tw-bg-primary tw-text-white tw-font-medium"
            tabindex="0"
            @click.stop="subscribe"
          >
            Complete Payment
          </GoodButton>
        </div>
      </div>

      <!-- Features List -->
      <div class="features-section tw-bg-primary tw-text-white tw-rounded-lg tw-p-8">
        <div class="tw-text-2xl tw-mb-4">What's Included:</div>
        <div class="tw-flex tw-flex-col tw-gap-2">
          <div
            v-for="(feature, index) in store.SUBSCRIPTION_FEATURES"
            :key="index"
            class="tw-flex tw-items-center"
          >
            <v-icon color="white" class="mr-2"> mdi-check </v-icon>
            {{ feature }}
          </div>
        </div>
      </div>
    </v-card>
  </v-dialog>
</template>

<script lang="ts">
// @ts-nocheck

import { defineComponent, ref, onMounted, useContext, watch, nextTick } from '@nuxtjs/composition-api'

import { useSubscriptionStore } from '~/composables/useSubscriptionStore'
import { useToast, useApi } from '~/composables/useCommon'

import GoodButton from './GoodButton.vue'
import GoodButtonReverted from './GoodButtonReverted.vue'





export default defineComponent({
  name: 'PaywallModal',

  components: {
    GoodButton,
    GoodButtonReverted,
  },

  props: {
    // Support v-model with modelValue prop
    modelValue: {
      type: Boolean,
      default: false
    }
  },

  emits: ['update:modelValue'],

  setup(props, { emit }) {
    const store = useSubscriptionStore()
    const toast = useToast()
    const loading = ref(false)
    const validatingPromoCode = ref(false)
    const selectedPlan = ref<string | null>(null)
    const elements = ref(null)
    const cardElement = ref(null)
    const api = useApi()
    const isOpen = ref(false)
    const preventFocusEvents = ref(false)
    const promoCode = ref('')

    // Generate a unique ID for the card element to avoid duplicates
    const uniqueCardElementId = `card-element-${Math.random().toString(36).substring(2, 11)}`

    const { $stripe, $config } = useContext()

    // Safely handle plan selection without causing focus loops
    const handlePlanSelect = (planId) => {
      // Set a flag to prevent focus events temporarily
      preventFocusEvents.value = true

      // Update the selected plan
      selectedPlan.value = planId

      // Reset the flag after a short delay
      setTimeout(() => {
        preventFocusEvents.value = false
      }, 100)
    }

    // Watch for changes from v-model
    watch(
      () => props.modelValue,
      (newVal) => {
        if (newVal !== isOpen.value) {
          isOpen.value = newVal
        }
      },
      { immediate: true }
    )

    // Watch for changes in the store (for backwards compatibility)
    watch(
      () => store.showPaywallModal,
      (newVal) => {
        if (newVal !== isOpen.value) {
          isOpen.value = newVal
        }
      }
    )

    // When local state changes, update both the store and emit the update event
    watch(
      () => isOpen.value,
      (newVal) => {
        // Update v-model value if needed
        if (props.modelValue !== newVal) {
          emit('update:modelValue', newVal)
        }

        // Keep store in sync for backwards compatibility
        if (store.showPaywallModal !== newVal) {
          store.showPaywallModal = newVal
        }

        // Reset selected plan when dialog closes
        if (!newVal) {
          selectedPlan.value = null
          // Clean up any Stripe elements when dialog closes
          if (cardElement.value) {
            try {
              cardElement.value.unmount()
              cardElement.value = null
            } catch (error) {
              console.error('Error unmounting card element:', error)
            }
          }
        }
      }
    )

    const closeModal = () => {
      isOpen.value = false
    }

    const mountStripeElement = () => {
      if (!$stripe) {
        console.error('Stripe not initialized')
        return
      }

      // Wait until the DOM is definitely ready
      setTimeout(() => {
        // Ensure the card element div exists before mounting
        const cardElementDiv = document.getElementById(uniqueCardElementId)
        if (!cardElementDiv) {
          console.error('Card element container not found')
          return
        }

        try {
          // Clean up existing elements
          if (cardElement.value) {
            cardElement.value.unmount()
            cardElement.value = null
          }

          // Create new elements
          elements.value = $stripe.elements()
          cardElement.value = elements.value.create('card', {
            style: {
              base: {
                fontSize: '16px',
                fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
                color: '#1F2937',
                '::placeholder': {
                  color: '#6B7280',
                },
                iconColor: '#6B7280',
                backgroundColor: '#FFFFFF',
                ':-webkit-autofill': {
                  color: '#1F2937',
                },
              },
              invalid: {
                color: '#DC2626',
                iconColor: '#DC2626',
              },
            },
          })

          cardElement.value.mount(`#${uniqueCardElementId}`)
        } catch (error) {
          console.error('Error mounting Stripe element:', error)
        }
      }, 500)
    }

    // Only mount Stripe elements when a plan is selected
    watch(
      () => selectedPlan.value,
      (newVal) => {
        if (newVal) {
          nextTick(() => {
            // Using nextTick to ensure DOM is updated before mounting
            mountStripeElement()
          })
        }
      }
    )

    // Also watch for dialog open to initialize properly
    watch(
      () => isOpen.value,
      (newVal) => {
        if (newVal && selectedPlan.value) {
          // Only mount if dialog is open and a plan is selected
          nextTick(() => {
            mountStripeElement()
          })
        }
      }
    )

    const validatePromo = async () => {
      if (!promoCode.value || !selectedPlan.value) {
        return
      }

      try {
        validatingPromoCode.value = true

        // Store the promo code in the store for persistence
        store.promoCode = promoCode.value

        // Validate the promo code
        const result = await store.validatePromoCode(api, promoCode.value, selectedPlan.value)

        // If validation failed, show a toast message
        if (!result.valid) {
          // We'll show the error in the UI instead of a toast
          // This provides a more consistent experience
        }
      } catch (error) {
        console.error('Error validating promo code:', error)
        // Set a generic error response in the store
        store.promoCodeValidation = {
          valid: false,
          message: 'Invalid promo code'
        }
      } finally {
        validatingPromoCode.value = false
      }
    }

    const subscribe = async () => {
      if (!selectedPlan.value) {
        toast.error('Please select a plan').goAway(3000)
        return
      }

      try {
        loading.value = true

        if (!cardElement.value) {
          throw new Error('Card element not initialized')
        }

        // Create payment method
        const { error: paymentMethodError, paymentMethod } = await $stripe.createPaymentMethod({
          type: 'card',
          card: cardElement.value,
        })

        if (paymentMethodError) {
          throw new Error(paymentMethodError.message)
        }

        // Validate payment method
        if (!paymentMethod?.id) {
          throw new Error('Failed to create payment method')
        }

        // Only use promo code if it's valid
        const usePromoCode = store.promoCodeValidation?.valid && promoCode.value

        // Create subscription using store
        const response = await store.createSubscription(
          api,
          selectedPlan.value,
          paymentMethod.id,
          usePromoCode ? promoCode.value : undefined
        )

        if (response?.error) {
          // If the error is related to promo code, show a simplified message
          if (response.error.includes('promo code')) {
            throw new Error('Invalid promo code')
          } else {
            throw new Error(response.error)
          }
        }

        // Success handling
        closeModal()
        toast.success('Subscription activated successfully').goAway(3000)
      } catch (error) {
        console.error('Subscription error:', error)
        toast.error(error.message || 'Failed to process subscription').goAway(3000)
      } finally {
        loading.value = false
      }
    }

    // Cleanup on component destroy
    onMounted(() => {
      return () => {
        if (cardElement.value) {
          try {
            cardElement.value.unmount()
          } catch (error) {
            console.error('Error unmounting card element:', error)
          }
        }
      }
    })

    return {
      store,
      loading,
      validatingPromoCode,
      selectedPlan,
      promoCode,
      validatePromo,
      subscribe,
      isOpen,
      closeModal,
      handlePlanSelect,
      preventFocusEvents,
      uniqueCardElementId
    }
  },
})
</script>

<style scoped>
.card-element-container {
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1rem;
  background: white;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  transition: border-color 0.15s ease-in-out;
}

.card-element-container:hover {
  border-color: #9ca3af;
}

:deep(.StripeElement--focus) {
  border-color: #6b7280;
  box-shadow:
    0 1px 3px 0 rgba(0, 0, 0, 0.1),
    0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

:deep(.StripeElement--invalid) {
  border-color: #dc2626;
}

/* Custom class for the dialog */
:global(.paywall-dialog) {
  z-index: 100;
  outline: none;
}

/* Add a higher specificity selector to override Vuetify's focus styles */
:global(.paywall-dialog .v-dialog) {
  outline: none !important;
}
</style>
