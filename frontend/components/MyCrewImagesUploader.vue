<script lang="ts">
// @ts-nocheck
import { defineComponent, onMounted, ref, computed } from '@nuxtjs/composition-api'

import { ImageFile } from '~/composables/useImageStore'
import GoodButton from '~/components/GoodButton.vue'

export default defineComponent({
  name: 'MyCrewImagesUploader',

  components: {
    GoodButton,
  },

  props: {
    images: {
      type: Array,
      default: () => [],
    },
    maxFiles: {
      type: Number,
      default: 20,
    },
    maxFileSize: {
      type: Number,
      default: 10, // in MB
    },
    isUploading: {
      type: Boolean,
      default: false,
    },
    hasUnsavedChanges: {
      type: Boolean,
      default: false,
    },
  },

  emits: ['files-selected', 'delete-image', 'images-reordered', 'error', 'save-changes'],

  setup(props, { emit }) {
    const showDelete = ref(false)
    const dropRegion = ref<HTMLElement | null>(null)
    const imagePreviewRegion = ref<HTMLElement | null>(null)
    const imageFiles = ref<FileList | null>(null)
    const inputSelector = ref<HTMLInputElement | null>(null)
    const draggedImageIndex = ref<number | null>(null)
    const isDraggingOver = ref(false)

    const uploadInProgress = computed(() => props.isUploading)

    const triggerFileInput = () => {
      if (uploadInProgress.value) return
      if (props.images.length >= props.maxFiles) {
        emit('error', `Maximum ${props.maxFiles} images allowed`)
        return
      }
      inputSelector.value?.click()
    }

    const preventDefault = (e: Event) => {
      e.preventDefault()
      e.stopPropagation()
    }

    const handleDragEnter = (e: DragEvent) => {
      preventDefault(e)
      isDraggingOver.value = true
    }

    const handleDragLeave = (e: DragEvent) => {
      preventDefault(e)
      isDraggingOver.value = false
    }

    const handleDrop = (e: DragEvent) => {
      preventDefault(e)
      isDraggingOver.value = false
      if (uploadInProgress.value) return

      const files = e.dataTransfer?.files
      if (files && files.length > 0) {
        // Check if adding these files would exceed the limit
        if (props.images.length + files.length > props.maxFiles) {
          emit('error', `Cannot upload more than ${props.maxFiles} images`)
          return
        }

        handleFiles(files)
      }
    }

    const handleDragStart = (index: number, event: DragEvent) => {
      draggedImageIndex.value = index

      // Set drag image (optional)
      if (event.dataTransfer && props.images[index]) {
        const img = new Image()
        img.src = props.images[index].url
        event.dataTransfer.setDragImage(img, 50, 50)
        // Set data to help with cross-browser compatibility
        event.dataTransfer.setData('text/plain', String(index))
      }
    }

    const handleDragOver = (index: number, event: DragEvent) => {
      preventDefault(event)
      // Only add visual indication if dragging a different item
      if (draggedImageIndex.value !== null && draggedImageIndex.value !== index) {
        event.currentTarget.classList.add('drag-over')
      }
    }

    const handleDragLeaveItem = (event: DragEvent) => {
      // Remove visual indication
      event.currentTarget.classList.remove('drag-over')
    }

    const handleDropReposition = (index: number, event: DragEvent) => {
      preventDefault(event)
      event.currentTarget.classList.remove('drag-over')

      if (draggedImageIndex.value !== null && draggedImageIndex.value !== index) {
        const newImages = [...props.images]
        const draggedImage = newImages[draggedImageIndex.value]

        // Remove from original position
        newImages.splice(draggedImageIndex.value, 1)

        // Insert at new position
        newImages.splice(index, 0, draggedImage)

        // Emit the reordered array
        emit('images-reordered', newImages)
      }

      draggedImageIndex.value = null
    }

    const addListeners = () => {
      if (dropRegion.value) {
        dropRegion.value.addEventListener('dragenter', handleDragEnter, false)
        dropRegion.value.addEventListener('dragleave', handleDragLeave, false)
        dropRegion.value.addEventListener('dragover', preventDefault, false)
        dropRegion.value.addEventListener('drop', handleDrop, false)
      }
    }

    const createInputSelector = () => {
      const input = document.createElement('input')
      input.type = 'file'
      input.accept = 'image/png, image/jpeg, image/jpg'
      input.multiple = true

      input.addEventListener('change', () => {
        if (input.files && input.files.length > 0) {
          // Check if adding these files would exceed the limit
          if (props.images.length + input.files.length > props.maxFiles) {
            emit('error', `Cannot upload more than ${props.maxFiles} images`)
            return
          }

          handleFiles(input.files)
          input.value = '' // Clear the input
        }
      })

      inputSelector.value = input
      addListeners()
    }

    const validateFile = (file: File): boolean => {
      // Check file type
      const validTypes = ['image/jpeg', 'image/png', 'image/jpg']
      if (!validTypes.includes(file.type)) {
        emit('error', `File "${file.name}" is not a valid image type. Only JPG and PNG are supported.`)
        return false
      }

      // Check file size
      const maxSizeBytes = props.maxFileSize * 1024 * 1024
      if (file.size > maxSizeBytes) {
        const sizeMB = (file.size / (1024 * 1024)).toFixed(2)
        emit('error', `File "${file.name}" (${sizeMB}MB) exceeds the maximum size of ${props.maxFileSize}MB. Please compress the image or choose a smaller one.`)
        return false
      }

      return true
    }

    const handleFiles = (files: FileList) => {
      const validFiles: File[] = []

      for (let i = 0; i < files.length; i++) {
        if (validateFile(files[i])) {
          validFiles.push(files[i])
        }
      }

      if (validFiles.length > 0) {
        emit('files-selected', validFiles)
      }
    }

    const deleteImage = (id: string) => {
      if (uploadInProgress.value) return
      emit('delete-image', id)
    }

    onMounted(() => {
      dropRegion.value = document.getElementById('drop-region')
      imagePreviewRegion.value = document.getElementById('image-preview')
      createInputSelector()
    })

    return {
      showDelete,
      dropRegion,
      imagePreviewRegion,
      imageFiles,
      inputSelector,
      isDraggingOver,
      uploadInProgress,
      triggerFileInput,
      preventDefault,
      handleDrop,
      createInputSelector,
      handleFiles,
      deleteImage,
      handleDragStart,
      handleDragOver,
      handleDragLeaveItem,
      handleDropReposition,
    }
  },
})
</script>

<template>
  <div
    id="drop-region"
    :class="[
      images.length === 0 ? 'tw-justify-center tw-items-center' : 'tw-justify-start tw-items-start',
      isDraggingOver ? 'dragging-over' : '',
      'tw-relative tw-border-2 tw-border-dashed tw-border-gray-300 tw-rounded-lg tw-p-6 tw-flex tw-flex-col tw-min-h-[400px] tw-transition-colors tw-duration-200',
    ]"
  >
    <!-- Loading Overlay (Covers everything inside drop-region) -->
    <div
      v-if="uploadInProgress"
      class="tw-absolute tw-inset-0 tw-bg-white tw-bg-opacity-80 tw-flex tw-flex-col tw-items-center tw-justify-center tw-z-10 tw-rounded-lg"
    >
      <v-progress-circular indeterminate color="primary" size="64" />
      <p class="tw-mt-4 tw-text-gray-600 tw-font-semibold">Processing images...</p>
      <p class="tw-mt-2 tw-text-sm tw-text-gray-500">This may take a moment depending on image size</p>
    </div>

    <!-- Content Area (Handles Empty State vs Grid) -->
    <div class="tw-flex-grow tw-w-full">
      <!-- Empty State -->
      <div
        v-if="images.length === 0 && !uploadInProgress"
        class="tw-flex tw-flex-col tw-items-center tw-justify-center tw-gap-8 tw-text-center tw-h-full"
        @click="triggerFileInput"
      >
        <v-icon size="100" color="grey lighten-1">mdi-cloud-upload-outline</v-icon>
        <div class="tw-flex tw-flex-col tw-items-center tw-gap-2">
          <div class="tw-text-lg tw-font-semibold tw-text-gray-700">
            Drag & Drop or Click to Upload
          </div>
          <div class="tw-text-sm tw-text-gray-500">
            Supports JPG, PNG. Max {{ maxFileSize }}MB each. Up to {{ maxFiles }} images.
          </div>
          <div class="tw-text-sm tw-text-gray-500 tw-mt-1">
            <v-icon small color="info" class="tw-mr-1">mdi-information-outline</v-icon>
            <span>For best results, use images between 800x600 and 2000x1500 pixels</span>
          </div>
          <GoodButton class="tw-mt-4" :disabled="uploadInProgress" @click.stop="triggerFileInput">
            Select Images
          </GoodButton>
        </div>
      </div>

      <!-- Image Grid (Only shown when images exist) -->
      <div v-else class="tw-w-full">
        <div class="tw-text-sm tw-text-gray-500 tw-mb-2 tw-flex tw-items-center tw-justify-between">
          <span>{{ images.length }} image{{ images.length !== 1 ? 's' : '' }}</span>
          <div v-if="images.length > 10" class="tw-flex tw-items-center">
            <v-icon small class="tw-mr-1">mdi-gesture-swipe-vertical</v-icon>
            <span>Scroll to see more images</span>
          </div>
          <span v-else></span> <!-- Empty span to maintain flex justify-between -->
        </div>
        <div
          class="tw-grid tw-grid-cols-2 sm:tw-grid-cols-3 md:tw-grid-cols-4 lg:tw-grid-cols-4 tw-gap-4 tw-mb-4 tw-max-h-[500px] tw-overflow-y-auto tw-pr-2"
        >
          <!-- Image Items -->
          <div
            v-for="(image, index) in images"
            :key="image.id || image.media_id || index"
            class="tw-relative tw-aspect-square tw-group tw-rounded-lg tw-overflow-hidden tw-border tw-border-gray-200"
            :draggable="!uploadInProgress"
            @dragstart="handleDragStart(index, $event)"
            @dragover="handleDragOver(index, $event)"
            @dragleave="handleDragLeaveItem($event)"
            @drop="handleDropReposition(index, $event)"
          >
            <img
              :src="image.url || image.src"
              alt="Uploaded Image"
              class="tw-object-cover tw-w-full tw-h-full"
            />
            <!-- Action Overlay -->
            <div
              class="tw-absolute tw-inset-0 tw-bg-black tw-bg-opacity-0 group-hover:tw-bg-opacity-50 tw-transition-opacity tw-duration-200 tw-flex tw-items-center tw-justify-center"
              :class="{ 'cursor-grabbing': draggedImageIndex === index }"
            >
              <button
                v-if="!uploadInProgress"
                class="tw-absolute tw-top-2 tw-right-2 tw-bg-red-500 tw-text-white tw-rounded-full tw-p-1 hover:tw-bg-red-600 tw-opacity-0 group-hover:tw-opacity-100 tw-transition-opacity tw-duration-200"
                aria-label="Delete image"
                @click.stop="deleteImage(image.id || image.media_id)"
              >
                <v-icon small>mdi-close</v-icon>
              </button>
            </div>
            <!-- Upload Progress per image -->
            <div
              v-if="!image.uploaded && uploadInProgress"
              class="tw-absolute tw-inset-0 tw-bg-white tw-bg-opacity-70 tw-flex tw-items-center tw-justify-center"
            >
              <v-progress-circular indeterminate color="primary" size="32" />
            </div>
          </div>

          <!-- Add More Button/Slot -->
          <div
            v-if="images.length < maxFiles && !uploadInProgress"
            class="tw-relative tw-aspect-square tw-border-2 tw-border-dashed tw-border-gray-300 tw-rounded-lg tw-flex tw-items-center tw-justify-center tw-text-gray-400 hover:tw-border-primary hover:tw-text-primary tw-cursor-pointer tw-transition-colors tw-duration-200"
            @click="triggerFileInput"
          >
            <div class="tw-text-center">
              <v-icon size="40">mdi-plus</v-icon>
              <div class="tw-text-sm tw-mt-1">Add More</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Save Changes Button (Placed *after* the content area, visibility depends only on hasUnsavedChanges) -->
    <div
      v-if="hasUnsavedChanges"
      class="tw-flex tw-justify-end tw-mt-6 tw-w-full tw-pt-4 tw-border-t tw-border-gray-200"
    >
      <GoodButton
        color="primary"
        :disabled="uploadInProgress"
        :class="{ 'tw-animate-pulse': !uploadInProgress }"
        @click="$emit('save-changes')"
      >
        Save Photo Changes
        <v-icon right dark>mdi-content-save</v-icon>
      </GoodButton>
    </div>

    <input
      ref="inputSelector"
      type="file"
      accept="image/png, image/jpeg, image/jpg"
      multiple
      class="tw-hidden"
    />
  </div>
</template>

<style scoped>
#drop-region {
  border: 1px dashed lightgray;
  min-height: 350px;
  max-height: 600px; /* Limit maximum height */
  border-radius: 10px;
  padding: 15px 8px;
  overflow: auto;
  transition: background-color 0.2s ease;
  display: flex; /* Ensure flex direction column works */
  flex-direction: column; /* Stack content and button */
}

.dragging-over {
  background-color: rgba(200, 200, 200, 0.2);
  border: 1px dashed #4a90e2;
}

.image-container {
  transition:
    transform 0.2s ease,
    box-shadow 0.2s ease;
  position: relative;
}

.image-container:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.drag-over {
  border: 2px dashed #4a90e2;
  border-radius: 10px;
  background-color: rgba(74, 144, 226, 0.1);
}

.uploaded-image {
  object-fit: cover;
  height: 220px;
  width: 360px;
  border-radius: 10px;
  margin: -28px 8px 8px;
}

.centered {
  display: flex;
  justify-content: center;
  align-items: center;
}

#drop-region:hover {
  background-color: rgb(247, 247, 247);
}

.icon-class {
  position: relative;
  margin-left: 340px;
  cursor: pointer;
}

@media only screen and (max-width: 450px) {
  .uploaded-image {
    width: 100%;
    object-fit: contain;
    margin: -42px 0 0;
  }

  .icon-class {
    margin-left: 92%;
  }

  #drop-region {
    min-height: 350px;
    max-height: 500px;
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}
.tw-animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
</style>
