<script lang="ts">
// @ts-nocheck
import { defineComponent } from '@nuxtjs/composition-api'

import { useCreateHomeProgressStore } from '~/composables/useCreateHomeStore'
import { amenities } from '~/constants'
import { useApi, useToast } from '~/composables/useCommon'
import { UploadedFile } from '~/types'

export default defineComponent({
  name: 'CreateHomeDescription',

  components: {},

  setup() {
    const api = useApi()
    const toast = useToast()

    const store = useCreateHomeProgressStore()

    const {
      createHomeData,
      setCurrentStep,
      validateCreateHomeData,
      clickOnRentalPlan,
      decreaseBedrooms,
      decreaseBeds,
      decreaseBathrooms,
      decreaseGuests,
      decreasePets,
      isAmenitySelected,
      clickOnAmenity,
      deletePhoto,
      setAsCoverPhoto,
    } = store

    return {
      createHomeData,
      validateCreateHomeData,
      setCurrentStep,
      clickOnRentalPlan,
      decreaseBedrooms,
      decreaseBeds,
      decreaseBathrooms,
      decreasePets,
      decreaseGuests,
      amenities,
      isAmenitySelected,
      clickOnAmenity,
      deletePhoto,
      setAsCoverPhoto,
    }
  },
})
</script>

<template>
  <v-row>
    <v-col cols="12" class="tw-mt-8 tw-flex tw-flex-col tw-gap-4">
      <div class="tw-text-2xl tw-font-semibold tw-text-zinc-600">Home Description</div>

      <div class="tw-text-lg tw-font-medium tw-text-zinc-500">
        In a brief sentence or two describe your home for your guests!
      </div>
    </v-col>

    <v-col cols="12" md="6">
      <v-textarea
        v-model="createHomeData.description"
        label="Description"
        outlined
        placeholder="Type Here"
        dense
        class="tw-mt-4"
        rows="8"
        counter="500"
        maxlength="500"
      />
    </v-col>
  </v-row>
</template>

<style scoped></style>
