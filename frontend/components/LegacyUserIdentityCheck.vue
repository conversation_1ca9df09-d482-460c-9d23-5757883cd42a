<template>
  <v-dialog v-model="showDialog" max-width="600px" persistent>
    <v-card class="tw-p-6 tw-rounded-xl">
      <div class="tw-text-2xl tw-font-semibold tw-text-primary tw-mb-4">
        Identity Verification Required
      </div>

      <div class="tw-mb-6 tw-text-zinc-600">
        <p class="tw-mb-2">
          As a valued legacy user of Twimo, we need to verify your identity to ensure the security of our platform.
        </p>
        <p class="tw-mb-2">
          This is a one-time process that takes just a few minutes. If you don't complete this verification,
          your homes will be moved to draft status.
        </p>
      </div>

      <div class="tw-flex tw-justify-between">
        <v-btn
          color="grey lighten-1"
          text
          @click="skipVerification"
        >
          Skip for now
        </v-btn>

        <v-btn
          color="primary"
          @click="startVerification"
        >
          Start Verification
        </v-btn>
      </div>
    </v-card>
  </v-dialog>
</template>

<script>
import { defineComponent, ref, onMounted, computed } from '@nuxtjs/composition-api'

import { useVerificationStore } from '~/composables/useVerificationStore'
import { useHostActivationStore } from '~/composables/useHostActivationStore'
import { useApi, useToast } from '~/composables/useCommon'

export default defineComponent({
  name: 'LegacyUserIdentityCheck',

  setup() {
    const verificationStore = useVerificationStore()
    const hostActivationStore = useHostActivationStore()
    const api = useApi()
    const toast = useToast()

    // State
    const showDialog = ref(false)

    // Check if user is a legacy user
    const isLegacyUser = computed(() => {
      const user = window.$nuxt.$store.getters['auth/getuser']
      return user && new Date(user.created_at) < new Date('2025-01-31T00:00:00Z')
    })

    // Check if user has verified their identity
    const isIdentityVerified = computed(() => {
      const user = window.$nuxt.$store.getters['auth/getuser']
      return user && user.is_identity_verified
    })

    // Get visit count
    const getVisitCount = () => {
      if (!isLegacyUser.value) return 0;

      const visitCount = parseInt(localStorage.getItem('twimo_legacy_user_visit_count') || '0')
      return visitCount
    }

    // Increment visit count
    const incrementVisitCount = () => {
      if (!isLegacyUser.value) return;

      const currentCount = getVisitCount()
      localStorage.setItem('twimo_legacy_user_visit_count', (currentCount + 1).toString())

      return currentCount + 1
    }

    // Check if we need to show the dialog or update home status
    const checkLegacyUserStatus = async () => {
      // Only proceed for legacy users who haven't verified identity
      if (!isLegacyUser.value || isIdentityVerified.value) return;

      // Get current visit count and increment it
      const visitCount = incrementVisitCount()
      console.log('Legacy user visit count:', visitCount)

      if (visitCount === 2) {
        // Second visit - show the dialog
        console.log('Second visit - showing verification dialog')
        showDialog.value = true
      } else if (visitCount >= 3) {
        // Third or later visit - update home status if not verified
        if (!isIdentityVerified.value) {
          console.log('Third or later visit - updating home status')
          try {
            const response = await api.post('/user/legacy-verification/update-homes-status')
            console.log('Home status update response:', response.data)

            // Show a toast notification
            toast.info('Your homes have been moved to draft status. Please complete identity verification to reactivate them.').goAway(5000)
          } catch (error) {
            console.error('Error updating home status:', error)
          }
        }
      }
    }

    // Start verification
    const startVerification = async () => {
      showDialog.value = false

      try {
        // Open the host activation modal and go directly to the identity verification step
        hostActivationStore.goToStep(2) // Step 2 is identity verification
        hostActivationStore.openActivationModal()

        // Log for debugging
        console.log('Opening host activation modal for legacy user identity verification')
      } catch (error) {
        console.error('Error starting verification:', error)
        toast.error('Failed to start verification. Please try again.').goAway(3000)
      }
    }

    // Skip verification
    const skipVerification = () => {
      showDialog.value = false
      toast.warning('Your homes will be moved to draft status on your next visit if you don\'t complete verification.').goAway(5000)
    }

    // Lifecycle hooks
    onMounted(async () => {
      // Check activation status first (this will also check verification status)
      await hostActivationStore.checkActivationStatus(api)

      // Check legacy user status
      checkLegacyUserStatus()
    })

    return {
      showDialog,
      startVerification,
      skipVerification
    }
  }
})
</script>
