<script lang="ts">
// @ts-nocheck
import { defineComponent } from '@nuxtjs/composition-api'
import FullCalendar from '@fullcalendar/vue'
import dayGridPlugin from '@fullcalendar/daygrid'
import timeGridPlugin from '@fullcalendar/timegrid'
import multiMonthPlugin from '@fullcalendar/multimonth'
import interactionPlugin from '@fullcalendar/interaction'

import MyCalendarInfoThumbItem from '~/components/MyCalendarInfoThumbItem.vue'
import CategoryFilter from '~/components/CategoryFilter.vue'
import { HostDashboardData } from '~/types'
import screenCheckerMixins from '~/mixins/screenChecker.mixins'

export default defineComponent({
  name: 'MyCalendar',

  components: {
    MyCalendarInfoThumbItem,
    CategoryFilter,
    FullCalendar,
  },

  mixins: [screenCheckerMixins],

  props: {
    hostDashBoardData: {
      type: Object as () => HostDashboardData,
      default: () => ({}),
    },
  },

  data() {
    return {
      categorySelected: [1, 2, 3, 4, 5, 6, 7],

      events: [],
      startDate: '',
      endDate: '',
      countOwnerUse: 0,
      countFamilyFriends: 0,
      countPublicBookings: 0,
      countSwaps: 0,
      countMyCrewJobs: 0,
      countBlockedDates: 0,
      countICalEvents: 0,
      isCreateEventOpen: false,
      startDateMenu: false,
      endDateMenu: false,
      filterStartDateMenu: false,
      filterEndDateMenu: false,
      houses: [],
      filter_by_items: [{ text: 'All Properties', value: 0 }],
      filterStartDate: null,
      filterEndDate: null,
      selectedPropertyFilter: 0,
      iCalurl: null,
      iCalName: null,
      selectedICalHomeId: null,
      icalmenu: false,
      linkmenu: false,
      showMyCrewOption: false,
      showBookingLink: false,
      showRecurrence: false,
      defaultProperty: null,
      user: { role_id: null },
      calendarTitle: '',

      categories: [],
      iCalList: [],
      filterViewAs: [
        { text: `Monthly`, value: `dayGridMonth` },
        { text: `Weekly`, value: `timeGridWeek` },
        { text: `Quarterly`, value: `multiMonthQuarter` },
        { text: `Yearly`, value: `multiMonthYear` },
      ],
      recurrenceList: [
        { text: `Once`, value: 1 },
        { text: `Daily`, value: 2 },
        { text: `Weekly`, value: 3 },
        { text: `Bi-Weekly`, value: 4 },
        { text: `Monthly`, value: 5 },
        { text: `Quarterly`, value: 6 },
        { text: `Yearly`, value: 7 },
      ],
      mycrewListoptions: [
        { text: `Yes, let's complete the job!`, value: 1 },
        { text: `No`, value: 2 },
      ],
      bookingLinkOptions: [
        { text: `Yes, let's complete the booking!`, value: 1 },
        { text: `No`, value: 2 },
      ],
      propertiesList: [],
      createForm: {
        startDate: '',
        endDate: '',
        home: null,
        category: null,
        title: null,
        notes: null,
        recurrence: 1,
        mycrew: 1,
        bookingLink: 1,
        blockDates: true,
      },
      currentMonth: new Date().toLocaleString('default', { month: 'long' }),
      currentYear: new Date().getUTCFullYear(),

      showEventSummary: false,
      selectedEvent: {},
      popupStyle: {},

      selectedView: 'dayGridMonth',
      calendarOptions: {
        plugins: [dayGridPlugin, timeGridPlugin, interactionPlugin, multiMonthPlugin],
        initialView: 'dayGridMonth',
        selectable: true,
        events: function (info, successCallback, failureCallback) {},
        datesSet: this.onDatesSet,
        select: this.handleDateSelect,
        eventDidMount: this.eventMounted,
        eventClick: this.handleEventClick,
        views: {
          quarterlyView: {
            type: 'dayGrid',
            duration: { months: 3 },
            buttonText: 'Quarterly',
            visibleRange: function (currentDate) {
              const start = currentDate.clone().startOf('quarter')
              const end = currentDate.clone().endOf('quarter')
              return { start, end }
            },
            titleFormat: { year: 'numeric', month: 'short' },
          },
          yearlyView: {
            type: 'dayGrid',
            duration: { year: 1 },
            buttonText: 'Yearly',
            titleFormat: { year: 'numeric' },
          },
          multiMonthQuarter: {
            type: 'multiMonth',
            duration: { months: 3 },
          },
          multiMonthYear: {
            type: 'multiMonth',
            duration: { year: 1 },
          },
        },
        // datesSet: this.adjustToQuarter
      },
    }
  },
  computed: {
    formattedStartDate() {
      return this.formatDate(this.createForm.startDate)
    },
    formattedEndDate() {
      return this.formatDate(this.createForm.endDate)
    },
    formattedFilterStartDate() {
      return this.formatDate(this.filterStartDate)
    },
    formattedFilterEndDate() {
      return this.formatDate(this.filterEndDate)
    },
    userInfo() {
      return this.$store.getters['auth/getuser']
    },
  },
  async mounted() {
    this.getBlockedDates()
    this.getICals()

    await this.$store.dispatch('auth/getUserDetails')
    this.userInfo = this.$store.getters['auth/getuser']

    // Get Categories
    try {
      const { data } = await this.$axios.get('mycalendar/get-categories')
      for (const c of data.categories) {
        this.categories.push({ text: c.label, value: c.id, color: c.color })
      }
    } catch (e) {
      console.log(e)
    }

    try {
      const { data } = await this.$axios.get('home/user/list/my?mode=minify&status=active')
      this.houses = data
      // this.houses.forEach(([key, value]) => { this.filter_by_items.push(value); });
      for (const v in this.houses) {
        this.filter_by_items.push({
          text: this.houses[v].title,
          value: this.houses[v].id,
        })
        this.propertiesList.push({
          text: this.houses[v].title,
          value: this.houses[v].id,
        })
      }
      
      this.createForm.home = this.defaultProperty = this.houses[0].id
    } catch (e) {
      console.log(e)
    }
  },
  methods: {
    eventMounted(arg) {},
    handleEventClick(info) {
      this.isCreateEventOpen = false
      this.selectedEvent = info.event
      this.showEventSummary = true
      const windowWidth = window.innerWidth;
      const popupWidth = 320; // Width of the popup
      let leftPosition = info.jsEvent.pageX;

      if (leftPosition + popupWidth > windowWidth) {
        leftPosition = windowWidth - popupWidth - 20; // 20px padding from the edge
      }

      let topPosition = info.jsEvent.pageY;
      if (windowWidth <= 600) { // Check if the device is mobile
        topPosition -= 100; // Reduce top by 80px
      }

      this.eventSummaryStyle = {
        top: `${topPosition}px`,
        left: `${leftPosition}px`,
      }
    },
    onDatesSet(info) {
      this.getBlockedDates(); 
    },
    categorySelection: function (categorySelected) {
      this.categorySelected = categorySelected
      this.calendarOptions.events = []
      this.getBlockedDates()
    },
    filterEvents() {},
    async getBlockedDates() {
      // SET THE CURRENT DATES
      const calendarApi = this.$refs.fullCalendar.getApi()

      const startDate = calendarApi.view.activeStart.toISOString().split('T')[0];
      const endDate = calendarApi.view.activeEnd.toISOString().split('T')[0];

      this.calendarTitle = calendarApi.view.title;

      // UPDATE THE EVENTS
      const blockedData = await this.$axios.get(
        `/mycalendar/get-events?start=${startDate}&end=${endDate}&categories=${this.categorySelected}&home=${this.selectedPropertyFilter}`
      )

      this.calendarOptions.events = []
      this.countOwnerUse =
        this.countSwaps =
        this.countPublicBookings =
        this.countMyCrewJobs =
        this.countBlockedDates =
        this.countFamilyFriends =
        this.countICalEvents =
          0

      blockedData.data.dashboard_data.forEach(el => {
        if (el.type === 1) this.countOwnerUse = el.count
        if (el.type === 2) this.countFamilyFriends = el.count
        if (el.type === 3) this.countPublicBookings = el.count
        if (el.type === 4) this.countSwaps = el.count
        if (el.type === 5) this.countMyCrewJobs = el.count
        if (el.type === 6) this.countBlockedDates = el.count
        if (el.type === 7) this.countICalEvents = el.count
      })

      blockedData.data.dates.forEach((el: {}) => {
        const newDate = new Date(el.end_at)
        newDate.setDate(newDate.getDate() + 1)
        const year = newDate.getFullYear()
        const month = String(newDate.getMonth() + 1).padStart(2, '0')
        const day = String(newDate.getDate()).padStart(2, '0')

        console.log(el)

        this.calendarOptions.events.push({
          start: el.start_at.slice(0, 10),
          end: `${year}-${month}-${day}`,
          color: `#${el.color}`,
          backgroundColor: `#${el.color}`,
          category: el.category,
          category_id: el.category_id,
          home_name: el.home_name,
          home_id: el.home_id,
          notes: el.notes,
          event_title: el.title,
          recurrence: el.recurrence,
          blockDates: el.block_dates,
          title: el.title + ' - ' + el.home_name,
          id: el.id,
        })
      })
    },
    nextMonth() {
      const calendarApi = this.$refs.fullCalendar.getApi()
      calendarApi.next()
      // this.getBlockedDates()
    },
    lastMonth() {
      const calendarApi = this.$refs.fullCalendar.getApi()
      calendarApi.prev()
      // this.getBlockedDates()
    },
    handleDateSelect(info) {
      this.updateCreateEventPopup(true)
      this.showEventSummary = false
      this.createForm.startDate = info.startStr

      const newDate = new Date(info.endStr)
      // newDate.setDate(newDate.getDate() - 1);
      const year = newDate.getFullYear()
      const month = String(newDate.getMonth() + 1).padStart(2, '0')
      const day = String(newDate.getDate()).padStart(2, '0')

      this.createForm.endDate = `${year}-${month}-${day}`
    },
    async submitCreateEventForm() {
      if (!this.validateForm()) {
        return
      }

      this.isCreateEventOpen = false
      this.loading = true

      if (
        this.selectedEvent != null &&
        this.selectedEvent != undefined &&
        this.selectedEvent.id != undefined
      ) {
        try {
          const { data } = await this.$axios.put(
            'mycalendar/update-event/' + this.selectedEvent.id,
            {
              form_data: this.createForm,
            }
          )
          console.log(data)
          if (data.status == 'success') {
            this.getBlockedDates()
          }
        } catch (err) {
          return this.$toast.error('Error while updating the event.').goAway(this.TOAST_DURATION)
        } finally {
          this.loading = false
        }
      } else {
        /* this.calendarOptions.events.push({
								start: this.createForm.startDate,
								end: this.createForm.endDate,
								color: '#63963D',
                                backgroundColor: '#CD6D6D',
								name: this.createForm.title,
                                title: this.createForm.title
							}); */

        try {
          const { data } = await this.$axios.post('mycalendar/create-event', {
            form_data: this.createForm,
          })
          console.log(data)
          if (data.status == 'success') {
            /* if (this.createForm.bookingLink == 1) {
              this.$router.push({
                path: '/' + data.home_slug + '/edit',
                query: {
                  ce_id: data.calendar_event_id,
                  title: this.createForm.title,
                  dates: this.formattedStartDate + ' - ' + this.formattedEndDate,
                },
              })
            } */

            this.getBlockedDates()
          }
        } catch (err) {
          console.log(err)
          return this.$toast.error('Error while creating the event.').goAway(this.TOAST_DURATION)
        } finally {
          this.loading = false
        }
      }
    },
    validateForm(): boolean {
      console.log(this.createForm.category)

      if (this.createForm.title == null || this.createForm.title == '') {
        this.$toast.error('Please enter the title').goAway(this.TOAST_DURATION)

        return false
      }

      if (this.createForm.category == null || this.createForm.category == '') {
        this.$toast.error('Please select the category').goAway(this.TOAST_DURATION)

        return false
      }

      if (this.createForm.home == null || this.createForm.home == '') {
        this.$toast.error('Please select the home').goAway(this.TOAST_DURATION)

        return false
      }

      return true
    },
    adjustToQuarter(info) {
      if (info.view.type === 'multiMonthQuarter') {
        const currentDate = info.view.currentStart

        const calendarApi = this.$refs.fullCalendar.getApi()
        const quarterStart = new Date(
          currentDate.getFullYear(),
          Math.floor(currentDate.getMonth() / 3) * 3,
          1
        )

        calendarApi.gotoDate(quarterStart)

        const quarterNumber = Math.floor(quarterStart.getMonth() / 3) + 1
        const yearNumber = quarterStart.getFullYear()
        calendarApi.setOption('title', `Q${quarterNumber} ${yearNumber}`)
        /* const quarterStart = new Date(currentDate.getFullYear(), Math.floor(currentDate.getMonth() / 3) * 3, 1)
                    info.view.currentStart = quarterStart
                    info.view.activeStart = quarterStart
                    info.view.activeEnd = new Date(quarterStart.getFullYear(), quarterStart.getMonth() + 3, 1)

                    const quarterNumber = Math.floor(quarterStart.getMonth() / 3) + 1
                    const yearNumber = quarterStart.getFullYear()
                    this.$refs.fullCalendar.getApi().setOption('title', `Q${quarterNumber} ${yearNumber}`)

                    this.$refs.fullCalendar.getApi().render() */
      }
    },
    updateCalendarRange() {
      this.filterStartDateMenu = this.filterEndDateMenu = false
      const calendarApi = this.$refs.fullCalendar.getApi()

      const endDate = new Date(this.filterEndDate);
      endDate.setDate(endDate.getDate() + 1);

      calendarApi.setOption('validRange', {
        start: this.filterStartDate,
        end: endDate.toISOString().split('T')[0],
      })
      calendarApi.render()
    },
    changeView() {
      const calendarApi = this.$refs.fullCalendar.getApi()
      calendarApi.changeView(this.selectedView)
      this.filterStartDate = this.filterEndDate = null
      if (this.selectedView == 'multiMonthQuarter') {
        this.adjustToQuarter(calendarApi)
      }
    },
    updateCreateEventPopup(isCreateEventOpen) {
      // Reset the create form
      ;(this.createForm = {
        startDate: '',
        endDate: '',
        home: this.defaultProperty,
        category: null,
        title: null,
        notes: null,
        recurrence: 1,
        blockDates: 1,
      }),
      this.selectedEvent = {}
      this.isCreateEventOpen = isCreateEventOpen
    },

    formatDate(date) {
      if (!date) return ''
      const options = {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        timeZone: 'UTC',
      }
      return new Date(date).toLocaleDateString('en-US', options)
    },
    onStartDateSelected(date) {
      this.createForm.startDate = date
      this.startDateMenu = false
    },
    onEndDateSelected(date) {
      this.createForm.endDate = date
      this.endDateMenu = false
    },
    async editEvent() {
      console.log('Edit event')

      this.showEventSummary = false
      this.isCreateEventOpen = true

      this.createForm.home = this.selectedEvent.extendedProps.home_id
      this.createForm.category = this.selectedEvent.extendedProps.category_id
      this.createForm.title = this.selectedEvent.extendedProps.event_title
      this.createForm.notes = this.selectedEvent.extendedProps.notes
      this.createForm.recurrence = this.selectedEvent.extendedProps.recurrence
      this.createForm.startDate = this.selectedEvent.startStr
      this.createForm.blockDates = this.selectedEvent.extendedProps.blockDates

      const newDate = new Date(this.selectedEvent.endStr)
      // newDate.setDate(newDate.getDate() - 1);
      const year = newDate.getFullYear()
      const month = String(newDate.getMonth() + 1).padStart(2, '0')
      const day = String(newDate.getDate()).padStart(2, '0')

      this.createForm.endDate = `${year}-${month}-${day}`
    },
    async deleteEvent(eventId) {
      if (confirm('Are you sure want to delete?')) {
        try {
          const { data } = await this.$axios.delete('mycalendar/delete-event/' + eventId)
          console.log(data)
          if (data.status == 'success') {
            this.showEventSummary = false
            this.getBlockedDates()
            return this.$toast
              .success('Event has been deleted successfully.')
              .goAway(this.TOAST_DURATION)
          }
        } catch (err) {
          console.log(err)
          return this.$toast.error('Error while deleting the record.').goAway(this.TOAST_DURATION)
        } finally {
          this.loading = false
        }
      }
    },
    closeEditPopup: function () {
      this.selectedEvent = null

      this.showEventSummary = false
    },
    formattedEventPopupStartDate() {
      return this.formatDate(this.selectedEvent.startStr)
    },
    formattedEventPopupEndDate() {
      const newDate = new Date(this.selectedEvent.endStr)
      const year = newDate.getFullYear()
      const month = String(newDate.getMonth() + 1).padStart(2, '0')
      const day = String(newDate.getDate()).padStart(2, '0')

      return this.formatDate(`${year}-${month}-${day}`)
    },
    handleIcal(data: {
      iCalurl: string
      iCalName: string
      selectedICalHomeId: string | number | any
    }) {
      if (this.selectedICalHomeId == null) {
        return this.$toast.error('Please select the property.').goAway(1000)
      }

      this.icalmenu = false
      this.$axios
        .post('mycalendar/ical-import', {
          home_id: this.selectedICalHomeId,
          ical_url: data.iCalurl ? data.iCalurl : null,
          ical_name: data.iCalName ? data.iCalName : null,
        })
        .then(resp => {
          if (resp) {
            if (resp.status == 'fail') {
              this.$toast.success('Error while importing.').goAway(3000)
            } else {
              this.$toast.success('Imported successfully.').goAway(3000)
            }
            this.getICals()
            this.getBlockedDates()
            this.icalmenu = false
          }
        })
        .catch(err => console.error(err))
    },
    getBackendUrl() {
      if (process.env.VUE_APP_BACKEND_URL !== 'http://localhost:8099/api/') {
					return 'https://api.twimo.com'
			} else {
					return 'http://localhost:8099'
			}
    },
    convertIcsToWebcal() {
      this.linkmenu = false
      return `${this.getBackendUrl()}/api/ical-export/${this.selectedICalHomeId}`.replace(
        /^http(s?):\/\//i,
        'webcal://'
      )
    },
    copyIcsLink() {
      if (this.selectedICalHomeId == null) {
        return this.$toast.error('Please select the property.').goAway(1000)
      }

      navigator.clipboard.writeText(
        `${this.getBackendUrl()}/api/ical-export/${this.selectedICalHomeId}`
      )

      this.linkmenu = false
      return this.$toast
        .success(
          'Calendar link has been copied. Please paste it in your import URL on external calendars.'
        )
        .goAway(5000)
    },
    upadteFields() {
      this.showBookingLink = this.showRecurrence = this.showMyCrewOption = false

      if (this.createForm.category == 2 || this.createForm.category == 3) {
        // this.showBookingLink = true
        this.createForm.bookingLink = 1
      }
      if (this.createForm.category == 6) {
        // this.showRecurrence = true
        this.createForm.recurrence = 1
      }
      if (this.createForm.category == 5) {
        // this.showMyCrewOption = true
        this.createForm.mycrew = 1
      }
    },
    async getICals() {
      // Get iCals
      try {
        const { data } = await this.$axios.get('mycalendar/get-icals')
        this.iCalList = data.result
      } catch (e) {
        console.log(e)
      }
    },
    async deleteIcal(id) {
      if (confirm('Are you sure want to delete?')) {
        try {
          const { data } = await this.$axios.delete('mycalendar/delete-ical/' + id)
          console.log(data)
          if (data.status == 'success') {
            this.iCalList = this.iCalList.filter(item => item.id !== id)
            this.getICals()
            this.getBlockedDates()
            return this.$toast
              .success('Calendar has been deleted successfully.')
              .goAway(this.TOAST_DURATION)
          }
        } catch (err) {
          console.log(err)
          return this.$toast.error('Error while deleting the record.').goAway(this.TOAST_DURATION)
        } finally {
          this.loading = false
        }
      }
    },
  },
})
</script>

<template>
  <v-col cols="12" class="tw-flex tw-flex-col tw-justify-center mc-container">
    <!--          Banner-->
    <div
      class="md:tw-px-[25px] tw-px-4 md:tw-h-[auto] tw-h-[auto] tw-bg-center tw-bg-no-repeat tw-bg-cover calendar-header"
    >
      <div class="heading">
        <strong>My Calendar Dashboard</strong>
        <strong class="mcCurrentDates">{{ calendarTitle }}</strong>
      </div>

      <p class="calendar-header__subtitle">What's On Your Calendar {{ (selectedView == 'dayGridMonth')? 'This Month' : '' }}</p>

      <div
        class="tw-flex tw-justify-start tw-mt-[30px] tw-mb-[30px] 2xl:tw-px-[50px] md:tw-flex-wrap tw-flex-nowrap tw-gap-4 dashboard-data-container"
      >
        <MyCalendarInfoThumbItem
          v-if="userInfo.role_id != null && userInfo.role_id != 4"
          title="Owner Use"
          :value="countOwnerUse"
          :description="`You have <strong>${countOwnerUse} Owner Use</strong> Dates on your Calendar this month`"
          tag="owner_user"
        />

        <MyCalendarInfoThumbItem
          v-if="userInfo.role_id != null && userInfo.role_id != 4"
          title="Friends & Family"
          :value="countFamilyFriends"
          :description="`You have <strong>${countFamilyFriends} Friends & Family Booking</strong> on your Calendar this month`"
          tag="friends_family"
        />

        <MyCalendarInfoThumbItem
          v-if="userInfo.role_id != null && userInfo.role_id != 4"
          title="Public Bookings"
          :value="countPublicBookings"
          :description="`You have <strong>${countPublicBookings} Booking</strong> on your Calendar this month`"
          tag="public_booking"
        />

        <MyCalendarInfoThumbItem
          v-if="userInfo.role_id != null && userInfo.role_id != 4"
          title="Swaps"
          :value="countSwaps"
          :description="`You have <strong>${countSwaps} Swaps</strong> on your Calendar this month`"
          tag="swaps"
        />

        <MyCalendarInfoThumbItem
          v-if="userInfo.role_id != null"
          title="MyCrew Jobs"
          :value="countMyCrewJobs"
          :description="`You have <strong>${countMyCrewJobs} MyCrew Jobs</strong> on your Calendar this month`"
          tag="mycrew_jobs"
        />

        <MyCalendarInfoThumbItem
          v-if="userInfo.role_id != null && userInfo.role_id != 4"
          title="Blocked Dates"
          :value="countBlockedDates"
          :description="`You have <strong>${countBlockedDates} Blocked Dates</strong> on your Calendar this month`"
          tag="blocked_dates"
        />

        <MyCalendarInfoThumbItem
          v-if="userInfo.role_id != null"
          title="External Events"
          :value="countICalEvents"
          :description="`You have <strong>${countICalEvents} iCal Events</strong> on your Calendar this month`"
          tag="ical_events"
        />
      </div>

      <div class="mc-nav-container">
        <button class="mc-nav-container__prev" @click="lastMonth()">
          <img src="~/assets/icon-arrow.png" alt="Last Month" /> previous         </button>
        <button class="mc-nav-container__next" @click="nextMonth()">
          next  <img src="~/assets/icon-arrow.png" alt="Next Month" />
        </button>
      </div>
    </div>

    <div class="mycalendar-container">
      <v-row class="filter_parent_container">
        <v-col md="3" sm="12" class="py-0"></v-col>
        <v-col md="9" sm="12" class="py-0">
          <v-sheet>
            <!--<h4>My Calendar</h4>-->

            <div class="filter_container">
              <div v-if="userInfo.role_id != null && userInfo.role_id != 4" class="filter_by">
                <v-select
                  v-model="selectedPropertyFilter"
                  :items="filter_by_items"
                  @change="getBlockedDates()"
                >
                  <template #selection="{ item }">
                    <v-avatar
                      size="28"
                      class="v-icon notranslate mx-1 mdi mdi-home theme--light grey--text"
                    >
                    </v-avatar>
                    {{ item.text }}
                  </template>
                </v-select>
              </div>
              <div class="dates_range">
                <v-col xs="6" sm="6" md="6" class="dates-container">
                  <v-menu
                    v-model="filterStartDateMenu"
                    :close-on-content-click="false"
                    transition="scale-transition"
                    offset-y
                    min-width="290px"
                  >
                    <template #activator="{ on, attrs }">
                      <v-text-field
                        v-model="formattedFilterStartDate"
                        label="Start Date"
                        prepend-icon="mdi-calendar"
                        readonly
                        v-bind="attrs"
                        v-on="on"
                      ></v-text-field>
                    </template>
                    <v-date-picker
                      v-model="filterStartDate"
                      no-title
                      @input="updateCalendarRange()"
                    ></v-date-picker>
                  </v-menu>
                </v-col>

                <v-col xs="6" sm="6" md="6" class="dates-container">
                  <v-menu
                    v-model="filterEndDateMenu"
                    :close-on-content-click="false"
                    transition="scale-transition"
                    offset-y
                    min-width="290px"
                  >
                    <template #activator="{ on, attrs }">
                      <v-text-field
                        v-model="formattedFilterEndDate"
                        label="End Date"
                        prepend-icon="mdi-calendar"
                        readonly
                        v-bind="attrs"
                        v-on="on"
                      ></v-text-field>
                    </template>
                    <v-date-picker
                      v-model="filterEndDate"
                      no-title
                      :min="filterStartDate"
                      @input="updateCalendarRange()"
                    ></v-date-picker>
                  </v-menu>
                </v-col>
              </div>
              <div class="view_as">
                <v-select
                  v-model="selectedView"
                  label="View As"
                  :items="filterViewAs"
                  @change="changeView"
                ></v-select>
              </div>
            </div>
          </v-sheet>
        </v-col>
      </v-row>

      <v-row class="cc-row">
        <v-col sm="12" md="3" class="py-0">
          <v-sheet class="mc-sidebar">
            <!-- Create Form Starts -->
            <div v-if="isCreateEventOpen" class="">
              <v-card class="mc-add-event">
                <v-card-text>
                  <v-container>
                    <v-row>
                      <v-col cols="12">
                        <v-select
                          v-model="createForm.category"
                          :items="categories"
                          label="Category *"
                          required
                          @change="upadteFields()"
                        >
                          <template #selection="{ item }">
                            <v-avatar size="28" :class="`mr-2 add_event_cat cat__${item.value}`">
                              <!--<v-img :src="item.image"></v-img>-->
                              <span class="info__indicator" :style="`background: #${item.color}`"
                                ><svg
                                  version="1.1"
                                  xmlns="http://www.w3.org/2000/svg"
                                  xmlns:xlink="http://www.w3.org/1999/xlink"
                                  x="0px"
                                  y="0px"
                                  width="10px"
                                  height="10px"
                                  viewBox="0 0 122.881 89.842"
                                  enable-background="new 0 0 122.881 89.842"
                                  xml:space="preserve"
                                >
                                  <g>
                                    <path
                                      fill="#FFF"
                                      d="M1.232,55.541c-1.533-1.388-1.652-3.756-0.265-5.289c1.388-1.534,3.756-1.652,5.29-0.265l34.053,30.878l76.099-79.699 c1.429-1.501,3.804-1.561,5.305-0.132c1.502,1.428,1.561,3.803,0.133,5.305L43.223,88.683l-0.005-0.005 c-1.396,1.468-3.716,1.563-5.227,0.196L1.232,55.541L1.232,55.541z"
                                    />
                                  </g></svg>
                              ></span>
                            </v-avatar>
                            {{ item.text }}
                          </template>

                          <template #item="{ item }">
                            <v-list-item-avatar
                              size="28"
                              :class="`add_event_cat cat__${item.value}`"
                            >
                              <!--<v-img :src="item.image"></v-img>-->
                              <span class="info__indicator" :style="`background: #${item.color}`"
                                ><svg
                                  version="1.1"
                                  xmlns="http://www.w3.org/2000/svg"
                                  xmlns:xlink="http://www.w3.org/1999/xlink"
                                  x="0px"
                                  y="0px"
                                  width="10px"
                                  height="10px"
                                  viewBox="0 0 122.881 89.842"
                                  enable-background="new 0 0 122.881 89.842"
                                  xml:space="preserve"
                                >
                                  <g>
                                    <path
                                      fill="#FFF"
                                      d="M1.232,55.541c-1.533-1.388-1.652-3.756-0.265-5.289c1.388-1.534,3.756-1.652,5.29-0.265l34.053,30.878l76.099-79.699 c1.429-1.501,3.804-1.561,5.305-0.132c1.502,1.428,1.561,3.803,0.133,5.305L43.223,88.683l-0.005-0.005 c-1.396,1.468-3.716,1.563-5.227,0.196L1.232,55.541L1.232,55.541z"
                                    />
                                  </g></svg>
                              ></span>
                            </v-list-item-avatar>
                            <v-list-item-content>
                              <v-list-item-title>{{ item.text }}</v-list-item-title>
                            </v-list-item-content>
                          </template>
                        </v-select>
                      </v-col>
                      <v-col cols="12">
                        <v-text-field
                          v-model="createForm.title"
                          label="Add Title *"
                          required
                        ></v-text-field>
                      </v-col>

                      <v-col cols="12">
                        <v-select
                          v-model="createForm.home"
                          :items="propertiesList"
                          label="Home *"
                          required
                        ></v-select>
                      </v-col>

                      <v-col cols="12" sm="6" md="6" class="dates-container">
                        <v-menu
                          ref="startDateMenu"
                          v-model="startDateMenu"
                          :close-on-content-click="false"
                          transition="scale-transition"
                          offset-y
                          min-width="290px"
                        >
                          <template #activator="{ on, attrs }">
                            <v-text-field
                              v-model="formattedStartDate"
                              label="Start Date"
                              prepend-icon="mdi-calendar"
                              readonly
                              v-bind="attrs"
                              v-on="on"
                            ></v-text-field>
                          </template>
                          <v-date-picker
                            v-model="createForm.startDate"
                            no-title
                            @input="onStartDateSelected"
                          ></v-date-picker>
                        </v-menu>
                      </v-col>

                      <v-col cols="12" sm="6" md="6" class="dates-container">
                        <v-menu
                          ref="endDateMenu"
                          v-model="endDateMenu"
                          :close-on-content-click="false"
                          transition="scale-transition"
                          offset-y
                          min-width="290px"
                        >
                          <template #activator="{ on, attrs }">
                            <v-text-field
                              v-model="formattedEndDate"
                              label="End Date"
                              prepend-icon="mdi-calendar"
                              readonly
                              v-bind="attrs"
                              v-on="on"
                            ></v-text-field>
                          </template>
                          <v-date-picker
                            v-model="createForm.endDate"
                            :min="createForm.startDate"
                            no-title
                            @input="onEndDateSelected"
                          ></v-date-picker>
                        </v-menu>
                      </v-col>

                      <v-col v-if="showRecurrence" cols="12" sm="6" md="6">
                        <v-select
                          v-model="createForm.recurrence"
                          :items="recurrenceList"
                          label="Recurrence"
                          required
                        ></v-select>
                      </v-col>

                      <v-col cols="12">
                        <v-text-field v-model="createForm.notes" label="Notes"></v-text-field>
                      </v-col>

                      <v-col cols="12">
                        <v-checkbox
                          v-model="createForm.blockDates"
                          class="checkbox-container"
                          label="Block the dates for booking"
                        ></v-checkbox>
                      </v-col>

                      <v-col v-if="showMyCrewOption" cols="12" sm="12" md="12">
                        <lable
                          >To complete this Job, would you like to finalize within MyCrew?</lable
                        >
                        <v-select
                          v-model="createForm.mycrew"
                          :items="mycrewListoptions"
                          required
                        ></v-select>
                      </v-col>

                      <v-col v-if="showBookingLink" cols="12" sm="12" md="12">
                        <lable
                          >To complete this Booking, would you like to send this guest their Booking
                          Link?
                        </lable>
                        <v-select
                          v-model="createForm.bookingLink"
                          :items="bookingLinkOptions"
                          required
                        ></v-select>
                      </v-col>
                    </v-row>
                  </v-container>
                  <small>*indicates required field</small>
                </v-card-text>
                <v-card-actions>
                  <v-spacer></v-spacer>
                  <v-btn class="btn btn-close" text @click="isCreateEventOpen = false">
                    exit
                  </v-btn>
                  <v-btn class="btn btn-save" text @click="submitCreateEventForm"> save </v-btn>
                </v-card-actions>
              </v-card>
            </div>
            <!-- Create Form Ends -->

            <div class="mc--filter">
              <CategoryFilter
                :category-selected="categorySelected"
                :role_id="userInfo.role_id"
                @category-selection="categorySelection"
                @update-create-event-popup="updateCreateEventPopup"
                @close-edit-popup="closeEditPopup"
              ></CategoryFilter>
            </div>

            <div class="d-flex pb-2 justify-space-between justify-md-start sync_calendar">
              <div class="d-flex align-center cursor-pointer mx-0 mx-md-2">
                <v-icon color="primary" small>mdi-tray-arrow-down</v-icon>
                <v-menu v-model="icalmenu" offset-y :close-on-content-click="false">
                  <template #activator="{ on, attrs }">
                    <small
                      v-bind="attrs"
                      style="color: #000000"
                      class="pl-1 font-weight-bold"
                      v-on="on"
                    >
                      Import your calendar
                    </small>
                  </template>
                  <v-list class="px-2 py-4 select_property_import" width="300px">
                    <v-select
                      v-model="selectedICalHomeId"
                      :items="propertiesList"
                      label="Select the Property"
                      class="select_property_import"
                    >
                      <template #selection="{ item }">
                        <v-avatar
                          size="28"
                          class="v-icon notranslate mx-1 mdi mdi-home theme--light grey--text"
                        >
                        </v-avatar>
                        {{ item.text }}
                      </template>
                    </v-select>

                    <v-text-field
                      v-model="iCalName"
                      outlined
                      hide-details
                      label="Calendar Name"
                      placeholder="Add your calendar name here"
                      class="ical_name"
                      clearable
                    />

                    <v-text-field
                      v-model="iCalurl"
                      outlined
                      hide-details
                      label="Calendar Link"
                      class="ical_link"
                      placeholder="Add your calendar link here"
                      clearable
                    />

                    <app-button
                      button-text="Save"
                      block
                      class="mt-3"
                      @click="handleIcal({ iCalurl, iCalName, selectedICalHomeId })"
                    />
                  </v-list>
                </v-menu>
              </div>

              <div class="d-flex align-center cursor-pointer mx-0 mx-md-4">
                <v-icon color="primary" small>mdi-tray-arrow-up</v-icon>
                <v-menu v-model="linkmenu" offset-y right :close-on-content-click="false">
                  <template #activator="{ on, attrs }">
                    <small
                      v-bind="attrs"
                      style="color: #000000"
                      class="pl-1 font-weight-bold"
                      v-on="on"
                    >
                      Add to your calendar
                    </small>
                  </template>
                  <good-card>
                    <v-list>
                      <v-select
                        v-model="selectedICalHomeId"
                        :items="propertiesList"
                        label="Select the Property"
                        class="select_property_export"
                      >
                        <template #selection="{ item }">
                          <v-avatar
                            size="28"
                            class="v-icon notranslate mx-1 mdi mdi-home theme--light grey--text"
                          >
                          </v-avatar>
                          {{ item.text }}
                        </template>
                      </v-select>

                      <!-- Webcal import link -->
                      <v-list-item>
                        <v-list-item-title>
                          <a
                            :href="convertIcsToWebcal()"
                            target="_blank"
                            class="hover:tw-underline"
                          >
                            <small class="tw-text-sm tw-font-medium"> Import to your device </small>
                          </a>
                        </v-list-item-title>
                      </v-list-item>
                      <!--              Ics Link-->
                      <v-list-item>
                        <v-list-item-title>
                          <a class="hover:tw-underline" @click="copyIcsLink()">
                            <small class="tw-text-sm tw-font-medium">
                              Connect to another website
                            </small>
                          </a>
                        </v-list-item-title>
                      </v-list-item>
                    </v-list>
                  </good-card>
                </v-menu>
              </div>
            </div>

            <div v-if="iCalList.length > 0" class="connected_icals">
              <h2>Connected Calendars</h2>
              <ul>
                <li v-for="(item, index) in iCalList" :key="item.id">
                  <h3>{{ item.ical_name }}</h3>
                  <h4>{{ item.title }}</h4>
                  <button @click="deleteIcal(item.id)">Delete</button>
                </li>
              </ul>
            </div>
          </v-sheet>
        </v-col>
        <v-col sm="12" md="9" class="py-0">
          <v-sheet class="custom-calendar mc-calendar">
            <client-only>
              <FullCalendar ref="fullCalendar" :options="calendarOptions"> </FullCalendar>
            </client-only>
            

            <div v-if="showEventSummary" :style="eventSummaryStyle" class="event-popup">
              <ul class="actions">
                <!-- <li>
                  <button
                    data-v-7408e29e=""
                    aria-hidden="true"
                    class="v-icon notranslate mx-1 mdi mdi-pencil theme--light grey--text"
                    style="font-size: 16px"
                    @click="editEvent()"
                  ></button>
                </li> -->
                <li>
                  <button
                    data-v-7408e29e=""
                    aria-hidden="true"
                    class="v-icon notranslate mx-1 mdi mdi-delete theme--light grey--text"
                    style="font-size: 16px"
                    @click="deleteEvent(selectedEvent.id)"
                  ></button>
                </li>
                <li>
                  <button
                    data-v-7408e29e=""
                    aria-hidden="true"
                    class="v-icon notranslate mx-1 mdi mdi-close theme--light grey--text"
                    style="font-size: 16px"
                    @click="closeEditPopup()"
                  ></button>
                </li>
              </ul>
              <h3>{{ selectedEvent.title }}</h3>
              <ul class="info_list">
                <li class="category">
                  <span
                    class="info__indicator"
                    :style="`background: ${selectedEvent.backgroundColor};`"
                    ><svg
                      version="1.1"
                      xmlns="http://www.w3.org/2000/svg"
                      xmlns:xlink="http://www.w3.org/1999/xlink"
                      x="0px"
                      y="0px"
                      width="10px"
                      height="10px"
                      viewBox="0 0 122.881 89.842"
                      enable-background="new 0 0 122.881 89.842"
                      xml:space="preserve"
                    >
                      <g>
                        <path
                          fill="#FFF"
                          d="M1.232,55.541c-1.533-1.388-1.652-3.756-0.265-5.289c1.388-1.534,3.756-1.652,5.29-0.265l34.053,30.878l76.099-79.699 c1.429-1.501,3.804-1.561,5.305-0.132c1.502,1.428,1.561,3.803,0.133,5.305L43.223,88.683l-0.005-0.005 c-1.396,1.468-3.716,1.563-5.227,0.196L1.232,55.541L1.232,55.541z"
                        />
                      </g></svg
                  ></span>
                  {{ selectedEvent.extendedProps.category }}
                </li>
                <li class="dates">
                  <i
                    data-v-7408e29e=""
                    aria-hidden="true"
                    class="v-icon notranslate mx-1 mdi mdi-calendar theme--light grey--text"
                  ></i>
                  {{ formattedEventPopupStartDate() }}
                  {{
                    formattedEventPopupStartDate() != formattedEventPopupEndDate()
                      ? ' - ' + formattedEventPopupEndDate()
                      : ''
                  }}
                </li>
                <li class="home">
                  <i
                    data-v-7408e29e=""
                    aria-hidden="true"
                    class="v-icon notranslate mx-1 mdi mdi-home theme--light grey--text"
                  ></i>
                  {{ selectedEvent.extendedProps.home_name }}
                </li>
              </ul>
            </div>
          </v-sheet>
        </v-col>
      </v-row>
    </div>
  </v-col>
</template>

<style>
.event-popup {
  position: absolute;
  width: 320px;

  background: #ffffff;
  border: 1px solid rgba(133, 133, 133, 0.5);
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
  border-radius: 11px;
  z-index: 10;

  padding: 15px 15px 10px 20px;
}

.event-popup h3 {
  color: #858585;
  font-weight: 600;
  font-size: 16px;
  margin-top: 5px;
}

.event-popup ul.actions {
  float: right;
  display: flex;
  margin-top: -10px;
  margin-right: -10px;
}

.event-popup ul.actions .v-icon.v-icon.grey--text:hover {
  color: #000000 !important;
}

.event-popup .info_list {
  margin: 15px 0px 0px;
  padding: 0px;
}

.event-popup .info_list li {
  display: flex;
  font-size: 14px;
  margin-bottom: 15px;
  color: #858585;
}

.event-popup .info_list li i {
  font-size: 20px;
  margin-right: 10px !important;
}

.event-popup .info_list .info__indicator {
  border-radius: 3px;
  padding: 3px;
  height: 16px;
  margin-right: 12px;
  margin-left: 6px;
}

.calendar-header {
  background: #c0c0c012;
  position: relative;
}

.calendar-header .heading {
  color: #858585;
  font-size: 2em;
  padding: 1.2em 0px 0px;
  letter-spacing: 1.2px;
  font-weight: 300;
}

.calendar-header__subtitle {
  color: #858585;
  font-size: 1.5em;
  font-weight: 300;
}

.calendar-header .heading strong {
  font-weight: 500;
}

.mc-thumb-item .small-description {
  font-weight: 500;
  font-size: 1rem !important;
  line-height: 1.55rem !important;
}

.mycalendar-container {
  padding: 50px 0px 50px;
}

.mycalendar-container h4 {
  color: #858585;
  font-size: 2em;
  margin-bottom: 25px;
}
.mycalendar-container .filter_container {
  display: flex;
  justify-content: space-between;
  margin-bottom: 60px;
}

.mc--filter {
  background: #85858514;
  padding: 15px;
  border-radius: 10px;
  color: #858585;
  box-shadow: 0px 4px 4px rgb(0 0 0 / 19%);
  border: 1px solid #85858521;
}
.mc--filter ul {
  margin: 0px;
  padding: 0px !important;
}
.mc--filter li {
  border: 1px solid rgba(133, 133, 133, 0.12941);
  background: #fff;
  font-size: 1.2em;
  margin: 0px 0px 10px 0px;
  border-radius: 10px;
  box-shadow: 0px 3px 3px #0000003b;
}
.mc--filter li label {
  display: flex;
  padding: 10px 15px;
  justify-content: space-between;
  position: relative;
  cursor: pointer;
  font-size: 16px;
}

.mc--filter li label:hover {
  color: #000000;
}

.mc--filter li input[type='checkbox'] {
  appearance: none;
  -webkit-appearance: none;
  display: flex;
  align-content: center;
  justify-content: center;
  font-size: 2rem;
  padding: 0.1rem;
  border-radius: 10px;
}

.mc--filter li input[type='checkbox']::before {
  content: '';
  width: 1rem;
  height: 1rem;
  clip-path: polygon(
    5.55% 56.4%,
    30.8% 81.92%,
    92.79% 20.59%,
    99.79% 27.67%,
    30.8% 95.93%,
    0% 64.06%
  );
  background-color: #000000;
  transform: scale(0);
  background-color: #fff;
  position: relative;
  z-index: 5;
  right: 3px;
  top: 2px;
}

.mc--filter li input[type='checkbox']:checked::before {
  transform: scale(1);
}

.mc--filter li .control__indicator {
  position: absolute;
  top: 10px;
  right: 15px;
  height: 25px;
  width: 25px;
  background: #e6e6e6;
  border-radius: 6px;
}
.mc--filter li input ~ .control__indicator {
  opacity: 0.5;
}
.mc--filter li input:checked ~ .control__indicator {
  opacity: 1;
}

/* .mc--filter .tag-owner_use input ~ .control__indicator{ background: #B13DBC; }
.mc--filter .tag-public_booking input ~ .control__indicator{ background: #C48A41; }
.mc--filter .tag-swaps input ~ .control__indicator{ background: #63963D; }
.mc--filter .tag-mycrew_jobs input ~ .control__indicator{ background: #3E49CB; }
.mc--filter .tag-blocked_dates input ~ .control__indicator{ background: #CD6D6D; }
.mc--filter .tag-friends_family input ~ .control__indicator{ background: #562F91; }
.mc--filter .tag-ical_events input ~ .control__indicator{ background: #666666; } */

.checkbox-container .v-input__slot {
  padding: 8px 5px !important;
}

.mc-calendar .v-calendar-weekly__day-label {
  padding-bottom: 80px;
}
.mc-calendar .v-calendar-weekly__day-label .v-btn {
  font-size: 16px;
}

.mc-calendar a {
  color: #353535 !important;
}
.mc-calendar th {
  text-align: left;
  border-left: 0px;
  border-right: 0px;
}
.mc-calendar table {
  border-top: 0px;
}
.fc-scrollgrid.fc-scrollgrid-liquid {
  border: 0px;
}
.mc-calendar .fc-scroller-harness-liquid {
  border-left: 1px solid var(--fc-border-color);
}
.mc-calendar .fc-view-harness {
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.24314);
  border-radius: 10px;
  overflow: hidden;
  border: 1px solid var(--fc-border-color);
  border-bottom: 0px;
}
.mc-calendar .fc .fc-daygrid-day-number {
  padding: 5px 10px;
  position: relative;
  z-index: 4;
}
.mc-calendar .fc-today-button {
  display: none;
}
.mc-calendar .fc .fc-button-primary {
  background: none;
  border: 0px;
  color: #858585;
}
.mc-calendar .fc .fc-button .fc-icon {
  margin-top: -5px;
}
.mc-calendar .fc-h-event .fc-event-main {
  padding: 2px 10px;
}
.mc-calendar .fc-event {
  box-shadow: 0px 4px 4px #00000043;
  border-radius: 25px;
  border: 0px;
}

.mc-calendar .v-calendar .v-event {
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.26275);
  border-radius: 25px;
  padding-left: 8px;
  font-size: 14px;
  height: 24px !important;
  line-height: 24px;
}

.filter_container > div {
  box-shadow: 0px 3px 4px rgb(0 0 0 / 18%);
  border: 1px solid rgba(133, 133, 133, 0.50196);
  border-radius: 8px;
  display: flex;
  align-items: center;
  height: 35px;
  overflow: hidden;
}
.filter_container .filter_by label {
  padding: 5px 15px;
  border-right: 1px solid rgba(133, 133, 133, 0.50196);
  color: #858585;
  margin-right: 15px;
  width: 110px;
}
.filter_container .v-input__slot:before {
  display: none;
}
.filter_container .view_as {
  padding: 5px 15px;
}
.filter_container .v-text-field.v-input--dense {
  padding-top: 8px;
}
.filter_container .dates_range .col-md-6:first-child {
  border-right: 1px solid rgba(133, 133, 133, 0.50196);
  overflow: hidden;
}
.filter_container .filter_by {
  width: 30%;
}
.filter_container .dates_range {
  width: 52%;
}
.filter_container .view_as {
  width: 15%;
}
/*.mc-calendar .fc-header-toolbar{
    display: block;
    text-align: center;
    color: #858585;
}
.mc-calendar .fc-header-toolbar .fc-button-group{
    position: relative;
    margin-top: -75px;
}*/

.mc-nav-container {
  position: absolute;
  right: 20px;
  top: 20px;
  display: flex;
}
.mc-nav-container > button {
  background-color: #6c6b6b78;
  padding: 3px 11px;
  border-radius: 11px;
  display: flex;
  color: #fff;
  align-items: center;
  margin-left: 10px;
  gap: 5px;
  transition: all 0.2s;
  box-shadow: 0px 4px 4px #00000055;
}
.mc-nav-container > button:last-child img {
  -webkit-transform: scaleX(-1);
  transform: scaleX(-1);
}
.mc-nav-container > button:hover {
  background: #000000;
}

.filter_container .v-input__slot {
  margin-bottom: 3px;
}

.fc .fc-highlight {
  background-color: #8585854d;
}

.select_property_export {
  padding: 0px 10px;
}

.mc-add-event .v-input__slot,
.select_property_import .v-input__slot,
.select_property_export .v-input__slot,
.import_form_container .v-input__slot {
  box-shadow: 0px 2px 4px rgb(0 0 0 / 29%);
  border: 1px solid #85858512;
  border-radius: 7px !important;
  padding: 2px 0px 2px 10px;
}

.mc-add-event .v-input__slot:before,
.select_property_import .v-input__slot:before,
.select_property_export .v-input__slot:before {
  display: none;
}

.ical_name {
  margin-bottom: 20px !important;
}

.select_property_import fieldset {
  border: 0px !important;
}

.mc-add-event .v-text-field {
  padding-top: 5px;
}

.mc-add-event .col-md-6 {
  padding-top: 5px;
}

.mc-add-event {
  /*padding-top: 35px;*/
  padding-bottom: 15px;
}

.mc-add-event .col {
  padding-top: 0px;
  padding-bottom: 0px;
}

.mc-add-event .v-text-field .v-label--active {
  transform: translateY(-25px) scale(0.75);
}

.mc-add-event .btn {
  background: #ffffff;
  border: 1px solid rgba(124, 12, 177, 0.1);
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
  border-radius: 9px;
  text-transform: none;
  height: 30px !important;
  color: #7c0cb1 !important;
}

.mc-add-event .btn.btn-save {
  background: #7c0cb1;
  color: #fff;
}

.mc-add-event .btn.btn-save .v-btn__content {
  color: #fff;
}

.mc-add-event .v-input__prepend-outer {
  margin-top: 8px;
  margin-left: 7px;
  position: absolute;
  z-index: 5;
}

.mc-add-event .dates-container .v-input__slot {
  padding-left: 40px;
}

.add_btn_container {
  text-align: center;
}
.add_btn_container button {
  background: #7c0cb1;
  color: #fff;
  font-weight: bold;
  text-align: center;
  padding: 10px 18px;
  border-radius: 45px;
  margin-bottom: 20px;
  margin-top: 10px;
  transition: all 0.2s;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
}
.add_btn_container button:hover {
  background: #500871;
}
.add_btn_container button i {
  color: #fff !important;
}

.add_event_cat.v-avatar {
  border-radius: 0px;
  margin-right: 0px;
}

.add_event_cat .info__indicator {
  height: 20px;
  width: 20px;
  background: #e6e6e6;
  border-radius: 3px;
  padding: 2px 0px 0px 3px;
}

.add_event_cat .info__indicator svg {
  width: 15px;
  margin-top: -4px;
  margin-left: -2px;
}

.cat__owner_use .info__indicator {
  background: #b13dbc;
}
.cat__friends_family .info__indicator {
  background: #c48a41;
}
.cat__public_booking .info__indicator {
  background: #63963d;
}
.cat__swaps .info__indicator {
  background: #3e49cb;
}
.cat__mycrew_jobs .info__indicator {
  background: #cd6d6d;
}
.cat__blocked_dates .info__indicator {
  background: #562f91;
}
.cat__ical_events .info__indicator {
  background: #666666;
}

.mc-sidebar {
  position: relative;
}

.mc-sidebar .theme--light.v-card {
  background: none;
}

.mc-sidebar .mc-add-event {
  position: absolute;
  right: -440px;
  width: 430px;
  top: 0px;
  max-width: unset;
  z-index: 5;
  background: #f6f6f6 !important;
}

.mc-sidebar .v-input__slot {
  background: #fff;
}

.fc .fc-toolbar {
  justify-content: left;
}

.sync_calendar {
  padding-top: 20px;
}

.sync_calendar small {
  font-size: 11px;
}

.connected_icals {
  border-top: 1px solid #85858580;
  padding: 15px 0px;
  margin-top: 15px;
}

.connected_icals h2 {
  font-size: 16px;
}

.connected_icals ul {
  border: 1px solid #85858580;
  border-radius: 25px;
  margin: 15px 0px;
  padding: 0px !important;
}

.connected_icals ul li {
  position: relative;
  padding: 15px 20px;
  border-bottom: 1px solid rgba(133, 133, 133, 0.50196);
}
.connected_icals ul li:last-child {
  border-bottom: 0px;
}

.connected_icals ul li h3 {
  font-size: 14px;
}
.connected_icals ul li h4 {
  font-size: 12px;
  margin: 0px;
}
.connected_icals ul li button {
  position: absolute;
  right: 20px;
  top: 25px;
  font-size: 12px;
  color: red;
}

.ppt-container {
  max-width: 500px;
  background: linear-gradient(90deg, #ffffff 21%, rgba(245, 245, 245, 0.5) 100%);
  border: 1px solid rgba(133, 133, 133, 0.1);
  box-shadow:
    0px 1px 4px rgba(0, 0, 0, 0.25),
    0px 4px 4px rgba(0, 0, 0, 0.25);
  border-radius: 18px;
  margin-left: 0px !important;
  margin-bottom: 30px !important;
  padding: 12px;
}

.ppt-container h3 {
  font-weight: 500;
  font-size: 20px;
  line-height: 20px;
  color: #6c6b6b;
}

.ppt-container h4 {
  font-weight: 400;
  font-size: 16px;
  line-height: 30px;
  color: #6c6b6b;
  margin-bottom: 10px;
}

.ppt-container__info {
  padding-left: 0px !important;
  margin-left: 0px;
  color: #858585;
  font-size: 13px;
}

.ppt-container__info strong {
  font-weight: 600;
}

.ppt-container img {
  border-radius: 14px;
}

@media screen and (min-width: 300px) and (max-width: 600px) {
  .mc-nav-container {
    position: static;
    width: 100%;
    justify-content: center;
  }
  .mc-nav-container > button {
    font-size: 13px;
  }
  .mc-nav-container > button img {
    width: 18px;
  }
  .calendar-header .heading {
    font-size: 1.2em;
  }
  .calendar-header__subtitle {
    font-size: 0.9em;
    margin-top: 5px;
  }
  .dashboard-data-container {
    padding: 9px;
    overflow-x: auto;
  }
  .dashboard-data-container > div {
    min-width: 160px !important;
    margin-right: 15px;
  }
  .mycalendar-container .filter_container {
    display: block;
    width: 100%;
  }
  .mycalendar-container .filter_container > div {
    width: 100%;
    margin-bottom: 5px;
  }
  .mycalendar-container > .row.filter_parent_container {
    display: block;
  }
  .cc-row > div:first-child {
    order: 2;
  }
  .cc-row > div:last-child {
    order: 1;
  }
  .custom-calendar {
    margin-bottom: 25px;
  }
  .mc-calendar .fc-view-harness {
    min-height: 360px;
  }
  .mc-sidebar .mc-add-event {
    position: static;
    width: 100%;
  }
}
</style>
