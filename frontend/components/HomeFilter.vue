<script lang="ts">
// @ts-nocheck

import { defineComponent } from '@nuxtjs/composition-api'


import AppDateRangePicker from '~/components/AppDateRangePicker.vue'
import { parseAddress, unParseAddress } from '~/helpers'
import GoodButtonIncreaseNumber from '~/components/GoodButtonIncreaseNumber.vue'
import screenCheckerMixins from '~/mixins/screenChecker.mixins'

import FilterModal from './FilterModal.vue'
import OpenStreetAutocomplete from './OpenStreetAutocomplete.vue'


export default defineComponent({
  name: 'HomeFilter',

  components: {
    AppDateRangePicker,
    OpenStreetAutocomplete,
    GoodButtonIncreaseNumber,
    FilterModal,
  },

  mixins: [screenCheckerMixins],

  props: {
    serverLoaded: {
      type: Boolean,
      default: false,
    },
    homeIds: {
      type: Array,
      default: () => [],
    },
    userLoggedIn: {
      type: Boolean,
      default: () => {},
    },
  },

  data: () => ({
    searched: false,
    extraText: '',
    serverLoadedInside: false,
    showSearchForm: false,

    filterData: {
      selectedLocation: null,
      selectedLocationData: null,
      startDate: null,
      endDate: null,
      selectedBookingTypes: [],
      rentalType: null,
      featured: false,
      recommended: false,
      skiLease: false,
      seasonalLease: false,
      searchButton: false,

      guests: 0,
      pets: 0,
      rentals: 1,
      shortTermRentals: 1,
      swaps: 1,

      typeOfSpace: null,
      minPrice: null,
      maxPrice: null,
      bedrooms: 0,
      beds: 0,
      bathrooms: 0,
      petFriendly: null,
      petType: null,
    },
  }),

  watch: {
    filterData: {
      handler: function (val) {
        if (this.serverLoadedInside) {
          this.serverLoadedInside = false
          return
        }

        const {
          startDate,
          endDate,
          selectedLocation,
          selectedLocationData,
          featured,
          recommended,
          skiLease,
          seasonalLease,
          guests,
          pets,
          rentals,
          shortTermRentals,
          swaps,
          typeOfSpace,
          minPrice,
          maxPrice,
          bedrooms,
          beds,
          bathrooms,
          petFriendly,
          petType,
        } = val

        const parsedSelectedLocation = selectedLocation ? parseAddress(selectedLocation) : undefined

        const query = {
          page: 1,
          per_page: 12,
          country: selectedLocation ? selectedLocationData.country : undefined,
          state: selectedLocation ? selectedLocationData.state : undefined,
          // Only include city if it's defined and not the same as state (to avoid filtering by both state and city for state searches)
          city: selectedLocation && selectedLocationData.city ? selectedLocationData.city : undefined,
          start: startDate && endDate ? startDate : undefined,
          end: startDate && endDate ? endDate : undefined,
          featured: featured ? 1 : undefined,
          recommended: recommended ? 1 : undefined,
          ski_lease: skiLease ? 1 : undefined,
          seasonal_lease: seasonalLease ? 1 : undefined,
          guests: guests || undefined,
          pets: pets || undefined,
          allow_booking: rentals ? 1 : 0,
          allow_swaps: swaps ? 1 : 0,
          type_of_space: typeOfSpace || undefined,
          min_price: minPrice || undefined,
          max_price: maxPrice || undefined,
          bedrooms: bedrooms || undefined,
          beds: beds || undefined,
          bathrooms: bathrooms || undefined,
          petFriendly: petFriendly || undefined,
          petType: petType || undefined,
          short_term_rentals: shortTermRentals ? 1 : 0,
        } as any

        // this.$router.push({ query })
      },
      deep: true,
    },

    userLoggedIn: {
      handler: function (val) {
        this.filterData.swaps = val ? 1 : 0
      },
    },
  },

  mounted() {
    this.serverLoadedInside = this.serverLoaded
    this.initDataFromQuery()
  },

  methods: {
    initDataFromQuery() {
      const {
        country,
        state,
        city,
        allow_booking,
        allow_swaps,
        allow_points,
        start,
        end,
        featured,
        recommended,
        ski_lease,
        seasonal_lease,
        guests,
        pets,
        type_of_space,
        min_price,
        max_price,
        bedrooms,
        beds,
        bathrooms,
        petFriendly,
        petType,
        short_term_rentals,
      } = this.$route.query

      let startDate = start
      let endDate = end

      if (!start || !end) {
        startDate = null
        endDate = null
      }

      const selectedLocation = unParseAddress(country, state, city) || null
      const selectedLocationData = {
        country: country || '',
        state: state || '',
        city: city || ''
      }

      // Set the filter data first
      this.filterData = {
        selectedLocation,
        selectedLocationData,
        startDate,
        endDate,
        guests: guests || 0,
        pets: pets || 0,
        rentals: allow_booking === '1' || allow_booking === undefined ? 1 : 0,
        swaps: allow_swaps === '1' || allow_swaps === undefined ? 1 : 0,
        selectedBookingTypes: [
          allow_booking ? 'book' : '',
          allow_swaps ? 'swap' : '',
          allow_points ? 'points' : '',
        ].filter(Boolean),
        featured: !!featured,
        recommended: !!recommended,
        skiLease: !!ski_lease,
        seasonalLease: !!seasonal_lease,
        shortTermRentals: short_term_rentals === '1' || short_term_rentals === undefined ? 1 : 0,
        typeOfSpace: type_of_space || 0,
        minPrice: min_price || 0,
        maxPrice: max_price || 0,
        bedrooms: bedrooms || 0,
        beds: beds || 0,
        bathrooms: bathrooms || 0,
        petFriendly: petFriendly || 0,
        petType: petType || 0,
      }

      // Then try to set the dates in the date picker if both dates exist
      if (startDate && endDate && this.$refs.datepicker?.setData) {
        this.$nextTick(() => {
          this.$refs.datepicker.setData([{ startDate, endDate }])
        })
      }
    },

    handleDates(data: { startDate: any; endDate: any }) {
      this.filterData.startDate = data.startDate
      this.filterData.endDate = data.endDate
    },

    setAddress(val: string, locationData: object) {
      this.searched = true
      this.filterData.selectedLocation = val;

      // We don't need to set selectedLocationData here anymore
      // as it's already set in handleAddressSelection

      // Just set the extraText for UI purposes
      if (val) {
        this.extraText = 'in'
        // Set the address in the OpenStreetAutocomplete component
        if (this.$refs.finder) {
          // For OpenStreetAutocomplete, we just need to update its address prop
          // The component will handle the rest internally
        }
      } else {
        this.extraText = ''
      }

      // Log the current state for debugging
      console.log('Current selectedLocationData in setAddress:', this.filterData.selectedLocationData);
    },

    datesCleared() {
      this.setAddress('', {})
      this.filterData.startDate = null
      this.filterData.endDate = null
    },

    clearSearch() {
				this.$refs.datepicker.clearDates();
				this.setAddress('', {})
				this.filterData.startDate = null
				this.filterData.endDate = null
				setTimeout(() => { this.$router.push('/explore'); }, 500);
		},

    searchByFilter() {
      // this.filterData.searchButton = true
      if (this.serverLoadedInside) {
          this.serverLoadedInside = false
          return
        }

        const {
          startDate,
          endDate,
          selectedLocation,
          selectedLocationData,
          featured,
          recommended,
          skiLease,
          seasonalLease,
          guests,
          pets,
          rentals,
          shortTermRentals,
          swaps,
          typeOfSpace,
          minPrice,
          maxPrice,
          bedrooms,
          beds,
          bathrooms,
          petFriendly,
          petType,
        } = this.filterData

        const parsedSelectedLocation = selectedLocation ? parseAddress(selectedLocation) : undefined

        // Log the data we're about to use for search
        console.log('Search data:', {
          selectedLocation,
          selectedLocationData,
          parsedSelectedLocation
        });

        const query = {
          page: 1,
          per_page: 12,
          country: selectedLocation ? selectedLocationData.country : undefined,
          state: selectedLocation ? selectedLocationData.state : undefined,
          // Only include city if it's defined and not the same as state (to avoid filtering by both state and city for state searches)
          city: selectedLocation && selectedLocationData.city ? selectedLocationData.city : undefined,
          start: startDate && endDate ? startDate : undefined,
          end: startDate && endDate ? endDate : undefined,
          featured: featured ? 1 : undefined,
          recommended: recommended ? 1 : undefined,
          ski_lease: skiLease ? 1 : undefined,
          seasonal_lease: seasonalLease ? 1 : undefined,
          guests: guests || undefined,
          pets: pets || undefined,
          allow_booking: rentals ? 1 : 0,
          allow_swaps: swaps ? 1 : 0,
          type_of_space: typeOfSpace || undefined,
          min_price: minPrice || undefined,
          max_price: maxPrice || undefined,
          bedrooms: bedrooms || undefined,
          beds: beds || undefined,
          bathrooms: bathrooms || undefined,
          petFriendly: petFriendly || undefined,
          petType: petType || undefined,
          short_term_rentals: shortTermRentals ? 1 : 0,
        } as any

        // Log the final query for debugging
        console.log('Final query:', query);

        this.$router.push({ query })
    },

    toggleFilterModal() {
      this.$refs.FilterModal.openDialog()
    },

    toggleSearchForm() {
      this.showSearchForm = !this.showSearchForm
    },

    updateBedrooms(newValue) {
      this.filterData.bedrooms = newValue
    },

    updateTypeOfSpace(newValue) {
      this.filterData.typeOfSpace = newValue
    },

    updateFilter(value) {
      this.filterData.bedrooms = value.bedrooms
      this.filterData.typeOfSpace = value.typeOfSpace
      this.filterData.bathrooms = value.bathrooms
      this.filterData.beds = value.beds
      this.filterData.minPrice = value.minPrice
      this.filterData.maxPrice = value.maxPrice
      this.filterData.petFriendly = value.petFriendly
      this.filterData.petType = value.petType
      this.filterData.pets = value.pets
    },

    handleAddressSelection(address, locationData) {
      this.filterData.selectedLocation = address

      // Parse the address to extract city, state, country
      if (address) {
        // Use our parseAddress function to extract components from the formatted address
        const parsedAddress = parseAddress(address)
        console.log('Parsed address:', parsedAddress)

        // Make sure we have address_components
        if (locationData && locationData.address_components) {
          // The address components should already be processed by the OpenStreetAutocomplete component
          const components = locationData.address_components

          // Log the components for debugging
          console.log('Address components from locationData:', components)

          // Check if this is a state-only search
          const isStateSearch = locationData.location_type === 'administrative' &&
                               components.state &&
                               (!components.city || components.city === components.state)

          // Create a new object with the parsed address components
          this.filterData.selectedLocationData = {
            country: parsedAddress.country || components.country || '',
            state: parsedAddress.state || components.state || '',
            // For state searches, don't set the city parameter to avoid filtering by city in the backend
            city: isStateSearch ? undefined : (parsedAddress.city || components.city || '')
          }

          console.log('Selected location data set to:', this.filterData.selectedLocationData)
        } else {
          // If we don't have address_components, use the parsed address
          // Check if this might be a state-only search (when state and city are the same)
          const isLikelyStateSearch = parsedAddress.state &&
                                     parsedAddress.city &&
                                     parsedAddress.state === parsedAddress.city

          this.filterData.selectedLocationData = {
            country: parsedAddress.country || '',
            state: parsedAddress.state || '',
            // For likely state searches, don't set the city parameter
            city: isLikelyStateSearch ? undefined : (parsedAddress.city || '')
          }

          console.log('Selected location data set from parsed address:', this.filterData.selectedLocationData)
        }
      } else {
        // Clear the location data if address is empty
        this.filterData.selectedLocationData = {
          country: '',
          state: '',
          city: ''
        }
      }

      this.setAddress(address, locationData)

      // Store location data if needed for map functionality
      if (locationData) {
        // Store coordinates for map functionality if needed
        if (this.mapData) {
          this.mapData.searchedLocation = [
            {
              lat: locationData.lat,
              lng: locationData.lng,
            },
          ]
        }
      }

      // Trigger search immediately if we have a location
      if (address) {
        this.searchByFilter()
      }
    },
  },
})
</script>

<template>
  <v-row>
    <v-col
      cols="12"
      class="tw-flex tw-flex-col md:tw-flex-row tw-align-middle tw-items-center tw-pl-[1.5rem]"
    >
      <v-row class="tw-w-full tw-mb-0 tw-block md:tw-hidden">
        <v-col cols="12" class="tw-text-center tw-mt-0">
          <v-btn
            class="tw-normal-case tw-tracking-normal tw-rounded-xl tw-font-semibold tw-text-xl v-btn v-btn--is-elevated tw-bg-white tw-pt-3 tw-pb-3 tw-h-[55px] tw-mb-[0px] tw-w-full tw-gap-[5px] tw-align-middle tw-justify-center btnGoAnywhere"
            @click="toggleSearchForm()"
          >
            <v-icon class="tw-text-3xl">mdi-magnify</v-icon>
            go anywhere
          </v-btn>
        </v-col>
      </v-row>

      <v-row v-if="isDesktop || showSearchForm">
        <v-col cols="12">
          <v-row class="tw-flex-col md:tw-flex-row">
            <v-col cols="12" md="11">
              <v-row class="searchFormFieldContainer">
                <v-col class="tw-relative tw-pl-5 tw-pr-5" cols="12" md="4">
                  <!--      SEARCH     -->
                  <OpenStreetAutocomplete
                    ref="finder"
                    :label="null"
                    :input-border="false"
                    :helper-text="null"
                    :address="filterData.selectedLocation"
                    placeholder="Anywhere"
                    class="input-shadow"
                    @address-selection="handleAddressSelection"
                  />
                </v-col>

                <v-col cols="12" :md="4" class="tw-pl-3 tw-pr-3 md:tw-pl-5 md:tw-pr-5 tw-relative filter-page-datepicker">
                  <AppDateRangePicker
                    ref="datepicker"
                    :parent-classes="$vuetify.breakpoint.mdAndUp ? 'tw-h-[40px]' : 'tw-h-[40px]'"
                    width="100%"
                    :show-default-slot="false"
                    :show-clear-dates="true"
                    :prop-dates="{
                      startDate: filterData.startDate,
                      endDate: filterData.endDate,
                    }"
                    :menu-attach="true"
                    opens="right"
                    :button-color="filterData.startDate && filterData.endDate ? '#000000' : 'black'"
                    date-icon="mdi-calendar-month-outline"
                    class="input-shadow tw-rounded-full"
                    @selected-dates="handleDates"
                    @handle-date-clear="datesCleared"
                  />
                </v-col>
                <v-col
                  class="tw-relative tw-pl-5 tw-pr-5 tw-pt-2 tw-text-center explore_sec"
                  cols="12"
                  md="2"
                >
                  <v-menu offset-y :close-on-content-click="false">
                    <template #activator="{ on, attrs }">
                      <v-btn
                        v-bind="attrs"
                        outlined
                        text
                        rounded
                        class="tw-border tw-border-gray-300 tw-normal-case tw-tracking-normal tw-text-base tw-w-full tw-justify-start lg:tw-justify-center"
                        v-on="on"
                      >
                        <v-icon color="#7C0CB1">mdi-account</v-icon>
                        <span class="tw-pl-[8px]">
                          {{
                            filterData.guests == 0
                              ? 'Add Guests'
                              : filterData.guests != 0
                                ? filterData.guests + ' Guest(s) '
                                : ''
                          }}
                        </span>
                        <!-- && filterData.pets == 0     + ((filterData.pets != 0)? filterData.pets+ ' Pet(s)' : '') -->
                      </v-btn>
                    </template>
                    <good-card card-text-classes="tw-p-3">
                      <div class="tw-flex tw-flex-col tw-items-left tw-mt-0">
                        <div class="tw-flex tw-flex-row tw-pt-1 tw-pb-1">
                          <span class="tw-w-1/2 tw-text-sm tw-py-[5px]"> Guests </span>
                          <GoodButtonIncreaseNumber
                            class="tw-w-1/2 tw-text-sm tw-py-[5px]"
                            :value="filterData.guests"
                            @decrease="filterData.guests > 0 ? filterData.guests-- : 0"
                            @increase="filterData.guests++"
                          ></GoodButtonIncreaseNumber>
                        </div>
                      </div>
                    </good-card>
                  </v-menu>
                </v-col>
                <v-col
                  class="tw-relative tw-pl-5 tw-pr-5 tw-pt-2 tw-text-center explore_sec"
                  cols="12"
                  md="2"
                >
                  <v-menu offset-y :close-on-content-click="false">
                    <template #activator="{ on, attrs }">
                      <v-btn
                        v-bind="attrs"
                        outlined
                        text
                        rounded
                        class="tw-border tw-border-gray-300 tw-normal-case tw-tracking-normal tw-text-base tw-w-full tw-justify-start lg:tw-justify-center"
                        v-on="on"
                      >
                        <v-icon color="#7C0CB1">mdi-home</v-icon>
                        <span class="tw-pl-[8px]">Rental Type</span>
                      </v-btn>
                    </template>
                    <good-card card-text-classes="tw-p-3 tw-z-10">
                      <div class="tw-flex tw-flex-col tw-items-left tw-mt-0">
                        <div
                          class="tw-flex tw-flex-row tw-items-center tw-pt-1 tw-pb-1 tw-border-b tw-border-[#0000002E]"
                        >
                          <span class="tw-w-3/4 tw-text-sm tw-py-[5px] tw-mr-[5px]">
                            Short Term Rentals
                          </span>
                          <v-switch
                            v-model="filterData.shortTermRentals"
                            color="primary"
                            dense
                            class="tw-m-0 tw-p-0"
                            hide-details
                          />
                        </div>

                        <div
                          class="tw-flex tw-flex-row tw-pt-1 tw-pb-1 tw-items-center tw-border-b tw-border-[#0000002E]"
                        >
                          <span class="tw-w-3/4 tw-text-sm tw-py-[5px] tw-mr-[5px]">
                            Long Term Rentals <br />
                            <span class="tw-w-1/2 tw-text-[11px] tw-py-[5px]"> 30 day+ </span>
                          </span>
                          <v-switch
                            v-model="filterData.rentals"
                            color="primary"
                            dense
                            class="tw-m-0 tw-p-0"
                            hide-details
                          />
                        </div>

                        <div
                          class="tw-flex tw-flex-row tw-items-center tw-pt-1 tw-pb-1 tw-border-b tw-border-[#0000002E]"
                        >
                          <span class="tw-w-3/4 tw-text-sm tw-py-[5px] tw-mr-[5px]">
                            Home Swaps<br />
                            <span
                              class="tw-w-1/2 tw-text-[11px] tw-py-[5px] tw-leading-[13px] tw-block tw-w-full"
                            >
                              only available to active <br />Twimo Hosts
                            </span>
                          </span>
                          <v-switch
                            v-model="filterData.swaps"
                            color="primary"
                            dense
                            class="tw-m-0 tw-p-0"
                            hide-details
                          />
                        </div>

                        <div class="tw-flex tw-flex-row tw-pt-1 tw-pb-1">
                          <span class="tw-w-2/2 tw-text-sm tw-py-[5px]">
                            Private Homes<br />
                            <span
                              class="tw-w-1/2 tw-text-[11px] tw-py-[5px] tw-leading-[13px] tw-block tw-w-full"
                            >
                              private homes are password protected and<br />
                              unable to be searched
                            </span>
                          </span>
                        </div>
                      </div>
                    </good-card>
                  </v-menu>
                </v-col>
              </v-row>
            </v-col>
            <v-col
              class="tw-pt-5 md:tw-pt-0 tw-text-right tw-col-12 md:tw-col-1 tw-flex md:tw-block  tw-justify-center"
            >
              <!--<v-btn
                class="tw-normal-case tw-tracking-normal tw-rounded-xl tw-font-semibold tw-text-2xl sm:tw-w-1/3 v-btn v-btn--is-elevated tw-bg-white tw-pt-3 tw-pb-3 tw-mr-3 tw-h-[55px] tw-mb-[10px] lg:tw-mb-0"
                @click="toggleFilterModal()"
              >
                <img src="../assets/filter-icon.png" class="tw-max-h-[35px]" />
                <span class="tw-block md:tw-hidden">Filter</span>
              </v-btn>-->

              <v-btn
                class="tw-normal-case tw-tracking-normal tw-rounded-xl tw-font-semibold tw-text-2xl sm:tw-w-1/2 v-btn v-btn--is-elevated v-btn--has-bg theme--dark v-size--large tw-pt-3 tw-pb-3 primary tw-h-[55px] search-button tw-min-w-[64px] md:tw-min-w-[78px]"
                @click="searchByFilter()"
              >
                <v-icon class="tw-text-xl md:tw-text-3xl"> mdi-magnify</v-icon>
                <span class="tw-block tw-text-base md:tw-hidden">Search</span>
              </v-btn>
            </v-col>
          </v-row>

          <v-row class="tw-mt-5 tw-mb-5 tw-hidden md:tw-flex">
						<v-col cols="11" class="tw-text-right tw-mt-[-1rem]">
							<v-btn
								v-if="Object.keys($route.query).length"
								class="tw-normal-case tw-shadow-none tw-p-0 tw-m-0 tw-h-auto tw-bg-transparent"
								@click="clearSearch()"
							>
								Clear Search
							</v-btn>
						</v-col>
					</v-row>

        </v-col>
      </v-row>

      <FilterModal
        ref="FilterModal"
        :type-of-space="filterData.typeOfSpace"
        :min-price="filterData.minPrice"
        :max-price="filterData.maxPrice"
        :bedrooms="filterData.bedrooms"
        :beds="filterData.beds"
        :bathrooms="filterData.bathrooms"
        :pet-friendly="filterData.petFriendly"
        :pet-type="filterData.petType"
        :guests="filterData.guests"
        @update:bedrooms="updateBedrooms"
        @update:type_of_space="updateTypeOfSpace"
        @update:filter="updateFilter"
      />
    </v-col>
  </v-row>
</template>

<style lang="scss" scoped>
.v-btn-toggle {
  .v-btn {
    &.primary {
      background-color: #7c0cb1 !important;
      color: white !important;
    }

    &:not(.primary) {
      background-color: #f5f5f5;
      color: #666666;
    }
  }
}

.v-chip {
  &.v-chip--outlined {
    border-color: #7c0cb1 !important;
    color: #7c0cb1 !important;

    &:hover {
      background-color: #7c0cb1 !important;
      color: white !important;
    }
  }
}

.v-select {
  .v-input__slot {
    min-height: 40px !important;
  }
}

.list-item {
  -webkit-transition: opacity 2s ease-in;
}

.list-item:hover {
  cursor: pointer;
  background-color: rgb(245, 245, 245);
}

.fade-in {
  opacity: 1;
  animation-name: fadeInOpacity;
  animation-iteration-count: 1;
  animation-timing-function: ease-in;
  animation-duration: 0.5s;
}

::v-deep .btnGoAnywhere .v-btn__content {
  align-items: flex-start !important;
  gap: 5px !important;
  line-height: 28px;
}

::v-deep .filter-page-datepicker .tw-text-zinc-400, ::v-deep .searchFormFieldContainer input[type="text"]::placeholder {
  color:rgba(0, 0, 0, 0.87) !important;
}

@keyframes fadeInOpacity {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

/* OpenStreetAutocomplete styling to match design */
.input-shadow .address-autocomplete {
  .tw-border {
    border-radius: 35px !important;
  }

  .tw-text-primary,
  .tw-ring-primary-100 {
    color: #7c0cb1 !important;
  }

  .tw-border-primary {
    border-color: #7c0cb1 !important;
  }

  .tw-ring-primary-100 {
    --tw-ring-color: rgba(124, 12, 177, 0.2) !important;
  }
}

::v-deep .v-autocomplete .v-select__slot > .v-label:not(.v-label--active) {
  @apply tw-font-bold tw-text-sm;
}

::v-deep .v-autocomplete .v-select__slot > input {
  @apply tw-cursor-pointer;
}

::v-deep .v-autocomplete .v-select__slot > .v-input__append-inner > .v-input__icon--append {
  display: none !important;
}

::v-deep .v-input:not(.v-select--is-menu-active) > .v-input__control > .v-input__slot > fieldset,
.v-btn--outlined {
  border: 0px;
}

::v-deep .filter-page-datepicker .tw-absolute button {
  display: none;
}

::v-deep .filter-page-datepicker > div {
  display: block !important;
}

::v-deep .filter-page-datepicker .v-menu__content{
  margin-top: -40px !important;
}

::v-deep .filter-page-datepicker .tw-gap-1{
  gap: 0rem !important;
}

/*.input-shadow, .input-guest-list{
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
    border-radius: 35px;
  }*/
.input-shadow .v-icon::before,
.input-guest-list .v-icon:before {
  color: #7c0cb1;
}

.searchFormFieldContainer {
  background: #fff;
  box-shadow: 0px 3px 2px rgb(0 0 0 / 38%);
  border-radius: 20px;
  border: 1px solid #0000001f;
}

::v-deep .searchFormFieldContainer .v-label {
  height: 22px;
}

.searchFormFieldContainer > div {
  padding: 8px 0px;
}

.searchFormFieldContainer > div:nth-child(2) {
  border-left: 1px solid #0000001f;
  border-right: 1px solid #0000001f;
}

.searchFormFieldContainer > div:nth-child(4) {
  border-left: 1px solid #0000001f;
}

.searchFormFieldContainer > div:nth-child(3),
.searchFormFieldContainer > div:nth-child(4) {
  padding-top: 10px !important;
}

::v-deep .searchFormFieldContainer input {
  padding: 5px 50px !important;
}

::v-deep .v-btn--outlined {
  border: 0px;
}

::v-deep .input-shadow .black--text {
  font-weight: normal !important;
  font-size: 16px !important;
  color: #000000 !important;
}

::v-deep .input-shadow label {
  font-weight: normal !important;
  font-size: 16px !important;
  color: #000000 !important;
}

.input-guest-list {
  margin: 0px;
  padding: 0px;
}

/*.input-guest-list .v-input__control {
    padding: 10px;
    border: 1px solid #ccc;
  }*/

> .input-guest-list .v-input__slot:before {
  display: none;
}

/*>>> .input-guest-list .v-input__control {
    padding: 3px 10px;
    border: 1px solid #ccc;
    border-radius: 30px;
  }*/
> .input-guest-list label {
  color: #000000;
}

> .input-guest-list label {
  background: #fff;
}

.fade-default {
  animation: none !important;
  transition: none !important;
  opacity: 1 !important;
  transform: none !important;
}

.search-button {
  background: conic-gradient(
    from 90deg at 50% 50%,
    rgba(56, 11, 115, 0.9) -124.46deg,
    #7c0cb1 73.63deg,
    rgba(56, 11, 115, 0.9) 235.54deg,
    #7c0cb1 433.63deg
  );
}

/* Additional styling for OpenStreetAutocomplete to match Vuetify inputs */
.input-shadow .address-autocomplete input {
  padding-top: 12px !important;
  padding-bottom: 12px !important;
  font-size: 16px !important;
}

.input-shadow .address-autocomplete label {
  margin-bottom: 2px !important;
  font-weight: 500 !important;
}

/* Make the OpenStreetAutocomplete map marker icon match the theme color */
.input-shadow .address-autocomplete .tw-text-gray-400 .v-icon:before {
  color: #7c0cb1 !important;
}

@media screen and (max-width: 600px) {
  ::v-deep .filter-page-datepicker .v-menu__content, ::v-deep .filter-page-datepicker .v-picker__body{
    max-width: 300px !important;
  }

}
</style>
