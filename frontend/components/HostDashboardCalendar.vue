<script lang="ts">
// @ts-nocheck
import { defineComponent } from '@nuxtjs/composition-api'

import BookedStays from '~/components/BookedStays.vue'

export default defineComponent({
  name: 'HostDashboardCalendar',

  components: {
    BookedStays,
  },

  props: {
    hostData: {
      type: Object,
      default: () => {},
    },
  },

  data() {
    return {}
  },

  mounted() {
    this.getDashboardData()
  },

  methods: {
    getDashboardData() {},
  },
})
</script>

<template>
  <div class="md:tw-px-[55px] tw-px-4 tw-mt-10">
    <div v-if="hostData.totalListings == 0">
      <div>
        <span class="tw-text-zinc-500 tw-text-3xl">Let's Get Some Bookings on the Calendar</span>
      </div>

      <div class="tw-text-zinc-500 tw-text-xl tw-font-light tw-mt-4">
        Once you add your Home, you will be able to see your booking details below
      </div>

      <NuxtLink to="/create" class="tw-inline-block">
        <img
          src="~/assets/add-home-btn.jpg"
          class="tw-boxShadow tw-rounded-xl tw-max-w-[400px] tw-mt-5"
        />
      </NuxtLink>
    </div>
    <div v-else>
      <div>
        <span class="tw-text-zinc-500 tw-text-3xl">Your Bookings</span>
      </div>

      <div
        v-if="hostData.totalListings > 1"
        class="tw-text-zinc-500 tw-text-xl tw-font-light tw-mt-2 tw-mb-2"
      >
        Select your home
      </div>
    </div>

    <BookedStays v-if="hostData.totalListings > 0" />
  </div>
</template>
<style scoped>
.tw-boxShadow {
  box-shadow:
    0px 2px 3px rgba(0, 0, 0, 0.25),
    0px 4px 4px rgba(0, 0, 0, 0.25);
}
</style>
