<script setup lang="ts">
// @ts-nocheck

import { storeToRefs } from 'pinia'
import { watch } from '@nuxtjs/composition-api'

import CustomPricingItem from '~/components/CustomPricingItem.vue'
import { useAdvancedPricesStore } from '~/composables/useStore'
import { useApi } from '~/composables/useCommon'

const props = defineProps({
  homeIdProp: {
    type: null,
    required: false,
    default: null,
  },
  preventLoad: {
    type: Boolean,
    default: false,
  },
})

const homeIdProp = props.homeIdProp

const api = useApi()
const store = useAdvancedPricesStore()

// Only load data if not prevented
if (!props.preventLoad) {
  store.getAdvancedPrices(api, homeIdProp)
}

// Watch for changes to preventLoad prop
watch(
  () => props.preventLoad,
  newValue => {
    if (!newValue) {
      // Load data when preventLoad becomes false
      store.getAdvancedPrices(api, homeIdProp)
    }
  }
)

const { advancedPrices, isLoaded } = storeToRefs(store)
</script>

<template>
  <v-row v-if="isLoaded" no-gutters>
    <!--			Table Header-->
    <template v-if="$vuetify.breakpoint.mdAndUp && !homeIdProp">
      <v-col cols="12" md="2" class="tw-text-zinc-500 tw-font-semibold tw-tracking-wide">
        Home
      </v-col>
      <v-col cols="12" md="2" class="tw-text-zinc-500 tw-font-semibold tw-tracking-wide">
        Check-In
      </v-col>
      <v-col cols="12" md="2" class="tw-text-zinc-500 tw-font-semibold tw-tracking-wide">
        Check-Out
      </v-col>
      <v-col cols="12" md="2" class="tw-text-zinc-500 tw-font-semibold tw-tracking-wide">
        Price
      </v-col>
      <v-col cols="12" md="2" class="tw-text-zinc-500 tw-font-semibold tw-tracking-wide">
        Availability
      </v-col>
      <v-col cols="12" md="2" class="tw-text-zinc-500 tw-font-semibold tw-tracking-wide">
        Action
      </v-col>
    </template>

    <!--			Table Items-->
    <v-col cols="12" class="tw-flex tw-flex-col tw-gap-4">
      <template v-if="advancedPrices.length">
        <CustomPricingItem
          v-for="item in advancedPrices"
          :key="item.id"
          :advanced-price="item"
          class="tw-mb-8"
          :home-id-prop="homeIdProp"
        />
      </template>
      <div v-else class="tw-text-center tw-py-12">
        <v-icon size="48" color="grey" class="tw-mb-4"> mdi-calendar-blank </v-icon>
        <p class="tw-text-gray-500 tw-text-lg">No pricing data available</p>
        <p class="tw-text-gray-400 tw-text-sm">Check back later for updated pricing information</p>
      </div>
    </v-col>
  </v-row>

  <div v-else class="tw-flex tw-justify-center tw-mt-20">
    <v-progress-circular :size="70" :width="7" color="primary" indeterminate />
  </div>
</template>
