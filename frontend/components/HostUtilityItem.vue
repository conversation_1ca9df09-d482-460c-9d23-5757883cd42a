<script lang="ts">
// @ts-nocheck
import { defineComponent } from '@nuxtjs/composition-api'

export default defineComponent({
  name: 'HostUtilityItem',

  props: {
    image: {
      type: String,
      default: '',
    },
    description: {
      type: String,
      default: '',
    },
    link: {
      type: String,
      default: '',
    },
    item: {
      type: Object,
      default: null,
    },
  },

  methods: {
    getImageUrl() {
      return this.item.image
    },
  },
})
</script>

<template>
  <div class="indiv-item tw-min-w-[100%] md:tw-min-w-[350px] tw-px-5 tw-py-5">
    <a :href="item.link" target="_blank">
      <img class="" :src="getImageUrl()" alt="" />
    </a>
    <div class="">
      <div
        class="tw-text-zinc-500 tw-text-base tw-text-center tw-pl-[15px] tw-pr-[15px] tw-pt-[20px] tw-pb-[10px]"
      >
        <a :href="item.link" target="_blank" class="tw-text-zinc-500" v-html="item.description"></a>
      </div>
    </div>
  </div>
</template>

<style scoped>
.indiv-item {
  box-shadow: 0px 2px 3px rgba(0, 0, 0, 0.25);
  border-radius: 15px;
  background-color: #f1f1f1ae;
}
</style>
