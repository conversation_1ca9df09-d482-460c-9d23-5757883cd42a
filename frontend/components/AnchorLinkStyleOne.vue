<script lang="ts">
import { defineComponent } from '@nuxtjs/composition-api'

export default defineComponent({
  name: 'AnchorLinkStyleOne',

  props: {
    text: {
      type: String,
      default: 'OK',
    },
    link: {
      type: String,
      default: '',
    },
    font_size: {
      type: String,
      default: '',
    },
  },
})
</script>

<template>
  <NuxtLink
    :to="link"
    class="anchor_style_one_button tw-py-3 tw-px-6 tw-font-bold tw-rounded-3xl hover:tw-bg-[#7c0cb1] hover:tw-text-white"
    :class="font_size"
  >
    {{ text }}
  </NuxtLink>
</template>

<style scoped>
.anchor_style_one_button {
  background: conic-gradient(
    from 90deg at 50% 50%,
    rgba(56, 11, 115, 0.9) -124.46deg,
    #7c0cb1 73.63deg,
    rgba(56, 11, 115, 0.9) 235.54deg,
    #7c0cb1 433.63deg
  );
  color: #fff;
  font-size: 14px;

  box-shadow: 0px 2px 3px rgba(0, 0, 0, 0.25);
  transition: all 0.2s;
}

.anchor_style_one_button:hover {
  background: #fff !important;
  color: #7c0cb1 !important;
}
</style>
