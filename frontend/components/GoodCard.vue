<script lang="ts">
import { defineComponent } from '@nuxtjs/composition-api'

export default defineComponent({
  name: 'GoodCard',
  props: {
    cardTextClasses: {
      type: String,
      default: 'tw-p-0',
    },
  },
})
</script>

<template>
  <v-card
    class="tw-border-solid tw-border-slate-300 tw-border tw-rounded-lg tw-w-full tw-drop-shadow-2xl"
    :elevation="0"
  >
    <v-card-text :class="cardTextClasses">
      <slot></slot>
    </v-card-text>
  </v-card>
</template>

<style scoped></style>
