<template>
  <v-dialog
    :value="isOpen"
    max-width="600"
    content-class="paywall-dialog"
    :retain-focus="false"
    @input="isOpen = $event"
    @click:outside="closeModal"
  >
    <v-card class="tw-p-8 tw-grid md:tw-grid-cols-1 tw-gap-8 tw-max-h-[90vh] tw-overflow-y-auto">
      <div>
        <!-- Header Section -->
        <div class="tw-text-3xl tw-font-bold tw-text-primary mb-2">Payment Required</div>
        <div class="tw-text-zinc-500 tw-text-lg mb-8">
            Please pay associated protection fees to verify instantaneously via Truvi, based on the information you've provided and confirm booking request.
        </div>

        <!-- Payment Options -->
        <div class="tw-flex tw-flex-col tw-gap-6 mb-8">
          <div>
            <span class="tw-text-lg tw-flex tw-flex-row tw-items-center">
                <span class="tw-mr-2">Liability Protection Fee:</span>
                <strong> ${{liabilityFee}} </strong>
            </span>
          </div>
        </div>

        <!-- Payment Form -->
        <div class="mt-6">
          <div :id="uniqueCardElementId" class="card-element-container mb-6"></div>
          <GoodButton
            block
            x-large
            :loading="loading"
            class="tw-bg-primary"
            tabindex="0"
            @click.stop="makePayment"
          >
            Make Payment and Proceed
          </GoodButton>
        </div>
      </div>

    </v-card>
  </v-dialog>
</template>

<script lang="ts">
// @ts-nocheck

import { defineComponent, ref, onMounted, useContext, watch, nextTick } from '@nuxtjs/composition-api'

import { useSubscriptionStore } from '~/composables/useSubscriptionStore'
import { useToast, useApi } from '~/composables/useCommon'

import GoodButton from './GoodButton.vue'
import GoodButtonReverted from './GoodButtonReverted.vue'





export default defineComponent({
  name: 'LiabilityPayModal',

  components: {
    GoodButton,
    GoodButtonReverted,
  },

  props: {
    // Support v-model with modelValue prop
    modelValue: {
      type: Boolean,
      default: false
    },
    liabilityFee: {
      type: Number,
      default: 0
    },
    bId: {
      type: String,
      default: ''
    },
    userEmail: {
      type: String,
      default: ''
    }
  },

  emits: ['update:modelValue'],

  setup(props, { emit }) {
    const store = useSubscriptionStore()
    const toast = useToast()
    const loading = ref(false)
    const selectedPlan = ref<string | null>(null)
    const elements = ref(null)
    const cardElement = ref(null)
    const api = useApi()
    const isOpen = ref(false)
    const preventFocusEvents = ref(false)

    // Generate a unique ID for the card element to avoid duplicates
    const uniqueCardElementId = `card-element-${Math.random().toString(36).substring(2, 11)}`

    const { $stripe } = useContext()

    // Safely handle plan selection without causing focus loops
    const handlePlanSelect = (planId) => {
      // Set a flag to prevent focus events temporarily
      preventFocusEvents.value = true

      // Update the selected plan
      selectedPlan.value = planId

      // Reset the flag after a short delay
      setTimeout(() => {
        preventFocusEvents.value = false
      }, 100)
    }

    // Watch for changes from v-model
    watch(
      () => props.modelValue,
      (newVal) => {
        if (newVal !== isOpen.value) {
          isOpen.value = newVal
        }
      },
      { immediate: true }
    )

    // Watch for changes in the store (for backwards compatibility)
    watch(
      () => store.showPaywallModal,
      (newVal) => {
        if (newVal !== isOpen.value) {
          isOpen.value = newVal
        }
      }
    )

    // When local state changes, update both the store and emit the update event
    watch(
      () => isOpen.value,
      (newVal) => {
        // Update v-model value if needed
        if (props.modelValue !== newVal) {
          emit('update-modelValue', newVal)
        }

        // Keep store in sync for backwards compatibility
        if (store.showPaywallModal !== newVal) {
          store.showPaywallModal = newVal
        }

        // Reset selected plan when dialog closes
        if (!newVal) {
          selectedPlan.value = null
          // Clean up any Stripe elements when dialog closes
          if (cardElement.value) {
            try {
              cardElement.value.unmount()
              cardElement.value = null
            } catch (error) {
              console.error('Error unmounting card element:', error)
            }
          }
        }
      }
    )

    const closeModal = () => {
      isOpen.value = false
    }

    const mountStripeElement = () => {
      if (!$stripe) {
        console.error('Stripe not initialized')
        return
      }

      // Wait until the DOM is definitely ready
      setTimeout(() => {
        // Ensure the card element div exists before mounting
        const cardElementDiv = document.getElementById(uniqueCardElementId)
        if (!cardElementDiv) {
          console.error('Card element container not found')
          return
        }

        try {
          // Clean up existing elements
          if (cardElement.value) {
            cardElement.value.unmount()
            cardElement.value = null
          }

          // Create new elements
          elements.value = $stripe.elements()
          cardElement.value = elements.value.create('card', {
            style: {
              base: {
                fontSize: '16px',
                fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
                color: '#1F2937',
                '::placeholder': {
                  color: '#6B7280',
                },
                iconColor: '#6B7280',
                backgroundColor: '#FFFFFF',
                ':-webkit-autofill': {
                  color: '#1F2937',
                },
              },
              invalid: {
                color: '#DC2626',
                iconColor: '#DC2626',
              },
            },
          })

          cardElement.value.mount(`#${uniqueCardElementId}`)
        } catch (error) {
          console.error('Error mounting Stripe element:', error)
        }
      }, 500)
    }

    // Only mount Stripe elements when a plan is selected
    watch(
      () => selectedPlan.value,
      (newVal) => {
        if (newVal) {
          nextTick(() => {
            // Using nextTick to ensure DOM is updated before mounting
            mountStripeElement()
          })
        }
      }
    )

    // Also watch for dialog open to initialize properly
    watch(
      () => isOpen.value,
      (newVal) => {
        if (newVal) {
          // Only mount if dialog is open
          nextTick(() => {
            mountStripeElement()
          })
        }
      }
    )

    const makePayment = async () => {
      if (!props.liabilityFee) {
        toast.error('Please enter the liability fee.').goAway(3000)
        return
      }

      try {
        loading.value = true

        if (!cardElement.value) {
          throw new Error('Card element not initialized')
        }

        // First, check if the booking has already been auto-approved
        // This can happen with pre-approved travelers
        try {
          const { data: currentBooking } = await api.get(`/bookings/${props.bId}`)

          // If the booking is already in ACCEPTED or COMPLETED status, it means it was auto-approved
          if (currentBooking.status === 'accepted' || currentBooking.status === 'completed') {
            // Close the modal
            closeModal()

            // Emit payment success to update the UI
            emit('payment-success')

            // Show a success message
            toast.success('Your booking was automatically approved! Please wait while processing...').goAway(3000)

            return
          }
        } catch (checkError) {
          console.error('Error checking booking status:', checkError)
          // Continue with payment process even if the check fails
        }

        const { data } = await api.get(`/bookings/${props.bId}/create-payment-intent`);

        console.log('Payment Intent:', data)

        // Confirm card payment
        const { error, paymentIntent } = await $stripe.confirmCardPayment(data.paymentIntent, {
            payment_method: {
                card: cardElement.value,
                billing_details: {
                    email: props.userEmail,
                }
            }
        })

        if (error) {
          console.error('Payment error:', error)
          throw new Error(error.message || 'Failed to make payment')
        } else {
          // Check if the booking was auto-approved during payment processing
          try {
            const { data: updatedBooking } = await api.get(`/bookings/${props.bId}`)

            // If the booking is already in ACCEPTED or COMPLETED status, show a different message
            if (updatedBooking.status === 'accepted' || updatedBooking.status === 'completed') {
              closeModal()
              emit('payment-success')
              toast.success('Payment successful! Your booking was automatically approved.').goAway(3000)
              return
            }
          } catch (checkError) {
            console.error('Error checking booking status after payment:', checkError)
            // Continue with normal flow if the check fails
          }

          // Normal flow - payment successful, proceed to request booking
          emit('payment-success')
          closeModal()
          toast.success('Payment has been made successfully. Please wait while processing..').goAway(3000)
        }

        console.log('Payment Intent:', paymentIntent)
      } catch (error) {
        console.error('Payment error:', error)
        toast.error(error.message || 'Failed to make payment').goAway(3000)
      } finally {
        loading.value = false
      }
    }

    // Cleanup on component destroy
    onMounted(() => {
      return () => {
        if (cardElement.value) {
          try {
            cardElement.value.unmount()
          } catch (error) {
            console.error('Error unmounting card element:', error)
          }
        }
      }
    })

    return {
      store,
      loading,
      selectedPlan,
      makePayment,
      isOpen,
      closeModal,
      handlePlanSelect,
      preventFocusEvents,
      uniqueCardElementId
    }
  },
})
</script>

<style scoped>
.card-element-container {
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1rem;
  background: white;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  transition: border-color 0.15s ease-in-out;
}

.card-element-container:hover {
  border-color: #9ca3af;
}

:deep(.StripeElement--focus) {
  border-color: #6b7280;
  box-shadow:
    0 1px 3px 0 rgba(0, 0, 0, 0.1),
    0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

:deep(.StripeElement--invalid) {
  border-color: #dc2626;
}

/* Custom class for the dialog */
:global(.paywall-dialog) {
  z-index: 100;
  outline: none;
}

/* Add a higher specificity selector to override Vuetify's focus styles */
:global(.paywall-dialog .v-dialog) {
  outline: none !important;
}
</style>
