<script lang="ts">
// @ts-nocheck
import { defineComponent } from '@nuxtjs/composition-api'

export default defineComponent({
  name: 'MyCrewDashboardTitle',

  props: {
    title: {
      type: String,
      default: 'Jobs',
    },
  },
})
</script>

<template>
  <v-col cols="12" class="tw-my-8 md:tw-flex tw-justify-between">
    <div class="tw-text-3xl tw-font-semibold tw-text-left tw-tracking-wide">
      <span class="tw-text-zinc-500">MyCrew </span
      ><span class="tw-text-[#672093] tw-tracking-wide">
        {{ title }}
      </span>
    </div>

    <slot></slot>
  </v-col>
</template>

<style scoped></style>
