<script lang="ts">
// @ts-nocheck
import { defineComponent, PropType } from '@nuxtjs/composition-api'

import { SpaceType } from '~/types'
import GoodCard2 from '~/components/GoodCard2.vue'

export default defineComponent({
  name: 'SpaceType',
  components: { GoodCard2 },

  props: {
    spaceType: {
      type: Object as PropType<SpaceType>,
      required: true,
    },
    isActivated: {
      type: Boolean,
      default: false,
    },
  },
})
</script>

<template>
  <good-card2
    :class="
      isActivated
        ? 'tw-bg-primary tw-text-white tw-border-primary'
        : 'tw-text-zinc-600 tw-border-zinc-600'
    "
    class="tw-px-4 tw-py-8 tw-min-h-[200px] tw-flex tw-flex-col tw-cursor-pointer"
  >
    <div class="tw-font-semibold tw-text-lg tw-mb-4">
      {{ spaceType.title }}
    </div>
    <div>
      {{ spaceType.description }}
    </div>
  </good-card2>
</template>

<style scoped></style>
