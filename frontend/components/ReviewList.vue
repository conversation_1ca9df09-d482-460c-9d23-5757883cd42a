<template>
  <div class="tw-flex tw-flex-col tw-gap-6">
    <div class="tw-flex tw-flex-col tw-gap-4">
      <div class="tw-flex tw-items-center tw-gap-2">
        <div class="tw-text-2xl tw-font-semibold">
          {{ averageRating }} Stars ({{ totalReviews }} Reviews)
        </div>
      </div>

      <!-- Latest Review -->
      <div v-if="latestReview" class="tw-flex tw-flex-col tw-gap-4">
        <div class="tw-text-xl tw-font-semibold">Latest Review</div>
        <div class="tw-flex tw-flex-col tw-gap-2 tw-p-4 tw-bg-white tw-rounded-lg tw-shadow">
          <div class="tw-flex tw-items-center tw-gap-4">
            <BeastImage
              :src="latestReview.reviewer.avatar || ''"
              :alt="latestReview.reviewer.first_name"
              class="tw-rounded-full tw-w-[20%] tw-object-cover"
            />
            <div class="tw-flex tw-flex-col tw-w-[80%]">
              <div class="tw-font-semibold">
                {{ latestReview.reviewer.first_name }}
                {{ latestReview.reviewer.last_name?.charAt(0) }}.
              </div>
              <v-rating
                :value="latestReview.rating"
                color="primary"
                background-color="grey lighten-2"
                readonly
                dense
                half-increments
                size="20"
              ></v-rating>
              <div class="tw-text-zinc-600 tw-mt-2">
                {{ latestReview.feedback }}
              </div>
              <div class="tw-text-sm tw-text-zinc-500 tw-mt-1">
                {{ formatDate(latestReview.created_at) }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Other Reviews -->
      <div v-if="otherReviews.length" class="tw-flex tw-flex-col tw-gap-4">
        <div class="tw-text-xl tw-font-semibold">Other Reviews</div>
        <div class="tw-grid tw-grid-cols-1 md:tw-grid-cols-2 tw-gap-4">
          <div
            v-for="review in otherReviews"
            :key="review.id"
            class="tw-flex tw-flex-col tw-gap-2 tw-p-4 tw-bg-white tw-rounded-lg tw-shadow"
          >
            <div class="tw-flex tw-items-center tw-gap-4">
              <BeastImage
                :src="review.reviewer.avatar || ''"
                :alt="review.reviewer.first_name"
                class="tw-rounded-full tw-w-[20%] tw-object-cover"
              />
              <div class="tw-flex tw-flex-col tw-w-[80%]">
                <div class="tw-font-semibold">
                  {{ review.reviewer.first_name }}
                  {{ review.reviewer.last_name?.charAt(0) }}.
                </div>
              </div>
            </div>
            <div class="tw-flex tw-flex-col tw-gap-2">
              <v-rating
                :value="review.rating"
                color="primary"
                background-color="grey lighten-2"
                readonly
                dense
                half-increments
                size="20"
              ></v-rating>
              <div class="tw-text-zinc-600">{{ review.feedback }}</div>
              <div class="tw-text-sm tw-text-zinc-500">
                {{ formatDate(review.created_at) }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed } from '@nuxtjs/composition-api'

import { useApi } from '~/composables/useCommon'
import BeastImage from '~/components/BeastImage.vue'

interface Review {
  id: number
  rating: number
  feedback: string
  type: 'guest_review' | 'host_review'
  created_at: string
  reviewer: {
    id: number
    first_name: string
    last_name: string
    avatar: string | null
  }
}

export default defineComponent({
  components: {
    BeastImage,
  },
  props: {
    booking: {
      type: Object,
      required: true,
    },
  },

  setup(props) {
    // Get all guest reviews for this home
    const guestReviews = computed(() => {
      return (
        props.booking.toHome?.reviews
          ?.filter(review => review.type === 'guest_review')
          .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime()) || []
      )
    })

    const latestReview = computed(() => guestReviews.value[0])

    const otherReviews = computed(() => guestReviews.value.slice(1))

    const averageRating = computed(() => {
      const total = guestReviews.value.reduce((sum, review) => sum + review.rating, 0)
      return guestReviews.value.length ? (total / guestReviews.value.length).toFixed(1) : '0'
    })

    const totalReviews = computed(() => guestReviews.value.length)

    const formatDate = (date: string) => {
      return new Date(date).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      })
    }

    return {
      latestReview,
      otherReviews,
      averageRating,
      totalReviews,
      formatDate,
    }
  },
})
</script>
