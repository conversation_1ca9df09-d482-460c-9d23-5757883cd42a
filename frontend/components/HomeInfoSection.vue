<script lang="ts">
// @ts-nocheck

import { computed, defineComponent } from '@nuxtjs/composition-api'

import HomeDetailUserInfoSectionComponent from '~/components/HomeDetailUserInfoSectionComponent.vue'

export default defineComponent({
  name: 'HomeInfoSection',

  components: { HomeDetailUserInfoSectionComponent },

  props: {
    houseInfo: {
      type: Object,
      required: true,
    },
    formattedDescription: {
      type: String,
      required: true,
    },
    seeMore: {
      type: Boolean,
      required: true,
    },
    homeOwnerHousesCount: {
      type: Number,
      required: true,
    },
  },

  setup(props, { emit }) {
    const displayedPets = computed(() => {
      const pets = props.houseInfo.extra_info?.pets || { enabled: 'no' }

      switch (pets.enabled) {
        case 'yes':
          return 'Pets allowed'
        case 'service animal only':
          return 'Service animals only'
        case 'no':
        default:
          return 'No pets allowed'
      }
    })

    const homeState = computed(() => {
      return props.houseInfo?.state_long?.trim() || ''
    })

    const toggleSeeMore = () => {
      emit('toggle-see-more')
    }

    return {
      displayedPets,
      toggleSeeMore,
      homeState,
    }
  },
})
</script>

<template>
  <div class="tw-flex tw-flex-col tw-gap-10">
    <!-- Home Title & Quick Info Section -->
    <div class="tw-flex tw-flex-col tw-gap-5">
      <h1 class="tw-text-zinc-700 tw-font-bold tw-text-3xl sm:tw-text-4xl">
        {{ houseInfo.title }}{{ homeState.length ? ', ' + homeState : '' }}
      </h1>

      <!-- Updated layout: Guests, Beds, Bathrooms in one row -->
      <div class="tw-flex tw-flex-wrap tw-gap-4 sm:tw-gap-8 tw-mt-2">
        <!-- Guest, Beds, Bathrooms aligned horizontally with better mobile support -->
        <div class="tw-flex tw-flex-wrap tw-gap-4 sm:tw-gap-8 tw-items-center">
          <div class="tw-flex tw-gap-3 tw-items-center">
            <div class="tw-bg-primary tw-bg-opacity-10 tw-p-2 tw-rounded-lg">
              <v-icon color="primary" size="24"> mdi-account-group </v-icon>
            </div>
            <div class="tw-flex tw-flex-col">
              <span class="tw-text-zinc-700 tw-font-medium tw-text-lg">{{
                houseInfo.guests || 0
              }}</span>
              <span class="tw-text-zinc-500 tw-text-sm">Guests</span>
            </div>
          </div>

          <div class="tw-flex tw-gap-3 tw-items-center">
            <div class="tw-bg-primary tw-bg-opacity-10 tw-p-2 tw-rounded-lg">
              <v-icon color="primary" size="24"> mdi-bed </v-icon>
            </div>
            <div class="tw-flex tw-flex-col">
              <span class="tw-text-zinc-700 tw-font-medium tw-text-lg">{{
                houseInfo.beds || 0
              }}</span>
              <span class="tw-text-zinc-500 tw-text-sm">Beds</span>
            </div>
          </div>

          <div class="tw-flex tw-gap-3 tw-items-center">
            <div class="tw-bg-primary tw-bg-opacity-10 tw-p-2 tw-rounded-lg">
              <v-icon color="primary" size="24"> mdi-shower </v-icon>
            </div>
            <div class="tw-flex tw-flex-col">
              <span class="tw-text-zinc-700 tw-font-medium tw-text-lg">{{
                houseInfo.baths || 0
              }}</span>
              <span class="tw-text-zinc-500 tw-text-sm">Bathrooms</span>
            </div>
          </div>
        </div>

        <!-- Pets information in a separate section -->
        <div class="tw-flex tw-gap-3 tw-items-center tw-mt-2 sm:tw-mt-0">
          <div class="tw-bg-primary tw-bg-opacity-10 tw-p-2 tw-rounded-lg">
            <v-icon color="primary" size="24"> mdi-paw </v-icon>
          </div>
          <div class="tw-flex tw-flex-col">
            <span class="tw-text-zinc-700 tw-font-medium tw-text-lg">{{ displayedPets }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Description Section -->
    <div class="tw-flex tw-flex-col tw-gap-4">
      <h2 class="tw-text-zinc-700 tw-font-bold tw-text-2xl">Home Description</h2>
      <div class="tw-text-zinc-600 tw-text-lg tw-leading-relaxed tw-whitespace-pre-line">
        {{ formattedDescription }}
      </div>
      <div v-if="houseInfo.description.length > 600" class="tw-flex tw-justify-end">
        <v-btn small text color="primary" class="tw-font-medium" @click="toggleSeeMore">
          {{ seeMore ? 'Show less' : 'Show more' }}
        </v-btn>
      </div>
    </div>

    <!-- Amenities Section -->
    <div class="tw-flex tw-flex-col tw-gap-5">
      <h2 class="tw-text-zinc-700 tw-font-bold tw-text-2xl">Amenities</h2>
      <template v-if="houseInfo.amenities != undefined && houseInfo.amenities.length > 0">
        <div
          class="tw-grid tw-gap-y-4 tw-gap-x-6 tw-grid-cols-1 sm:tw-grid-cols-2 md:tw-grid-cols-3"
        >
          <div
            v-for="(amenity, index) in houseInfo.amenities"
            :key="index"
            class="tw-flex tw-items-center tw-gap-2 tw-text-zinc-600"
          >
            <v-icon color="primary" small> mdi-check-circle </v-icon>
            <span>{{ amenity }}</span>
          </div>
        </div>
      </template>
      <template v-else>
        <div class="tw-p-4 tw-bg-gray-50 tw-rounded-lg tw-text-zinc-500 tw-text-center">
          No amenities listed for this property
        </div>
      </template>
    </div>

    <!-- Host Info Section -->
    <HomeDetailUserInfoSectionComponent
      :house-info="houseInfo"
      :home-owner-houses-count="homeOwnerHousesCount"
      class="tw-pt-2"
    />
  </div>
</template>

<style scoped>
.tw-whitespace-pre-line {
  white-space: pre-line;
}
</style>
