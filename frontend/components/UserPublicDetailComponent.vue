<template>
  <v-row no-gutters class="mt-10">
    <v-spacer />
    <v-col cols="12" md="3" class="d-flex flex-column left-panel-card">
      <v-row no-gutters>
        <v-col class="d-flex flex-column align-center">
          <v-avatar v-if="userInfo.avatar" size="130" class="mb-4 md:mb-0">
            <v-img :src="userInfo.avatar" />
          </v-avatar>
          <img
            v-else
            class="mb-4 md:mb-0"
            :src="require('~/assets/default.png')"
            :alt="userInfo.firstName"
          />
          <v-row v-if="100 === userInfo.profileCompletion" no-gutters>
            <v-col class="d-flex align-center">
              <img
                :src="require('~/assets/check1.svg')"
                class="mr-1"
                height="15"
                width="15"
                alt="Identity verified"
              />
              <span class="font-weight-bold text-sm text-no-wrap">Identity Verified</span>
            </v-col>
          </v-row>
        </v-col>
      </v-row>
      <v-row no-gutters class="mt-15">
        <v-col class="d-flex flex-column align-left">
          <v-divider />
          <div class="font-weight-bold text-lg-h5 mt-8">About</div>
          <div class="grey--text mt-5 text-caption">
            {{ userInfo.about }}
          </div>
        </v-col>
      </v-row>
    </v-col>
    <v-spacer />
    <v-col cols="12" md="8" class="d-flex flex-column right-panel-card">
      <v-row>
        <v-col v-for="home in userHomes" :key="home.id" cols="12" md="6">
          <hot-home-card :data="home" small-card />
        </v-col>
      </v-row>
    </v-col>
  </v-row>
</template>

<script>
export default {
  name: 'UserPublicDetailComponent',
  props: {
    userInfo: {
      type: Object,
      required: true,
    },
    userHomes: {
      type: Array,
      required: true,
    },
  },
}
</script>

<style scoped>
.left-panel-card {
  min-height: 465px;
  height: fit-content;
  border: 1px solid rgba(153, 153, 153, 0.2);
  border-radius: 10px;
  padding: 20px !important;
}

.right-panel-card {
}
</style>
