<template>
  <v-card flat class="tw-bg-white tw-shadow-sm tw-rounded-xl">
    <v-card-title class="tw-flex tw-items-center tw-gap-2 tw-pb-6">
      <v-icon color="primary" size="24">mdi-account</v-icon>
      <span class="tw-text-2xl tw-font-semibold tw-text-zinc-700">Basic Information</span>
      <v-spacer />
      <div class="tw-flex tw-items-center tw-gap-2">
        <v-icon
          v-if="saveStatus === 'saved'"
          color="success"
          size="20"
          class="tw-transition-all tw-duration-300"
        >
          mdi-check-circle
        </v-icon>
        <v-icon
          v-else-if="saveStatus === 'saving'"
          color="primary"
          size="20"
          class="mdi-spin"
        >
          mdi-loading
        </v-icon>
        <v-icon
          v-else-if="saveStatus === 'error'"
          color="error"
          size="20"
        >
          mdi-alert-circle
        </v-icon>
      </div>
    </v-card-title>

    <v-card-text>
      <v-form ref="basicInfoFormRef" class="tw-grid md:tw-grid-cols-2 tw-gap-6" lazy-validation>
        <v-text-field
          v-model="localFirstName"
          light
          dense
          label="First Name*"
          required
          type="text"
          outlined
          :rules="formRules.firstName"
          class="tw-rounded-lg"
          @input="onFieldChange"
        />

        <v-text-field
          v-model="localLastName"
          light
          dense
          label="Last Name*"
          required
          type="text"
          outlined
          :rules="formRules.lastName"
          class="tw-rounded-lg"
          @input="onFieldChange"
        />
      </v-form>

      <div class="tw-mt-6 tw-flex tw-justify-between tw-items-center">
        <div class="tw-text-sm tw-text-zinc-500">
          <span v-if="hasUnsavedChanges" class="tw-text-orange-600">
            You have unsaved changes
          </span>
          <span v-else-if="saveStatus === 'saved'" class="tw-text-green-600">
            Changes saved successfully
          </span>
        </div>
        
        <GoodButton
          :loading="saveStatus === 'saving'"
          :disabled="!hasUnsavedChanges || !isFormValid"
          class="tw-px-6 tw-py-2"
          @click="saveBasicInfo"
        >
          <div class="tw-flex tw-items-center tw-gap-2">
            <v-icon size="16">
              {{ saveStatus === 'saving' ? 'mdi-loading mdi-spin' : 'mdi-content-save' }}
            </v-icon>
            <span>Save Changes</span>
          </div>
        </GoodButton>
      </div>
    </v-card-text>
  </v-card>
</template>

<script>
import { defineComponent, ref, computed, watch, onUnmounted } from '@nuxtjs/composition-api'

import { useToast, useApi } from '~/composables/useCommon'
import GoodButton from '~/components/GoodButton.vue'

export default defineComponent({
  name: 'BasicInfoSection',
  components: {
    GoodButton,
  },
  props: {
    firstName: {
      type: String,
      default: '',
    },
    lastName: {
      type: String,
      default: '',
    },
  },
  emits: ['update:firstName', 'update:lastName', 'saved'],
  setup(props, { emit }) {
    const $toast = useToast()
    const $axios = useApi()
    
    // Form refs
    const basicInfoFormRef = ref(null)
    
    // Local state
    const localFirstName = ref(props.firstName)
    const localLastName = ref(props.lastName)
    const saveStatus = ref('') // '', 'saving', 'saved', 'error'
    const hasUnsavedChanges = ref(false)
    
    // Auto-save timer
    let autoSaveTimer = null
    
    // Form validation rules
    const formRules = {
      firstName: [v => !!v || 'First Name is required'],
      lastName: [v => !!v || 'Last Name is required'],
    }
    
    // Computed properties
    const isFormValid = computed(() => {
      return localFirstName.value && localLastName.value
    })
    
    // Watch for prop changes
    watch(() => props.firstName, (newVal) => {
      localFirstName.value = newVal
      hasUnsavedChanges.value = false
    })
    
    watch(() => props.lastName, (newVal) => {
      localLastName.value = newVal
      hasUnsavedChanges.value = false
    })
    
    // Methods
    const onFieldChange = () => {
      hasUnsavedChanges.value = true
      saveStatus.value = ''
      
      // Clear existing timer
      if (autoSaveTimer) {
        clearTimeout(autoSaveTimer)
      }
      
      // Set up auto-save with 2 second delay
      autoSaveTimer = setTimeout(() => {
        if (hasUnsavedChanges.value && isFormValid.value) {
          saveBasicInfo()
        }
      }, 2000)
    }
    
    const saveBasicInfo = async () => {
      if (!isFormValid.value) {
        $toast.error('Please fill all required fields correctly').goAway(3000)
        return
      }
      
      saveStatus.value = 'saving'
      
      try {
        const requestData = {}
        
        // Only include changed fields
        if (localFirstName.value !== props.firstName) {
          requestData.first_name = localFirstName.value
        }
        if (localLastName.value !== props.lastName) {
          requestData.last_name = localLastName.value
        }
        
        // If no changes, don't make request
        if (Object.keys(requestData).length === 0) {
          saveStatus.value = ''
          hasUnsavedChanges.value = false
          return
        }
        
        const response = await $axios.post('user/update-basic-info', requestData)
        
        // Update parent component
        emit('update:firstName', localFirstName.value)
        emit('update:lastName', localLastName.value)
        emit('saved', response.data)
        
        saveStatus.value = 'saved'
        hasUnsavedChanges.value = false
        
        $toast.success('Basic information updated successfully').goAway(3000)
        
        // Clear saved status after 3 seconds
        setTimeout(() => {
          if (saveStatus.value === 'saved') {
            saveStatus.value = ''
          }
        }, 3000)
        
      } catch (error) {
        console.error('Error updating basic info:', error)
        saveStatus.value = 'error'
        
        const errorMessage = error.response?.data?.message || 'Error updating basic information'
        $toast.error(errorMessage).goAway(3000)
        
        // Clear error status after 5 seconds
        setTimeout(() => {
          if (saveStatus.value === 'error') {
            saveStatus.value = ''
          }
        }, 5000)
      }
    }
    
    // Cleanup
    const cleanup = () => {
      if (autoSaveTimer) {
        clearTimeout(autoSaveTimer)
      }
    }
    
    // Lifecycle
    onUnmounted(() => {
      cleanup()
    })
    
    return {
      basicInfoFormRef,
      localFirstName,
      localLastName,
      saveStatus,
      hasUnsavedChanges,
      formRules,
      isFormValid,
      onFieldChange,
      saveBasicInfo,
    }
  },
})
</script>
