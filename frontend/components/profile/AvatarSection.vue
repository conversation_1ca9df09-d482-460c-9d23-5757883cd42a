<template>
  <v-card flat class="tw-bg-white tw-shadow-sm tw-rounded-xl">
    <v-card-title class="tw-flex tw-items-center tw-gap-2 tw-pb-6">
      <v-icon color="primary" size="24">mdi-camera</v-icon>
      <span class="tw-text-2xl tw-font-semibold tw-text-zinc-700">Profile Picture</span>
      <v-spacer />
      <div class="tw-flex tw-items-center tw-gap-2">
        <v-icon
          v-if="saveStatus === 'saved'"
          color="success"
          size="20"
          class="tw-transition-all tw-duration-300"
        >
          mdi-check-circle
        </v-icon>
        <v-icon
          v-else-if="saveStatus === 'saving'"
          color="primary"
          size="20"
          class="mdi-spin"
        >
          mdi-loading
        </v-icon>
        <v-icon
          v-else-if="saveStatus === 'error'"
          color="error"
          size="20"
        >
          mdi-alert-circle
        </v-icon>
      </div>
    </v-card-title>

    <v-card-text>
      <div class="tw-flex tw-flex-col tw-items-center tw-gap-6">
        <!-- Avatar Display with Interactive Overlay -->
        <div
          class="tw-relative tw-group tw-cursor-pointer tw-transition-all tw-duration-300"
          tabindex="0"
          role="button"
          :aria-label="avatar ? 'Update profile picture' : 'Add profile picture'"
          @click="triggerUpload"
          @keydown.enter="triggerUpload"
          @keydown.space.prevent="triggerUpload"
        >
          <!-- Avatar Container -->
          <div class="tw-relative tw-rounded-full tw-overflow-hidden tw-border-4 tw-border-white tw-shadow-lg tw-transition-all tw-duration-300 group-hover:tw-shadow-xl group-focus:tw-shadow-xl group-focus:tw-ring-4 group-focus:tw-ring-primary group-focus:tw-ring-opacity-20">
            <div class="tw-w-32 tw-h-32 tw-bg-gray-100 tw-flex tw-items-center tw-justify-center">
              <img
                v-if="getAvatarImage"
                ref="avatarImageRef"
                :src="getAvatarImage"
                :alt="user?.first_name ? `${user.first_name}'s profile picture` : 'Profile picture'"
                class="tw-w-full tw-h-full tw-object-cover tw-transition-all tw-duration-300 group-hover:tw-scale-105"
                @error="handleImageError"
              />
              <div v-else class="tw-flex tw-flex-col tw-items-center tw-justify-center tw-text-gray-400">
                <v-icon size="48" color="gray">mdi-account-circle</v-icon>
                <span class="tw-text-xs tw-mt-1">No photo</span>
              </div>
            </div>

            <!-- Hover Overlay -->
            <div class="tw-absolute tw-inset-0 tw-bg-black tw-bg-opacity-0 group-hover:tw-bg-opacity-40 tw-transition-all tw-duration-300 tw-flex tw-items-center tw-justify-center">
              <div class="tw-opacity-0 group-hover:tw-opacity-100 tw-transition-all tw-duration-300 tw-text-white tw-text-center">
                <v-icon color="white" size="24" class="tw-mb-1">mdi-camera</v-icon>
                <div class="tw-text-sm tw-font-medium">
                  {{ getAvatarImage ? 'Change' : 'Add Photo' }}
                </div>
              </div>
            </div>
          </div>

          <!-- Edit Icon -->
          <div
            class="tw-absolute tw--bottom-1 tw--right-1 tw-w-10 tw-h-10 tw-bg-primary tw-rounded-full tw-flex tw-items-center tw-justify-center tw-shadow-lg tw-transition-all tw-duration-300 group-hover:tw-scale-110 group-hover:tw-shadow-xl"
            :class="{ 'tw-animate-pulse': saveStatus === 'saving' }"
          >
            <v-icon
              color="white"
              size="20"
              :class="{ 'mdi-spin': saveStatus === 'saving' }"
            >
              {{ saveStatus === 'saving' ? 'mdi-loading' : 'mdi-pencil' }}
            </v-icon>
          </div>
        </div>

        <!-- Upload Instructions -->
        <div class="tw-text-center tw-max-w-sm">
          <div class="tw-text-sm tw-text-gray-600 tw-mb-2">
            Click the image above to {{ getAvatarImage ? 'change' : 'add' }} your profile picture
          </div>
          <div class="tw-text-xs tw-text-gray-500">
            Supports JPEG, PNG, and WebP files up to 5MB
          </div>
        </div>

        <!-- Hidden File Input -->
        <input
          ref="fileInputRef"
          type="file"
          accept="image/jpeg,image/png,image/webp"
          class="tw-hidden"
          @change="onFileChanged"
        />

        <!-- Status Message -->
        <div v-if="saveStatus && saveStatus !== ''" class="tw-text-center">
          <div
            v-if="saveStatus === 'saving'"
            class="tw-flex tw-items-center tw-gap-2 tw-text-blue-600 tw-text-sm"
          >
            <v-icon color="primary" size="16" class="mdi-spin">mdi-loading</v-icon>
            <span>Uploading photo...</span>
          </div>
          <div
            v-else-if="saveStatus === 'saved'"
            class="tw-flex tw-items-center tw-gap-2 tw-text-green-600 tw-text-sm"
          >
            <v-icon color="success" size="16">mdi-check-circle</v-icon>
            <span>Photo updated successfully</span>
          </div>
          <div
            v-else-if="saveStatus === 'error'"
            class="tw-flex tw-items-center tw-gap-2 tw-text-red-600 tw-text-sm"
          >
            <v-icon color="error" size="16">mdi-alert-circle</v-icon>
            <span>Error uploading photo</span>
          </div>
        </div>
      </div>
    </v-card-text>
  </v-card>
</template>

<script>
import { defineComponent, ref, computed } from '@nuxtjs/composition-api'

import { useToast, useApi } from '~/composables/useCommon'

export default defineComponent({
  name: 'AvatarSection',
  props: {
    user: {
      type: Object,
      required: true,
    },
    avatar: {
      type: String,
      default: null,
    },
  },
  emits: ['saved'],
  setup(props, { emit }) {
    const $toast = useToast()
    const $axios = useApi()

    // Refs
    const fileInputRef = ref(null)
    const avatarImageRef = ref(null)
    const saveStatus = ref('') // '', 'saving', 'saved', 'error'
    const imageError = ref(false)

    // Computed properties
    const getAvatarImage = computed(() => {
      if (imageError.value) {
        return null
      }
      if (props.avatar) {
        return props.avatar
      }
      if (props.user?.avatar_url) {
        return props.user.avatar_url
      }
      return null
    })

    // Methods
    const validateFileType = (file) => {
      const allowedTypes = ['image/jpeg', 'image/png', 'image/webp']
      const maxSize = 5 * 1024 * 1024 // 5MB

      if (!allowedTypes.includes(file.type)) {
        $toast.error('Please select a valid image file (JPEG, PNG, or WebP)').goAway(3000)
        return false
      }

      if (file.size > maxSize) {
        $toast.error('File size must be less than 5MB').goAway(3000)
        return false
      }

      return true
    }

    const triggerUpload = () => {
      fileInputRef.value?.click()
    }

    const handleImageError = () => {
      imageError.value = true
    }

    const onFileChanged = (e) => {
      const file = e.target.files[0]
      if (!file) {
        return
      }

      if (!validateFileType(file)) {
        e.target.value = ''
        return
      }

      // Reset image error state
      imageError.value = false

      // Show preview immediately
      const previewUrl = URL.createObjectURL(file)
      if (avatarImageRef.value) {
        avatarImageRef.value.src = previewUrl
      }

      // Start upload
      updateAvatar(file)

      // Clear the input so the same file can be selected again
      e.target.value = ''
    }
    
    const updateAvatar = async (file) => {
      if (!props.user) {
        $toast.error('User data not available. Please try again later.').goAway(3000)
        return
      }

      saveStatus.value = 'saving'

      try {
        const request = new FormData()
        
        // Include required fields for the full profile update endpoint
        request.append('first_name', props.user.first_name)
        request.append('last_name', props.user.last_name)
        request.append('email', props.user.email)
        request.append('phone_number', props.user.phone_number)
        request.append('about', props.user.about || '')
        
        // Add the avatar file
        request.append('avatar', file)

        const response = await $axios.post('user/update', request, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        })

        saveStatus.value = 'saved'
        emit('saved', response.data)
        
        $toast.success('Profile picture updated successfully').goAway(3000)
        
        // Clear saved status after 3 seconds
        setTimeout(() => {
          if (saveStatus.value === 'saved') {
            saveStatus.value = ''
          }
        }, 3000)

      } catch (error) {
        console.error('Error updating avatar:', error)
        saveStatus.value = 'error'
        
        const errorMessage = error.response?.data?.message || 'Error updating profile picture'
        $toast.error(errorMessage).goAway(3000)
        
        // Clear error status after 5 seconds
        setTimeout(() => {
          if (saveStatus.value === 'error') {
            saveStatus.value = ''
          }
        }, 5000)
      }
    }
    
    return {
      fileInputRef,
      avatarImageRef,
      saveStatus,
      imageError,
      getAvatarImage,
      validateFileType,
      triggerUpload,
      handleImageError,
      onFileChanged,
      updateAvatar,
    }
  },
})
</script>
