<template>
  <v-card flat class="tw-bg-white tw-shadow-sm tw-rounded-xl">
    <v-card-title class="tw-flex tw-items-center tw-gap-2 tw-pb-6">
      <v-icon color="primary" size="24">mdi-text-account</v-icon>
      <span class="tw-text-2xl tw-font-semibold tw-text-zinc-700">About Me</span>
      <v-spacer />
      <div class="tw-flex tw-items-center tw-gap-2">
        <div class="tw-text-sm tw-text-zinc-500">
          {{ safeAboutText ? safeAboutText.length : 0 }}/500
        </div>
        <v-icon
          v-if="saveStatus === 'saved'"
          color="success"
          size="20"
          class="tw-transition-all tw-duration-300"
        >
          mdi-check-circle
        </v-icon>
        <v-icon
          v-else-if="saveStatus === 'saving'"
          color="primary"
          size="20"
          class="mdi-spin"
        >
          mdi-loading
        </v-icon>
        <v-icon
          v-else-if="saveStatus === 'error'"
          color="error"
          size="20"
        >
          mdi-alert-circle
        </v-icon>
      </div>
    </v-card-title>

    <v-card-text>
      <v-form ref="aboutFormRef" lazy-validation>
        <v-textarea
          v-model="safeAboutText"
          label="Tell us about yourself"
          placeholder="Share a bit about yourself, your interests, and what makes you a great host or guest..."
          outlined
          class="tw-rounded-lg"
          rows="5"
          maxlength="500"
          :rules="formRules.about"
          hide-details="auto"
          @input="onAboutInput"
          @focus="setAboutFocused(true)"
          @blur="setAboutFocused(false)"
        />
      </v-form>

      <div class="tw-mt-4 tw-flex tw-justify-between tw-items-center">
        <div class="tw-text-sm tw-text-zinc-500">
          <span v-if="hasUnsavedChanges" class="tw-text-orange-600">
            You have unsaved changes
          </span>
          <span v-else-if="saveStatus === 'saved'" class="tw-text-green-600">
            Changes saved successfully
          </span>
        </div>
        
        <GoodButton
          :loading="saveStatus === 'saving'"
          :disabled="!hasUnsavedChanges || !isFormValid"
          class="tw-px-6 tw-py-2"
          @click="saveAbout"
        >
          <div class="tw-flex tw-items-center tw-gap-2">
            <v-icon size="16">
              {{ saveStatus === 'saving' ? 'mdi-loading mdi-spin' : 'mdi-content-save' }}
            </v-icon>
            <span>Save Changes</span>
          </div>
        </GoodButton>
      </div>
    </v-card-text>
  </v-card>
</template>

<script>
import { defineComponent, ref, computed, watch, onUnmounted } from '@nuxtjs/composition-api'

import { useToast, useApi } from '~/composables/useCommon'
import GoodButton from '~/components/GoodButton.vue'

export default defineComponent({
  name: 'AboutSection',
  components: {
    GoodButton,
  },
  props: {
    about: {
      type: String,
      default: '',
    },
  },
  emits: ['update:about', 'saved'],
  setup(props, { emit }) {
    const $toast = useToast()
    const $axios = useApi()
    
    // Form refs
    const aboutFormRef = ref(null)
    
    // Local state
    const localAbout = ref(props.about || '')
    const saveStatus = ref('') // '', 'saving', 'saved', 'error'
    const hasUnsavedChanges = ref(false)
    const aboutFocused = ref(false)
    
    // Auto-save timer
    let autoSaveTimer = null
    
    // Form validation rules
    const formRules = {
      about: [v => !v || v.length <= 500 || 'About me must not exceed 500 characters'],
    }
    
    // Computed properties
    const safeAboutText = computed({
      get() {
        return localAbout.value === null || localAbout.value === 'null' ? '' : localAbout.value
      },
      set(value) {
        localAbout.value = value
      }
    })
    
    const isFormValid = computed(() => {
      return !safeAboutText.value || safeAboutText.value.length <= 500
    })
    
    // Watch for prop changes
    watch(() => props.about, (newVal) => {
      localAbout.value = newVal || ''
      hasUnsavedChanges.value = false
    })
    
    // Methods
    const setAboutFocused = (focused) => {
      aboutFocused.value = focused
    }
    
    const onAboutInput = () => {
      hasUnsavedChanges.value = true
      saveStatus.value = ''
      
      // Clear existing timer
      if (autoSaveTimer) {
        clearTimeout(autoSaveTimer)
      }
      
      // Set up auto-save with 3 second delay (longer for textarea)
      autoSaveTimer = setTimeout(() => {
        if (hasUnsavedChanges.value && isFormValid.value) {
          saveAbout()
        }
      }, 3000)
    }
    
    const saveAbout = async () => {
      if (!isFormValid.value) {
        $toast.error('About text must not exceed 500 characters').goAway(3000)
        return
      }
      
      saveStatus.value = 'saving'
      
      try {
        const requestData = {
          about: safeAboutText.value === '' ? null : safeAboutText.value
        }
        
        // If no changes, don't make request
        if (requestData.about === props.about) {
          saveStatus.value = ''
          hasUnsavedChanges.value = false
          return
        }
        
        const response = await $axios.post('user/update-basic-info', requestData)
        
        // Update parent component
        emit('update:about', safeAboutText.value)
        emit('saved', response.data)
        
        saveStatus.value = 'saved'
        hasUnsavedChanges.value = false
        
        $toast.success('About section updated successfully').goAway(3000)
        
        // Clear saved status after 3 seconds
        setTimeout(() => {
          if (saveStatus.value === 'saved') {
            saveStatus.value = ''
          }
        }, 3000)
        
      } catch (error) {
        console.error('Error updating about:', error)
        saveStatus.value = 'error'
        
        const errorMessage = error.response?.data?.message || 'Error updating about section'
        $toast.error(errorMessage).goAway(3000)
        
        // Clear error status after 5 seconds
        setTimeout(() => {
          if (saveStatus.value === 'error') {
            saveStatus.value = ''
          }
        }, 5000)
      }
    }
    
    // Cleanup
    const cleanup = () => {
      if (autoSaveTimer) {
        clearTimeout(autoSaveTimer)
      }
    }
    
    // Lifecycle
    onUnmounted(() => {
      cleanup()
    })
    
    return {
      aboutFormRef,
      safeAboutText,
      saveStatus,
      hasUnsavedChanges,
      aboutFocused,
      formRules,
      isFormValid,
      setAboutFocused,
      onAboutInput,
      saveAbout,
    }
  },
})
</script>
