<script lang="ts">
// @ts-nocheck

import { defineComponent, ref } from '@nuxtjs/composition-api'

import { useAuthStore } from '~/composables/useAuthStore'
import GoodButton from '~/components/GoodButton.vue'

export default defineComponent({
  name: 'AuthLoginForm',

  components: { GoodButton },

  props: {
    dense: {
      type: Boolean,
      default: false,
    },
    showRememberMe: {
      type: Boolean,
      default: true,
    },
    showForgotPassword: {
      type: Boolean,
      default: true,
    },
    showSignupLink: {
      type: Boolean,
      default: true,
    },
    customClass: {
      type: String,
      default: '',
    },
  },

  setup(_props) {
    const { login, sendResetPasswordEmail, isSubmitting } = useAuthStore()
    const formRef = ref(null)
    const email = ref('')
    const password = ref('')
    const rememberMe = ref(false)
    const valid = ref(true)
    const errors = ref([])

    // Reset password state
    const showResetPassword = ref(false)
    const resetEmail = ref('')
    const resetError = ref<[string, string]>(['', ''])

    // Add email validation rules
    const emailRules = [
      (v: string) => !!v || 'Email is required',
      (v: string) => /.+@.+\..+/.test(v) || 'Email must be valid',
    ]

    // Load remembered email if exists
    if (typeof window !== 'undefined') {
      const savedEmail = localStorage.getItem('rememberedEmail')
      if (savedEmail) {
        email.value = savedEmail
        rememberMe.value = true
      }
    }

    const handleLogin = async () => {
      if (!formRef.value?.validate()) return

      try {
        // Check if we're in a booking flow and store the path
        if (typeof window !== 'undefined') {
          const currentPath = window.location.pathname
          if (currentPath && currentPath.includes('/bookings/')) {
            console.log('AuthLoginForm: Detected booking flow, storing path:', currentPath)
            localStorage.setItem('twimo_auth_redirect', currentPath)
          }
        }

        const result = await login({
          email: email.value,
          password: password.value,
        })

        if (result.success) {
          // Handle remember me
          if (rememberMe.value) {
            localStorage.setItem('rememberedEmail', email.value.trim())
          } else {
            localStorage.removeItem('rememberedEmail')
          }
        } else {
          errors.value = [
            result.error?.response?.data?.error || 'Login failed. Please check your credentials.',
          ]
        }
      } catch (error) {
        errors.value = ['An unexpected error occurred']
      }
    }

    const handleResetPassword = async () => {
      try {
        await sendResetPasswordEmail(resetEmail.value)
      } catch (error) {
        console.error('Reset password error:', error)
      }
      // Always show success message regardless of result
      resetError.value = [
        'If an account exists with this email, you will receive password reset instructions.',
        'green',
      ]
      setTimeout(() => {
        resetError.value = ['', '']
        showResetPassword.value = false
      }, 5000)
    }

    return {
      formRef,
      email,
      password,
      rememberMe,
      valid,
      errors,
      isSubmitting,
      handleLogin,
      showResetPassword,
      resetEmail,
      resetError,
      handleResetPassword,
      emailRules,
    }
  },
})
</script>

<template>
  <div class="tw-flex tw-flex-col tw-gap-8">
    <template v-if="!showResetPassword">
      <v-form
        ref="formRef"
        v-model="valid"
        lazy-validation
        :class="customClass"
        class="tw-flex tw-flex-col tw-gap-8"
      >
        <v-text-field
          v-model="email"
          :rules="[v => !!v || 'Email is required']"
          type="email"
          hide-details
          outlined
          label="Email Address"
          :dense="dense"
          class="tw-bg-white tw-rounded-xl tw-shadow-md"
          @keyup.enter.prevent="handleLogin"
        />

        <v-text-field
          v-model="password"
          :rules="[v => !!v || 'Password is required']"
          label="Password"
          type="password"
          outlined
          class="tw-bg-white tw-rounded-xl tw-shadow-md"
          hide-details
          :dense="dense"
          @keyup.enter.prevent="handleLogin"
        />

        <div
          v-if="showRememberMe || showForgotPassword"
          class="tw-flex tw-justify-between tw-items-center"
        >
          <v-checkbox
            v-if="showRememberMe"
            v-model="rememberMe"
            label="Remember me"
            hide-details
            class="tw-mt-0 tw-mb-0 white-label-checkbox"
          />
          <a
            v-if="showForgotPassword"
            class="tw-cursor-pointer tw-text-white tw-text-sm tw-font-semibold"
            @click="showResetPassword = true"
          >
            Forgot Password?
          </a>
        </div>

        <span
          v-for="(error, index) in errors"
          :key="index"
          class="tw-text-red-500 tw-text-center tw-mx-auto tw-font-semibold"
          >{{ error }}</span
        >

        <div class="tw-flex tw-flex-col tw-justify-center tw-items-center tw-gap-4">
          <good-button
            class="tw-text-xl sm:tw-w-1/2 tw-w-full"
            :disabled="!valid"
            :loading="isSubmitting"
            @click.prevent="handleLogin"
          >
            {{ isSubmitting ? 'Logging in...' : 'Login' }}
          </good-button>
        </div>

        <div v-if="showSignupLink" class="tw-text-white tw-text-center tw-text-lg">
          Don't have an account?
          <a class="tw-font-semibold tw-text-white tw-underline" @click="$router.push('/signup')"
            >sign up now</a
          >
        </div>
      </v-form>
    </template>

    <v-form v-else class="tw-flex tw-flex-col tw-gap-4 tw-mt-4">
      <v-text-field
        v-model="resetEmail"
        class="tw-bg-white"
        type="email"
        label="Email Address"
        outlined
        :rules="emailRules"
        hide-details="auto"
        :loading="isSubmitting"
      />

      <good-button
        class="tw-text-xl tw-w-1/2 tw-mx-auto"
        :disabled="!resetEmail || !emailRules.every(rule => rule(resetEmail) === true)"
        :loading="isSubmitting"
        @click="handleResetPassword"
      >
        Reset Password
      </good-button>

      <div :class="['text-center font-bold', `${resetError[1]}--text`]">
        {{ resetError[0] }}
      </div>

      <div class="tw-text-white tw-text-center tw-text-sm">
        <a
          class="tw-cursor-pointer tw-font-semibold tw-text-white hover:tw-underline"
          @click="showResetPassword = false"
        >
          Back to Login
        </a>
      </div>
    </v-form>
  </div>
</template>

<style scoped>
::v-deep .white-label-checkbox .v-label {
  @apply tw-text-white tw-font-semibold tw-text-sm;
}
</style>
