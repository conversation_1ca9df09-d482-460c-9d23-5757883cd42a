<script lang="ts">
// @ts-nocheck

import {
  computed,
  defineComponent,
  onMounted,
  ref,
  useRouter,
  useRoute,
} from '@nuxtjs/composition-api'


import BeastImage from '~/components/BeastImage.vue'
import HomeFirstBasicInfo from '~/components/HomeFirstBasicInfo.vue'
import HomeSecondBasicInfo from '~/components/HomeSecondBasicInfo.vue'
import { useApi, useIsUserLoggedIn, useToast } from '~/composables/useCommon'
import { TOAST_DURATION } from '~/constants'
import { formatNumberToDisplay } from '~/helpers'
import { HomeTo } from '~/types'
import { useSavedHomeCategoriesStore } from '~/composables/useSavedHomeCategoriesStore'

import GoodButton from './GoodButton.vue'


export default defineComponent({
  name: 'HotHomeCard',

  components: {
    BeastImage,
    HomeSecondBasicInfo,
    HomeFirstBasicInfo,
    GoodButton,
  },

  props: {
    data: {
      type: Object,
      required: true,
    },

    dates: {
      type: Object,
      required: false,
      default: null,
    },

    smallCard: {
      type: Boolean,
      default: () => false,
    },

    hasSwapEnabled: {
      type: Boolean,
      default: () => false,
    },

    hideSaveIcon: {
      type: Boolean,
      default: () => false,
    },

    isLoggedIn: {
				type: Boolean,
				default: () => false,
    },

    userType: {
        type: String,
        default: () => 'public',
    }
  },

  setup(props) {
    const api = useApi()
    const toast = useToast()
    const router = useRouter()
    const imageSrc = ref('')
    const saveDialog = ref(false)
    const newCategoryName = ref('')
    const showNewListInput = ref(false)
    const isUserLoggedIn = useIsUserLoggedIn()
    const route = useRoute()
    const navigationInProgress = ref(false)

    // Use the store
    const savedHomeCategoriesStore = useSavedHomeCategoriesStore()

    // Computed property for isHomeSaved
    const isHomeSaved = computed(() => savedHomeCategoriesStore.isHomeSaved(props.data.id))

    onMounted(async () => {
      imageSrc.value = props.data.photos[0]?.thumb || null
      // Fetch categories if they haven't been loaded yet
      if (savedHomeCategoriesStore.categories.length === 0) {
        await savedHomeCategoriesStore.fetchCategories()
      }
    })

    const handleSaveHome = (e: Event) => {
      e.preventDefault()
      e.stopPropagation()

      if (!isUserLoggedIn.value) {
        router.push('/login')
        return
      }

      saveDialog.value = true
    }

    const saveToCategory = async (categoryId: number) => {
      try {
        await api.post(`saved-home-categories/${categoryId}/homes/${props.data.id}`)
        await savedHomeCategoriesStore.refreshCategories(api)
        saveDialog.value = false
        toast.success('Home saved successfully').goAway(TOAST_DURATION)
      } catch (error) {
        console.error('Failed to save home:', error)
        toast.error('Failed to save home').goAway(TOAST_DURATION)
      }
    }

    const createNewCategory = async () => {
      if (!newCategoryName.value) return

      try {
        await api.post('saved-home-categories', {
          name: newCategoryName.value,
        })

        await savedHomeCategoriesStore.refreshCategories(api)

        newCategoryName.value = ''
        showNewListInput.value = false
        toast.success('Category created').goAway(TOAST_DURATION)
      } catch (error) {
        toast.error('Failed to create category').goAway(TOAST_DURATION)

        console.error('Failed to create category:', error)
      }
    }

    const homeLocation = computed(() => {
      const isValid = (str: string) => !(str === 'null' || str === null || str === ' ')

      const city = isValid(props.data.city_long) ? `${props.data.city_long},` : ''
      const state = isValid(props.data.state_long) ? `${props.data.state_long},` : ''
      const country = isValid(props.data.country_long) ? props.data.country_long : ''

      return `${city} ${state} ${country}`.trim()
    })

    const trimTitle = computed(() => {
      const val = props.data.title
      if (val && val.length > 65) {
        return val.slice(0, 65) + '...'
      }
      return val
    })

    const linkTo = computed(() => {
      const params = { path: `/${props.data.slug}` }
      if (route.value.query.allow_swaps) {
        params.query = { booking_type: 'swap' }
      }
      console.log('Computed linkTo value:', params);

      if(props.data.allow_booking == 0 && props.data.allow_swaps == 1 && !isUserLoggedIn.value){
					params = { path: `/login` }
			}

      return params
    })

    // Modified to use programmatic navigation instead of NuxtLink
    const navigateToHome = event => {
      event.preventDefault()

      // Debounce navigation requests
      if (navigationInProgress.value) {
        console.log('Navigation already in progress, ignoring click')
        return
      }

      navigationInProgress.value = true
      console.log('Card clicked - navigating to:', props.data.slug)
      console.log('Current route:', route.value.fullPath)
      console.log('LinkTo computed value:', linkTo.value)

      // If navigating to login page, store the home slug for redirect after login
      if (linkTo.value.path === '/login' && process.client) {
        // Store the home page URL to redirect back after login/signup
        localStorage.setItem('twimo_auth_redirect', `/${props.data.slug}`)
        console.log('Setting redirect path before login:', `/${props.data.slug}`)
      }

      // Use programmatic navigation
      router.push(linkTo.value)

      // Reset navigation flag after a delay
      setTimeout(() => {
        navigationInProgress.value = false
      }, 1000)
    }

    return {
      imageSrc,
      isUserLoggedIn,
      formatNumberToDisplay,
      homeLocation,
      trimTitle,
      saveDialog,
      categories: computed(() => savedHomeCategoriesStore.categories),
      newCategoryName,
      isHomeSaved,
      handleSaveHome,
      saveToCategory,
      createNewCategory,
      showNewListInput,
      linkTo,
      isHomeInCategory: category =>
        savedHomeCategoriesStore.isHomeInCategory(category.id, props.data.id),
      navigateToHome,
    }
  },

  methods: {
			emitDisplayModal() {
				console.log('Emitting displaySignupModal')
				this.$emit('displaySignupModal');
			}
	},
})
</script>

<template>
  <div class="text-decoration-none" @click="((data.allow_booking == 0 && data.allow_swaps == 1 && !isUserLoggedIn.value && userType != 'host') || (userType == 'host' && !hasSwapEnabled))? emitDisplayModal() :  navigateToHome($event)">
    <v-card
      :height="smallCard ? '400' : '430'"
      class="border-left-top border-right-top transition-swing"
      elevation="0"
    >
      <v-card-text class="pa-0">
        <div class="tw-relative">
          <!-- Just the save icon -->
          <v-tooltip v-if="!hideSaveIcon" bottom color="primary">
            <template #activator="{ on, attrs }">
              <div
                v-bind="attrs"
                class="tw-absolute tw-top-2 tw-right-2 tw-z-10"
                v-on="on"
                @click.prevent.stop="handleSaveHome"
              >
                <v-icon
                  class="tw-cursor-pointer"
                  :class="isHomeSaved ? 'tw-text-primary' : 'tw-text-white'"
                >
                  {{ isHomeSaved ? 'mdi-bookmark' : 'mdi-bookmark-outline' }}
                </v-icon>
              </div>
            </template>
            <span class="tw-text-xs">Save this home</span>
          </v-tooltip>



          <BeastImage
            class="border-left-top border-right-top tw-rounded-b-lg tw-rounded-t-lg explore-image-container tw-object-cover tw-h-[260px] tw-max-w-full"
            :src="data.photos[0]?.thumb ?? require('~/assets/explore-missing-home.webp')"
            :alt="data.title"
            :title="data.title"
          />
        </div>

        <!--            Info part-->
        <div class="tw-mt-1">
          <!--              Title & Address-->
          <div class="pa-0">
            <div v-if="homeLocation.length" class="tw-flex tw-flex-row tw-items-center">
              <span class="tw-text-[1.2rem] tw-pt-1 homecard-location">{{ homeLocation }}</span>
            </div>

            <div class="tw-text-[1rem] homecard-title">
              {{ trimTitle }}
            </div>

            <div
              v-if="data.allow_booking && data.nightly_rate"
              class="tw-text-[1.2rem] tw-pt-[5px] tw-pb-[5px] homecard-pricing"
            >
              ${{ formatNumberToDisplay(+data.nightly_rate) }} nightly rate
            </div>
          </div>

          <!--              Basic infos-->
          <div class="d-flex align-center justify-between mt-2 tw-flex-wrap" style="color: #232323">
            <!-- Home basic infos -->
            <HomeFirstBasicInfo :data="data" />

            <!-- Home basic infos -->
            <HomeSecondBasicInfo :data="data" />

            <div
              class="tw-flex tw-flex-row tw-items-center tw-justify-end tw-flex-grow"
              style="justify-content: end; align-items: end"
            >
              <v-tooltip bottom color="primary">
                <template #activator="{ on, attrs }">
                  <div v-bind="attrs" v-on="on">
                    <div
                      v-if="data.allow_swaps"
                      class="v-icon notranslate cursor-pointer v-icon--link mdi mdi-swap-horizontal-circle theme--dark tw-text-black"
                    ></div>
                  </div>
                </template>
                <span class="tw-text-xs"> Can be swapped </span>
              </v-tooltip>
            </div>
          </div>
        </div>

        <v-dialog v-model="saveDialog" max-width="500">
          <v-card class="pa-6">
            <div class="tw-flex tw-justify-between tw-items-center tw-mb-6">
              <div class="tw-text-2xl tw-font-semibold tw-text-primary">
                Add to Saved Homes
              </div>
              <v-icon @click="saveDialog = false">mdi-close-circle-outline</v-icon>
            </div>

            <div class="tw-grid tw-grid-cols-2 tw-gap-4 tw-mb-4">
              <!-- Category boxes -->
              <div
                v-for="category in categories"
                :key="category.id"
                class="tw-cursor-pointer tw-relative"
                @click="saveToCategory(category.id)"
              >
                <div class="tw-aspect-square tw-rounded-lg tw-overflow-hidden tw-relative">
                  <BeastImage
                    class="tw-w-full tw-h-full tw-object-cover"
                    :src="category.thumbnail"
                    :alt="category.name"
                    :title="category.name"
                  />
                  <!-- Centered checkmark icon (in image only) -->
                  <v-icon
                    v-if="isHomeInCategory(category)"
                    class="tw-absolute tw-left-1/2 tw-top-1/2 tw-transform -tw-translate-x-1/2 -tw-translate-y-1/2 tw-text-primary"
                    size="36"
                  >
                    mdi-check-circle
                  </v-icon>
                </div>
                <div class="tw-p-3">
                  <span class="tw-text-lg tw-text-zinc-500">{{ category.name }}</span>
                </div>
              </div>

              <!-- Add New List section -->
              <div class="tw-cursor-pointer">
                <div
                  class="tw-aspect-square tw-rounded-lg tw-border-2 tw-border-dashed tw-border-gray-300 tw-flex tw-items-center tw-justify-center"
                  @click="showNewListInput = true"
                >
                  <v-icon large class="tw-text-gray-400">mdi-plus-circle-outline</v-icon>
                </div>
                <div class="tw-p-3">
                  <span class="tw-text-lg tw-text-zinc-500">Add New</span>
                </div>
              </div>
            </div>

            <!-- New List Input Dialog -->
            <v-dialog v-model="showNewListInput" max-width="400">
              <v-card class="pa-6">
                <div class="tw-text-2xl tw-mb-6 tw-text-zinc-600 tw-font-semibold">
                  Add New Category
                </div>
                <v-text-field v-model="newCategoryName" label="Enter Name" outlined dense />
                <div class="tw-flex tw-justify-end tw-mt-4">
                  <GoodButton class="tw-py-2 tw-text-lg" @click="createNewCategory">
                    save
                  </GoodButton>
                </div>
              </v-card>
            </v-dialog>

            <!-- Link to saved homes page -->
            <div class="tw-mt-4 tw-text-center">
              <NuxtLink
                to="/saved-homes"
                class="tw-text-primary hover:tw-underline tw-text-base"
                @click="saveDialog = false"
              >
                View all my saved homes
              </NuxtLink>
            </div>
          </v-card>
        </v-dialog>
      </v-card-text>
    </v-card>
  </div>
</template>

<style scoped>
.border-right-top {
  border-top-right-radius: 6px;
}

.border-left-top {
  border-top-left-radius: 6px;
}

.wdp-ribbon {
  display: inline-block;
  padding: 1px 8px;
  position: absolute;
  right: -5px;
  top: -5px;
  line-height: 18px;
  height: 22px;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 0;
  text-shadow: none;
  font-weight: bold;
  font-size: 8px;
  background-color: white !important;
  color: black;
}

.wdp-ribbon-two:before {
  display: inline-block;
  content: '';
  position: absolute;
  top: -3px;
  border-width: 10px 6px;
  border-color: white;
  border-left-color: transparent !important;
  left: -7px;
}

.explore-image-container {
  border-radius: 18px !important;
  /* box-shadow: 0px 3px 4px #00000066; */
  margin-bottom: 10px;
}

.homecard-location {
  font-size: 1.2rem;
  /* font-family: 'Proxima Nova'; */
  color: #6c6b6b;
  font-weight: 600;
}

.homecard-title,
.homecard-pricing {
  color: #6c6b6b;
  /* font-family: 'Proxima Nova'; */
}

.text-decoration-none {
  text-decoration: none;
  cursor: pointer;
}
</style>
  text-decoration: none;
  cursor: pointer;
}
</style>
