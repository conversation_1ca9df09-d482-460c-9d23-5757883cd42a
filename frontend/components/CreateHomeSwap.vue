<script lang="ts">
// @ts-nocheck
import { defineComponent } from '@nuxtjs/composition-api'

import GoodButtonReverted from '~/components/GoodButtonReverted.vue'
import GoodButton from '~/components/GoodButton.vue'
import { useCreateHomeProgressStore } from '~/composables/useCreateHomeStore'
import { rentalPlans } from '~/constants'

export default defineComponent({
  name: 'CreateHomeSwap',

  components: { GoodButton, GoodButtonReverted },

  setup() {
    const store = useCreateHomeProgressStore()

    const { createHomeData, setCurrentStep, validateCreateHomeData, clickOnRentalPlan } = store

    return {
      createHomeData,
      validateCreateHomeData,
      rentalPlans,
      setCurrentStep,
      clickOnRentalPlan,
    }
  },
})
</script>

<template>
  <v-row>
    <v-col cols="12" class="tw-mt-8 tw-flex tw-flex-col tw-gap-4">
      <div class="tw-text-2xl tw-font-semibold tw-text-zinc-600">Home Swap</div>

      <div class="tw-text-lg tw-font-medium tw-text-zinc-500">
        As a Twimo Hosts you have the exclusive offer to take part of our Home Swapping program!
      </div>

      <div class="tw-text-zinc-500">
        Twimo Hosts have the ability to swap with other Twimo Host around the world. Would you be
        interested in marking this home as “Open” to home swapping?
      </div>
    </v-col>

    <v-col cols="12" md="8">
      <GoodButton
        v-if="createHomeData.isSwap"
        class="tw-w-full tw-text-2xl tw-py-8 tw-justify-start"
        @click="createHomeData.isSwap = !createHomeData.isSwap"
      >
        Yes!
      </GoodButton>

      <GoodButtonReverted
        v-else
        class="tw-w-full tw-text-2xl tw-py-8 tw-justify-start"
        @click="createHomeData.isSwap = !createHomeData.isSwap"
      >
        Yes!
      </GoodButtonReverted>
    </v-col>

    <v-col cols="12" md="8">
      <GoodButton
        v-if="!createHomeData.isSwap"
        class="tw-w-full tw-text-2xl tw-py-8 tw-justify-start"
        @click="createHomeData.isSwap = !createHomeData.isSwap"
      >
        No, not right now
      </GoodButton>

      <GoodButtonReverted
        v-else
        class="tw-w-full tw-text-2xl tw-py-8 tw-justify-start"
        @click="createHomeData.isSwap = !createHomeData.isSwap"
      >
        No, not right now
      </GoodButtonReverted>
    </v-col>

    <v-col cols="12" class="tw-text-zinc-500">
      You can always decline Swap proposals, or change these Settings in the future
    </v-col>
  </v-row>
</template>

<style scoped></style>
