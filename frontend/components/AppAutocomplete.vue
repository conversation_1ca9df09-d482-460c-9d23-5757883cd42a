<script lang="ts">
// @ts-nocheck

import { defineComponent } from '@nuxtjs/composition-api'

export default defineComponent({
  props: {
    address: {
      type: String,
      default: () => null,
    },
    label: {
      type: String,
      default: () => 'Address*',
    },
    filterLabel: {
      type: Boolean,
      default: () => false,
    },
    dense: {
      type: Boolean,
      default: () => true,
    },
    color: {
      type: String,
      default: () => 'primary',
    },
    clearable: {
      type: Boolean,
      default: () => false,
    },
    showTrendingLocations: {
      type: <PERSON>olean,
      default: () => false,
    },
    type: {
      type: String,
      default: () => 'textview',
    },
    onFocusIn: {
      type: Function,
      default: () => null,
    },
    onFocusOut: {
      type: Function,
      default: () => null,
    },
    extraProps: {
      type: Object as () => {
        rounded?: boolean
        filled?: boolean
        'solo-inverted'?: boolean
        solo?: boolean
        flat?: boolean
      },
      default: () => ({}),
    },
  },

  data: () => ({
    value: '',
    search: '',
    items: [],
    loading: false,
    debounce: null,
    hideData: true,
  }),

  watch: {
    address: function (val) {
      if (val) {
        this.items.push(val)
        this.value = val
      }
    },

    search: function (val) {
      if (!val || val.length < 3) {
        this.items = []
        return
      }

      clearTimeout(this.debounce)

      this.debounce = setTimeout(() => {
        this.getLocations()
      }, 300)
    },

    type: function () {
      this.reset()
    },
  },

  mounted() {
    if (this.address) {
      this.value = this.address
      this.items.push(this.address)
    }
  },

  methods: {
    getLocations() {
      if (!this.search || this.search.length < 3) {
        this.items = []
        return
      }

      this.loading = true

      this.$axios
        .get(`/geo/places/search?input=${this.search.trim()}`)
        .then(
          (resp: {
            data: {
              predictions: {
                description: string
              }[]
            }
          }) => {
            this.loading = false
            const data = resp.data.predictions.map(el => {
              return el.description
            })

            if (this.type === 'mapview') {
              return (this.items = data)
            }

            if (this.showTrendingLocations) {
              return this.$emit('searched-locations', data)
            }

            this.items = data
          }
        )
        .catch(err => console.error(err))
    },

    handleChange(val: any) {
      this.$emit('address-selection', val)
    },

    setAddress(val: string) {
      this.value = val
      this.items.push(val)
    },

    reset() {
      this.value = ''
      this.search = ''
      this.items = []
      this.loading = false
    },
  },
})
</script>

<template>
  <v-autocomplete
    ref="locations"
    v-model="value"
    v-model:search-input="search"
    :dense="dense"
    :label="label"
    outlined
    :items="items"
    :loading="loading"
    :color="color"
    hide-details
    :hide-no-data="hideData"
    :clearable="clearable"
    :placeholder="showTrendingLocations ? 'In' : ''"
    :rounded="extraProps.rounded"
    :filled="extraProps.filled"
    :solo-inverted="extraProps['solo-inverted']"
    :solo="extraProps.solo"
    :flat="extraProps.flat"
    @onblur="onFocusOut"
    @change="handleChange"
    @focusin="onFocusIn"
    @focusout="onFocusOut"
    @click:clear="reset"
  >
    <template v-if="filterLabel" #label>
      <div>
        <v-icon color="#7C0CB1"> mdi-map-marker </v-icon>
        <span class="tw-ml-1 tw-text-black">{{ label }}</span>
      </div>
    </template>
  </v-autocomplete>
</template>
