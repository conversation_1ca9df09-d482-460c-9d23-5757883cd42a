<script lang="ts">
import { defineComponent } from '@nuxtjs/composition-api'

export default defineComponent({
  name: 'GoodCreateButton',
  props: {
    text: {
      type: String,
      default: 'Create',
    },
  },
})
</script>

<template>
  <v-btn outlined color="black" v-on="$listeners">
    <span class="tw-text-slate-900">{{ text }}</span>
    <v-icon class="tw-ml-2" color="black"> mdi-plus </v-icon>
  </v-btn>
</template>

<style scoped></style>
