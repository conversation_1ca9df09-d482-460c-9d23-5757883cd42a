<script lang="ts">
import { defineComponent, computed } from '@nuxtjs/composition-api'

import GoodButton from '~/components/GoodButton.vue'

export default defineComponent({
  components: {
    GoodButton
  },
  props: {
    value: {
      type: String,
      required: true
    }
  },

  setup(props, { emit }) {
    const isSwap = computed(() => props.value === 'SWAP')

    const toggleBookingType = (type: string) => {
      if (type !== props.value) {
        emit('input', type)
      }
    }

    return {
      isSwap,
      toggleBookingType
    }
  }
})
</script>

<template>
  <div class="booking-type-toggle tw-mb-6 tw-px-4">
    <div class="tw-flex tw-justify-between tw-items-center tw-w-full">
      <div class="tw-flex tw-justify-center tw-bg-gray-100 tw-rounded-full tw-p-1 tw-shadow-sm tw-max-w-md">
        <button
          class="tw-flex-1 tw-py-2 tw-px-3 sm:tw-px-4 tw-rounded-full tw-text-sm sm:tw-text-base tw-font-medium tw-transition-all tw-duration-200 tw-flex tw-items-center tw-justify-center"
          :class="!isSwap ? 'tw-bg-primary tw-text-white tw-shadow-md' : 'tw-text-gray-600 hover:tw-bg-gray-200'"
          @click="toggleBookingType('RENT')"
        >
          <v-icon small class="tw-mr-1 sm:tw-mr-2" :color="!isSwap ? 'white' : 'grey darken-1'">mdi-home</v-icon>
          <span>Bookings</span>
        </button>
        <button
          class="tw-flex-1 tw-py-2 tw-px-3 sm:tw-px-4 tw-rounded-full tw-text-sm sm:tw-text-base tw-font-medium tw-transition-all tw-duration-200 tw-flex tw-items-center tw-justify-center"
          :class="isSwap ? 'tw-bg-primary tw-text-white tw-shadow-md' : 'tw-text-gray-600 hover:tw-bg-gray-200'"
          @click="toggleBookingType('SWAP')"
        >
          <v-icon small class="tw-mr-1 sm:tw-mr-2" :color="isSwap ? 'white' : 'grey darken-1'">mdi-swap-horizontal</v-icon>
          <span>Swaps</span>
        </button>
      </div>

      <GoodButton
        class="tw-ml-2"
        @click="$router.push('/explore?page=1&per_page=12' + (isSwap ? '&allow_swaps=1' : ''))"
      >
        Find Homes
      </GoodButton>
    </div>
  </div>
</template>

<style scoped>
.booking-type-toggle {
  position: relative;
  z-index: 1;
}
</style>
