<script lang="ts">
// @ts-nocheck
import { defineComponent } from '@nuxtjs/composition-api'

export default defineComponent({
  name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',

  props: {
    disabled: {
      type: Boolean,
      default: false,
    },
  },
})
</script>

<template>
  <v-btn
    color="#858585"
    outlined
    large
    :disabled="disabled"
    class="tw-rounded-xl tw-py-4"
    v-on="$listeners"
  >
    <div
      class="tw-normal-case tw-tracking-normal tw-text-zinc-500 tw-rounded-xl tw-font-semibold tw-flex tw-flex-row tw-justify-center tw-items-center tw-gap-2"
    >
      <slot></slot>
    </div>
  </v-btn>
</template>

<style scoped></style>
