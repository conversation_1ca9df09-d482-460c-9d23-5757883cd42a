<template>
  <v-btn
    :color="color"
    :outlined="outlined"
    :block="block"
    :disabled="disabled"
    depressed
    :class="!outlined ? `${textColor}--text` : ''"
    :x-small="xsmall"
    :small="small"
    :large="large"
    :text="text"
    :x-large="xlarge"
    :rounded="rounded"
    :style="{
      textTransform: lowercased ? 'lowercase' : 'none',
      fontWeight: 600,
      fontSize: fontSize,
      padding: '0px 8px',
    }"
    v-on="$listeners"
  >
    <div class="d-flex flex-column">
      <small v-if="showButtonHeading" style="text-transform: none">{{ buttonHeading }}</small>
      <div class="d-flex align-center">
        <v-icon v-if="showIcon" class="pr-1">
          {{ iconName }}
        </v-icon>
        {{ buttonText }}
      </div>
    </div>
  </v-btn>
</template>

<script>
export default {
  props: {
    showIcon: {
      type: Boolean,
      default: () => false,
    },
    iconName: {
      type: String,
      default: () => '',
    },
    showButtonHeading: {
      type: Boolean,
      default: () => false,
    },
    buttonHeading: {
      type: String,
      default: () => 'Heading',
    },
    buttonText: {
      type: String,
      default: () => 'Button',
    },
    textColor: {
      type: String,
      default: () => 'white',
    },
    large: {
      type: Boolean,
      default: () => false,
    },
    small: {
      type: Boolean,
      default: () => false,
    },
    xsmall: {
      type: Boolean,
      default: () => false,
    },
    xlarge: {
      type: Boolean,
      default: () => false,
    },
    disabled: {
      type: Boolean,
      default: () => false,
    },
    color: {
      type: String,
      default: () => '#000000',
    },
    text: {
      type: Boolean,
      default: () => false,
    },
    block: {
      type: Boolean,
      default: () => false,
    },
    outlined: {
      type: Boolean,
      default: () => false,
    },
    rounded: {
      type: Boolean,
      default: () => false,
    },
    fontSize: {
      type: String,
      default: () => '14px',
    },
    lowercased: {
      type: Boolean,
      default: () => false,
    },
  },
}
</script>
