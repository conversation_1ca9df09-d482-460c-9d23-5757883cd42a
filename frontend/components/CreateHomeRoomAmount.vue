<script lang="ts">
// @ts-nocheck
import { defineComponent } from '@nuxtjs/composition-api'

import GoodButton from '~/components/GoodButton.vue'
import GoodButtonIncreaseNumber from '~/components/GoodButtonIncreaseNumber.vue'
import { useCreateHomeProgressStore } from '~/composables/useCreateHomeStore'
import { petFriendlyNumbers, petFriendlyOptions, petFriendlyTypes, rentalPlans } from '~/constants'

export default defineComponent({
  name: 'CreateHomeRoomAmount',

  components: {
    GoodButtonIncreaseNumber,
    GoodButton,
  },

  setup() {
    const store = useCreateHomeProgressStore()

    const {
      createHomeData,
      setCurrentStep,
      validateCreateHomeData,
      clickOnRentalPlan,
      decreaseBedrooms,
      decreaseBeds,
      decreaseBathrooms,
      decreaseGuests,
      decreasePets,
    } = store

    return {
      createHomeData,
      validateCreateHomeData,
      rentalPlans,
      setCurrentStep,
      clickOnRentalPlan,
      decreaseBedrooms,
      decreaseBeds,
      decreaseBathrooms,
      decreasePets,
      decreaseGuests,
      petFriendlyOptions,
      petFriendlyTypes,
      petFriendlyNumbers,
    }
  },
})
</script>

<template>
  <v-row>
    <v-col cols="12" class="tw-mt-8 tw-flex tw-flex-col tw-gap-4">
      <div class="tw-text-2xl tw-font-semibold tw-text-zinc-600">Bed, Bath and Guest Count</div>

      <div class="tw-text-lg tw-font-medium tw-text-zinc-500">
        Input your home information below:
      </div>
    </v-col>

    <v-col cols="12" md="6" offset-md="3" class="tw-flex tw-flex-col tw-gap-3">
      <div class="tw-flex tw-flex-row tw-gap-2">
        <GoodButton class="tw-w-1/2 tw-text-lg tw-py-[29px]"> Bedrooms </GoodButton>
        <GoodButtonIncreaseNumber
          class="tw-w-1/2 tw-text-lg tw-py-[28px]"
          :value="createHomeData.bedrooms"
          @decrease="decreaseBedrooms"
          @increase="createHomeData.bedrooms++"
        />
      </div>

      <div class="tw-flex tw-flex-row tw-gap-2">
        <GoodButton class="tw-w-1/2 tw-text-lg tw-py-[29px]"> Beds </GoodButton>
        <GoodButtonIncreaseNumber
          class="tw-w-1/2 tw-text-lg tw-py-[28px]"
          :value="createHomeData.beds"
          @decrease="decreaseBeds"
          @increase="createHomeData.beds++"
        />
      </div>

      <div class="tw-flex tw-flex-row tw-gap-2">
        <GoodButton class="tw-w-1/2 tw-text-lg tw-py-[29px]"> Bathrooms </GoodButton>
        <GoodButtonIncreaseNumber
          class="tw-w-1/2 tw-text-lg tw-py-[28px]"
          :value="createHomeData.bathrooms"
          @decrease="decreaseBathrooms"
          @increase="createHomeData.bathrooms++"
        />
      </div>

      <div class="tw-flex tw-flex-row tw-gap-2">
        <GoodButton class="tw-w-1/2 tw-text-lg tw-py-[29px]"> Guests </GoodButton>
        <GoodButtonIncreaseNumber
          class="tw-w-1/2 tw-text-lg tw-py-[28px]"
          :value="createHomeData.guests"
          @decrease="decreaseGuests"
          @increase="createHomeData.guests++"
        />
      </div>
    </v-col>

    <v-col cols="12" md="6" offset-md="3" class="tw-flex tw-flex-row tw-gap-2 tw-pr-0 tw-pt-0">
      <GoodButton class="tw-w-1/2 tw-text-lg tw-py-[29px]"> Pet Friendly? </GoodButton>
      <div class="tw-w-1/2 tw-flex tw-flex-row tw-gap-2">
        <v-select
          v-model="createHomeData.pets.enabled"
          :items="petFriendlyOptions"
          label="Y / N"
          outlined
          hide-details
          class="tw-w-1/2 tw-rounded-xl tw-border tw-h-fit"
        />
        <!-- <v-select
					v-model="createHomeData.pets.type"
					:items="petFriendlyTypes"
					label="Type"
					outlined
					hide-details
					class="tw-w-1/2 tw-rounded-xl tw-border tw-h-fit"
				></v-select> -->
      </div>
    </v-col>

    <!-- <v-col cols="12" md="3" class="tw-pt-0">
			<v-select
				v-model="createHomeData.pets.number"
				:items="petFriendlyNumbers"
				label="#"
				outlined
				hide-details
				class="tw-w-1/2 tw-rounded-xl tw-border"
			></v-select>
		</v-col> -->
  </v-row>
</template>

<style scoped></style>
