<template>
  <v-row>
    <v-col cols="12" class="tw-p-0">
      <div class="tw-relative">
        <v-carousel
          hide-delimiter-background
          :show-arrows-on-hover="true"
          height="320"
          class="tw-rounded-lg tw-overflow-hidden tw-elevation-2"
        >
          <template #prev="{ on, attrs }">
            <v-btn
              icon
              dark
              v-bind="attrs"
              class="tw-bg-black tw-bg-opacity-30 tw-absolute tw-z-10 tw-ml-2"
              v-on="on"
            >
              <v-icon>mdi-chevron-left</v-icon>
            </v-btn>
          </template>

          <template #next="{ on, attrs }">
            <v-btn
              icon
              dark
              v-bind="attrs"
              class="tw-bg-black tw-bg-opacity-30 tw-absolute tw-z-10 tw-right-0 tw-mr-2"
              v-on="on"
            >
              <v-icon>mdi-chevron-right</v-icon>
            </v-btn>
          </template>

          <v-carousel-item
            v-for="(item, i) in photos.photos"
            :key="i"
            eager
            @click="openAllPictures(item)"
          >
            <div class="tw-relative tw-h-full">
              <v-img
                :alt="`twimo.com - ${photos.title}`"
                height="100%"
                :src="item.thumb"
                eager
                class="tw-transition-transform tw-duration-500 tw-transform-gpu"
              />
              <div
                class="tw-absolute tw-inset-0 tw-bg-gradient-to-b tw-from-black tw-from-0% tw-via-transparent tw-via-30% tw-to-transparent tw-opacity-20"
              ></div>
            </div>
          </v-carousel-item>
        </v-carousel>

        <v-btn
          small
          dark
          class="tw-absolute tw-right-4 tw-top-4 tw-z-10 tw-bg-black tw-bg-opacity-40 tw-shadow-sm"
          @click.stop="openAllPictures"
        >
          <v-icon left small> mdi-image-multiple </v-icon>
          View all
        </v-btn>

        <div class="tw-absolute tw-right-4 tw-bottom-4 tw-z-10">
          <div
            class="tw-bg-black tw-bg-opacity-50 tw-text-white tw-px-3 tw-py-1 tw-rounded-full tw-text-xs tw-font-medium"
          >
            {{ photos.photos ? currentIndex + 1 : 0 }}/{{
              photos.photos ? photos.photos.length : 0
            }}
          </div>
        </div>
      </div>
      <full-screen-gallery ref="fullscreengallery" :house-info="photos" />
    </v-col>
  </v-row>
</template>

<script>
export default {
  props: {
    photos: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      currentIndex: 0,
    }
  },
  watch: {
    '$vuetify.breakpoint.width': {
      handler() {
        // Reset carousel when orientation changes
        this.currentIndex = 0
      },
    },
  },
  methods: {
    openAllPictures(imageSrc = {}) {
      if (imageSrc.src) {
        this.$refs.fullscreengallery.openImage(imageSrc)
      }
      this.$refs.fullscreengallery.toggleDialog()
    },
  },
}
</script>

<style scoped>
.v-carousel >>> .v-window__next,
.v-carousel >>> .v-window__prev {
  margin: 0 !important;
}
</style>
