<script setup>
import { ref } from 'vue'

defineProps({
  items: {
    type: Array,
  },
})

const activeIndex = ref(null)

function beforeEnter(el) {
  el.style.height = '0'
}
function enter(el) {
  el.style.height = el.scrollHeight + 'px'
}
function leave(el) {
  el.style.height = '0'
}
function toggleFaq(index) {
  this.activeIndex = this.activeIndex === index ? null : index
}
function isActive(index) {
  return this.activeIndex === index
}
</script>
<template>
  <div class="">
    <div
      v-for="(item, i) in items"
      :key="i"
      class="tw-bg-[#FFFFFF] tw-rounded-2xl tw-p-5 tw-pb-2 tw-mb-5 tw-shadow-theme"
    >
      <h3
        class="tw-text-base tw-text-[#360877] tw-relative tw-font-medium tw-cursor-pointer tw-m-0 tw-mb-2 tw-mt-0 tw-pr-8"
        @click="toggleFaq(i)"
      >
        {{ item.title }}
        <!-- <span
					class="tw-faq__item--icon tw-absolute tw-right-0 tw-top-2/4"
					:class="isActive(i) ? 'active' : ''"
				>
					<span
						class="tw-block tw-h-[5px] tw-w-5 tw-bg-[#360877] tw-relative tw-transition-all tw-duration-300"
					></span>
					<span
						:class="isActive(i) && 'rotate-[0deg]'"
						class="tw-block tw-h-[5px] tw-w-5 tw-bg-[#360877] tw-relative tw-transition-all tw-duration-300 tw-rotate-90 tw-top-[-5px]"
					></span>
				</span> -->
      </h3>
      <Transition @before-enter="beforeEnter" @enter="enter" @leave="leave">
        <div
          v-show="isActive(i)"
          class="tw-overflow-hidden tw-text-base tw-text-[#360877] tw-transition-all tw-duration-500 tw-ease-in-out"
          v-html="item.content"
        ></div>
      </Transition>
    </div>
  </div>
</template>
<style scoped>
.tw-shadow-theme {
  box-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);
}

.fade-default {
  animation: none;
  opacity: 1;
  transform: none;
}
</style>
