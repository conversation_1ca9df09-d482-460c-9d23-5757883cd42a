<script lang="ts">
import { defineComponent } from '@nuxtjs/composition-api'

import GoodCard from '~/components/GoodCard.vue'

export default defineComponent({
  name: 'GoodDataTable',
  components: { GoodCard },
  props: {
    headers: {
      type: Array,
      default: () => [],
    },
    items: {
      type: Array,
      default: () => [],
    },
    itemsPerPage: {
      type: Number,
      default: 15,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    loadingText: {
      type: String,
      default: 'Loading... Please wait',
    },
    clickRowHandler: {
      type: Function,
      default: () => {},
    },
    sortBy: {
      type: Array,
      default: () => [],
    },
    sortDesc: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      search: '',
      page: 1,
      pageCount: 0,
    }
  },
})
</script>

<template>
  <good-card card-text-classes="tw-p-5">
    <v-card-title class="tw-w-1/3 tw-mb-5 tw-ml-auto">
      <v-text-field
        v-model="search"
        outlined
        dense
        append-icon="mdi-magnify"
        label="Search"
        single-line
        color="black"
        hide-details
      />
    </v-card-title>

    <v-data-table
      v-model:page="page"
      :search="search"
      :loading="loading"
      :loading-text="loadingText"
      elevation="0"
      :headers="headers"
      :items="items"
      :items-per-page="itemsPerPage"
      item-key="id"
      hide-default-footer
      :sort-by="sortBy"
      :sort-desc="sortDesc"
      multi-sort
      class="tw-min-h-[300px]"
      @page-count="pageCount = $event"
      @click:row="clickRowHandler($event)"
    >
      <template v-for="(_, slot) of $slots" #[slot]="scope">
        <slot :name="slot" v-bind="scope"></slot>
      </template>
    </v-data-table>

    <div class="text-center pt-5">
      <v-pagination v-model="page" :length="pageCount" circle color="black" />
    </div>
  </good-card>
</template>

<style>
.v-pagination__item,
.v-pagination__item--active {
  box-shadow: none !important;
}

.v-pagination__navigation {
  box-shadow: none !important;
}

tr {
  cursor: pointer;
}
</style>
