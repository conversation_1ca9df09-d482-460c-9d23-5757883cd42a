<script lang="ts">
// @ts-nocheck
import { defineComponent, computed } from '@nuxtjs/composition-api'

import GoodCard2 from '~/components/GoodCard2.vue'

export default defineComponent({
  name: 'Amenity',

  components: { GoodCard2 },

  props: {
    amenity: {
      type: String,
      required: true,
    },
    isActivated: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },

  setup(props) {
    const cardClass = computed(() => {
      const baseClass = 'tw-px-4 tw-py-2 tw-min-h-[50px] tw-flex tw-flex-col tw-rounded-md '

      if (props.disabled) {
        return (
          baseClass +
          'tw-bg-gray-200 tw-text-gray-500 tw-border tw-border-gray-300 tw-cursor-not-allowed'
        )
      }

      if (props.isActivated) {
        return (
          baseClass + 'tw-bg-primary tw-text-white tw-border tw-border-primary tw-cursor-pointer'
        )
      }

      return (
        baseClass + 'tw-bg-white tw-text-zinc-600 tw-border tw-border-zinc-500 tw-cursor-pointer'
      )
    })

    return { cardClass }
  },
})
</script>

<template>
  <good-card2 :class="cardClass">
    <div class="tw-flex tw-items-center tw-justify-between">
      <div class="tw-font-semibold tw-text-lg">
        {{ amenity }}
      </div>
    </div>
  </good-card2>
</template>

<style scoped>
.tw-cursor-not-allowed {
  pointer-events: none;
}
</style>
