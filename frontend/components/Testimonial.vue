<template>
  <div class="testimonials_container">
    <div v-for="(item, i) in testimonials" :key="i" class="testimonial__item">
      <div class="testimonial__item--image">
        <img :src="item.image" :alt="item.name" />
      </div>
      <div class="testimonial__item--comment">“{{ item.comment }}”</div>
      <span class="testimonial__item--name">{{ item.name }}</span>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      testimonials: [
        {
          image: require('~/assets/customer-1.jpg'),
          comment:
            'We love to explore new places and our favorite thing about using points is the flexibility it gives us to travel more often.',
          name: '<PERSON>',
        },
        {
          image: require('~/assets/reviewer-profile.jpeg'),
          comment:
            'We began to rent our home in Tahoe to only friends and family a year ago but had a hard time managing ourselves. This platform made bookings so much easier, and more convenient to be able to charge cleaning fees directly.',
          name: '<PERSON>',
        },
        {
          image: require('~/assets/customer-2.jpeg'),
          comment:
            'We love to travel, but with owning a vacation home we have a hard time justifying the cost. The ability to swap with other homeowners for free has opened up a whole new world of exploration for us!',
          name: '<PERSON> B',
        },
        {
          image: require('~/assets/customer-3.jpeg'),
          comment:
            'I love using this platform because it draws more traffic to my short term rental.',
          name: 'Keith D',
        },
        {
          image: require('~/assets/customer-4.jpeg'),
          comment: 'It’s way easier to find deals on this site.',
          name: 'Lindsey B',
        },
      ],
    }
  },
}
</script>
<style scoped>
.testimonial__item {
  width: 31%;
  background-color: #fbfbfb;
  box-shadow: 0 4px 4px rgba(0, 0, 0, 0.25);
  border-radius: 20px;
  padding: 25px;
}
.testimonial__item--image {
  margin-bottom: 30px;
}
.testimonial__item--image img {
  width: 130px;
  height: 130px;
  object-fit: cover;
  border-radius: 50%;
  display: block;
  /* margin: auto; */
}
.testimonial__item--comment {
  line-height: 25px;
  font-size: 18px;
  font-weight: 500;
  color: #7b7b7b;
}
.testimonial__item--name {
  color: #7b7b7b;
  margin-top: 20px;
  font-weight: 600;
  font-size: 18px;
  display: block;
}
@media screen and (min-width: 768px) {
  .testimonial__item--image img {
    /* padding: 20px; */
    box-sizing: border-box;
  }
}
@media screen and (max-width: 1024px) and (min-width: 768px) {
  .testimonial__item {
    width: 47%;
  }
}
@media screen and (max-width: 767px) {
  .testimonial__item {
    width: 100%;
  }
  .testimonial__item--comment {
    line-height: 22px;
  }
}
</style>
