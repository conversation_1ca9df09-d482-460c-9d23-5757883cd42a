<script lang="ts">
// @ts-nocheck

import { defineComponent } from '@nuxtjs/composition-api'

export default defineComponent({
  props: {
    loading: {
      type: Boolean,
      default: () => false,
    },
    mapData: {
      type: Object,
      default: () => {},
    },
  },

  data: () => ({
    map: null,
    currentMarkers: [],
    isGoogleMapsLoaded: false,
  }),

  watch: {
    mapData: {
      handler: function (val) {
        this.currentMarkers.forEach(marker => {
          marker.setMap(null)
        })

        this.layPins(val.homes)

        if (val.homes.length) {
          this.changeCenter(val.homes[0].coordinates[1], val.homes[0].coordinates[0], 2)
        } else {
          this.changeCenter(-1.9974336826297852, -141.6912321651001, 1)
        }
      },
    },
  },

  mounted() {
    this.$googleMapsLoader(() => {
      this.isGoogleMapsLoaded = true
      this.showMap()
    })
  },

  methods: {
    getHTML(home: {
      title: string
      beds: any
      baths: any
      description: string | any[]
      location: string | any[]
      id: any
    }) {
      return `
        <div style='width: 100%;'>
          <h3 style='text-transform: capitalize; font-size: 14px;'>${
            home.title
              ? home.title.length > 30
                ? home.title.slice(0, 30) + '...'
                : home.title
              : ''
          }</h3>
          <div style='display: flex; align-items: center; margin-top: 7px;'>
            <div style='display: flex; align-items: center;'>
              <img width='18px' height='18px' src='data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFoAAABaCAYAAAA4qEECAAAABmJLR0QA/wD/AP+gvaeTAAACSElEQVR4nO3czWoTURjG8X+Dm4IrXbhxYQN6Ba7diLfgRy9KBAXFdUWX1l6AuG516xUopvjRhS4UJC4yiMRk5syZk3dmwv8HL4FkTvrkIWQ+2hQkSZIkSZIkSZIkSZKKm/QdYNvtAo+As2oeVvepsAfAfGnu95poS33i/6JnvSYasZ2ax+YZa7SGO7ogFh3EooNYdBCLDmLRQSw6iEUHseggFh3EooNsqugJsA8cAR+BX9XtK+Bei5+7fFGrj/kOvAeeArdaN9HhRTaZAu9q1s+Bt9V2uRn6nDfAtYTsyXKKngKniYFnwF5mhr7nK3CjIXuytkVPaH4nL88J9R8jfRfaVPbVmuzJ2ha9nxn4bkaGoczrmuzJ2hZ9lBn2MCPDkOZmTf6/Sh51XA9eNxS3UzYqWfSFzHUXC2boQ9JOsWTRXzLXfS6YoQ+XUzYqWfRJ5rrjghn6cD5lo5JFP89c96JghlFqe9QxYXHG12aPfcx4j6NTOkmS86RTFmd8KeFmwJXMDEObTnKfdI/F53XTO7np9Lsuw9Cmky5POmFxxncIfAB+VrcvgTuM6+pdkaL9k7AgXvgPYtFBLDqIRQex6O6SOswpuu9DqaHNbzp+x6fvFzDGWfsdn5zjaK13Clxa9YBFl7eyU3eGQSw6iEUHseggFh3EooNYdBCLDmLRQSw6iEUHOZexxl/OLrS6FuQ7OohFB6kr+mzFfd82FWSEWvVTV/SzFfcdtI6zvYr1sws8AX5U8xj/792/ivezg0cadexHkiRJ2pA/jyzoWinUvfoAAAAASUVORK5CYII='  alt=''/>
              <span style='margin-top: 5px; margin-left: 5px; font-weight: bold; font-size: 14px;'> ${
                home.beds
              } </span>
            </div>

            <div style='display: flex; align-items: center; margin-left: 10px;'>
              <img width='18px' height='18px' src='data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAABmJLR0QA/wD/AP+gvaeTAAACcElEQVR4nO2aPYvVQBiFn10WwbVU8APEysZStLMR/4CdYrGCjZ2dpfYWWmntF9pos4WWVorgIoor6ILoooK7ilrIKnfVxGIIXLKTDzNnZhLuPDDNhJx535NMkpk3kEgkEolEIjGhTMUOoIGdwC6R1h/gNTAS6XlnM7AG5ML2EtgSMgkXZoBfaA3IgSMhk3DlEtrkV4Ct4wP0/RkwDRwCtgu0MuAhsDre2XcDCnYAs44aP4AvgliiMI/77X/ZJjztOfA+8d3WmQwIHUVEkgG2zmRA6CgikgywdSYDQkcRiRFmZbmBSTHgE+ZrcAOTZICVZEDIKCKSDKg6MGPpmwP2+YulE67xrFQdKBuwH7juOFgfaT0FvlHxuhg4rQ1YBs4BP72GE5YceBc7CFdctsRe1QnbHoIFx4E9joGr2Otw7q2uJ95GX5QI3T5TqgOUqfsOeFtrT//JgNPA164CR4l/Bbu2EXCqa+IF2zAV1djJ/G97BBxom2RTZegBcLitWADWgMelvgwz198A94EF5YDHiH9Fx9s9ZXLQvBi6CzxTD+rAU7VgkwF/MU/STD1wR+QGtOUC8W//HNjtO9EqpjCrxJjJv/eeZQObgKvEM+Ca/xTbcRZYJ7wBJ0Mk15aDwBITMv+rmAWuECb5Xq/n54Df+DXgjq/gFbvCN4DzAp06lnwJq7bFLwIfRFo2Oi9pm1AZsA7cFGnZ8LZHqSyMzAu1ynj7n1FpwAI128+OeKtgKYVz4IlQLwhqZ1+I9QoGMQUAFsV6BYMxYFmsVzAYAz6K9QoGY8AqpsCqxttv7nWlsS5kwAngDGb/QMFzzN5kwgf/AN7lJxtypGOWAAAAAElFTkSuQmCC' alt=''/>
              <span style='margin-top: 5px; margin-left: 5px; font-weight: bold; font-size: 14px;'> ${
                home.baths
              } </span>
            </div>
          </div>

          <div>
            <p style='margin: 10px 0;'>
              ${home.description.slice(0, 70)}...
            </p>
          </div>

          <div>
            <p style='margin: 10px 0;'>
              ${home.location.slice(0, 70)}...
            </p>
          </div>

          <div style='text-align: right; margin-top: 7px;'>
            <a style='color: #775bc3; font-size: 13px; text-decoration: none; font-weight: bold;' href='${
              window.location.protocol
            }//${window.location.host}/home/<USER>/${home.id}'>VIEW HOME</a>
          </div>
        </div>
      `
    },

    layPins(homes: any[] = []) {
      homes.forEach(home => {
        const coordinates = home.coordinates

        const contentString = this.getHTML(home)

        const infowindow = new google.maps.InfoWindow({
          content: contentString,
        })

        const marker = new google.maps.Marker({
          position: new google.maps.LatLng(coordinates[1], coordinates[0]),
          map: this.map,
          title: home.title,
        })

        marker.addListener('click', () => {
          infowindow.open({
            anchor: marker,
            map: this.map,
            shouldFocus: true,
          })
        })

        this.currentMarkers.push(marker)
      })
    },

    changeCenter(lat: number, lng: number, zoomLevel = 2) {
      if (!this.map) {
        console.error('Map is undefined')
        return
      }
      this.map.setCenter({ lat, lng })
      this.map.setZoom(zoomLevel)
    },

    showMap() {
      this.map = new google.maps.Map(document.getElementById('map'), {
        center: { lat: 30.39183, lng: -92.329102 },
        zoom: 2,
        disableDefaultUI: false,
        mapTypeId: 'terrain',
      })
      this.addListeners()
      this.layPins(this.mapData.homes)
    },

    addListeners() {
      this.map.addListener('zoom_changed', () => {
        this.handleZoomButtonClick(this.map.getZoom())
      })

      this.map.addListener('click', mouseEvent => {
        this.handleMouseClickOnMap(mouseEvent)
      })

      this.map.addListener('drag', () => {
        const { lat, lng } = this.map.getCenter()
        this.$emit('update-lat-lng', {
          lat: lat(),
          lng: lng(),
          zoomLevel: this.map.getZoom(),
        })
      })
    },

    handleMouseClickOnMap(data: { latLng: any }) {
      const { latLng } = data
      this.$emit('update-lat-lng', {
        lat: latLng.lat(),
        lng: latLng.lng(),
        zoomLevel: this.map.getZoom(),
      })
    },

    handleZoomButtonClick(zoomLevel: number) {
      if (zoomLevel >= 5) {
        this.$emit('show-area-button', true)
      } else {
        this.$emit('show-area-button', false)
      }
    },

    changeZoomLevel(level: any) {
      this.map.setZoom(level)
    },
  },
})
</script>

<template>
  <div>
    <div
      id="map"
      class="tw-border tw-border-gray-300 tw-rounded-md tw-shadow-sm tw-min-w-[390px] tw-min-h-[390px]"
    ></div>
    <v-progress-linear v-if="loading" indeterminate height="2" color="primary" />
  </div>
</template>

<style scoped></style>
