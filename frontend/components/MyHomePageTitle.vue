<script lang="ts">
// @ts-nocheck
import { defineComponent } from '@nuxtjs/composition-api'

export default defineComponent({
  name: 'MyHomePageTitle',

  props: {
    title: {
      type: String,
      default: 'Homes',
    },
  },
})
</script>

<template>
  <v-col cols="12" class="tw-my-8 tw-flex tw-justify-between">
    <div class="tw-text-3xl tw-font-semibold tw-text-left tw-tracking-wide">
      <span class="tw-text-primary">My </span
      ><span class="tw-text-primary tw-tracking-wide">
        {{ title }}
      </span>
    </div>

    <slot></slot>
  </v-col>
</template>

<style scoped></style>
