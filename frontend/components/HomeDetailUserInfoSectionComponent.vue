<script lang="ts">
// @ts-nocheck

import { computed, defineComponent } from '@nuxtjs/composition-api'

import { useScreenSize } from '~/composables/useScreenSize'

export default defineComponent({
  name: 'HomeDetailUserInfoSectionComponent',

  props: {
    houseInfo: {
      type: Object,
      required: true,
    },
    homeOwnerHousesCount: {
      type: Number,
      required: true,
    },
  },

  setup(props) {
    const { isMobile } = useScreenSize()

    const avatarSize = computed(() => (isMobile.value ? 160 : 130))

    const hostName = computed(() => {
      return `${props.houseInfo?.user?.first_name} ${props.houseInfo?.user?.last_name?.charAt(0)}.`
    })

    return {
      isMobile,
      avatarSize,
      hostName,
    }
  },
})
</script>

<template>
  <v-row no-gutters>
    <v-col cols="12">
      <h1 class="tw-text-zinc-500 tw-font-bold tw-text-2xl">About {{ hostName }}</h1>
    </v-col>

    <v-row>
      <template v-if="isMobile">
        <v-col cols="12" class="d-flex justify-center flex-column align-center">
          <v-avatar v-if="houseInfo?.user?.avatar" class="mb-4 md:mb-0" :size="avatarSize">
            <v-img :src="houseInfo?.user?.avatar" />
          </v-avatar>
          <img
            v-else
            class="mb-4 md:mb-0"
            aspect-ratio="1.5"
            :src="require('~/assets/default.png')"
            alt=""
          />
          <div
            v-if="houseInfo?.user?.profile_completion === 100"
            class="d-flex align-center justify-center"
          >
            <img
              height="15"
              width="15"
              :src="require('~/assets/check1.svg')"
              class="mr-1"
              alt="twimo"
            />
            <span class="font-weight-bold text-sm">Identity Verified</span>
          </div>
        </v-col>
      </template>
      <v-col cols="12" :md="isMobile ? 12 : 9" class="d-flex flex-column my-8">
        <div class="tw-text-xl tw-text-zinc-500">
          {{ houseInfo?.user?.about }}
        </div>
        <div v-if="homeOwnerHousesCount > 1" class="d-flex justify-end full-width">
          <v-btn
            :to="`/u/${houseInfo?.user?.shared_url}`"
            small
            link
            text
            color="#000000"
            class="font-weight-bold"
          >
            View More
          </v-btn>
        </div>
      </v-col>
      <template v-if="!isMobile">
        <v-col cols="12" md="3" class="d-flex justify-center flex-column align-center">
          <v-avatar v-if="houseInfo?.user?.avatar" class="mb-4 md:mb-0" :size="avatarSize">
            <v-img :src="houseInfo?.user?.avatar" />
          </v-avatar>
          <img
            v-else
            class="mb-4 md:mb-0"
            aspect-ratio="1.5"
            :src="require('~/assets/default.png')"
            alt=""
          />
          <div
            v-if="houseInfo?.user?.profile_completion === 100"
            class="d-flex align-center justify-center"
          >
            <img
              height="15"
              width="15"
              :src="require('~/assets/check1.svg')"
              class="mr-1"
              alt="twimo"
            />
            <span class="font-weight-bold text-sm">Identity Verified</span>
          </div>
        </v-col>
      </template>
    </v-row>
  </v-row>
</template>

<style scoped></style>
