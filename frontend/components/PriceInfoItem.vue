<script lang="ts">
// @ts-nocheck
import { defineComponent } from '@nuxtjs/composition-api'

export default defineComponent({
  name: 'PriceInfoItem',

  props: {
    title: {
      type: String,
      default: '',
    },
    value: {
      type: [String, Number],
      default: 0,
    },
  },
})
</script>

<template>
  <div
    class="tw-flex tw-flex-row tw-border tw-w-full tw-justify-between tw-items-center tw-rounded-xl"
  >
    <div class="tw-px-4 tw-py-4 tw-text-lg tw-text-zinc-500 tw-w-[65%]">
      {{ title }}
    </div>
    <div
      class="tw-px-4 tw-py-4 tw-text-lg tw-text-zinc-500 tw-w-[35%] tw-border-l tw-rounded-r-xl tw-bg-slate-300"
    >
      {{ value }}
    </div>
  </div>
</template>

<style scoped></style>
