<template>
  <div>
    <div v-if="showEdit" style="margin-bottom: -10px" class="d-flex justify-end">
      <v-btn icon color="#000000" x-small>
        <v-icon @click="$emit('handle-edit')"> mdi-pencil </v-icon>
      </v-btn>
    </div>
    <v-avatar style="border: 1px solid lightgray" size="150">
      <v-img :src="preview ? preview : getImage" :lazy-src="preview ? preview : getImage">
        <template #placeholder>
          <v-row class="fill-height ma-0" align="center" justify="center">
            <v-progress-circular indeterminate color="grey" />
          </v-row>
        </template>
      </v-img>
    </v-avatar>
  </div>
</template>

<script>
export default {
  props: {
    showEdit: {
      type: Boolean,
      default: () => false,
    },
    avatarImage: {
      required: true,
    },
    isHome: {
      type: Boolean,
      default: false,
    },
  },
  data: () => ({
    defaultImage: require('assets/default.png'),
    preview: null,
  }),
  computed: {
    getImage: {
      get() {
        if (this.avatarImage) {
          return this.avatarImage
        }
        return require('assets/default.png')
      },
    },
  },
  methods: {
    showPreview(val) {
      this.preview = val
    },
  },
}
</script>
