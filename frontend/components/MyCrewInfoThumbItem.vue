<script lang="ts">
// @ts-nocheck
import { defineComponent } from '@nuxtjs/composition-api'

export default defineComponent({
  name: 'MyCrewInfoThumbItem',

  props: {
    title: {
      type: String,
      default: '',
    },
    value: {
      type: [String, Number],
      default: '',
    },
    description: {
      type: String,
      default: '',
    },
  },
})
</script>

<template>
  <div
    class="tw-w-[200px] tw-min-w-[200px] md:tw-w-[13%] tw-h-[150px] hover:tw-bg-[#480a8333] md:tw-max-w-[250px] tw-rounded-[15px] tw-px-[25px] tw-py-[20px] tw-transition-all"
    style="background: #fff; box-shadow: 0px 3px 2px 0px #00000059"
  >
    <div class="tw-text-[1.1rem] tw-text-left tw-font-semibold tw-text-[#6C6B6B]">
      {{ title }}
    </div>
    <div class="tw-text-[#6C6B6B] tw-text-[0.9rem] tw-mt-0">
      {{ description }}
    </div>
    <div class="tw-text-[#3f3f3f] tw-text-5xl tw-font-bold tw-mt-2">
      {{ value }}
    </div>
  </div>
</template>

<style scoped></style>
