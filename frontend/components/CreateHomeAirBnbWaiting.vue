<script lang="ts">
// @ts-nocheck
import { defineComponent, computed } from '@nuxtjs/composition-api'
import { storeToRefs } from 'pinia'

import { useCreateHomeProgressStore } from '~/composables/useCreateHomeStore'
import ImagesUploader from '~/components/ImagesUploader.vue'
import GoodCard2 from '~/components/GoodCard2.vue'
import GoodButtonIncreaseNumber from '~/components/GoodButtonIncreaseNumber.vue'
import GoodButton from '~/components/GoodButton.vue'
import AppAutocomplete from '~/components/AppAutocomplete.vue'
import GoodButtonReverted from '~/components/GoodButtonReverted.vue'
import Amenity from '~/components/Amenity.vue'
import PhotoProcessingStatus from '~/components/PhotoProcessingStatus.vue'
import screenCheckerMixins from '~/mixins/screenChecker.mixins'
import { UploadedFile } from '~/types'
import {
  amenities,
  petFriendlyNumbers,
  petFriendlyOptions,
  petFriendlyTypes,
  MANDATORY_SAFETY_AMENITIES,
} from '~/constants'
import { useApi, useToast } from '~/composables/useCommon'
import { useImageStore } from '~/composables/useImageStore'
import { useCreateHomeImageStore } from '~/composables/useCreateHomeImageStore'

export default defineComponent({
  name: 'CreateHomeAirBnbWaiting',

  components: {
    Amenity,
    GoodButtonReverted,
    AppAutocomplete,
    GoodButton,
    GoodButtonIncreaseNumber,
    GoodCard2,
    ImagesUploader,
    PhotoProcessingStatus,
  },

  mixins: [screenCheckerMixins],

  setup() {
    const api = useApi()
    const toast = useToast()
    const store = useCreateHomeProgressStore()
    const imageStore = useImageStore()
    const createHomeImageStore = useCreateHomeImageStore()

    const isFromAirbnb = computed(() => createHomeImageStore.isFromAirbnb)
    const processingStatus = computed(() => createHomeImageStore.processingStatus)
    const createdHomeId = computed(() => store.createdHomeId)

    const isSafetyAmenity = (amenity: string) => {
      return MANDATORY_SAFETY_AMENITIES.includes(amenity)
    }

    const {
      startEditTitle,
      closeEditTitleDialog,
      updateTitle,
      startEditAmount,
      closeEditAmountDialog,
      updateAmount,
      startEditAddress,
      closeEditAddressDialog,
      updateAddress,
      startEditAmenities,
      closeEditAmenitiesDialog,
      clickOnAmenityEdit,
      updateAmenities,
      startEditDescription,
      closeEditDescriptionDialog,
      updateDescription,
      createHomeData,
      deletePhoto,
      setAsCoverPhoto,
      displayedPets,
      isAmenitySelectedEdit,
    } = store

    const {
      airbnbUrl,
      airbnbFetched,
      editTitleDialog,
      editTitleDialogData,
      editAmountDialog,
      editAmountDialogData,
      editAddressDialog,
      editAddressDialogData,
      editAmenitiesDialog,
      editAmenitiesDialogData,
      editDescriptionDialog,
      editDescriptionDialogData,
    } = storeToRefs(store)

    const uploadFiles = async (e: any) => {
      const files = [...e]

      try {
        // Add files to the image store
        imageStore.addImages(files)

        // Update the local createHomeData with the images (without uploading yet)
        createHomeData.photos = imageStore.images.value.map(img => ({
          media_id: img.media_id,
          url: img.url
        }))

        toast.success(`Added ${files.length} image(s) for upload`)
      } catch (error) {
        console.error('Error adding images:', error)
        toast.error('Failed to add images')
      }
    }

    const imagesReordered = (images: any[]) => {
      // Update the image store ordering
      imageStore.reorderImages(images)

      // Also update the local createHomeData
      createHomeData.photos = imageStore.images.value.map(img => ({
        media_id: img.media_id,
        url: img.url
      }))
    }

    return {
      airbnbUrl,
      airbnbFetched,
      editTitleDialog,
      editTitleDialogData,
      editAmountDialog,
      editAmountDialogData,
      editAddressDialog,
      editAddressDialogData,
      editAmenitiesDialog,
      editAmenitiesDialogData,
      isAmenitySelectedEdit,
      editDescriptionDialog,
      editDescriptionDialogData,
      startEditTitle,
      closeEditTitleDialog,
      updateTitle,
      startEditAmount,
      closeEditAmountDialog,
      updateAmount,
      startEditAddress,
      closeEditAddressDialog,
      updateAddress,
      startEditAmenities,
      closeEditAmenitiesDialog,
      clickOnAmenityEdit,
      updateAmenities,
      startEditDescription,
      closeEditDescriptionDialog,
      updateDescription,
      createHomeData,
      uploadFiles,
      deletePhoto,
      setAsCoverPhoto,
      petFriendlyTypes,
      petFriendlyNumbers,
      petFriendlyOptions,
      displayedPets,
      amenities,
      imagesReordered,
      isSafetyAmenity,
      isFromAirbnb,
      processingStatus,
      createdHomeId,
    }
  },

  methods: {
    triggerUploader() {
      ;(this.$refs.imageUploader as any)?.triggerFileInput()
    },
  },
})
</script>

<template>
  <v-row>
    <v-col v-if="!airbnbFetched" cols="12" md="8" class="tw-mt-8 tw-flex tw-flex-col tw-gap-4">
      <div class="tw-text-2xl tw-font-semibold tw-text-zinc-600">Thank you!</div>

      <div class="tw-text-lg tw-font-medium tw-text-zinc-500">
        We are pulling the data from the link you provided. This will take a few seconds
      </div>

      <div class="tw-flex tw-justify-center tw-items-center">
        <v-progress-circular indeterminate color="primary" size="64" />
      </div>
    </v-col>

    <v-col v-else cols="12" class="tw-mt-8 tw-flex tw-flex-col tw-gap-4">
      <div class="tw-text-2xl tw-font-semibold tw-text-zinc-600">Your Airbnb Rental</div>

      <div class="tw-text-lg tw-font-medium tw-text-zinc-500">
        Below are the details we found from your Airbnb rental. Please review and make sure
        everything looks good. If you need to edit or update any information below you can click
        into the sections directly. We will have you fill out additional details such as pricing,
        calendar, etc.
      </div>

      <!-- Photo processing status notification for all photo uploads - component will handle visibility based on status -->
      <PhotoProcessingStatus
        v-if="createdHomeId"
        :home-id="createdHomeId"
      />

      <v-row no-gutters>
        <v-col cols="12" class="tw-flex tw-flex-col tw-gap-4">
          <div class="tw-text-2xl tw-font-semibold tw-text-zinc-600">Photos</div>
          <div
v-if="processingStatus && processingStatus !== 'completed' && !processingStatus.startsWith('completed:')"
               class="tw-mb-4 tw-text-sm tw-text-gray-600">
            <p v-if="isFromAirbnb">We're importing all photos from your Airbnb listing. This process will continue in the background even after you complete creating your home.</p>
            <p v-else>Your photos will be processed in the background. You can continue with creating your home without waiting for uploads to complete.</p>
          </div>
          <ImagesUploader
            ref="imageUploader"
            class="tw-cursor-pointer tw-h-[500px]"
            :images="createHomeData.photos"
            @files-uploaded="uploadFiles"
            @delete-file="deletePhoto"
            @set-as-cover-photo="setAsCoverPhoto"
            @images-reordered="imagesReordered"
          />
        </v-col>
      </v-row>

      <v-row class="tw-mt-4">
        <!--				Left side-->
        <v-col cols="12" md="12">
          <v-row>
            <!--						Title-->
            <v-col cols="12" md="6" @click="startEditTitle">
              <GoodCard2 class="tw-cursor-pointer tw-h-40">
                <div class="tw-text-2xl tw-font-semibold tw-text-zinc-600 tw-mb-4">Title</div>
                <div class="tw-text-xl tw-mb-4 tw-text-zinc-500">
                  {{ createHomeData.title }}
                </div>
              </GoodCard2>
            </v-col>
            <!--						Guests and Pets-->
            <v-col cols="12" md="6" @click="startEditAmount">
              <GoodCard2 class="tw-cursor-pointer tw-h-40">
                <div class="tw-text-2xl tw-font-semibold tw-text-zinc-600 tw-mb-4">
                  Number of Guests
                </div>
                <div class="tw-text-xl tw-mb-4 tw-text-zinc-500 tw-flex tw-gap-4">
                  <div class="tw-flex tw-items-center tw-gap-2">
                    <span>{{ createHomeData.guests }}</span>
                    <v-icon color="primary" size="24" dark>mdi-account</v-icon>
                  </div>
                  <div class="tw-flex tw-items-center tw-gap-2">
                    <span>{{ displayedPets }}</span>
                    <v-icon color="primary" size="24" dark>mdi-paw</v-icon>
                  </div>
                </div>
              </GoodCard2>
            </v-col>
            <!--						Bedrooms-->
            <v-col cols="12" md="6" @click="startEditAmount">
              <GoodCard2 class="tw-cursor-pointer tw-h-40">
                <div class="tw-text-2xl tw-font-semibold tw-text-zinc-600 tw-mb-4">
                  Number of Bedrooms
                </div>
                <div class="tw-text-xl tw-mb-4 tw-text-zinc-500 tw-flex tw-gap-4">
                  {{ createHomeData.bedrooms }} Bedrooms
                </div>
              </GoodCard2>
            </v-col>
            <!--						Address-->
            <v-col cols="12" md="6" @click="startEditAddress">
              <GoodCard2 class="tw-cursor-pointer tw-h-40">
                <div class="tw-text-2xl tw-font-semibold tw-text-zinc-600 tw-mb-4">
                  Home Address
                </div>
                <div class="tw-text-xl tw-mb-4 tw-text-zinc-500 tw-flex tw-gap-4">
                  {{ createHomeData.address }}
                </div>
              </GoodCard2>
            </v-col>
            <!--		Beds & Bathrooms-->
            <v-col cols="12" md="6" @click="startEditAmount">
              <GoodCard2 class="tw-cursor-pointer tw-h-40">
                <div class="tw-text-2xl tw-font-semibold tw-text-zinc-600 tw-mb-4">
                  Bed and Bath
                </div>
                <div class="tw-text-xl tw-mb-4 tw-text-zinc-500 tw-flex tw-gap-4">
                  <div class="tw-flex tw-items-center tw-gap-2">
                    <span>{{ createHomeData.beds }} Beds</span>
                    <v-icon color="primary" size="24" dark>mdi-bed</v-icon>
                  </div>
                  <div class="tw-flex tw-items-center tw-gap-2">
                    <span>{{ createHomeData.bathrooms }} Bathrooms</span>
                    <v-icon color="primary" size="24" dark>mdi-shower</v-icon>
                  </div>
                </div>
              </GoodCard2>
            </v-col>
            <!--						Amenities-->
            <v-col cols="12" @click="startEditAmenities">
              <GoodCard2 class="tw-cursor-pointer">
                <div class="tw-text-2xl tw-font-semibold tw-text-zinc-600 tw-mb-4">Amenities</div>
                <template v-if="createHomeData.amenities.length > 0">
                  <div class="tw-grid tw-gap-y-8 tw-grid-cols-2 md:tw-grid-cols-4">
                    <div
                      v-for="(amenity, index) in createHomeData.amenities"
                      :key="index"
                      class="tw-text-xl tw-text-zinc-500"
                    >
                      {{ amenity }}
                    </div>
                  </div>
                </template>

                <template v-else>
                  <div class="tw-text-xl tw-mb-4 tw-text-zinc-500">
                    Select amenities for your home
                  </div>
                </template>
              </GoodCard2>
            </v-col>
            <!--						Description-->
            <v-col cols="12" @click="startEditDescription">
              <GoodCard2 class="tw-cursor-pointer">
                <div class="tw-text-2xl tw-font-semibold tw-text-zinc-600 tw-mb-4">
                  Home Description
                </div>
                <div class="tw-text-xl tw-mb-4 tw-text-zinc-500">
                  {{ createHomeData.description }}
                </div>
              </GoodCard2>
            </v-col>
          </v-row>
        </v-col>
      </v-row>
    </v-col>

    <!--    Edit modals-->
    <!--		Title-->
    <v-dialog v-model="editTitleDialog" :max-width="isDesktop ? '30%' : '100%'">
      <GoodCard2>
        <div class="tw-text-2xl tw-font-semibold tw-text-zinc-600 tw-mb-4">Home Title</div>
        <div class="tw-text-xl tw-text-zinc-500 tw-mb-4">
          The title of your home will be what you and your guests see on your Twimo custom URL
        </div>
        <v-text-field
          v-model="editTitleDialogData"
          outlined
          dense
          label="Title"
          hide-details
          type="text"
          required
        />
        <div class="tw-flex tw-justify-end tw-mt-8 tw-gap-4">
          <GoodButtonReverted @click="closeEditTitleDialog"> Exit </GoodButtonReverted>

          <GoodButton @click="updateTitle"> Save</GoodButton>
        </div>
      </GoodCard2>
    </v-dialog>
    <!--		Amount (Guests, Beds, Bathrooms, ...)-->
    <v-dialog v-model="editAmountDialog" :max-width="isDesktop ? '30%' : '100%'">
      <GoodCard2>
        <div class="tw-text-2xl tw-font-semibold tw-text-zinc-600 tw-mb-4">
          Guests, Bedroom and Bathroom Count
        </div>
        <div class="tw-text-xl tw-text-zinc-500 tw-mb-4">
          Input your guest count, bedrooms, beds, and bathrooms below. Pets if applicable.
        </div>
        <v-col cols="12" class="tw-flex tw-flex-col tw-gap-3">
          <div class="tw-flex tw-flex-row tw-gap-2">
            <GoodButton class="tw-w-1/2 tw-text-lg tw-py-[29px]"> Bedrooms </GoodButton>
            <GoodButtonIncreaseNumber
              class="tw-w-1/2 tw-text-lg tw-py-[28px]"
              :value="editAmountDialogData.bedrooms"
              @decrease="editAmountDialogData.bedrooms > 0 ? editAmountDialogData.bedrooms-- : 0"
              @increase="editAmountDialogData.bedrooms++"
            ></GoodButtonIncreaseNumber>
          </div>

          <div class="tw-flex tw-flex-row tw-gap-2">
            <GoodButton class="tw-w-1/2 tw-text-lg tw-py-[29px]"> Beds </GoodButton>
            <GoodButtonIncreaseNumber
              class="tw-w-1/2 tw-text-lg tw-py-[28px]"
              :value="editAmountDialogData.beds"
              @decrease="editAmountDialogData.beds > 0 ? editAmountDialogData.beds-- : 0"
              @increase="editAmountDialogData.beds++"
            ></GoodButtonIncreaseNumber>
          </div>

          <div class="tw-flex tw-flex-row tw-gap-2">
            <GoodButton class="tw-w-1/2 tw-text-lg tw-py-[29px]"> Bathrooms </GoodButton>
            <GoodButtonIncreaseNumber
              class="tw-w-1/2 tw-text-lg tw-py-[28px]"
              :value="editAmountDialogData.bathrooms"
              @decrease="editAmountDialogData.bathrooms > 0 ? editAmountDialogData.bathrooms-- : 0"
              @increase="editAmountDialogData.bathrooms++"
            ></GoodButtonIncreaseNumber>
          </div>

          <div class="tw-flex tw-flex-row tw-gap-2">
            <GoodButton class="tw-w-1/2 tw-text-lg tw-py-[29px]"> Guests </GoodButton>
            <GoodButtonIncreaseNumber
              class="tw-w-1/2 tw-text-lg tw-py-[28px]"
              :value="editAmountDialogData.guests"
              @decrease="editAmountDialogData.guests > 1 ? editAmountDialogData.guests-- : 1"
              @increase="editAmountDialogData.guests++"
            ></GoodButtonIncreaseNumber>
          </div>
        </v-col>

        <v-col cols="12" class="tw-flex tw-flex-row tw-gap-2 tw-pr-0 tw-pt-0">
          <GoodButton class="tw-w-1/2 tw-text-lg tw-py-[29px]"> Pet Friendly? </GoodButton>
          <div class="tw-w-1/2 tw-flex tw-flex-row tw-gap-2">
            <v-select
              v-model="editAmountDialogData.pets.enabled"
              :items="petFriendlyOptions"
              label="Y / N"
              outlined
              hide-details
              class="tw-w-1/2 tw-rounded-xl tw-border tw-h-fit"
            ></v-select>
            <!-- <v-select
							v-model="editAmountDialogData.pets.type"
							:items="petFriendlyTypes"
							label="Type"
							outlined
							hide-details
							class="tw-w-1/2 tw-rounded-xl tw-border tw-h-fit"
						></v-select> -->
          </div>
        </v-col>

        <!-- <v-col cols="12" class="tw-pt-0">
					<v-select
						v-model="editAmountDialogData.pets.number"
						:items="petFriendlyNumbers"
						label="#"
						outlined
						hide-details
						class="tw-w-1/2 tw-rounded-xl tw-border"
					></v-select>
				</v-col> -->

        <div class="tw-flex tw-justify-end tw-mt-8 tw-gap-4">
          <GoodButtonReverted @click="closeEditAmountDialog"> Exit </GoodButtonReverted>

          <GoodButton @click="updateAmount"> Save</GoodButton>
        </div>
      </GoodCard2>
    </v-dialog>
    <!--		Address-->
    <v-dialog v-model="editAddressDialog" :max-width="isDesktop ? '30%' : '100%'">
      <GoodCard2>
        <div class="tw-text-2xl tw-font-semibold tw-text-zinc-600 tw-mb-4">Home Address</div>
        <div class="tw-text-xl tw-text-zinc-500 tw-mb-4">Enter Home Address Below</div>
        <v-col cols="12">
          <AppAutocomplete
            class="tw-rounded-xl"
            :address="editAddressDialogData"
            @address-selection="editAddressDialogData = $event"
          />
        </v-col>
        <div class="tw-flex tw-justify-end tw-mt-8 tw-gap-4">
          <GoodButtonReverted @click="closeEditAddressDialog"> Exit </GoodButtonReverted>

          <GoodButton @click="updateAddress"> Save</GoodButton>
        </div>
      </GoodCard2>
    </v-dialog>
    <!--		Amenities-->
    <v-dialog v-model="editAmenitiesDialog" :max-width="isDesktop ? '30%' : '100%'">
      <GoodCard2>
        <div class="tw-text-2xl tw-font-semibold tw-text-zinc-600 tw-mb-4">Amenities</div>
        <div class="tw-text-xl tw-text-zinc-500 tw-mb-4">Check all applicable amenities below</div>
        <v-row>
          <v-col
            v-for="(amenity, index) in amenities"
            :key="index"
            cols="6"
            @click="clickOnAmenityEdit(amenity)"
          >
            <Amenity
              :amenity="amenity"
              :is-activated="isAmenitySelectedEdit(amenity)"
              :disabled="isSafetyAmenity(amenity)"
            />
          </v-col>
        </v-row>
        <div class="tw-flex tw-justify-end tw-mt-8 tw-gap-4">
          <GoodButtonReverted @click="closeEditAmenitiesDialog"> Exit </GoodButtonReverted>

          <GoodButton @click="updateAmenities"> Save</GoodButton>
        </div>
      </GoodCard2>
    </v-dialog>
    <!--		Description-->
    <v-dialog v-model="editDescriptionDialog" :max-width="isDesktop ? '30%' : '100%'">
      <GoodCard2>
        <div class="tw-text-2xl tw-font-semibold tw-text-zinc-600 tw-mb-4">Home Description</div>
        <div class="tw-text-xl tw-text-zinc-500 tw-mb-4">
          In a brief sentence or two describe what makes your home your home and your neighborhood
          unique!
        </div>
        <v-col cols="12">
          <v-textarea
            v-model="editDescriptionDialogData"
            label="Description*"
            outlined
            placeholder="Type Here"
            dense
            class="tw-mt-4"
            rows="8"
            counter="500"
            maxlength="500"
          />
        </v-col>
        <div class="tw-flex tw-justify-end tw-mt-8 tw-gap-4">
          <GoodButtonReverted @click="closeEditDescriptionDialog"> Exit </GoodButtonReverted>

          <GoodButton @click="updateDescription(api)"> Save</GoodButton>
        </div>
      </GoodCard2>
    </v-dialog>
  </v-row>
</template>

<style scoped></style>
