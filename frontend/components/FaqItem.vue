<script>
export default {
  props: {
    item: Array,
  },
  data() {
    return {
      activeIndex: null,
    }
  },
  methods: {
    beforeEnter(el) {
      el.style.height = '0'
    },
    enter(el) {
      el.style.height = el.scrollHeight + 'px'
    },
    leave(el) {
      el.style.height = '0'
    },
    toggle(index) {
      this.activeIndex = this.activeIndex === index ? null : index
    },
    isActive(index) {
      return this.activeIndex === index
    },
  },
}
</script>
<template>
  <div>
    <div v-for="(item, i) in item" :key="i" class="faq__item">
      <h3 class="faq__item--title tw-text-base" @click="toggle(i)">
        {{ item.title }}
        <!-- <span class="faq__item--icon" :class="isActive(i) ? 'active' : ''">
					<span></span><span></span>
				</span> -->
      </h3>
      <Transition @before-enter="beforeEnter" @enter="enter" @leave="leave">
        <div
          v-show="isActive(i)"
          class="faq__item--description tw-text-base"
          v-html="item.content"
        ></div>
      </Transition>
    </div>
  </div>
</template>
<style scoped>
.faq__item {
  background-color: #ffffffd9;
  border-radius: 15px;
  padding: 20px 20px;
  box-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);
  border-radius: 15px;
}
.faq__item:not(:last-of-type) {
  margin-bottom: 20px;
}
.faq__item--icon {
  position: absolute;
  right: 0;
  top: 50%;
}
.faq__item--icon span {
  display: block;
  height: 3px;
  width: 20px;
  background-color: #360877;
  position: relative;
  transition: all 200ms;
}
.faq__item--icon span:last-of-type {
  transform: rotate(90deg);
  top: -2.8px;
}
.faq__item--icon.active span:last-of-type {
  transform: rotate(0deg);
}
.faq__item--title {
  font-weight: 600;
  line-height: 30px;
  color: #360877;
  margin: 0;
  position: relative;
  padding-right: 30px;
  cursor: pointer;
}
.faq__item--description {
  overflow: hidden;
  transition: height 0.5s ease;
  line-height: 22px;
  color: #360877;
  transition: all 300ms ease;
  margin-top: 15px;
}
</style>
