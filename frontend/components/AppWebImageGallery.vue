<template>
  <v-row no-gutters class="tw-mt-5 tw-mb-4">
    <v-col cols="12" :md="photosLength < 3 ? 12 : 6" class="tw-flex tw-relative">
      <app-button
        v-if="photos && photos.length > 0"
        color="white"
        text-color="black"
        small
        button-text="View all photos"
        class="tw-absolute tw-z-[1] tw-border tw-border-gray-300 tw-rounded-lg tw-font-medium hover:tw-bg-gray-100 tw-top-[16px] tw-right-[16px] tw-shadow-sm hover:tw-shadow"
        @click="openAllPictures"
      >
        <v-icon left small> mdi-image-multiple </v-icon>
      </app-button>

      <v-col v-if="photos && photos.length > 0" class="tw-pr-0" cols="12" :md="photosLength === 1 ? 12 : 6">
        <div
          class="tw-relative tw-overflow-hidden tw-h-[385px] tw-rounded-l-lg"
          :class="{ 'tw-rounded-lg tw-w-[740px] md:tw-max-w-[740px]': photosLength === 1, 'tw-w-full': photosLength !== 1 }"
        >
          <v-img
            class="tw-h-full tw-object-cover tw-transition-transform tw-duration-500 hover:tw-scale-105 hover:tw-cursor-pointer"
            :src="photos[0].thumb"
            :lazy-src="photos[0].thumb"
            :alt="`twimo.com - ${houseInfo.title}`"
            eager
            @click="openAllPictures(photos[0])"
          />
          <div
            class="tw-absolute tw-inset-0 tw-bg-black tw-bg-opacity-0 hover:tw-bg-opacity-10 tw-transition-all tw-duration-300"
          ></div>
        </div>
      </v-col>

      <!-- No images placeholder -->
      <v-col v-if="!photos || photos.length === 0" class="tw-pr-0" cols="12">
        <div class="tw-relative tw-overflow-hidden tw-h-[385px] tw-rounded-lg tw-w-[740px] md:tw-max-w-[740px] tw-bg-gray-100 tw-flex tw-items-center tw-justify-center">
          <v-icon size="64" color="grey">mdi-image-off</v-icon>
          <div class="tw-text-gray-500 tw-mt-4 tw-text-lg">No images available</div>
        </div>
      </v-col>

      <v-col v-if="photos[1]" cols="12" md="6" class="tw-pl-1 tw-pr-0">
        <div
          class="tw-relative tw-overflow-hidden tw-h-[385px]"
          :class="photosLength === 2 ? 'tw-rounded-r-lg' : ''"
        >
          <v-img
            class="tw-w-full tw-h-full tw-object-cover tw-transition-transform tw-duration-500 hover:tw-scale-105 hover:tw-cursor-pointer"
            :src="photos[1].thumb"
            :lazy-src="photos[1].thumb"
            :alt="`twimo.com - ${houseInfo.title}`"
            eager
            @click="openAllPictures(photos[1])"
          />
          <div
            class="tw-absolute tw-inset-0 tw-bg-black tw-bg-opacity-0 hover:tw-bg-opacity-10 tw-transition-all tw-duration-300"
          ></div>
        </div>
      </v-col>
    </v-col>

    <v-col v-if="photos && photosLength > 2" cols="12" md="6" class="d-flex tw-flex-wrap">
      <v-col v-if="photos[2]" cols="12" md="6" class="tw-pr-0 tw-pb-0 tw-pl-1">
        <div class="tw-relative tw-overflow-hidden tw-h-[190px]">
          <v-img
            class="tw-w-full tw-h-full tw-object-cover tw-transition-transform tw-duration-500 hover:tw-scale-105 hover:tw-cursor-pointer"
            :src="photos[2].thumb"
            :lazy-src="photos[2].thumb"
            :alt="`twimo.com - ${houseInfo.title}`"
            eager
            @click="openAllPictures(photos[2])"
          />
          <div
            class="tw-absolute tw-inset-0 tw-bg-black tw-bg-opacity-0 hover:tw-bg-opacity-10 tw-transition-all tw-duration-300"
          ></div>
        </div>
      </v-col>

      <v-col v-if="photos[3]" cols="12" md="6" class="tw-pb-0 tw-pl-1">
        <div class="tw-relative tw-overflow-hidden tw-h-[190px] tw-rounded-tr-lg">
          <v-img
            class="tw-w-full tw-h-full tw-object-cover tw-transition-transform tw-duration-500 hover:tw-scale-105 hover:tw-cursor-pointer"
            :src="photos[3].thumb"
            :lazy-src="photos[3].thumb"
            :alt="`twimo.com ${houseInfo.title}`"
            eager
            @click="openAllPictures(photos[3])"
          />
          <div
            class="tw-absolute tw-inset-0 tw-bg-black tw-bg-opacity-0 hover:tw-bg-opacity-10 tw-transition-all tw-duration-300"
          ></div>
        </div>
      </v-col>

      <v-col v-if="photos[4]" cols="12" md="6" class="tw-pr-0 tw-pl-1 tw-pt-1">
        <div class="tw-relative tw-overflow-hidden tw-h-[190px]">
          <v-img
            class="tw-w-full tw-h-full tw-object-cover tw-transition-transform tw-duration-500 hover:tw-scale-105 hover:tw-cursor-pointer"
            :src="photos[4].thumb"
            :lazy-src="photos[4].thumb"
            :alt="`twimo.com - ${houseInfo.title}`"
            eager
            @click="openAllPictures(photos[4])"
          />
          <div
            class="tw-absolute tw-inset-0 tw-bg-black tw-bg-opacity-0 hover:tw-bg-opacity-10 tw-transition-all tw-duration-300"
          ></div>
        </div>
      </v-col>

      <v-col v-if="photos[5]" cols="12" md="6" class="tw-pt-1 tw-pl-1">
        <div class="tw-relative tw-overflow-hidden tw-h-[190px] tw-rounded-br-lg">
          <v-img
            class="tw-w-full tw-h-full tw-object-cover tw-transition-transform tw-duration-500 hover:tw-scale-105 hover:tw-cursor-pointer"
            :src="photos[5].thumb"
            :lazy-src="photos[5].thumb"
            :alt="`twimo.com - ${houseInfo.title}`"
            eager
            @click="openAllPictures(photos[5])"
          />
          <div
            class="tw-absolute tw-inset-0 tw-bg-black tw-bg-opacity-0 hover:tw-bg-opacity-10 tw-transition-all tw-duration-300"
          ></div>

          <!-- Show more photos badge for 6+ images -->
          <div
            v-if="photosLength > 6"
            class="tw-absolute tw-inset-0 tw-flex tw-items-center tw-justify-center tw-bg-black tw-bg-opacity-30 hover:tw-bg-opacity-50 tw-transition-all tw-duration-300 tw-cursor-pointer"
            @click="openAllPictures"
          >
            <div class="tw-text-white tw-text-center">
              <v-icon size="36" color="white"> mdi-image-multiple </v-icon>
              <div class="tw-mt-2 tw-font-medium">+{{ photosLength - 5 }} more</div>
            </div>
          </div>
        </div>
      </v-col>
    </v-col>

    <full-screen-gallery ref="fullscreengallery" :house-info="houseInfo" />
  </v-row>
</template>

<script>
import FullScreenGallery from '~/components/FullScreenGallery.vue'
import AppButton from '~/components/AppButton.vue'

export default {
  components: { AppButton, FullScreenGallery },
  props: {
    houseInfo: {
      type: Object,
      required: true,
    },
  },
  computed: {
    photos() {
      return this.houseInfo.photos
    },
    photosLength() {
      return this.houseInfo.photos.length
    },
  },
  methods: {
    openAllPictures(photo) {
      this.$refs.fullscreengallery.toggleDialog(photo)
    },
  },
}
</script>

<style scoped></style>
