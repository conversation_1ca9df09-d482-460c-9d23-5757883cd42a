<template>
  <v-dialog v-model="show" max-width="600" persistent>
    <GoodCard2 class="tw-flex tw-flex-col tw-gap-4 tw-p-6">
      <div class="tw-flex tw-items-center tw-gap-3 tw-mb-2">
        <v-icon color="warning" size="32">mdi-alert-circle</v-icon>
        <div class="tw-text-2xl tw-font-semibold tw-text-primary">
          Cancellation Not Allowed
        </div>
      </div>
      
      <div class="tw-text-lg tw-text-zinc-700 tw-leading-relaxed">
        {{ message }}
      </div>
      
      <div class="tw-bg-amber-50 tw-rounded-lg tw-p-4 tw-border tw-border-amber-200">
        <div class="tw-flex tw-items-start tw-gap-2">
          <v-icon color="amber darken-2" size="20" class="tw-mt-0.5">mdi-information</v-icon>
          <div class="tw-text-amber-800 tw-text-sm">
            <div class="tw-font-medium tw-mb-1">Need to make changes?</div>
            <div>
              Please contact your {{ isHost ? 'guest' : 'host' }} directly to discuss any changes to your booking. 
              You can send them a message through the platform.
            </div>
          </div>
        </div>
      </div>
      
      <div class="tw-flex tw-justify-end tw-gap-3 tw-mt-4">
        <GoodButton
          class="tw-font-medium tw-px-6 tw-py-3"
          @click="$emit('message')"
        >
          <v-icon left size="18">mdi-message</v-icon>
          Send Message
        </GoodButton>
        <GoodButtonReverted
          class="tw-font-medium tw-px-6 tw-py-3"
          @click="close"
        >
          Close
        </GoodButtonReverted>
      </div>
    </GoodCard2>
  </v-dialog>
</template>

<script lang="ts">
import { defineComponent, computed, watch } from '@nuxtjs/composition-api'
import GoodCard2 from './GoodCard2.vue'
import GoodButton from './GoodButton.vue'
import GoodButtonReverted from './GoodButtonReverted.vue'

export default defineComponent({
  name: 'CancellationPolicyDialog',
  
  components: {
    GoodCard2,
    GoodButton,
    GoodButtonReverted,
  },
  
  props: {
    modelValue: {
      type: Boolean,
      default: false,
    },
    message: {
      type: String,
      default: 'You are unable to cancel this booking due to the cancellation policy in place.',
    },
    isHost: {
      type: Boolean,
      default: false,
    },
  },
  
  emits: ['update:modelValue', 'message'],
  
  setup(props, { emit }) {
    const show = computed({
      get: () => {
        console.log('CancellationPolicyDialog show getter:', props.modelValue)
        return props.modelValue
      },
      set: (value) => {
        console.log('CancellationPolicyDialog show setter:', value)
        emit('update:modelValue', value)
      },
    })

    const close = () => {
      console.log('CancellationPolicyDialog close called')
      show.value = false
    }

    // Watch for prop changes
    watch(() => props.modelValue, (newValue) => {
      console.log('CancellationPolicyDialog modelValue changed:', newValue)
    })

    watch(() => props.message, (newMessage) => {
      console.log('CancellationPolicyDialog message changed:', newMessage)
    })

    return {
      show,
      close,
    }
  },
})
</script>
