<script lang="ts">
// @ts-nocheck

import {
  defineComponent,
  ref,
  onMounted,
  computed,
  useContext,
  watch,
} from '@nuxtjs/composition-api'

import GoodButtonIncreaseNumber from '~/components/GoodButtonIncreaseNumber.vue'
import { petFriendlyNumbers, petFriendlyOptions, petFriendlyTypes } from '~/constants'
import { useToast, useApi } from '~/composables/useCommon'

import GoodButton from './GoodButton.vue'
import GoodButtonReverted from './GoodButtonReverted.vue'

export default defineComponent({
  name: 'FilterModal',

  components: {
    GoodButton,
    GoodButtonReverted,
    GoodButtonIncreaseNumber,
  },

  props: {
    typeOfSpace: {
      type: [String, Number],
      default: '',
    },
    bedrooms: {
      type: [Number],
      default: 0,
    },
    guests: {
      type: [Number],
      default: 0,
    },
    bathrooms: {
      type: [Number],
      default: 0,
    },
    beds: {
      type: [Number],
      default: 0,
    },
    pets: {
      type: [Number],
      default: 0,
    },
    minPrice: {
      type: [String, Number],
      default: '',
    },
    maxPrice: {
      type: [String, Number],
      default: '',
    },
    petFriendly: {
      type: [String, Number],
      default: '',
    },
    petType: {
      type: [String, Number],
      default: '',
    },
  },

  setup() {
    return {
      petFriendlyOptions,
      petFriendlyTypes,
    }
  },

  data() {
    return {
      dialog: false,
      localBedrooms: this.bedrooms,
      localTypeOfSpace: this.typeOfSpace,
      localBathrooms: this.bathrooms,
      localBeds: this.beds,
      localPets: this.pets,
      localMinPrice: this.minPrice,
      localMaxPrice: this.maxPrice,
      localPetFriendly: this.petFriendly,
      localPetType: this.petType,
    }
  },

  watch: {
    bedrooms(val) {
      this.localBedrooms = val
    },
    typeOfSpace(val) {
      this.localTypeOfSpace = val
    },
    bathrooms(val) {
      this.localBathrooms = val
    },
    beds(val) {
      this.localBeds = val
    },
    pets(val) {
      this.localPets = val
    },
    minPrice(val) {
      this.localMinPrice = val
    },
    maxPrice(val) {
      this.localMaxPrice = val
    },
    petFriendly(val) {
      this.localPetFriendly = val
    },
    petType(val) {
      this.localPetType = val
    },
    /* localBedrooms(val) {
                this.$emit('update:bedrooms', val);
            },
            localTypeOfSpace(val) {
                this.$emit('update:type_of_space', val);
            },
            localBathrooms(val) {
                this.$emit('update:bathrooms', val);
            },
            localBeds(val) {
                this.$emit('update:bedrooms', val);
            },
            localMinPrice(val) {
                this.$emit('update:bedrooms', val);
            },
            localMaxPrice(val) {
                this.$emit('update:bedrooms', val);
            },
            localPetFriendly(val) {
                this.$emit('update:bedrooms', val);
            },
            localPetType(val) {
                this.$emit('update:bedrooms', val);
            }, */
  },

  methods: {
    openDialog() {
      this.dialog = true
    },
    updateFilter() {
      this.dialog = false
      this.$emit('update:filter', {
        bedrooms: this.localBedrooms,
        typeOfSpace: this.localTypeOfSpace,
        bathrooms: this.localBathrooms,
        beds: this.localBeds,
        pets: this.localPets,
        minPrice: this.localMinPrice,
        maxPrice: this.localMaxPrice,
        petFriendly: this.localPetFriendly,
        petType: this.localPetType,
      })
    },
  },
})
</script>

<template>
  <v-dialog v-model="dialog" max-width="500">
    <v-card class="tw-p-8 tw-max-h-[90vh] tw-relative tw-overflow-y-auto">
      <h2 class="tw-text-2xl tw-text-[#6C6B6B]">Filters</h2>
      <div class="tw-border-b-[1px] tw-pb-10 tw-pt-5">
        <v-menu offset-y>
          <template #activator="{ on, attrs }">
            <v-btn
              v-bind="attrs"
              outlined
              text
              class="tw-border tw-flex tw-justify-between tw-border-[#6C6C6C1A] tw-shadow-input tw-normal-case tw-tracking-normal tw-text-base tw-w-[100%] md:tw-w-[25rem] tw-rounded-xl tw-py-6 tw-text-[#6C6C6C]"
              v-on="on"
            >
              {{
                localTypeOfSpace == null || localTypeOfSpace == ''
                  ? 'Type of Space'
                  : localTypeOfSpace == 1
                    ? 'An Entire Space'
                    : 'Shared Interior Space'
              }}

              <v-icon color="#7C0CB1"> mdi-menu-down </v-icon>
            </v-btn>
          </template>
          <good-card card-text-classes="tw-p-0">
            <div class="tw-flex tw-flex-col tw-items-left tw-mt-0 tw-max-w-[400px]">
              <div
                class="tw-block tw-normal-case tw-text-[#6C6C6C] tw-text-[#6C6C6C] hover:tw-text-[#FFF] hover:tw-bg-[#7c0cb1] tw-pt-[1rem] tw-pb-[0.5rem] tw-px-[1rem] tw-border-b-[1px] tw-border-[#6C6C6C1A] tw-cursor-pointer"
                @click="localTypeOfSpace = 1"
              >
                <h3 class="tw-text-xl">An Entire Space</h3>
                <p>
                  Guests will have the entire space to themselves, including a private entrance with
                  no shared spaces
                </p>
              </div>
              <div
                class="tw-block tw-normal-case tw-text-[#6C6C6C] tw-text-[#6C6C6C] hover:tw-text-[#FFF] hover:tw-bg-[#7c0cb1] tw-pt-[1rem] tw-pb-[0.5rem] tw-px-[1rem] tw-cursor-pointer"
                @click="localTypeOfSpace = 2"
              >
                <h3 class="tw-text-xl">Shared Interior Space</h3>
                <p>
                  Guests will have a room with private door for sleeping. Other interior areas can
                  be shared such as kitchen, bathrooms, etc.
                </p>
              </div>
            </div>
          </good-card>
        </v-menu>
      </div>

      <h3 class="tw-text-xl tw-text-[#6C6B6B] tw-pt-[2.3rem]">Price Range</h3>
      <div class="tw-border-b-[1px] tw-pb-[2.5rem] tw-pt-[1rem]">
        <div class="tw-flex tw-items-center tw-gap-5">
          <span>
            <v-text-field
              id="price_min"
              v-model="localMinPrice"
              type="text"
              hide-details
              outlined
              label="Minimum"
              class="tw-bg-white tw-rounded-xl tw-border-[#6C6C6C1A] tw-shadow-input"
              @focus="invalid = false"
            />
          </span>
          <span class="tw-text-bold tw-text-3xl"> - </span>
          <span>
            <v-text-field
              id="price_max"
              v-model="localMaxPrice"
              type="text"
              hide-details
              outlined
              label="Maximum"
              class="tw-bg-white tw-rounded-xl tw-border-[#6C6C6C1A] tw-shadow-input"
              @focus="invalid = false"
            />
          </span>
        </div>
      </div>

      <h3 class="tw-text-xl tw-text-[#6C6B6B] tw-pt-[2.3rem]">Rooms and Beds</h3>
      <div class="">
        <div
          class="tw-flex tw-gap-5 tw-flex-row tw-pt-[1rem] tw-pb-[1rem] tw-max-w-[100%] md:tw-max-w-[80%]"
        >
          <span class="tw-w-1/2 tw-text-sm tw-py-[15px] tw-text-center tw-rounded-xl custom-shadow">
            Bedrooms
          </span>
          <GoodButtonIncreaseNumber
            class="tw-w-1/2 tw-text-sm tw-border-[#7C0CB11A] tw-h-[50px] tw-py-[15px] custom-shadow"
            :value="localBedrooms"
            @decrease="localBedrooms > 0 ? localBedrooms-- : 0"
            @increase="localBedrooms++"
          />
        </div>
        <div
          class="tw-flex tw-gap-5 tw-flex-row tw-pt-[1rem] tw-pb-[1rem] tw-max-w-[100%] md:tw-max-w-[80%]"
        >
          <span class="tw-w-1/2 tw-text-sm tw-py-[15px] tw-text-center tw-rounded-xl custom-shadow">
            Beds
          </span>
          <GoodButtonIncreaseNumber
            class="tw-w-1/2 tw-text-sm tw-border-[#7C0CB11A] tw-h-[50px] tw-py-[15px] custom-shadow"
            :value="localBeds"
            @decrease="localBeds > 0 ? localBeds-- : 0"
            @increase="localBeds++"
          />
        </div>
        <div
          class="tw-flex tw-gap-5 tw-flex-row tw-pt-[1rem] tw-pb-[1rem] tw-max-w-[100%] md:tw-max-w-[80%]"
        >
          <span class="tw-w-1/2 tw-text-sm tw-py-[15px] tw-text-center tw-rounded-xl custom-shadow">
            Bathrooms
          </span>
          <GoodButtonIncreaseNumber
            class="tw-w-1/2 tw-text-sm tw-border-[#7C0CB11A] tw-h-[50px] tw-py-[15px] custom-shadow"
            :value="localBathrooms"
            @decrease="localBathrooms > 0 ? localBathrooms-- : 0"
            @increase="localBathrooms++"
          />
        </div>
        <div
          class="tw-flex tw-gap-5 tw-flex-row tw-pt-[1rem] tw-pb-[1rem] tw-max-w-[100%] md:tw-max-w-[80%]"
        >
          <span class="tw-w-1/2 tw-text-sm tw-py-[15px] tw-text-center tw-rounded-xl custom-shadow">
            Pets
          </span>
          <GoodButtonIncreaseNumber
            class="tw-w-1/2 tw-text-sm tw-border-[#7C0CB11A] tw-h-[50px] tw-py-[15px] custom-shadow"
            :value="localPets"
            @decrease="localPets > 0 ? localPets-- : 0"
            @increase="localPets++"
          />
        </div>
        <div
          class="tw-flex tw-gap-5 tw-flex-row tw-pt-[1rem] tw-pb-[2rem] tw-max-w-[100%] md:tw-max-w-[80%] tw-items-center"
        >
          <span
            class="tw-w-1/2 tw-text-sm tw-py-[15px] tw-items-center tw-text-center tw-rounded-xl custom-shadow"
          >
            Pet Friendly
          </span>
          <v-select
            v-model="localPetFriendly"
            :items="petFriendlyOptions"
            label="Y / N"
            outlined
            hide-details
            class="tw-w-1/2 tw-rounded-xl tw-border tw-h-fit"
          />
          <!-- <v-select
                        v-model="localPetType"
                        :items="petFriendlyTypes"
                        label="Type"
                        outlined
                        hide-details
                        class="tw-w-1/2 tw-rounded-xl tw-border tw-h-fit"
                    ></v-select> -->
        </div>
      </div>

      <div class="tw-text-left">
        <GoodButton @click="updateFilter()"> Apply </GoodButton>
        <v-btn
          class="tw-ml-[10px] tw-shadow-none tw-normal-case tw-bg-white"
          @click="dialog = false"
        >
          Close
        </v-btn>
      </div>
    </v-card>
  </v-dialog>
</template>

<style scoped>
#card-element {
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1rem;
  background: white;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  transition: border-color 0.15s ease-in-out;
}

#card-element:hover {
  border-color: #9ca3af;
}

#card-element.StripeElement--focus {
  border-color: #6b7280;
  box-shadow:
    0 1px 3px 0 rgba(0, 0, 0, 0.1),
    0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.custom-shadow {
  box-shadow: 0 2px 3px 0 rgb(0 0 0 / 31%);
}

#card-element.StripeElement--invalid {
  border-color: #dc2626;
}
</style>
