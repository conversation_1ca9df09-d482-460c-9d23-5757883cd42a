<script lang="ts">
// @ts-nocheck
import { defineComponent, PropType, ref } from '@nuxtjs/composition-api'

import { RentalPlan } from '~/types'
import BoldPurpleText from '~/components/BoldPurpleText.vue'

export default defineComponent({
  name: 'RentalPlanV2',

  components: { BoldPurpleText },

  props: {
    rentalPlan: {
      type: Object as PropType<RentalPlan>,
      required: true,
    },
    isActivated: {
      type: Boolean,
      default: false,
    },
    switchable: {
      type: Boolean,
      default: false,
    },
  },

  setup(props) {
    const isEnabled = ref(props.isActivated)

    return {
      isEnabled,
    }
  },
})
</script>

<template>
  <div class="tw-flex sm:tw-w-[60%] tw-drop-shadow-2xl">
    <template v-if="isEnabled">
      <div class="tw-flex tw-flex-col tw-w-[80%] tw-gap-2 tw-p-4 tw-rounded-l-3xl tw-bg-primary">
        <div class="tw-font-semibold tw-text-white">
          {{ rentalPlan.title }}
        </div>
        <div class="tw-text-sm tw-text-white">
          {{ rentalPlan.description }}
        </div>
      </div>
      <div
        class="tw-flex tw-w-[20%] tw-bg-white tw-rounded-r-3xl tw-items-center tw-justify-center tw-flex-col sm:tw-flex-row"
      >
        <BoldPurpleText>ON</BoldPurpleText>

        <v-switch
          v-if="switchable"
          v-model="isEnabled"
          class="tw-mx-2"
          color="primary"
          @change="$emit('switch', isEnabled)"
        ></v-switch>
      </div>
    </template>
    <template v-else>
      <div class="tw-flex tw-flex-col tw-w-[80%] tw-gap-2 tw-p-4 tw-rounded-l-3xl tw-bg-[#858585]">
        <div class="tw-font-semibold tw-text-white">
          {{ rentalPlan.title }}
        </div>
        <div class="tw-text-sm tw-text-white">
          {{ rentalPlan.description }}
        </div>
      </div>
      <div
        class="tw-flex tw-w-[20%] tw-bg-white tw-rounded-r-3xl tw-items-center tw-justify-center tw-flex-col sm:tw-flex-row"
      >
        <BoldPurpleText class="tw-text-[#858585]">OFF</BoldPurpleText>

        <v-switch
          v-if="switchable"
          v-model="isEnabled"
          class="tw-mx-2"
          color="#858585"
          @change="$emit('switch', isEnabled)"
        ></v-switch>
      </div>
    </template>
  </div>
</template>

<style scoped></style>
