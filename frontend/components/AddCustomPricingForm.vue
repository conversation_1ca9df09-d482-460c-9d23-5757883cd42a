<script lang="ts" setup>
// @ts-nocheck

import GoodButton from '~/components/GoodButton.vue'
import { useAdvancedPricing } from '~/composables/useAdvancedPricing'

const props = defineProps({
  homeIdProp: {
    type: null,
    required: false,
    default: null,
  },
})

const homeIdProp = props.homeIdProp

const {
  hostHomes,
  create,
  formattedEndDateComputed,
  formattedStartDateComputed,
  startDateMenu,
  endDateMenu,
  startDateSelected,
  endDateSelected,
  homeId,
  validateDates,
  isAppliedForAllFridays,
  isAppliedForAllSaturdays,
  nightlyRate,
  form,
} = useAdvancedPricing(homeIdProp)
</script>

<template>
  <v-form ref="form" lazy-validation>
    <v-row no-gutters class="tw-mt-5">
      <!--			Select home-->
      <v-col v-if="!homeIdProp" cols="12" md="3">
        <v-select
          v-model="homeId"
          :rules="[v => !!v || 'Home is required']"
          light
          dense
          required
          type="text"
          outlined
          :items="hostHomes"
          item-text="title"
          item-value="id"
          label="Guest Booked Home*"
          class="tw-mr-5"
        />
      </v-col>

      <!--			Start date-->
      <v-col cols="12" :md="homeIdProp ? 6 : 3">
        <v-dialog
          ref="startDateMenu"
          v-model="startDateMenu"
          
          persistent
          width="290px"
        >
          <template #activator="{ on, attrs }">
            <v-text-field
              v-model="formattedStartDateComputed"
              :rules="[validateDates]"
              label="Check in*"
              required
              readonly
              light
              dense
              type="text"
              outlined
              class="tw-mr-5"
              v-bind="attrs"
              v-on="on"
            />
          </template>

          <v-date-picker
            v-model="startDateSelected"
            no-title
            scrollable
            required
            :allowed-dates="date => date >= new Date().toISOString().substr(0, 10)"
          >
            <v-spacer />
            <v-btn text color="primary" @click="startDateMenu = false"> Cancel </v-btn>
            <v-btn text color="primary" @click="$refs.startDateMenu.save(startDateSelected)">
              OK
            </v-btn>
          </v-date-picker>
        </v-dialog>
        <v-checkbox
          v-model="isAppliedForAllFridays"
          label="Apply to Friday's Only"
          class="tw-p-0 tw-m-0"
        />
      </v-col>

      <!--			End date-->
      <v-col cols="12" :md="homeIdProp ? 6 : 3">
        <v-dialog
          ref="endDateMenu"
          v-model="endDateMenu"
          
          persistent
          width="290px"
        >
          <template #activator="{ on, attrs }">
            <v-text-field
              v-model="formattedEndDateComputed"
              :rules="[validateDates]"
              label="Check out*"
              readonly
              required
              light
              dense
              type="text"
              outlined
              class="tw-mr-5"
              v-bind="attrs"
              v-on="on"
            />
          </template>
          <v-date-picker
            v-model="endDateSelected"
            no-title
            scrollable
            required
            :allowed-dates="date => date >= new Date().toISOString().substr(0, 10)"
          >
            <v-spacer />
            <v-btn text color="primary" @click="endDateMenu = false"> Cancel </v-btn>
            <v-btn text color="primary" @click="$refs.endDateMenu.save(endDateSelected)">
              OK
            </v-btn>
          </v-date-picker>
        </v-dialog>
        <v-checkbox
          v-model="isAppliedForAllSaturdays"
          label="Apply to Saturday's Only"
          class="tw-p-0 tw-m-0"
        />
      </v-col>

      <!--			Price-->
      <v-col cols="12" :md="homeIdProp ? 12 : 2">
        <v-text-field
          v-model="nightlyRate"
          :rules="[v => !!v || 'Price is required']"
          label="Price*"
          light
          dense
          required
          type="number"
          outlined
          class="tw-mr-5"
        />
      </v-col>
      <v-col cols="12" md="1">
        <GoodButton @click="create">
          <v-icon>mdi-plus</v-icon>
          Add
        </GoodButton>
      </v-col>
    </v-row>
  </v-form>
</template>

<style scoped>
::v-deep label {
  @apply tw-text-sm;
}
</style>
