<template>
  <v-dialog v-model="showModal" max-width="1100" content-class="tw-h-auto" @click:outside="onClose">
    <v-card
      class="tw-p-8 tw-overflow-y-auto tw-rounded-xl"
      :class="{ 'tw-max-h-[90vh]': step !== 'verification' }"
    >
      <div v-if="step === 'initial'" class="tw-text-center">
        <div class="tw-text-3xl tw-font-bold tw-text-primary mb-6">
          Identity Verification Required
        </div>
        <div class="tw-text-zinc-600 tw-text-lg mb-8 tw-max-w-3xl tw-mx-auto">
          Before your home can be activated, we need to verify your identity for safety and trust.
          This is a one-time process that takes just a few minutes.
        </div>

        <div class="tw-flex tw-justify-center tw-mb-8">
          <v-img
            :src="require('~/assets/partners/stripe-identity.png')"
            alt="Stripe Identity"
            max-height="70"
            contain
          />
        </div>

        <div
          class="verification-steps tw-bg-gray-50 tw-rounded-xl tw-p-6 tw-mb-8 tw-max-w-2xl tw-mx-auto"
        >
          <div class="tw-text-xl tw-font-semibold tw-text-zinc-800 tw-mb-4">You'll need to:</div>
          <div class="tw-flex tw-flex-col tw-gap-4 tw-text-left">
            <div class="tw-flex tw-items-start tw-gap-3">
              <v-icon color="primary"> mdi-card-account-details-outline </v-icon>
              <div>
                <div class="tw-font-medium tw-text-zinc-800">
                  Take a photo of your government-issued ID
                </div>
                <div class="tw-text-zinc-600 tw-text-sm">
                  Passport, driver's license, or national ID card
                </div>
              </div>
            </div>
            <div class="tw-flex tw-items-start tw-gap-3">
              <v-icon color="primary"> mdi-face-recognition </v-icon>
              <div>
                <div class="tw-font-medium tw-text-zinc-800">
                  Take a quick selfie to verify it's you
                </div>
                <div class="tw-text-zinc-600 tw-text-sm">
                  This helps us match your ID to your identity
                </div>
              </div>
            </div>
          </div>
        </div>

        <GoodButton
          :loading="loading"
          class="tw-w-full md:tw-w-auto tw-bg-primary tw-px-8 tw-py-3 tw-text-lg"
          @click="startVerification"
        >
          Begin Verification
        </GoodButton>
      </div>

      <div v-else-if="step === 'verification'" class="tw-text-center">
        <div class="tw-text-3xl tw-font-bold tw-text-primary mb-4">Complete Your Verification</div>

        <div
          v-if="!store.verificationSessionUrl"
          class="tw-flex tw-flex-col tw-items-center tw-justify-center tw-py-8"
        >
          <v-progress-circular indeterminate color="primary" class="mb-4" size="36" />
          <div class="tw-text-zinc-600 tw-text-lg">
            Please wait while we prepare the verification process...
          </div>
        </div>

        <div v-else>
          <div class="tw-mb-2 tw-bg-blue-50 tw-border tw-border-blue-200 tw-rounded-lg tw-p-4 tw-text-left">
            <div class="tw-flex tw-items-start">
              <v-icon color="info" class="tw-mr-3 tw-mt-0.5">mdi-information-outline</v-icon>
              <div>
                <p class="tw-text-zinc-700 tw-mb-1">
                  <span class="tw-font-medium">Complete the verification steps in the frame below.</span>
                  The page will automatically update when verification is complete.
                </p>
                <p class="tw-text-zinc-600 tw-text-sm">
                  If you complete verification on another device, this page will refresh automatically.
                </p>
              </div>
            </div>
          </div>

          <iframe
            id="stripe-verification-frame"
            :src="store.verificationSessionUrl"
            class="tw-w-full tw-border tw-border-gray-200 tw-rounded-lg tw-shadow-sm tw-mb-6"
            height="650"
            frameborder="0"
            title="Stripe Identity Verification"
            @load="handleIframeLoad"
          ></iframe>

          <div class="tw-flex tw-justify-center tw-mt-4">
            <GoodButtonReverted class="tw-px-8" @click="onClose">
              Cancel Verification
            </GoodButtonReverted>
          </div>
        </div>
      </div>

      <div v-else-if="step === 'success'" class="tw-text-center tw-py-4">
        <v-icon color="success" size="64" class="mb-6"> mdi-check-circle </v-icon>
        <div class="tw-text-3xl tw-font-bold tw-text-green-600 mb-4">Verification Complete!</div>
        <div class="tw-text-zinc-600 tw-text-lg mb-8 tw-max-w-lg tw-mx-auto">
          Thank you for verifying your identity. Your home is now being activated.
        </div>
        <GoodButton class="tw-bg-primary tw-px-8" @click="onComplete"> Continue </GoodButton>
      </div>

      <div v-else-if="step === 'failed'" class="tw-text-center tw-py-4">
        <v-icon color="error" size="64" class="mb-6"> mdi-alert-circle </v-icon>
        <div class="tw-text-3xl tw-font-bold tw-text-red-600 mb-4">Verification Failed</div>
        <div class="tw-text-zinc-600 tw-text-lg mb-8 tw-max-w-lg tw-mx-auto">
          We couldn't verify your identity. Please try again or contact support if you continue to
          experience issues.
        </div>
        <div class="tw-flex tw-flex-col md:tw-flex-row tw-gap-4 tw-justify-center">
          <GoodButton class="tw-bg-primary" @click="startVerification"> Try Again </GoodButton>
          <GoodButtonReverted @click="onClose"> Close </GoodButtonReverted>
        </div>
      </div>

      <div v-else-if="step === 'canceled'" class="tw-text-center tw-py-4">
        <v-icon color="warning" size="64" class="mb-6"> mdi-cancel </v-icon>
        <div class="tw-text-3xl tw-font-bold tw-text-amber-600 mb-4">Verification Canceled</div>
        <div class="tw-text-zinc-600 tw-text-lg mb-8 tw-max-w-lg tw-mx-auto">
          You've canceled the verification process. You can try again when you're ready.
        </div>
        <div class="tw-flex tw-flex-col md:tw-flex-row tw-gap-4 tw-justify-center">
          <GoodButton class="tw-bg-primary" @click="startVerification"> Try Again </GoodButton>
          <GoodButtonReverted @click="onClose"> Close </GoodButtonReverted>
        </div>
      </div>
    </v-card>
  </v-dialog>
</template>

<script lang="ts">
// @ts-nocheck
import { defineComponent, ref, watch, onMounted, onBeforeUnmount } from '@nuxtjs/composition-api'

import { useToast, useApi } from '~/composables/useCommon'
import { useVerificationStore } from '~/composables/useVerificationStore'

import GoodButton from './GoodButton.vue'
import GoodButtonReverted from './GoodButtonReverted.vue'

export default defineComponent({
  name: 'StripeIdentityModal',

  components: {
    GoodButton,
    GoodButtonReverted,
  },

  props: {
    value: {
      type: Boolean,
      default: false,
    },
    homeId: {
      type: [Number, String],
      default: null,
    },
  },

  emits: ['input', 'verification-complete', 'verification-failed', 'verification-canceled'],

  setup(props, { emit }) {
    const api = useApi()
    const toast = useToast()
    const loading = ref(false)
    const step = ref('initial')
    const store = useVerificationStore()
    const showModal = ref(props.value)
    const iframeLoaded = ref(false)
    const pollingErrorCount = ref(0)

    // Use the store's global toast flag instead of local flag
    const showSuccessToast = () => {
      // Don't log unless debugging is needed
      if (!store.hasShownSuccessToast) {
        // Set the flag FIRST before showing toast to prevent race conditions
        store.hasShownSuccessToast = true
        // Then show the toast
        toast.success('Identity verification completed successfully').goAway(3000)
        console.log('[StripeIdentity] Showing success toast (first time only)')
      } else {
        console.log('[StripeIdentity] Success toast already shown, skipping')
      }
    }

    // Force close handler - more aggressive than the regular close
    const forceCloseModal = () => {
      console.log('[StripeIdentity] FORCE CLOSING MODAL')
      // Clear all intervals and timeouts
      if (statusCheckInterval) {
        clearInterval(statusCheckInterval)
        statusCheckInterval = null
      }

      if (safetyTimeout) {
        clearTimeout(safetyTimeout)
        safetyTimeout = null
      }

      if (statusLogInterval) {
        clearInterval(statusLogInterval)
        statusLogInterval = null
      }

      // Force cleanup in the store
      store.hideVerification()

      // Reset internal modal state
      showModal.value = false

      // Notify parent directly
      emit('input', false)

      console.log('[StripeIdentity] Modal state after force close:', {
        localShowModal: showModal.value,
        storeShowModal: store.showVerificationModal,
        step: step.value,
      })
    }

    // Log important events for debugging
    const logEvent = (event, details = {}) => {
      console.log(`[StripeIdentity] ${event}:`, {
        step: step.value,
        hasUrl: !!store.verificationSessionUrl,
        ...details,
      })
    }

    // Safety timeout to prevent modal from being stuck open
    let safetyTimeout = null

    // Function to set up safety timeout
    const setupSafetyTimeout = () => {
      // Clear any existing timeout
      if (safetyTimeout) {
        clearTimeout(safetyTimeout)
        safetyTimeout = null
      }

      // Only set timeout if we're in verification step
      if (step.value === 'verification') {
        logEvent('Setting up safety timeout')
        // Close modal after 5 minutes (300000ms) if no activity
        safetyTimeout = setTimeout(() => {
          if (showModal.value && step.value === 'verification') {
            logEvent('Safety timeout triggered - closing modal')
            toast.info('Verification process timed out. You can try again if needed.').goAway(5000)
            showModal.value = false
          }
        }, 300000) // 5 minutes
      }
    }

    // Set up event listener for messages from the iframe
    const setupIframeListener = () => {
      logEvent('Setting up iframe message listener')

      // Track if we've already handled a completion message to prevent duplicates
      let hasHandledCompletion = false

      const handleMessage = event => {
        // Log all messages for debugging
        console.log('[StripeIdentity] Received message from origin:', event.origin, 'data:', event.data)

        // Process messages from Stripe domains and also from our own domain (for redirects)
        if (!event.origin.includes('stripe.com') && !event.origin.includes(window.location.hostname)) {
          return
        }

        try {
          // Stripe sends a message when verification is complete
          // The message may vary, but we can detect key events
          let data = event.data

          // Try to parse string data as JSON
          if (typeof data === 'string') {
            try {
              data = JSON.parse(data)
            } catch (e) {
              // If it's not valid JSON, check if it contains completion keywords
              if (data.includes('complete') ||
                  data.includes('success') ||
                  data.includes('finished') ||
                  data.includes('verified')) {
                data = { type: 'completion_detected_in_string' }
              }
            }
          }

          // Check for verification complete signals with broader detection
          if (data && !hasHandledCompletion) {
            // Look specifically for STRIPE_IDENTITY_SESSION_COMPLETE and success messages
            // Use a more comprehensive detection approach
            const isCompleted =
              // Check type field
              (data.type &&
                (data.type === 'STRIPE_IDENTITY_SESSION_COMPLETE' ||
                  data.type === 'success' ||
                  data.type.includes('complete') ||
                  data.type.includes('finish') ||
                  data.type.includes('verified'))) ||
              // Check action field
              (data.action &&
                (data.action.includes('complete') ||
                 data.action.includes('finish') ||
                 data.action.includes('success') ||
                 data.action.includes('verified'))) ||
              // Check status field
              (data.status &&
                (data.status === 'complete' ||
                 data.status === 'finished' ||
                 data.status === 'verified' ||
                 data.status === 'success')) ||
              // Check event field
              (data.event &&
                (data.event.includes('complete') ||
                 data.event.includes('finish') ||
                 data.event.includes('success') ||
                 data.event.includes('verified'))) ||
              // Check for redirect_status which Stripe sometimes uses
              (data.redirect_status &&
                (data.redirect_status === 'succeeded' ||
                 data.redirect_status === 'complete' ||
                 data.redirect_status === 'verified'))

            if (isCompleted) {
              logEvent('Detected verification completion in iframe', {
                data: typeof data.type === 'string' ? data.type : 'unknown',
                origin: event.origin
              })

              // Mark as handled to prevent duplicates
              hasHandledCompletion = true

              // Force close the modal without waiting
              forceCloseModal()

              // Show completion toast if it hasn't been shown
              showSuccessToast()

              // Check verification status in the background to update state
              store.verificationCompleted(api).catch(error => {
                console.error('Error checking verification status after iframe message:', error)
              })

              // Notify parent of completion
              emit('verification-complete')

              // Redirect to completion page for better UX
              window.location.href = '/identity-verification/complete'
            }
          }
        } catch (error) {
          console.error('Error processing iframe message:', error)
        }
      }

      // Remove any existing listeners first to prevent duplicates
      window.removeEventListener('message', handleMessage)

      // Then add the new listener
      window.addEventListener('message', handleMessage)

      // Return removal function for cleanup
      return () => {
        window.removeEventListener('message', handleMessage)
      }
    }

    // Variable to store cleanup function
    let removeIframeListener = null

    // This is critical - properly connect both value prop and store state
    watch(
      [() => props.value, () => store.showVerificationModal],
      ([newPropValue, newStoreValue]) => {
        logEvent('Modal visibility triggers changed', {
          propValue: newPropValue,
          storeValue: newStoreValue,
        })

        // If either prop or store state is true, show the modal
        const shouldShowModal = newPropValue || newStoreValue

        if (showModal.value !== shouldShowModal) {
          showModal.value = shouldShowModal
          logEvent(`Setting local showModal to ${shouldShowModal}`)

          // Set home ID in store when modal is opened
          if (shouldShowModal && props.homeId) {
            store.showVerification(Number(props.homeId))
            step.value = 'initial' // Ensure we start at the initial step
            setupSafetyTimeout()
          } else if (!shouldShowModal) {
            // If closing, make sure everything is cleaned up
            forceCloseModal()
          }
        }
      },
      { immediate: true } // Check values immediately on mount
    )

    // When our local modal state changes, notify all systems
    watch(showModal, newValue => {
      logEvent('Modal local state changed', { newValue })

      // Update prop value through event
      emit('input', newValue)

      // If modal is closing, make sure to clean up properly
      if (!newValue) {
        forceCloseModal()
      }
    })

    // Watch for verification status changes in the store
    watch(
      () => store.isVerified,
      isVerified => {
        if (isVerified && showModal.value) {
          logEvent('Detected verification completed through store status change')

          // Close the modal if verification is successful
          forceCloseModal()

          // Show completion toast if needed
          showSuccessToast()

          // Notify parent
          emit('verification-complete')
        }
      }
    )

    const onClose = () => {
      logEvent('Close button clicked')
      showModal.value = false
    }

    const handleIframeLoad = () => {
      logEvent('Iframe loaded')
      iframeLoaded.value = true

      // Set up listener when iframe loads
      if (!removeIframeListener) {
        removeIframeListener = setupIframeListener()
      }
    }

    const startVerification = async () => {
      try {
        logEvent('Starting verification')
        loading.value = true
        iframeLoaded.value = false
        step.value = 'verification'
        pollingErrorCount.value = 0 // Reset error count

        // Reset safety timeout when starting verification
        setupSafetyTimeout()

        // Log current state for debugging
        console.log('[StripeIdentity] Current state before verification:', {
          homeId: props.homeId,
          currentHomeId: store.currentHomeId,
          hasSessionUrl: !!store.verificationSessionUrl,
          showModal: showModal.value,
        })

        // Use store to create verification session
        if (props.homeId) {
          await store.checkVerificationStatusAndOpenVerification(api, Number(props.homeId))
        } else {
          await store.createVerificationSession(api)
        }

        logEvent('Verification session created', {
          hasUrl: !!store.verificationSessionUrl,
          url: store.verificationSessionUrl
            ? store.verificationSessionUrl.substring(0, 30) + '...'
            : 'null',
          sessionId: store.verificationSessionId || 'null',
        })

        // Make sure we have a verification URL
        if (!store.verificationSessionUrl) {
          // Show a more specific error message
          console.error('Verification URL is missing - backend may not be properly configured')

          // Try one more time with a direct call
          console.log('[StripeIdentity] Attempting direct verification session creation')
          try {
            const { data } = await api.post('/identity/create-verification')
            if (data && data.verification_url) {
              console.log(
                '[StripeIdentity] Direct call successful, got URL:',
                data.verification_url.substring(0, 30) + '...'
              )
              store.verificationSessionUrl = data.verification_url
              store.verificationSessionId = data.session_id || null
            }
          } catch (retryError) {
            console.error('[StripeIdentity] Direct call also failed:', retryError)
          }

          // If still no URL, show error
          if (!store.verificationSessionUrl) {
            toast
              .error(
                'Verification service is currently unavailable. Please try again later or contact support.'
              )
              .goAway(5000)
            throw new Error('No verification URL returned from server')
          }
        }

        // Check if URL looks valid
        if (!store.verificationSessionUrl.includes('https://')) {
          console.error('Invalid verification URL format:', store.verificationSessionUrl)
          toast.error('Invalid verification URL. Please contact support.').goAway(5000)
          throw new Error('Invalid verification URL format')
        }

        // Start polling for verification status
        setupPollingInterval()
      } catch (error) {
        console.error('Verification error:', error)
        toast.error('Failed to start verification process. Please try again.').goAway(3000)
        step.value = 'initial'
      } finally {
        loading.value = false
      }
    }

    const checkVerificationStatus = async () => {
      try {
        logEvent('Checking verification status after user action')
        loading.value = true

        // Close the modal immediately regardless of verification outcome
        forceCloseModal()

        // Notify parent component
        emit('verification-complete')

        // Actually check verification status with the backend
        if (store.verificationSessionId) {
          const result = await store.verificationCompleted(api)
          logEvent('Verification completion result', result)

          // Show toast based on status
          if (result.verified) {
            showSuccessToast()
          } else if (result.status === 'canceled') {
            toast.info('Verification was canceled. You can try again later.').goAway(5000)
          } else if (result.status === 'failed' || result.status === 'expired') {
            toast.error('Verification failed. You can try again later.').goAway(5000)
          } else {
            // Still in progress or requires more action
            toast
              .info(
                'Verification is still in progress. You may try approving bookings again shortly.'
              )
              .goAway(5000)
          }
        } else {
          // No session ID, just inform the user
          console.warn('No verification session ID found')
          toast
            .info(
              'Verification is still in progress. You may try approving bookings again shortly.'
            )
            .goAway(5000)
        }
      } catch (error) {
        console.error('Verification check error:', error)
        toast.error('Failed to check verification status. Please try again.').goAway(3000)
      } finally {
        loading.value = false
      }
    }

    // Set up an interval to poll for verification status
    const setupPollingInterval = () => {
      logEvent('Setting up polling interval')

      if (statusCheckInterval) {
        clearInterval(statusCheckInterval)
        statusCheckInterval = null
      }

      // Reset error count when starting new polling
      pollingErrorCount.value = 0

      // Poll more frequently (every 2 seconds) to improve responsiveness
      statusCheckInterval = setInterval(async () => {
        try {
          // Only poll if we're on the verification step and have a session ID
          if (step.value === 'verification' && store.verificationSessionId) {
            logEvent('Polling for verification status')

            // Check if user still has the modal open
            if (!showModal.value) {
              logEvent('Modal closed, stopping polling')
              clearInterval(statusCheckInterval)
              statusCheckInterval = null
              return
            }

            const result = await store.verificationCompleted(api)
            logEvent('Polling verification result', result)

            // Reset error count on successful poll
            pollingErrorCount.value = 0

            // If verification is complete, close the modal automatically
            if (result.verified) {
              logEvent('Verification completed via polling')

              // Force close the modal to ensure it's closed immediately
              forceCloseModal()

              // Notify parent
              emit('verification-complete')

              // Show the success toast (handled by the showSuccessToast function)
              showSuccessToast()

              // Redirect to the completion page to ensure proper handling
              // This helps with mobile verification where the user might complete on another device
              window.location.href = '/identity-verification/complete'

              // Clear interval
              clearInterval(statusCheckInterval)
              statusCheckInterval = null
            }
            // If verification failed or was cancelled, update UI but don't close automatically
            else if (
              result.status === 'failed' ||
              result.status === 'expired' ||
              result.status === 'canceled'
            ) {
              logEvent('Verification failed or cancelled via polling')
              // Update step but let user decide to close
              step.value = result.status === 'canceled' ? 'canceled' : 'failed'

              // Clear interval
              clearInterval(statusCheckInterval)
              statusCheckInterval = null
            }
            // Otherwise, continue polling (pending or requires_input)
          }
        } catch (error) {
          pollingErrorCount.value++
          console.error('Error polling verification status:', error)

          // Stop polling after 5 consecutive errors
          if (pollingErrorCount.value >= 5) {
            logEvent('Too many polling errors, stopping polling')
            clearInterval(statusCheckInterval)
            statusCheckInterval = null
          }
        }
      }, 2000) // Poll every 2 seconds for better responsiveness

      return statusCheckInterval
    }

    watch(
      () => step.value,
      (newStep, oldStep) => {
        logEvent('Step changed', { from: oldStep, to: newStep })

        // Clear any existing interval just in case
        if (statusCheckInterval) {
          clearInterval(statusCheckInterval)
          statusCheckInterval = null
        }

        // Start polling if we're on the verification step
        if (newStep === 'verification' && store.verificationSessionId) {
          setupPollingInterval()
        }
      }
    )

    const onComplete = () => {
      logEvent('Success screen completion button clicked')
      showModal.value = false
    }

    // Simple polling interval that can be cleaned up
    let statusCheckInterval = null
    let statusLogInterval = null // For debugging

    onMounted(() => {
      logEvent('Component mounted')

      // Clean up any existing listeners or intervals first
      if (removeIframeListener) {
        removeIframeListener()
        removeIframeListener = null
      }

      if (statusCheckInterval) {
        clearInterval(statusCheckInterval)
        statusCheckInterval = null
      }

      if (statusLogInterval) {
        clearInterval(statusLogInterval)
        statusLogInterval = null
      }

      if (safetyTimeout) {
        clearTimeout(safetyTimeout)
        safetyTimeout = null
      }

      // Reset the global toast flag on mount ONLY if verification is not already completed
      if (!store.isVerified) {
        store.resetToastFlag()
      }

      // Check if user is already verified and close modal if needed
      if (store.isVerified && showModal.value) {
        logEvent('User already verified on mount, closing modal')
        forceCloseModal()
        emit('verification-complete')
      }

      // Go to verification step if URL exists
      if (store.verificationSessionUrl) {
        step.value = 'verification'
      }

      // Set up iframe message listener on mount
      removeIframeListener = setupIframeListener()

      // Start polling immediately if we're in verification step
      if (step.value === 'verification' && store.verificationSessionId) {
        setupPollingInterval()
      }

      // Set up a periodic logger for debugging
      statusLogInterval = setInterval(() => {
        if (showModal.value || store.showVerificationModal) {
          console.log('[StripeIdentity Debug]', {
            isModalOpen: showModal.value,
            step: step.value,
            storeModal: store.showVerificationModal,
            storeVerified: store.isVerified,
            hasSessionUrl: !!store.verificationSessionUrl,
            hasSessionId: !!store.verificationSessionId,
            successToastShown: store.hasShownSuccessToast,
            propsValue: props.value,
            homeId: props.homeId,
          })
        }
      }, 5000) // Log every 5 seconds
    })

    onBeforeUnmount(() => {
      logEvent('Component unmounting')

      // Clean up all intervals, timeouts, and listeners
      if (removeIframeListener) {
        removeIframeListener()
        removeIframeListener = null
      }

      if (statusCheckInterval) {
        clearInterval(statusCheckInterval)
        statusCheckInterval = null
      }

      if (statusLogInterval) {
        clearInterval(statusLogInterval)
        statusLogInterval = null
      }

      if (safetyTimeout) {
        clearTimeout(safetyTimeout)
        safetyTimeout = null
      }

      forceCloseModal()
    })

    return {
      showModal,
      loading,
      step,
      store,
      iframeLoaded,
      startVerification,
      checkVerificationStatus,
      onClose,
      onComplete,
      handleIframeLoad,
      pollingErrorCount,
    }
  },
})
</script>

<style scoped>
#stripe-verification-frame {
  min-height: 500px;
  height: 650px;
  transition: height 0.3s ease;
}

.verification-steps {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid #eaeaea;
}
</style>
