<script lang="ts">
// @ts-nocheck
import { defineComponent } from '@nuxtjs/composition-api'

import HostUtilityItem from '~/components/HostUtilityItem.vue'

export default defineComponent({
  name: 'HostDashboard',

  components: {
    HostUtilityItem,
  },

  props: {},

  data() {
    return {
      hostBlogItems: [],
    }
  },

  watch: {
    hostDashboardData(newValue) {},
  },

  mounted() {
    this.getDashboardData()
  },

  methods: {
    async getDashboardData() {
      try {
        const { data } = await this.$axios.get(
          'https://blog.twimo.com/wp-json/help/v1/posts-grouped?post_type=help_center&taxonomy=help_category'
        )

        data.forEach(e => {
          e.posts.forEach(p => {
            this.hostBlogItems.push({
              image: p.featured_image,
              description: p.title,
              link: p.youtube_url,
            })
          })
        })
      } catch (error) {
        console.error(error)
      } finally {
        this.isLoading = false
      }
    },
  },
})
</script>

<template>
  <div class="md:tw-px-[55px] tw-px-4 tw-mt-20 tw-mb-20">
    <div class="tw-text-center">
      <span class="tw-text-zinc-500 tw-text-3xl">Twimo Help Resources</span>
    </div>

    <div class="tw-text-zinc-500 tw-text-2xl tw-font-light tw-mt-4 tw-mb-[3rem] tw-text-center">
      Need some additional questions answered? Check out our
      <br class="tw-hidden md:tw-block" />help center for step-by-step insturctional videos
    </div>

    <div
      class="tw-flex tw-flex-col md:tw-flex-row tw-overflow-auto tw-pb-5 tw-justify-around tw-gap-[2rem]"
    >
      <HostUtilityItem v-for="(item, index) in hostBlogItems" :key="index" :item="item" />
    </div>
  </div>
</template>

<style scoped></style>
