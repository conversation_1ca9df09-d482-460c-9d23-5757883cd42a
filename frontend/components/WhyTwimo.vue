<script>
export default {
  props: ['image', 'title', 'description', 'animate_delay', 'alt'],
}
</script>
<template>
  <div
    class="md:tw-mb-0 tw-flex tw-flex-row tw-pt-[0rem] tw-pb-[0rem] tw-px-[1.8rem] tw-items-left why_twimo_v2 animate tw-rounded-2xl "
    :class="`animate-${animate_delay}`"
  >
    <div v-if="image" class="tw-mb-[0rem] tw-w-[14%] md:tw-w-[8%] tw-flex tw-items-start tw-justify-left">
      <img
        :src="image"
        class="tw-object-contain tw-w-[4rem] tw-aspect-square tw-transition-all tw-cursor-pointer tw-ml-[-1rem]"
        :alt="alt"
      />
    </div>
    <div class="tw-w-[86%] md:tw-w-[92%]">
        <h3
          v-if="title"
          class="tw-text-[#360877] tw-font-[Poppins] tw-text-[15px] md:tw-text-[1rem] tw-mt-[5px] tw-mb-1 tw-font-medium"
          v-html="title"
        ></h3>
        <div
          class="tw-text-[#360877] tw-font-[Poppins] tw-text-[13px] md:tw-text-[13px] tw-leading-normal tw-font-light"
          v-html="description"
        ></div>
    </div>
  </div>
</template>

<style scoped>
.why_twimo_v2 {
  background: rgba(255, 255, 255);
}

::v-deep .why_twimo_v2 p{
  margin: 0;
  padding: 0;
}

.fade-default {
  animation: none;
}

.animate-1 {
  transition-delay: 0.1s !important;
}

.animate-2 {
  transition-delay: 0.3s !important;
}

.animate-3 {
  transition-delay: 0.5s !important;
}
</style>
