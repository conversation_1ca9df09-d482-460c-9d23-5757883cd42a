<script lang="ts">
// @ts-nocheck

import { defineComponent, toRefs, onMounted, ref } from '@nuxtjs/composition-api'

import AppLoggedInLinks from '~/components/AppLoggedInLinks.vue'
import AppLoggedOutLinks from '~/components/AppLoggedOutLinks.vue'
import { useAuthStore } from '~/composables/useAuthStore'

export default defineComponent({
  components: {
    AppLoggedInLinks,
    AppLoggedOutLinks,
  },

  setup() {
    const { isLoggedIn, isVendorType, isTravelerType } = toRefs(useAuthStore())
    const isMounted = ref(false)

    onMounted(() => {
      isMounted.value = true
    })

    return {
      isLoggedIn,
      isVendorType,
      isTravelerType,
      isMounted
    }
  },
})
</script>

<template>
  <v-row no-gutters class="tw-py-3 header-app tw-border tw-border-b tw-border-gray-200">
    <v-col cols="4" class="tw-flex tw-items-center" style="position: relative">
      <nuxt-link :to="!isLoggedIn ? '/' : (isVendorType ? '/vendor' : (isTravelerType ? '/explore' : '/home'))" class="d-flex align-center mr-2">
        <v-img
          :src="require('~/assets/newhome/twimo-header-logo.png')"
          alt="Twimo Logo"
          :height="45"
          :width="200"
          class="rounded-lg tw-z-[100]"
          loading="lazy"
        />
      </nuxt-link>
    </v-col>

    <app-logged-in-links v-if="isLoggedIn" />

    <app-logged-out-links v-else />
  </v-row>
</template>

<style>
.header-app {
  padding-left: 24px !important;
  padding-right: 24px !important;
}

@media (min-width: 1440px) {
  .header-app {
    padding-left: 64px !important;
    padding-right: 64px !important;
  }
}

@media (min-width: 1600px) {
  .header-app {
    padding-left: 80px !important;
    padding-right: 80px !important;
  }
}

@media (min-width: 1800px) {
  .header-app {
    padding-left: 96px !important;
    padding-right: 96px !important;
  }
}

@media (min-width: 2000px) {
  .header-app {
    padding-left: 120px !important;
    padding-right: 120px !important;
  }
}

.v-toolbar__content {
  padding-left: 0 !important;
  padding-right: 0 !important;
}
</style>
