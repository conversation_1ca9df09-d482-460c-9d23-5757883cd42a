<template>
  <div>
    <v-dialog
      :value="showModal"
      max-width="900"
      content-class="host-activation-dialog"
      :retain-focus="false"
      @input="showModal = $event"
      @click:outside="closeModal"
    >
      <v-card class="tw-p-0 tw-overflow-hidden tw-rounded-xl">
        <!-- Header with steps -->
        <div class="tw-bg-gray-50 tw-border-b tw-border-gray-200 tw-p-4 sm:tw-p-6">
          <div class="tw-flex tw-justify-between tw-items-center tw-mb-4 sm:tw-mb-6">
            <h2 class="tw-text-xl sm:tw-text-2xl tw-font-bold tw-text-zinc-800">Host Activation</h2>
            <v-btn icon @click="closeModal">
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </div>

          <div class="tw-flex tw-items-center tw-justify-between tw-relative">
            <!-- Progress bar -->
            <div class="tw-absolute tw-h-1 tw-bg-gray-200 tw-w-full tw-top-1/2 tw-transform tw-translate-y-1/2 tw-z-0"></div>
            <div
              class="tw-absolute tw-h-1 tw-bg-primary tw-top-1/2 tw-transform tw-translate-y-1/2 tw-z-0 tw-transition-all tw-duration-300"
              :style="{ width: `${(currentStepIndex / (steps.length - 1)) * 100}%` }"
            ></div>

            <!-- Step circles -->
            <div
              v-for="(step, index) in steps"
              :key="step.id"
              class="tw-flex tw-flex-col tw-items-center tw-z-10"
            >
              <div
                class="tw-w-8 tw-h-8 sm:tw-w-10 sm:tw-h-10 tw-rounded-full tw-flex tw-items-center tw-justify-center tw-mb-1 sm:tw-mb-2 tw-transition-colors tw-duration-200"
                :class="[
                  steps[index].completed ? 'tw-bg-green-100 tw-text-green-600' :
                  index === currentStepIndex ? 'tw-bg-primary tw-text-white' :
                  'tw-bg-gray-200 tw-text-gray-500'
                ]"
                @click="goToStep(index)"
              >
                <v-icon v-if="steps[index].completed">mdi-check</v-icon>
                <span v-else>{{ index + 1 }}</span>
              </div>
              <div
                class="tw-text-xs sm:tw-text-sm tw-font-medium tw-text-center"
                :class="[index === currentStepIndex ? 'tw-text-primary' : 'tw-text-gray-500', steps[index].completed ? 'tw-font-medium' : '']"
              >
                {{ step.title }}
              </div>
            </div>
          </div>
        </div>

        <!-- Content area -->
        <div class="tw-p-4 sm:tw-p-6">
          <!-- Step 1: Add Home -->
          <div v-if="currentStepIndex === 0" class="tw-text-center">
            <!-- Completed state -->
            <div v-if="steps[0].completed" class="tw-text-center">
              <div class="tw-mb-6">
                <v-icon color="success" size="64">mdi-check-circle</v-icon>
              </div>
              <h3 class="tw-text-xl sm:tw-text-2xl tw-font-bold tw-text-green-600 tw-mb-4">Home Added Successfully!</h3>
              <p class="tw-text-sm sm:tw-text-base tw-text-zinc-600 tw-mb-8 tw-max-w-lg tw-mx-auto">
                You've successfully added your home to Twimo. You can add more homes or continue with the next steps
                to fully activate your host account.
              </p>
            </div>

            <!-- Not completed state -->
            <div v-else class="tw-text-center">
              <div class="tw-mb-6">
                <img
                  src="~/assets/newhome/home-swap.png"
                  alt="Add your home"
                  class="tw-w-full sm:tw-w-64 tw-mx-auto"
                />
              </div>
              <h3 class="tw-text-xl sm:tw-text-2xl tw-font-bold tw-text-zinc-800 tw-mb-4">Add Your First Home</h3>
              <p class="tw-text-sm sm:tw-text-base tw-text-zinc-600 tw-mb-8 tw-max-w-lg tw-mx-auto">
                Start by adding your first home to Twimo. This will allow you to share your space with others,
                manage bookings, and access all host features.
              </p>
              <div class="tw-flex tw-justify-center">
                <GoodButton class="tw-w-full sm:tw-w-auto" @click="navigateTo('/create')">
                  Add Your Home
                </GoodButton>
              </div>
            </div>
          </div>

          <!-- Step 2: Subscribe -->
          <div v-else-if="currentStepIndex === 1" class="tw-text-center">
            <!-- Completed state -->
            <div v-if="steps[1].completed" class="tw-text-center">
              <div class="tw-mb-6">
                <v-icon color="success" size="64">mdi-check-circle</v-icon>
              </div>
              <h3 class="tw-text-xl sm:tw-text-2xl tw-font-bold tw-text-green-600 tw-mb-4">Host Membership Active!</h3>
              <p class="tw-text-sm sm:tw-text-base tw-text-zinc-600 tw-mb-8 tw-max-w-lg tw-mx-auto">
                You've successfully activated your host membership. You now have access to all Twimo host features
                including home swaps, public bookings, vendor management, and more.
              </p>
              <div class="tw-flex tw-justify-center">
                <GoodButton class="tw-w-full sm:tw-w-auto" @click="goToNextStep">
                  Continue to Next Step
                </GoodButton>
              </div>
            </div>

            <!-- Not completed state -->
            <div v-else class="tw-text-center">
              <div class="tw-mb-6">
                <v-icon color="primary" size="64">mdi-account-check</v-icon>
              </div>
              <h3 class="tw-text-xl sm:tw-text-2xl tw-font-bold tw-text-zinc-800 tw-mb-4">Activate Host Membership</h3>
              <p class="tw-text-sm sm:tw-text-base tw-text-zinc-600 tw-mb-8 tw-max-w-lg tw-mx-auto">
                Unlock all Twimo host features by subscribing to our host membership. Enjoy benefits like
                home swaps, public bookings, vendor management, and more.
              </p>

              <div class="tw-flex tw-justify-center">
                <GoodButton class="tw-w-full sm:tw-w-auto" @click="openSubscriptionModal">
                  Subscribe Now
                </GoodButton>
              </div>
            </div>
          </div>

          <!-- Step 3: Verify Identity -->
          <div v-else-if="currentStepIndex === 2" class="tw-text-center">
            <!-- Completed state -->
            <div v-if="steps[2].completed" class="tw-text-center">
              <div class="tw-mb-6">
                <v-icon color="success" size="64">mdi-check-circle</v-icon>
              </div>
              <h3 class="tw-text-xl sm:tw-text-2xl tw-font-bold tw-text-green-600 tw-mb-4">Identity Verified!</h3>
              <p class="tw-text-sm sm:tw-text-base tw-text-zinc-600 tw-mb-8 tw-max-w-lg tw-mx-auto">
                Your identity has been successfully verified. This helps build trust in the Twimo community
                and ensures a safe experience for all users.
              </p>
              <div class="tw-flex tw-justify-center">
                <GoodButton class="tw-w-full sm:tw-w-auto" @click="goToNextStep">
                  Continue to Completion
                </GoodButton>
              </div>
            </div>

            <!-- Not completed state -->
            <div v-else class="tw-text-center">
              <div class="tw-mb-6">
                <v-icon color="primary" size="64">mdi-shield-account</v-icon>
              </div>
              <h3 class="tw-text-xl sm:tw-text-2xl tw-font-bold tw-text-zinc-800 tw-mb-4">Verify Your Identity</h3>
              <p class="tw-text-sm sm:tw-text-base tw-text-zinc-600 tw-mb-8 tw-max-w-lg tw-mx-auto">
                For safety and trust, we need to verify your identity. This is a one-time process that takes just a few minutes.
                You'll need to provide a government-issued ID and take a selfie.
              </p>

              <div
                class="verification-steps tw-bg-gray-50 tw-rounded-xl tw-p-4 sm:tw-p-6 tw-mb-8 tw-max-w-lg tw-mx-auto tw-text-left"
              >
                <div class="tw-text-lg sm:tw-text-xl tw-font-semibold tw-text-zinc-800 tw-mb-4">You'll need to:</div>
                <div class="tw-flex tw-flex-col tw-gap-4">
                  <div class="tw-flex tw-items-start tw-gap-3">
                    <v-icon color="primary"> mdi-card-account-details-outline </v-icon>
                    <div>
                      <div class="tw-font-medium tw-text-zinc-800">
                        Take a photo of your government-issued ID
                      </div>
                      <div class="tw-text-zinc-600 tw-text-xs sm:tw-text-sm">
                        Passport, driver's license, or national ID card
                      </div>
                    </div>
                  </div>
                  <div class="tw-flex tw-items-start tw-gap-3">
                    <v-icon color="primary"> mdi-face-recognition </v-icon>
                    <div>
                      <div class="tw-font-medium tw-text-zinc-800">
                        Take a quick selfie to verify it's you
                      </div>
                      <div class="tw-text-zinc-600 tw-text-xs sm:tw-text-sm">
                        This helps us confirm your identity matches your ID
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="tw-flex tw-justify-center">
                <GoodButton class="tw-w-full sm:tw-w-auto" @click="openVerificationModal">
                  Start Verification
                </GoodButton>
              </div>
            </div>
          </div>

          <!-- Completed -->
          <div v-else-if="currentStepIndex === 3" class="tw-text-center">
            <div class="tw-mb-6">
              <v-icon color="success" size="64">mdi-check-circle</v-icon>
            </div>
            <h3 class="tw-text-xl sm:tw-text-2xl tw-font-bold tw-text-green-600 tw-mb-4">All Set!</h3>
            <p class="tw-text-sm sm:tw-text-base tw-text-zinc-600 tw-mb-8 tw-max-w-lg tw-mx-auto">
              Congratulations! You've completed all the steps to activate your host account.
              You can now fully use all Twimo host features.
            </p>
            <div class="tw-flex tw-justify-center">
              <GoodButton class="tw-w-full sm:tw-w-auto" @click="closeModal">
                Go to Dashboard
              </GoodButton>
            </div>
          </div>
        </div>

        <!-- Footer with navigation buttons -->
        <div class="tw-bg-gray-50 tw-border-t tw-border-gray-200 tw-p-4 tw-flex tw-flex-col sm:tw-flex-row tw-justify-between tw-gap-2">
          <GoodButtonReverted
            v-if="showBackButton"
            class="tw-order-1 sm:tw-order-none tw-w-full sm:tw-w-auto"
            @click="goToPreviousStep"
          >
            Back
          </GoodButtonReverted>
          <div v-else></div>

          <div class="tw-flex tw-flex-col sm:tw-flex-row tw-gap-2 tw-w-full sm:tw-w-auto tw-order-2 sm:tw-order-none">
            <!-- Skip button only shown for incomplete steps (excluding step 0) -->
            <GoodButtonReverted
              v-if="showSkipButton"
              class="tw-w-full sm:tw-w-auto"
              @click="goToNextStep"
            >
              Skip for now
            </GoodButtonReverted>

            <!-- Continue button shown only when the current step is completed (excluding final step) -->
            <GoodButton
              v-if="showContinueButton"
              class="tw-w-full sm:tw-w-auto"
              @click="goToNextStep"
            >
              {{ continueButtonText }}
            </GoodButton>
          </div>
        </div>
      </v-card>
    </v-dialog>

    <!-- Modals -->
    <PaywallModal
      :model-value="showPaywallModal"
      @update:model-value="showPaywallModal = $event"
    />

    <StripeIdentityModal
      v-model="showIdentityModal"
      @verification-complete="onVerificationComplete"
    />
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, watch, onMounted, onBeforeUnmount , useRouter } from '@nuxtjs/composition-api'

import { useHostActivationStore } from '~/composables/useHostActivationStore'
import { useSubscriptionStore } from '~/composables/useSubscriptionStore'
import { useVerificationStore } from '~/composables/useVerificationStore'
import { useApi, useToast } from '~/composables/useCommon'
import GoodButton from '~/components/GoodButton.vue'
import GoodButtonReverted from '~/components/GoodButtonReverted.vue'
import PaywallModal from '~/components/PaywallModal.vue'
import StripeIdentityModal from '~/components/StripeIdentityModal.vue'

export default defineComponent({
  name: 'HostActivationModal',

  components: {
    GoodButton,
    GoodButtonReverted,
    PaywallModal,
    StripeIdentityModal
  },

  setup() {
    const hostActivationStore = useHostActivationStore()
    const subscriptionStore = useSubscriptionStore()
    const verificationStore = useVerificationStore()
    const router = useRouter()
    const api = useApi()
    const toast = useToast()

    // Local state
    const showModal = computed({
      get: () => hostActivationStore.showActivationModal,
      set: (value) => {
        // When closing via the dialog's built-in mechanisms (like clicking outside),
        // we need to respect the manually opened flag
        if (!value) hostActivationStore.closeActivationModal()
      }
    })

    const steps = computed(() => hostActivationStore.steps)
    const currentStepIndex = computed(() => hostActivationStore.currentStepIndex)

    // Button visibility and text logic
    const showBackButton = computed(() => currentStepIndex.value > 0 && currentStepIndex.value < 3);
    const isCurrentStepCompleted = computed(() => steps.value[currentStepIndex.value]?.completed ?? false);
    const showSkipButton = computed(() =>
      currentStepIndex.value > 0 && // Cannot skip step 0
      currentStepIndex.value < 3 && // No skip on final step
      !isCurrentStepCompleted.value // Only show if not completed
    );
    const showContinueButton = computed(() =>
      currentStepIndex.value < 3 && // Not on final screen
      isCurrentStepCompleted.value // Only if current step is done
    );
    const continueButtonText = computed(() => {
      if (!showContinueButton.value) return '';
      return currentStepIndex.value === 2 ? 'Continue to Completion' : 'Continue to Next Step';
    });

    // Modal states
    const showPaywallModal = ref(false)
    const showIdentityModal = ref(false)

    // Methods
    const closeModal = () => {
      // When explicitly closing via the close button, we should close the modal
      hostActivationStore.closeActivationModal()
    }

    const goToNextStep = () => {
      hostActivationStore.goToNextStep()
    }

    const goToPreviousStep = () => {
      hostActivationStore.goToPreviousStep()
    }

    const goToStep = (index: number) => {
      hostActivationStore.goToStep(index)
    }

    const navigateTo = (route: string) => {
      closeModal()
      router.push(route)
    }

    // Modal methods
    const openSubscriptionModal = () => {
      // Close the host activation modal
      closeModal()

      // Open the subscription modal
      subscriptionStore.showPaywallModal = true
      showPaywallModal.value = true

      // Watch for subscription changes
      const unwatch = watch(
        () => subscriptionStore.hasSubscription,
        async (hasSubscription) => {
          if (hasSubscription) {
            // Update activation status
            await hostActivationStore.checkActivationStatus(api)

            // Allow the modal to be opened automatically in this case
            hostActivationStore.setPreventAutoOpen(false)

            // Reopen the host activation modal and go to next step
            hostActivationStore.openActivationModal()

            // Remove the watcher
            unwatch()
          }
        }
      )
    }

    // Identity verification methods
    const openVerificationModal = () => {
      // Close the host activation modal
      closeModal()

      // Open the verification modal
      showIdentityModal.value = true
    }

    const onVerificationComplete = async () => {
      // Allow the modal to be opened automatically in this case
      hostActivationStore.setPreventAutoOpen(false)

      // Update activation status
      await hostActivationStore.checkActivationStatus(api)

      // Reopen the host activation modal
      hostActivationStore.openActivationModal()
    }

    // We don't need to check activation status on component mount anymore
    // This is now handled by the HomeEditActivationWrapper component
    // onMounted(async () => {
    //   await hostActivationStore.checkActivationStatus(api)
    // })

    // Watch for changes in verification status but don't auto-open the modal
    watch(
      () => verificationStore.isVerified,
      async (isVerified) => {
        if (isVerified) {
          await hostActivationStore.checkActivationStatus(api)
          if (hostActivationStore.isFullyActivated) {
            hostActivationStore.goToStep(3) // Go to completion step
            // Don't auto-open the modal
            // hostActivationStore.openActivationModal()
          }
        }
      }
    )

    // Watch for dialog open to initialize properly
    watch(
      () => showModal.value,
      (newVal) => {
        if (newVal) {
          // Check activation status when modal opens
          hostActivationStore.checkActivationStatus(api)
        }
      }
    )

    return {
      showModal,
      steps,
      currentStepIndex,
      closeModal,
      goToNextStep,
      goToPreviousStep,
      goToStep,
      navigateTo,

      // Modal states
      showPaywallModal,
      showIdentityModal,

      // Modal methods
      openSubscriptionModal,
      openVerificationModal,
      onVerificationComplete,

      // Button computed properties
      showBackButton,
      showSkipButton,
      showContinueButton,
      continueButtonText
    }
  }
})
</script>

<style scoped>
.host-activation-dialog {
  border-radius: 16px;
  overflow: hidden;
}
</style>
