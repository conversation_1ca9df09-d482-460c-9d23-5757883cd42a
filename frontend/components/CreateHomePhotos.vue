<script lang="ts">
// @ts-nocheck
import {
  defineComponent,
  ref,
  reactive,
  toRefs,
  watch,
  onMounted,
  computed,
} from '@nuxtjs/composition-api'

import { useCreateHomeProgressStore } from '~/composables/useCreateHomeStore'
import { useCreateHomeImageStore } from '~/composables/useCreateHomeImageStore'
import { useToast } from '~/composables/useCommon'
import ImagesUploader from '~/components/ImagesUploader.vue'

export default defineComponent({
  name: 'CreateHomePhotos',

  components: {
    ImagesUploader,
  },

  setup() {
    const toast = useToast()
    const store = useCreateHomeProgressStore()
    const imageStore = useCreateHomeImageStore()
    const processingFiles = ref(false)

    // Get store methods and properties
    const { createHomeData, setCurrentStep } = store

    // Use a simple computed property that handles undefined values
    const displayImages = computed(() => {
      // Safely access the images array with fallback to empty array
      const storeImages = imageStore.images || []

      return storeImages.map(img => ({
        ...img,
        id: img.id,
        name: img.name || (img.file ? img.file.name : 'Image'),
        size: img.size || (img.file ? img.file.size : 0),
      }))
    })

    // Check if we have enough images for public rental
    const hasRequiredImages = computed(() => {
      return displayImages.value.length >= 3
    })

    // Check if public rental is enabled
    const isPublicRentalEnabled = computed(() => {
      return createHomeData.isBooking || createHomeData.offerAsSeasonalLease
    })

    onMounted(() => {
      console.log('CreateHomePhotos mounted, displayImages count:', displayImages.value.length)
    })

    // Handle files selected
    const handleFilesSelected = (files: File[]) => {
      if (!files || files.length === 0) {
        return
      }

      processingFiles.value = true
      console.log(`Processing ${files.length} files`)

      try {
        // Add images to store - this is a synchronous operation
        imageStore.addImages(files)

        // Show success toast
        toast.success(`Added ${files.length} image(s) for upload`)

        // Log success
        console.log(`Added ${files.length} files, total in store:`, imageStore.images.length)
      } catch (error) {
        console.error('Error adding images:', error)
        toast.error('Failed to add images')
      } finally {
        processingFiles.value = false
      }
    }

    // Handle image deletion
    const handleDeleteImage = (imageId: string) => {
      try {
        imageStore.deleteImage(imageId)
      } catch (error) {
        console.error('Error deleting image:', error)
        toast.error('Failed to delete image')
      }
    }

    // Handle image reordering - IMPORTANT: Match the event name from ImagesUploader
    const handleImagesReordered = reorderedImages => {
      try {
        console.log('Images reordered, new order:', reorderedImages)
        imageStore.reorderImages(reorderedImages)
        toast.success('Image order updated')
      } catch (error) {
        console.error('Error reordering images:', error)
        toast.error('Failed to reorder images')
      }
    }

    // Handle uploader errors
    const handleUploadError = (errorMessage: string) => {
      toast.error(errorMessage).goAway(3000)
    }

    return {
      createHomeData,
      setCurrentStep,
      displayImages,
      isUploading: imageStore.isUploading,
      processingFiles,
      handleFilesSelected,
      handleDeleteImage,
      handleImagesReordered,
      handleUploadError,
      hasRequiredImages,
      isPublicRentalEnabled,
    }
  },

  methods: {
    triggerUploader() {
      ;(this.$refs.imageUploader as any)?.triggerFileInput()
    },
  },
})
</script>

<template>
  <v-row>
    <v-col cols="12" class="tw-mt-8 tw-flex tw-flex-col tw-gap-4">
      <div class="tw-text-2xl tw-font-semibold tw-text-zinc-600">Let's Add Some Photos</div>
      <div v-if="displayImages.length > 0" class="tw-text-sm tw-text-green-600">
        {{ displayImages.length }} image(s) ready to upload
      </div>

      <!-- Enhanced warning for public rental listings -->
      <div
        v-if="isPublicRentalEnabled && displayImages.length < 3"
        class="tw-p-3 tw-bg-red-50 tw-border tw-border-red-200 tw-rounded-md tw-text-red-600"
      >
        <strong>Required:</strong> At least 3 images are needed for public rental listings. Your
        home won't be accessible to the public until you add at least 3 images.
      </div>

      <!-- Image guidelines -->
      <div class="tw-p-3 tw-bg-blue-50 tw-border tw-border-blue-200 tw-rounded-md tw-text-blue-700 tw-mt-2">
        <div class="tw-font-medium tw-mb-1">Image Guidelines:</div>
        <ul class="tw-list-disc tw-pl-5 tw-text-sm">
          <li>Use high-quality, well-lit photos that showcase your property</li>
          <li>Supported formats: JPG, PNG (max 10MB per image)</li>
          <li>Recommended dimensions: 800x600 to 2000x1500 pixels</li>
          <li>Include photos of all main rooms and exterior views</li>
          <li>The first image will be your cover photo (drag to reorder)</li>
        </ul>
      </div>

      <div v-if="displayImages.length > 1" class="tw-text-sm tw-text-gray-600 tw-mt-2">
        Drag and drop to reorder images. The first image will be your cover photo.
      </div>
    </v-col>

    <v-col cols="12" class="tw-flex tw-flex-col tw-gap-4">
      <ImagesUploader
        ref="imageUploader"
        class="tw-min-h-[500px]"
        :images="displayImages"
        :max-files="20"
        :max-file-size="10"
        :is-uploading="isUploading || processingFiles"
        @files-selected="handleFilesSelected"
        @delete-image="handleDeleteImage"
        @images-reordered="handleImagesReordered"
        @error="handleUploadError"
      />
    </v-col>
  </v-row>
</template>

<style scoped></style>
