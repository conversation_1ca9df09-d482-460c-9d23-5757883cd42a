<script lang="ts">
// @ts-nocheck
import { defineComponent } from '@nuxtjs/composition-api'

export default defineComponent({
  name: '<PERSON><PERSON><PERSON><PERSON>',

  props: {
    disabled: {
      type: Boolean,
      default: false,
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },
})
</script>

<template>
  <v-btn
    class="tw-normal-case tw-tracking-normal tw-rounded-full tw-font-semibold"
    large
    :disabled="disabled"
    :loading="loading"
    dark
    v-on="$listeners"
  >
    <slot></slot>
  </v-btn>
</template>

<style scoped>
button {
  background: conic-gradient(
    from 90deg at 50% 50%,
    rgba(56, 11, 115, 0.9) -124.46deg,
    #7c0cb1 73.63deg,
    rgba(56, 11, 115, 0.9) 235.54deg,
    #7c0cb1 433.63deg
  ) !important;
}
</style>
