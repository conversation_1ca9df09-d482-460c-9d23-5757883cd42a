<script>
export default {
  props: {
    features: {
      type: Array,
      required: true,
    },
    twimo_features: {
      type: Array,
      required: true,
    },
    other_features: {
      type: Array,
      required: true,
    },
  },
}
</script>
<template>
  <div>
    <div
      class="tw-flex tw-gap-[0.3rem] tw-rounded-[10px] tw-overflow-hidden tw-mb-[2rem] tw-shadow-[0px_4px_4px_rgba(54,8,119,0.26)]"
    >
      <div
        class="tw-w-6/12 tw-bg-[#2C005B] tw-flex tw-items-center tw-justify-center tw-gap-[0.5rem] tw-pl-[0.2rem]"
      >
        <img src="@/assets/twimo-white.png" class="tw-max-w-none tw-w-auto tw-h-[20px]" />
      </div>
      <div
        class="tw-w-6/12 tw-flex tw-bg-[#2C005B80] tw-align-center tw-justify-center tw-gap-[0rem] tw-p-[0.2rem] tw-items-center tw-text-center tw-font-semibold tw-text-white tw-text-[16px] tw-pt-[0.5rem] tw-pb-[0.5rem] tw-justify-center"
      >
        Airbnb, VRBO,<br />
        PM Companies
      </div>
    </div>

    <div v-for="(item, i) in features" :key="i" class="tw-mb-[1rem]">
      <h3
        class="tw-text-[#2C005B] tw-text-[18px] tw-text-center tw-mb-[5px] tw-font-[700]"
        v-html="item"
      ></h3>

      <div
        class="tw-flex tw-gap-[0.3rem] tw-rounded-[10px] tw-overflow-hidden tw-shadow-[0px_4px_4px_rgba(54,8,119,0.26)]"
      >
        <div
          class="tw-w-6/12 tw-bg-[#2C005B] tw-flex tw-items-center tw-justify-center tw-gap-[0.5rem] tw-pl-[0.2rem] tw-pt-[0.6rem] tw-pb-[0.6rem]"
        >
          <div class="tw-text-white" v-html="twimo_features[i]"></div>
        </div>
        <div
          class="tw-w-6/12 tw-flex tw-bg-[#2C005B80] tw-align-center tw-justify-center tw-gap-[0rem] tw-p-[0.2rem] tw-items-center tw-pt-[0.6rem] tw-pb-[0.6rem] tw-justify-center"
        >
          <div class="tw-text-center tw-text-white tw-pr-[0.4rem]" v-html="other_features[i]"></div>
        </div>
      </div>
    </div>
  </div>
</template>
