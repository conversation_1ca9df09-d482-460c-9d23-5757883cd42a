<script>
import Swiper from 'swiper/bundle';

import 'swiper/swiper-bundle.css';

export default {
  components: {
    Swiper,
  },
  props: {
    data: {
      type: Array,
      required: true,
    },
  },

  data() {
    return {
        swiperInstance: null,
    }
  },

  mounted() {
    this.swiperInstance = new Swiper(this.$refs.testimonialSwiper, {
      loop: true,
      slidesPerView: 'auto',
      spaceBetween: 20,
      centeredSlides: false,
      pagination: {
        el: '.swiper-pagination',
        clickable: true
      },     
      navigation: {
        nextEl: '.swiper-button-next',
        prevEl: '.swiper-button-prev'
      },
      breakpoints: {
            // when window width is >= 320px
            320: {
                slidesPerView: 1.2,
                spaceBetween: 5
            },
            1200: {
                slidesPerView: 1.5,
                spaceBetween: 20,
                centeredSlides: true,
            },
      },
    })
  }
};
</script>

<template>
    <div ref="testimonialSwiper" class="swiper-container testimonial-slider">
        <div class="swiper-wrapper">
            <div
                v-for="(item, index) in data"
                :key="index"
                class="swiper-slide"
            >
                <div 
                    :style="{
                            backgroundImage: `url(${item.bgImage})`,
                            }"
                    class="indiv-testimonial tw-min-h-[250px] tw-flex tw-items-center tw-justify-center md:tw-min-h-[80vh] tw-p-5 tw-mr-3 tw-ml-3 tw-mb-5">
                    <div class="testimonial-inside tw-max-w-[500px] tw-mx-auto tw-text-base md:tw-text-xl tw-font-[Poppins]">
                        {{ item.content }}
                    </div>
                </div>
                
            </div>
        </div>

        <!-- Optional controls -->
        <div class="swiper-pagination"></div>
        <!--<div class="swiper-button-next"></div>
        <div class="swiper-button-prev"></div>-->
    </div>    
</template>

<style scoped>
.indiv-testimonial {
    background-size: cover;
    background-position: center;
    border-radius: 10px;
}

.testimonial-inside {
    background:linear-gradient(0deg, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2)), linear-gradient(0deg, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2)), rgba(44, 0, 91, 0.5);
    padding: 20px;
    border-radius: 10px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color:#FFF;
}

.swiper-pagination {
    bottom:-5px !important;
}

::v-deep .swiper-pagination .swiper-pagination-bullet-active {
    background: #360877 !important;
}

.testimonial-slider .swiper-slide {
  opacity: 0.5;
}
.testimonial-slider .swiper-slide.swiper-slide-active {
  opacity:1;
}

@media screen and (max-width: 600px) {

}
</style>
