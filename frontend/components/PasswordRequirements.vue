<template>
  <div 
    v-if="password || showRequirements"
    :class="[
      'tw-p-3 tw-rounded-2xl tw-border tw-transition-all tw-duration-300',
      darkMode 
        ? 'tw-bg-white/10 tw-border-white/20 tw-backdrop-blur-sm' 
        : 'tw-bg-gray-50 tw-border-gray-200'
    ]"
  >
    <div
:class="[
      'tw-text-sm tw-font-medium tw-mb-2',
      darkMode ? 'tw-text-white' : 'tw-text-gray-700'
    ]">
      Password Requirements:
    </div>
    
    <div class="tw-space-y-1">
      <!-- Minimum Length Requirement -->
      <div class="tw-flex tw-items-center tw-gap-2">
        <v-icon 
          :color="requirements.minLength ? 'success' : (darkMode ? 'white' : 'grey')"
          size="16"
          class="tw-transition-colors tw-duration-200"
        >
          {{ requirements.minLength ? 'mdi-check-circle' : 'mdi-circle-outline' }}
        </v-icon>
        <span
:class="[
          'tw-text-xs tw-transition-colors tw-duration-200',
          requirements.minLength 
            ? 'tw-text-green-600 tw-font-medium' 
            : darkMode 
              ? 'tw-text-white/70' 
              : 'tw-text-gray-600'
        ]">
          At least 8 characters
        </span>
      </div>

      <!-- Maximum Length Requirement -->
      <div class="tw-flex tw-items-center tw-gap-2">
        <v-icon
          :color="requirements.maxLength ? 'success' : (darkMode ? 'white' : 'grey')"
          size="16"
          class="tw-transition-colors tw-duration-200"
        >
          {{ requirements.maxLength ? 'mdi-check-circle' : 'mdi-circle-outline' }}
        </v-icon>
        <span
:class="[
          'tw-text-xs tw-transition-colors tw-duration-200',
          requirements.maxLength
            ? 'tw-text-green-600 tw-font-medium'
            : darkMode
              ? 'tw-text-white/70'
              : 'tw-text-gray-600'
        ]">
          No more than 24 characters
        </span>
      </div>

      <!-- Password Strength Tips (only show when password is being typed) -->
      <div v-if="password && !allRequirementsMet" class="tw-mt-2 tw-pt-2 tw-border-t" :class="darkMode ? 'tw-border-white/20' : 'tw-border-gray-200'">
        <div class="tw-flex tw-items-start tw-gap-2">
          <v-icon
            :color="darkMode ? 'white' : 'grey'"
            size="14"
            class="tw-mt-0.5"
          >
            mdi-lightbulb-outline
          </v-icon>
          <div
:class="[
            'tw-text-xs',
            darkMode ? 'tw-text-white/60' : 'tw-text-gray-500'
          ]">
            <strong>Tip:</strong> Use a mix of letters, numbers, and symbols for better security
          </div>
        </div>
      </div>
    </div>

    <!-- Overall Status -->
    <div v-if="password" class="tw-mt-3 tw-pt-2 tw-border-t" :class="darkMode ? 'tw-border-white/20' : 'tw-border-gray-200'">
      <div class="tw-flex tw-items-center tw-gap-2">
        <v-icon 
          :color="allRequirementsMet ? 'success' : 'warning'"
          size="16"
        >
          {{ allRequirementsMet ? 'mdi-shield-check' : 'mdi-shield-alert' }}
        </v-icon>
        <span
:class="[
          'tw-text-xs tw-font-medium',
          allRequirementsMet 
            ? 'tw-text-green-600' 
            : darkMode 
              ? 'tw-text-yellow-400' 
              : 'tw-text-yellow-600'
        ]">
          {{ allRequirementsMet ? 'Password meets all requirements' : 'Password needs improvement' }}
        </span>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, computed } from '@nuxtjs/composition-api'

export default defineComponent({
  name: 'PasswordRequirements',
  
  props: {
    password: {
      type: String,
      default: '',
    },
    darkMode: {
      type: Boolean,
      default: false,
    },
    showRequirements: {
      type: Boolean,
      default: true,
    },
  },

  setup(props) {
    // Individual requirement checks
    const requirements = computed(() => ({
      minLength: props.password.length >= 8,
      maxLength: props.password.length <= 24,
    }))

    // Overall validation status
    const allRequirementsMet = computed(() => {
      return Object.values(requirements.value).every(req => req)
    })

    return {
      requirements,
      allRequirementsMet,
    }
  },
})
</script>

<style scoped>
/* Additional styling for smooth transitions */
.tw-transition-all {
  transition-property: all;
}

.tw-transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
}

.tw-duration-200 {
  transition-duration: 200ms;
}

.tw-duration-300 {
  transition-duration: 300ms;
}

/* Ensure icons are properly aligned */
:deep(.v-icon) {
  vertical-align: middle;
}
</style>
