<script setup>
import { ref } from 'vue'
defineProps({
  title: {
    type: String,
  },
  price: {
    type: String,
  },
  features: {
    type: Array,
  },
  buttonName: {
    type: String,
  },
  buttonLink: {
    type: String,
  },
})
</script>
<template>
  <div
    :style="{ boxShadow: '0 0px 8px #00000040' }"
    class="tw-max-w-[350px] tw-rounded-2xl tw-p-8 tw-px-7 tw-bg-center tw-bg-no-repeat tw-bg-gradient-to-t tw-from-[rgba(255, 255, 255, 1)] tw-to-[rgba(225, 225, 225, 0)]"
  >
    <h2 class="tw-text-[#360877] tw-text-2xl md:tw-text-[26px] tw-mb-2 tw-leading-10 tw-font-[700]">
      {{ title }}
    </h2>
    <h3
      v-if="price"
      class="tw-text-[#360877] tw-text-xl md:tw-text-2xl tw-mb-10 tw-font-extrabold"
      v-html="price"
    ></h3>
    <p class="tw-text-[#360877] tw-text-xl md:tw-text-xl tw-mb-4 tw-font-light">What’s Included:</p>
    <ul class="tw-p-0 tw-m-0">
      <li
        v-for="(item, i) in features"
        :key="i"
        :style="{ backgroundSize: '22px' }"
        class="tw-bg-[url('~/assets/newhome/checkmark_icon.png')] tw-mb-4 tw-bg-no-repeat tw-bg-left-top tw-pl-8 tw-text-base tw-text-[#360877] tw-font-medium"
      >
        {{ item }}
      </li>
    </ul>
    <div v-if="buttonName" class="text-end">
      <NuxtLink
        :to="buttonLink"
        class="about__page__banner--button tw-bg-white tw-shadow-theme tw-py-2 tw-px-4 tw-text-[#7C0CB1] tw-transition-all tw-duration-300 hover:tw-bg-[#7C0CB1] hover:tw-text-white tw-rounded-theme tw-text-base tw-font-extrabold tw-rounded-3xl"
      >
        {{ buttonName }}
      </NuxtLink>
      <!--             <NuxtLink :href="buttonLink" class="bg-white shadow-theme py-3 px-6 text-[#7C0CB1] transition-all duration-300 hover:bg-[#7C0CB1] hover:text-white rounded-theme text-xl font-extrabold">{{ buttonName }}</NuxtLink>
 -->
    </div>
  </div>
</template>
