<script lang="ts">
// @ts-nocheck
import { defineComponent, watch } from '@nuxtjs/composition-api'
import { storeToRefs } from 'pinia'

import { useCreateHomeProgressStore } from '~/composables/useCreateHomeStore'
import { checkExistingSlug, slugify } from '~/helpers'
import { spaceTypes } from '~/constants'
import { useApi } from '~/composables/useCommon'

export default defineComponent({
  name: 'CreateHomeTitle',

  setup() {
    const store = useCreateHomeProgressStore()

    const api = useApi()

    const {
      createHomeData,
      validateCreateHomeData,
      isSpaceTypeSelected,
      clickOnTypeOfSpace,
      fetchPreviewSlug,
    } = store

    const { previewSlug } = storeToRefs(store)

    watch(
      () => createHomeData.title,
      () => {
        fetchPreviewSlug(api)
      }
    )

    return {
      createHomeData,
      validateCreateHomeData,
      isSpaceTypeSelected,
      slugify,
      spaceTypes,
      clickOnTypeOfSpace,
      previewSlug,
      checkExistingSlug,
    }
  },
})
</script>

<template>
  <v-row>
    <v-col cols="12" md="8" class="tw-mt-8 tw-flex tw-flex-col tw-gap-4">
      <div class="tw-text-2xl tw-font-semibold tw-text-zinc-600">
        To begin, let’s title your vacation home:
      </div>

      <div class="tw-text-lg tw-font-medium tw-text-zinc-500">
        The title of your home will be what you and your guests see on your Twimo custom URL.
      </div>

      <v-text-field
        v-model="createHomeData.title"
        label="Title*"
        outlined
        hide-details
        dense
        required
      />

      <div
        v-if="
          createHomeData.title &&
          previewSlug &&
          !checkExistingSlug(createHomeData.title, previewSlug)
        "
        class="tw-text-zinc-500"
      >
        "{{ createHomeData.title }}" is being used by another owner, therefore your URL will appear
        with an added number. If you want to continue, click next or update your Home Title.
      </div>

      <div
        v-if="createHomeData.title"
        class="tw-text-zinc-500 tw-text-md tw-flex tw-items-center tw-font-medium tw-gap-2"
      >
        <v-icon small color="primary"> mdi-check </v-icon>
        <span>https://twimo.com/{{ previewSlug }}</span>
      </div>
    </v-col>
  </v-row>
</template>

<style scoped></style>
