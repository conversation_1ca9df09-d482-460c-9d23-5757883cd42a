<template>
  <div class="image__content--container">
    <div class="image__content__container--image tw-hidden md:tw-block">
      <img :src="src" :alt="alt" width="100%" class="hover:tw-scale-125 tw-transition-all" />
    </div>
    <div class="image__content__container--content">
      <h4 v-if="sub_heading" class="sub_heading tw-text-left">
        {{ sub_heading }}
      </h4>
      <h3 v-if="heading" class="heading tw-text-left tw-font-[Poppins]" v-html="heading"></h3>

      <div class="image__content__container--image tw-mb-5 tw-block md:tw-hidden">
        <img :src="src" :alt="alt" width="100%" class="md:hover:tw-scale-125 tw-transition-all" />
      </div>

      <div v-if="content" class="content  tw-font-[Poppins]" v-html="content"></div>
      <div v-if="buttonName" class="button_container tw-text-center md:tw-text-left">
        <a :href="buttonLink" class="button" :class="bgColor" :target="target">{{ buttonName }}</a>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    src: String,
    alt: String,
    heading: String,
    sub_heading: String,
    content: String,
    buttonName: String,
    buttonLink: String,
    target: {
      type: String,
      default: '_self',
    },
    bgColor: {
      default: 'bg-white',
    },
  },
}
</script>
<style scoped>
.image__content--container {
  display: flex;
  gap: 20px;
  justify-content: space-between;
  align-items: center;
}
.image__content--container[order='DESC'] {
  flex-direction: row-reverse;
}
.image__content__container--image {
  width: 45%;
}
.image__content__container--content {
  width: 55%;
}
.image__content--container[order='DESC'] .image__content__container--content {
}

.image__content__container--image img {
  width: 100%;
  object-fit: cover;
  border-radius: 20px;
}
.image__content__container--content {
  padding: 0 20px;
  color: #7b7b7b;
}
.sub_heading {
  color: #360877;
  font-weight: 700;
  font-size: 16px;
  margin-bottom: 10px;
}
.heading {
  color: #360877;
  font-weight: 500;
  font-size: 30px;
  line-height: 35px;
  margin-bottom: 20px;
}
.content,
.content p {
  color: #360877;
  line-height: 25px;
  font-size: 16px;
  margin-bottom: 20px;
  font-weight: 300;
}
.button_container {
  margin-top: 30px;
}
.button {
  border-radius: 30px;
  font-size: 14x;
  line-height: 22px;
  padding: 9px 30px;
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
  display: inline-block;
  font-weight: 800;
  transition: all 300ms ease;
  background: conic-gradient(
    from 90deg at 50% 50%,
    rgba(56, 11, 115, 0.9) -124.46deg,
    #7c0cb1 73.63deg,
    rgba(56, 11, 115, 0.9) 235.54deg,
    #7c0cb1 433.63deg
  );
}
.button:hover {
  background: #fff;
}

.bg-purple {
  background-color: #7c0cb1;
  color: white;
}
.bg-purple:hover {
  color: #7c0cb1;
  background-color: #fff;
}
.bg-white {
  color: #7c0cb1;
  background-color: white;
}
.bg-white:hover {
  background-color: #7c0cb1;
  color: #fff;
}
@media screen and (max-width: 767px) {
  .heading {
    font-size: 26px;
  }
  .image__content__container--image img {
    max-width: 400px;
    display: block;
    margin: auto;
  }
  .image__content--container {
    flex-direction: column;
  }
  .image__content--container[order='DESC'] {
    flex-direction: column;
  }
  .image__content__container--image,
  .image__content__container--content {
    width: 100%;
  }
  .image__content__container--content {
    padding: 0;
  }
}
</style>
