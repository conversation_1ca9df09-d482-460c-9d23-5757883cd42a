<script lang="ts">
// @ts-nocheck
import { defineComponent } from '@nuxtjs/composition-api'

export default defineComponent({
  name: 'CategoryFilter',

  props: {
    role_id: {
      type: Number,
      required: true,
    },
  },

  data() {
    return {
      categorySelected: [],
      categoryFilter: [],
    }
  },

  async mounted() {
    try {
      const { data } = await this.$axios.get('mycalendar/get-categories')

      this.categoryFilter = data.categories

      for (const c of data.categories) {
        this.categorySelected.push(c.id)
      }
    } catch (e) {
      console.log(e)
    }
  },

  methods: {
    categorySelection: function () {
      this.$emit('category-selection', this.categorySelected)
    },
    updateCreateEventPopup: function () {
      this.$emit('update-create-event-popup', true)
      this.$emit('close-edit-popup', true)
    },
  },
})
</script>

<template>
  <div>
    <div v-if="role_id != null && role_id != 4" class="add_btn_container">
      <GoodButton class="tw-mt-2" size="small" @click="updateCreateEventPopup">
        <i
          aria-hidden="true"
          class="v-icon notranslate mdi mdi-plus theme--light"
          data-v-52dbc9e4=""
        ></i>
        Add Event
      </GoodButton>
    </div>

    <p>Check all calendars you would like applied in the calendar view</p>

    <ul>
      <li
        v-for="(option, index) in categoryFilter"
        v-if="role_id != null && ((role_id == 4 && [5, 7].includes(option.id)) || role_id != 4)"
        :key="index"
      >
        <label class="form-control" :class="'tag-' + option.id" :data-color="option.color">
          {{ option.label }}
          <input
            v-model="categorySelected"
            type="checkbox"
            name="checkbox-checked"
            class="categorySelection"
            :value="option.id"
            @change="categorySelection()"
          />
          <div class="control__indicator" :style="`background-color:#${option.color}`"></div>
        </label>
      </li>
    </ul>
  </div>
</template>

<style scoped></style>
