<script lang="ts">
// @ts-nocheck
import { defineComponent } from '@nuxtjs/composition-api'

import { useCreateHomeProgressStore } from '~/composables/useCreateHomeStore'
import { spaceTypes } from '~/constants'

export default defineComponent({
  name: 'CreateHomeSpaceType',

  setup() {
    const store = useCreateHomeProgressStore()
    const { createHomeData } = store

    const homeTypes = [
      'House',
      'Apartment',
      'Condo',
      'Guest House',
      'Houseboat',
      'Treehouse',
      'RV/Camper',
    ]

    return {
      createHomeData,
      homeTypes,
      spaceTypes,
    }
  },
})
</script>

<template>
  <v-row>
    <v-col cols="12" class="tw-mt-8 tw-flex tw-flex-col tw-gap-4">
      <div class="tw-text-2xl tw-font-semibold tw-text-zinc-600">
        Input your home information below:
      </div>
      <div class="tw-text-lg tw-font-medium tw-text-zinc-500">Please select your home type:</div>
    </v-col>

    <v-col cols="12" md="8">
      <div class="tw-text-2xl tw-font-semibold tw-text-zinc-600 tw-mb-4">Home Type</div>
      <v-select
        v-model="createHomeData.homeType"
        :items="homeTypes"
        outlined
        dense
        placeholder="Select your home type"
      />
    </v-col>

    <v-col cols="12" md="8">
      <div class="tw-text-2xl tw-font-semibold tw-text-zinc-600 tw-mb-4">Type of Space</div>
      <v-select
        v-model="createHomeData.typeOfSpace"
        :items="spaceTypes"
        item-text="title"
        item-value="title"
        outlined
        dense
        placeholder="Select your space type"
      >
        <template #item="{ item }">
          <v-list-item-content>
            <v-list-item-title class="tw-text-lg tw-font-semibold tw-text-zinc-600">
              {{ item.title }}
            </v-list-item-title>
            <v-list-item-subtitle class="tw-text-sm tw-font-medium tw-text-zinc-500">
              {{ item.description }}
            </v-list-item-subtitle>
          </v-list-item-content>
        </template>
      </v-select>
    </v-col>

    <v-col cols="12" md="8">
      <div class="tw-text-2xl tw-font-semibold tw-text-zinc-600 tw-mb-4">Additional Notes</div>
      <v-textarea
        v-model="createHomeData.additionalNotes"
        outlined
        dense
        placeholder="If you have a community pool, shared yard, or if you will occupy a space within the home, please note those details here."
        rows="6"
      />
    </v-col>
  </v-row>
</template>
