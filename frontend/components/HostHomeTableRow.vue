<script lang="ts">
// @ts-nocheck
import { defineComponent, PropType } from '@nuxtjs/composition-api'

import { HomeTo } from '~/types'
import BeastImage from '~/components/BeastImage.vue'
import screenCheckerMixins from '~/mixins/screenChecker.mixins'

export default defineComponent({
  name: 'HostHomeTableRow',

  components: { BeastImage },

  mixins: [screenCheckerMixins],

  props: {
    item: {
      type: Object as PropType<HomeTo>,
      required: true,
    },
    getImageUrl: {
      type: Function as PropType<(item: HomeTo) => string>,
      required: true,
    },
    getStatusColor: {
      type: Function as PropType<(status: string) => string>,
      required: true,
    },
    capitalizeFirstLetter: {
      type: Function as PropType<(str: string) => string>,
      required: true,
    },
    updateHomeSingleField: {
      type: Function as PropType<(item: HomeTo, field: string, value: any) => void>,
      required: true,
    },
    handleEdit: {
      type: Function as PropType<(item: HomeTo) => void>,
      required: true,
    },
    handleArchive: {
      type: Function as PropType<(item: HomeTo) => void>,
      required: true,
    },
  },
})
</script>

<template>
  <v-card
    class="tw-h-full tw-cursor-pointer tw-overflow-hidden border-left-top border-right-top transition-swing"
    elevation="0"
  >
    <v-card-text class="pa-0">
      <!-- Image Container - Updated to be square -->
      <div class="tw-relative tw-aspect-square">
        <BeastImage
          class="border-left-top border-right-top tw-rounded-b-lg tw-rounded-t-lg explore-image-container tw-object-cover tw-w-full tw-h-full"
          :src="getImageUrl(item)"
          :alt="item.title"
          :title="item.title"
        />

        <!-- Status Pill -->
        <div class="tw-absolute tw-top-4 tw-right-4">
          <div
            class="tw-px-3 tw-py-1 tw-rounded-full tw-bg-white tw-shadow-sm tw-flex tw-items-center"
          >
            <span
              class="tw-w-2 tw-h-2 tw-rounded-full tw-mr-2"
              :class="item.status === 'active' ? 'tw-bg-green-500' : 'tw-bg-red-500'"
            ></span>
            <span class="tw-text-sm tw-font-medium">
              {{ item.status === 'active' ? 'active' : 'draft' }}
            </span>
          </div>
        </div>
      </div>

      <!-- Content Section -->
      <div class="tw-mt-1 pa-4">
        <h3 class="tw-text-xl tw-font-medium tw-text-gray-800">
          {{ item.title }}
        </h3>
      </div>
    </v-card-text>
  </v-card>
</template>

<style scoped>
.border-right-top {
  border-top-right-radius: 6px;
}

.border-left-top {
  border-top-left-radius: 6px;
}

.explore-image-container {
  border-radius: 18px !important;
  /* box-shadow: 0px 3px 4px #00000066; */
  margin-bottom: 10px;
}
</style>
