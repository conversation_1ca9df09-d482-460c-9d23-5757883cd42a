<script lang="ts">
// @ts-ignore

import { defineComponent, onMounted, ref, useRoute, watch, computed } from '@nuxtjs/composition-api'
import { storeToRefs } from 'pinia'
import { mapGetters } from 'vuex'

import OpenStreetAutocomplete from '~/components/OpenStreetAutocomplete.vue'
import BeastImage from '~/components/BeastImage.vue'
import GoodCard2 from '~/components/GoodCard2.vue'
import SmallDot from '~/components/SmallDot.vue'
import AddCustomPricingForm from '~/components/AddCustomPricingForm.vue'
import CustomPricingList from '~/components/CustomPricingList.vue'
import Amenity from '~/components/Amenity.vue'
import ChillButton from '~/components/ChillButton.vue'
import GoodButton from '~/components/GoodButton.vue'
import GoodButtonIncreaseNumber from '~/components/GoodButtonIncreaseNumber.vue'
import GoodButtonReverted from '~/components/GoodButtonReverted.vue'
import ImagesUploader from '~/components/ImagesUploader.vue'
import SpaceType from '~/components/SpaceType.vue'
import PaywallModal from '~/components/PaywallModal.vue'
import HostActivationModal from '~/components/HostActivationModal.vue'
import { useApi, useToast } from '~/composables/useCommon'
import { useSubscriptionStore } from '~/composables/useSubscriptionStore'
import { useEditHomeStore } from '~/composables/useEditHomeStore'
import { useImageStore } from '~/composables/useImageStore'
import { useHostActivationStore } from '~/composables/useHostActivationStore'
import {
  amenities,
  CANCELLATION_POLICY_TYPE,
  petFriendlyNumbers,
  petFriendlyOptions,
  petFriendlyTypes,
  spaceTypes,
  MANDATORY_SAFETY_AMENITIES,
} from '~/constants'
import { capitalizeFirstLetter, debounce } from '~/helpers'
import screenCheckerMixins from '~/mixins/screenChecker.mixins'
import PhotoProcessingStatus from '~/components/PhotoProcessingStatus.vue'
import HomeCompletionNotification from '~/components/HomeCompletionNotification.vue'
import ConfirmationDialog from '~/components/ConfirmationDialog.vue'

export default defineComponent({
  components: {
    BeastImage,
    ChillButton,
    CustomPricingList,
    AddCustomPricingForm,
    Amenity,
    OpenStreetAutocomplete,
    SpaceType,
    GoodButtonIncreaseNumber,
    GoodButton,
    GoodButtonReverted,
    SmallDot,
    GoodCard2,
    ImagesUploader,
    PaywallModal,
    HostActivationModal,
    PhotoProcessingStatus,
    HomeCompletionNotification,
    ConfirmationDialog,
  },

  ssr: false,

  mixins: [screenCheckerMixins],

  props: {
    propSlug: {
      type: null,
      default: null,
    },
    homeData: {
      type: Object,
      default: null,
      required: false,
    },
  },

  setup(props) {
    const homeData = props.homeData
    const api = useApi()
    const toast = useToast()
    const subscriptionStore = useSubscriptionStore()
    const hostActivationStore = useHostActivationStore()
    const store = useEditHomeStore()
    const imageStore = useImageStore()
    const route = useRoute()
    const slug = homeData?.slug || props.propSlug || route.value.params.slug || route.value.params.id

    // Ref to control API calls during initialization
    const preventApiCalls = ref(true)
    const isInitializing = ref(true)

    // Flag to prevent unwanted CustomPricing API calls during photo operations
    const preventCustomPricingLoad = ref(false)

    // Destructure all the necessary refs from the store
    const { editHomeData } = store

    // Create reactive refs from subscription store to ensure state is tracked properly
    const { showPaywallModal, hasSubscription } = storeToRefs(subscriptionStore)

    const isSafetyAmenity = (amenity: string) => {
      return MANDATORY_SAFETY_AMENITIES.includes(amenity)
    }

    const {
      fetchHomeData,
      deletePhoto,
      startEditTitle,
      closeEditTitleDialog,
      updateTitle,
      editAmountDialogData,
      startEditAmount,
      closeEditAmountDialog,
      updateAmount,
      startEditTypeOfSpace,
      closeEditTypeOfSpaceDialog,
      updateTypeOfSpace,
      clickOnTypeOfSpace,
      isSpaceTypeSelected,
      startEditAddress,
      closeEditAddressDialog,
      updateAddress,
      startEditAmenities,
      closeEditAmenitiesDialog,
      clickOnAmenity,
      isAmenitySelected,
      updateAmenities,
      startEditDescription,
      closeEditDescriptionDialog,
      updateDescription,
      updatePhotos,
      updateSettingField,
      shareHome,
      sharePrivateHome,
      privateHomeAdjustedPriceDialogData,
      clickOnPrivateHomeAdjustedPrice,
      saveAdjustedPrice,
      copyPrivateHomeLink,
      createPassword,
      resetPassword,
      sendEmail,
      updateCancellationPolicy,
      updateCheckInOut,
      initializeHouseRules,
      updateHouseRules,
      saveCheckIn,
      saveCheckOut,
      saveQuietHoursStart,
      saveQuietHoursEnd,
      autogenerateSharableLink,
      setDefaultPolicies,
      updateNoEvents,
      updateNoSmoking,
      updateQuietHours,
      updateAdditionalRules,
      uploadProgress,
      isUploading,
      maxPhotos,
      uploadFiles,
      decreaseBedrooms,
      decreaseBeds,
      decreaseBathrooms,
      increaseBathrooms,
      decreaseGuests,
      toggleHomeStatus,
      archiveHome,
      unarchiveHome,
    } = store

    const {
      isLoading,
      getStatusColor,
      editTitleDialog,
      editAmountDialog,
      editTitleDialogData,
      displayedPets,
      editTypeOfSpaceDialog,
      editTypeOfSpaceDialogData,
      editAddressDialog,
      editAddressDialogData,
      editAmenitiesDialog,
      editAmenitiesDialogData,
      editDescriptionDialog,
      editDescriptionDialogData,
      photosRan,
      skipWatch,
      fetchedHomeData,
      privateHomeAdjustedPriceDialog,
      userWantToAdjustPrice,
      privateHomeCreatePasswordDialog,
      passwordChangedDialog,
      password,
      confirmPassword,
      resetPasswordDialog,
      emailDropdown,
      emailPreviewDialog,
      emailSentDialog,
      email,
      sharableLink,
      showTwimoPolicies,
      menuCheckIn,
      menuCheckOut,
      formattedCheckIn,
      formattedCheckOut,
      formattedQuietHoursStart,
      formattedQuietHoursEnd,
      menuQuietHoursStart,
      menuQuietHoursEnd,
      isFriendsAndFamilyOnly,
      isPublicRental,
      isLongTermRental,
      isStatusLoading,
    } = storeToRefs(store)

    const {
      images: homeImages,
      isDirty: hasImageChanges,
      isUploading: isUploadingImages,
      addImages,
      deleteImage,
      reorderImages,
    } = imageStore

    // Disable image editing while backend photo processing is in progress
    const processingStatus = computed(() => editHomeData.photo_processing_status)
    const disableImageEditing = computed(() => {
      // prevent editing until status is completed or completed:<count>/<total>
      return processingStatus.value != null && !processingStatus.value.startsWith('completed')
    })

    const debouncedUpdate = debounce(updatePhotos, 500)

    const debounceUpdateSingle = debounce(updateSettingField, 500)

    const uiToggleActive = ref(false)
    const showSeasonalRentalConfirmation = ref(false)

    // Handler for seasonal rental toggle
    const handleSeasonalRentalToggle = () => {
      // If trying to enable seasonal rental but regular rental is off
      if (!editHomeData.isBooking && !editHomeData.offerAsSeasonalLease) {
        // Prevent the toggle from changing and show confirmation dialog
        showSeasonalRentalConfirmation.value = true
        return
      }

      // Otherwise, just update the setting normally
      updateSettingField(api, 'offer_as_seasonal_lease', editHomeData.offerAsSeasonalLease)
    }

    // Handler for confirmation dialog confirm action
    const enableBothRentals = () => {
      // Enable both regular rental and seasonal rental
      editHomeData.isBooking = true
      editHomeData.offerAsSeasonalLease = true

      // Update both settings in the backend
      updateSettingField(api, 'allow_booking', true)
      updateSettingField(api, 'offer_as_seasonal_lease', true)
    }

    watch(
      () => editHomeData.status,
      newStatus => {
        // Always update the UI toggle to match the actual status, regardless of preventApiCalls
        console.log(`[EditHome] Status changed to ${newStatus}, updating toggle`)
        uiToggleActive.value = newStatus === 'active'

        // If status changed to active, check if we need to update the activation status
        // But only if we're not in the initialization phase
        if (newStatus === 'active' && !preventApiCalls.value && !isInitializing.value) {
          console.log('[EditHome] Status changed to active, checking activation status')
          hostActivationStore.checkActivationStatus(api)
        }
      },
      { immediate: true }
    )

    const handleToggleHomeStatus = async () => {
      // If trying to activate the home
      if (!editHomeData.status || editHomeData.status === 'draft') {
        // First check activation status
        await hostActivationStore.checkActivationStatus(api)

        // If not fully activated, open the activation modal instead of toggling
        if (!hostActivationStore.isFullyActivated) {
          // Reset the toggle back to its original state
          uiToggleActive.value = false

          // Open the activation modal
          hostActivationStore.openActivationModal()
          return
        }
      }

      // If deactivating or if fully activated, proceed with normal toggle
      const result = await toggleHomeStatus(api, subscriptionStore)

      if (result && !result.success) {
        uiToggleActive.value = !uiToggleActive.value
      }
    }

    const handleArchiveHome = async () => {
      await archiveHome(api)
    }

    const handleUnarchiveHome = async () => {
      const result = await unarchiveHome(api, subscriptionStore)

      // If activation is required, the modal will already be open from the unarchiveHome function
      if (result && !result.success && !result.activationRequired) {
        toast.error('Failed to unarchive home. Please try again.').goAway(3000)
      }
    }

    onMounted(async () => {
      console.log('[EditHome] Component mounted, starting API calls')

      preventApiCalls.value = true

      await fetchHomeData(api, slug)

      // Explicitly set the toggle state based on the fetched home status
      uiToggleActive.value = editHomeData.status === 'active'
      console.log(`[EditHome] fetchHomeData completed, setting toggle to ${uiToggleActive.value} based on status ${editHomeData.status}`)

      console.log('[EditHome] Calling setDefaultPolicies')
      await setDefaultPolicies(api)
      console.log('[EditHome] setDefaultPolicies completed')

      // Initialize house rules with default values if they haven't been set
      console.log('[EditHome] Initializing house rules')
      await initializeHouseRules(api)
      console.log('[EditHome] House rules initialization completed')

      // Check activation status with a 2-second delay
      console.log('[EditHome] Scheduling activation status check with 2-second delay')
      setTimeout(async () => {
        console.log('[EditHome] Checking activation status after delay')
        await hostActivationStore.checkActivationStatus(api)
        // Ensure the modal doesn't open automatically
        hostActivationStore.setPreventAutoOpen(true)
        hostActivationStore.closeActivationModal()
        console.log('[EditHome] Activation status check completed')
      }, 2000) // 2-second delay - reduced to ensure it completes before HomeEditActivationWrapper's check

      const urlParams = new URLSearchParams(window.location.search)
      if (urlParams.get('ce_id') != null && hasSubscription.value) {
        console.log('[EditHome] ce_id parameter found, calling sharePrivateHome')
        sharePrivateHome(api)
      }

      // Initialize the image store with existing photos
      if (editHomeData.photos && editHomeData.photos.length > 0) {
        store.initializeImageStore(imageStore)
      }

      // Re-enable API calls from watchers after initialization
      preventApiCalls.value = false
      isInitializing.value = false
      console.log('[EditHome] onMounted completed')
    })

    watch(
      () => editHomeData.isBooking,
      newValue => {
        console.log(`[EditHome] isBooking watcher triggered with value: ${newValue}`)

        // Skip API call during initialization or if prevented
        if (preventApiCalls.value) {
          console.log('[EditHome] Skipping setDefaultPolicies call during initialization')
          return
        }

        setDefaultPolicies(api)
      }
    )

    watch(
      () => editHomeData.offerAsSeasonalLease,
      newValue => {
        console.log(`[EditHome] offerAsSeasonalLease watcher triggered with value: ${newValue}`)

        // Skip API call during initialization or if prevented
        if (preventApiCalls.value) {
          console.log('[EditHome] Skipping setDefaultPolicies call during initialization')
          return
        }

        setDefaultPolicies(api)
      }
    )

    watch(
      () => editHomeData.photos,
      () => {
        console.log('[EditHome] photos watcher triggered')

        // Skip update if photo changes are being handled by the image store
        // This prevents duplicate API calls when we're explicitly saving photos
        if (photosRan.value) {
          console.log('[EditHome] photosRan is true, skipping update')
          photosRan.value = false
          return
        }

        // Skip update when we're programmatically updating photos
        if (skipWatch.value) {
          console.log('[EditHome] skipWatch is true, skipping update')
          skipWatch.value = false
          return
        }

        // Skip API call during initialization or if prevented
        if (preventApiCalls.value || isUploadingImages.value) {
          console.log(
            '[EditHome] Skipping debouncedUpdate call: initialization or upload in progress'
          )
          return
        }

        // We should only reach this point for automatic photo updates
        // that aren't handled by the image store's save function
        console.log('[EditHome] Calling debouncedUpdate')
        debouncedUpdate(api)
      },
      { immediate: false, deep: true }
    )

    const imagesReordered = images => {
      // Convert UploadedFile[] to the format expected by editHomeData.photos
      // This assumes UploadedFile has media_id, src/url properties
      editHomeData.photos = images.map(img => ({
        media_id: typeof img.media_id === 'string' ? parseInt(img.media_id, 10) : img.media_id,
        url: img.url || img.src || '',
        name: img.name || img.filename || '',
      }))
    }

    const frontendUrl = ref('https://twimo.com')

    onMounted(() => {
      frontendUrl.value = window.location.origin
    })

    const closeDialog = (dialogRef: any) => {
      dialogRef.value = false
    }

    const closeResetPasswordDialog = () => {
      resetPasswordDialog.value = false
    }

    const closePasswordChangedDialog = () => {
      passwordChangedDialog.value = false
    }

    const handleMenuCheckInInput = (val: any): void => {
      menuCheckIn.value = val
    }
    const handleMenuCheckOutInput = (val: any): void => {
      menuCheckOut.value = val
    }
    const handleMenuQuietHoursStartInput = (val: any): void => {
      menuQuietHoursStart.value = val
    }
    const handleMenuQuietHoursEndInput = (val: any): void => {
      menuQuietHoursEnd.value = val
    }
    const handleQuietHoursStartChange = (val: any): void => {
      if (editHomeData.houseRules.quietHoursTime) {
        editHomeData.houseRules.quietHoursTime.startTime = val
        saveQuietHoursStart(api)
      }
    }
    const handleQuietHoursEndChange = (val: any): void => {
      if (editHomeData.houseRules.quietHoursTime) {
        editHomeData.houseRules.quietHoursTime.endTime = val
        saveQuietHoursEnd(api)
      }
    }

    // Handle photo refresh request from PhotoProcessingStatus component
    const handlePhotoRefresh = async () => {
      try {
        console.log('[EditHome] Handling photo refresh request')

        // Fetch the latest home data to get updated photos
        await store.fetchHomeData(api, editHomeData.slug)

        // Re-initialize the image store with the updated photos
        store.initializeImageStore(imageStore)

        toast.success('Photos refreshed successfully')
      } catch (error) {
        console.error('Error refreshing photos:', error)
        toast.error('Failed to refresh photos')
      }
    }

    // Handle files selected for upload
    const handleFilesSelected = (files: File[]) => {
      try {
        addImages(files)
        toast.success(`Added ${files.length} image(s) for upload`)
      } catch (error) {
        console.error('Error adding images:', error)
        toast.error('Failed to add images')
      }
    }

    // Custom handler for deleting images that automatically saves changes
    const handleDeleteImage = (id: string) => {
      try {
        console.log('Deleting image with ID:', id)

        // Log before state
        console.log(
          'Images BEFORE deletion:',
          homeImages.value.map(img => ({
            id: img.id,
            media_id: img.media_id,
            uploaded: img.uploaded,
          }))
        )

        // Call the imageStore's deleteImage function
        deleteImage(id)

        // Log after state
        console.log(
          'Images AFTER deletion:',
          homeImages.value.map(img => ({
            id: img.id,
            media_id: img.media_id,
            uploaded: img.uploaded,
          }))
        )

        console.log(
          'Remaining media_ids to sync with backend:',
          homeImages.value.filter(img => img.uploaded && img.media_id).map(img => img.media_id)
        )

        // Show toast notification that changes will be saved when "Save Photo Changes" is clicked
        toast.info('Photo removed. Click "Save Photo Changes" to apply changes.')
      } catch (error: any) {
        console.error('Error deleting image:', error)
        toast.error('Failed to delete image: ' + (error?.message || 'Unknown error'))
      }
    }

    // Custom handler for reordering images that automatically saves changes
    const handleReorderImages = (newOrder: any[]) => {
      try {
        // Call the imageStore's reorderImages function
        reorderImages(newOrder)

        // Show toast notification that changes will be saved when "Save Photo Changes" is clicked
        toast.info('Image order updated. Click "Save Photo Changes" to apply changes.')
      } catch (error) {
        console.error('Error reordering images:', error)
        toast.error('Failed to update image order')
      }
    }

    // Handle save photos button clicked
    const savePhotoChanges = async () => {
      if (!hasImageChanges.value) {
        toast.info('No changes to save')
        return
      }

      try {
        // Prevent custom pricing requests during photo save
        preventCustomPricingLoad.value = true

        // Show loading state
        const loadingToast = toast.info('Saving photo changes...', {
          duration: 0, // Don't auto-close
        })

        // Call the store method to update photos - pass true to skip duplicate server updates
        const success = await store.updatePhotosFromImageStore(api, imageStore, null, true)

        // Close the loading toast
        loadingToast.goAway(0)

        if (success) {
          // Provide feedback about the specific changes that were saved
          const changes = []

          if (homeImages.value.some(img => !img.uploaded)) {
            changes.push('new uploads')
          }
          if (hasImageChanges.value) {
            // This covers reordering and deletion
            changes.push('ordering changes')
          }

          const message =
            changes.length > 0
              ? `Photos saved successfully (${changes.join(', ')})`
              : 'Photos saved successfully'

          toast.success(message)

          // Fetch the latest photo processing status after saving photos
          try {
            console.log('Fetching latest photo processing status after save')
            const { data } = await api.get(`user/homes/${editHomeData.id}/photo-processing-status`)

            if (data && data.status) {
              console.log('Updated photo processing status:', data.status)
              // Update the photo processing status in the store
              editHomeData.photo_processing_status = data.status

              // If photos were just uploaded and are now being processed, refresh the home data
              // to get the latest photos with their processing status
              if (data.status === 'queued' || data.status === 'processing' ||
                  (data.status && data.status.startsWith('processing:'))) {
                console.log('Photos are being processed, refreshing home data')
                await store.fetchHomeData(api, editHomeData.slug)

                // Re-initialize the image store with the updated photos
                store.initializeImageStore(imageStore)
              }
            }
          } catch (statusError) {
            console.error('Error fetching photo processing status:', statusError)
            // Don't show an error to the user, as the photos were saved successfully
          }
        } else {
          toast.error('Failed to save photos')
          console.error('Failed to save photos')
        }
      } catch (error) {
        console.error('Error in savePhotoChanges:', error)
        toast.error('Error saving photos: ' + ((error as Error)?.message || 'Unknown error'))
      } finally {
        // Re-enable custom pricing requests after photo save completes
        setTimeout(() => {
          preventCustomPricingLoad.value = false
        }, 1000) // Small delay to ensure no race conditions
      }
    }

    // Define a local capitalizeFirstLetter function in case the imported one isn't working
    const localCapitalizeFirstLetter = (str: string) => {
      if (!str) return ''
      return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase()
    }

    // Computed property to check if the home is from Airbnb
    const isFromAirbnb = computed(() => {
      // Check both the editHomeData and fetchedHomeData for the is_from_airbnb flag
      return !!editHomeData.is_from_airbnb || !!fetchedHomeData.value?.is_from_airbnb
    })

    // Determine which information might be missing
    const missingInfo = computed(() => {
      const missing = []

      // Check for missing title
      if (!editHomeData.title) {
        missing.push('Home title')
      }

      // Check for missing amenities
      if (!editHomeData.amenities || editHomeData.amenities.length === 0) {
        missing.push('Amenities')
      }

      // Check for missing description
      if (!editHomeData.description) {
        missing.push('Home description')
      }

      // Check for missing pricing details
      const nightlyRateValue = typeof editHomeData.nightlyRate === 'string'
        ? parseFloat(editHomeData.nightlyRate)
        : editHomeData.nightlyRate;

      if (nightlyRateValue === null ||
          nightlyRateValue === undefined ||
          isNaN(nightlyRateValue) ||
          nightlyRateValue <= 0) {
        missing.push('Pricing details')
      }

      // Check for missing cleaning fee
      const cleaningFeeValue = typeof editHomeData.cleaningFee === 'string'
        ? parseFloat(editHomeData.cleaningFee)
        : editHomeData.cleaningFee;

      if (cleaningFeeValue === null ||
          cleaningFeeValue === undefined ||
          isNaN(cleaningFeeValue) ||
          cleaningFeeValue <= 0) {
        missing.push('Cleaning fee')
      }

      // Check for missing type of space
      if (!editHomeData.typeOfSpace || editHomeData.typeOfSpace.length === 0) {
        missing.push('Type of space')
      }

      // Check for missing address
      if (!editHomeData.address) {
        missing.push('Home address')
      } else if (addressMissingHouseNumber.value) {
        missing.push('Complete address with house/building number')
      }

      // Check for missing beds, bedrooms, bathrooms, or guests
      if (!editHomeData.beds || editHomeData.beds < 1) {
        missing.push('Number of beds')
      }

      if (!editHomeData.bedrooms || editHomeData.bedrooms < 1) {
        missing.push('Number of bedrooms')
      }

      if (!editHomeData.bathrooms || editHomeData.bathrooms < 1) {
        missing.push('Number of bathrooms')
      }

      if (!editHomeData.guests || editHomeData.guests < 1) {
        missing.push('Number of guests')
      }

      // Check for missing photos
      if (!editHomeData.photos || editHomeData.photos.length === 0) {
        missing.push('Photos')
      }

      // House rules are not considered part of the required home information

      return missing
    })

    // Check if there's any missing information
    const hasMissingInfo = computed(() => {
      return missingInfo.value.length > 0
    })

    // Define the total number of fields that should be completed
    const totalFields = computed(() => {
      // Count all the fields we're checking in the missingInfo computed property
      return 11 // Title, Amenities, Description, Pricing, Cleaning fee, Type of space, Address, Beds, Bedrooms, Bathrooms, Guests, Photos (excluding House rules)
    })

    // Check if the address is missing a house number
    const addressMissingHouseNumber = ref(false)
    const selectedAddressDetails = ref(null)

    // Function to check if address has a house number
    const checkAddressHasHouseNumber = async () => {
      if (!editHomeData.address) return

      try {
        const response = await fetch(
          `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(editHomeData.address)}&limit=1&addressdetails=1`
        )

        if (response.ok) {
          const data = await response.json()
          if (data && data.length > 0 && data[0].address) {
            // Check if the address has a house number
            addressMissingHouseNumber.value = !data[0].address.house_number
          } else {
            addressMissingHouseNumber.value = true
          }
        }
      } catch (error) {
        console.error('Error checking address for house number:', error)
      }
    }

    // Check address when component is mounted or address changes
    watch(() => editHomeData.address, () => {
      if (editHomeData.address) {
        checkAddressHasHouseNumber()
      } else {
        addressMissingHouseNumber.value = false
      }
    }, { immediate: true })

    // Log the Airbnb status for debugging
    watch(isFromAirbnb, (newValue) => {
      console.log('[EditHome] isFromAirbnb changed:', newValue)
    }, { immediate: true })

    // Log missing information for debugging
    watch(missingInfo, (newValue) => {
      console.log('[EditHome] Missing information:', newValue)
      if (newValue.includes('Pricing details')) {
        console.log('[EditHome] Nightly rate value:', editHomeData.nightlyRate, 'Type:', typeof editHomeData.nightlyRate)
      }
      if (newValue.includes('Cleaning fee')) {
        console.log('[EditHome] Cleaning fee value:', editHomeData.cleaningFee, 'Type:', typeof editHomeData.cleaningFee)
      }
    }, { immediate: true })

    return {
      api,
      toast,
      editHomeData,
      isLoading,
      isFromAirbnb,
      missingInfo,
      hasMissingInfo,
      totalFields,
      addressMissingHouseNumber,
      selectedAddressDetails,
      uploadFiles,
      deletePhoto,
      getStatusColor,
      startEditTitle,
      editTitleDialog,
      editTitleDialogData,
      closeEditTitleDialog,
      updateTitle,
      displayedPets,
      editAmountDialog,
      editAmountDialogData,
      startEditAmount,
      closeEditAmountDialog,
      updateAmount,
      petFriendlyTypes,
      petFriendlyNumbers,
      petFriendlyOptions,
      editTypeOfSpaceDialog,
      editTypeOfSpaceDialogData,
      startEditTypeOfSpace,
      closeEditTypeOfSpaceDialog,
      updateTypeOfSpace,
      spaceTypes,
      isSpaceTypeSelected,
      clickOnTypeOfSpace,
      editAddressDialog,
      editAddressDialogData,
      startEditAddress,
      closeEditAddressDialog,
      updateAddress,
      editAmenitiesDialog,
      editAmenitiesDialogData,
      startEditAmenities,
      closeEditAmenitiesDialog,
      clickOnAmenity,
      isAmenitySelected,
      updateAmenities,
      amenities,
      editDescriptionDialog,
      editDescriptionDialogData,
      startEditDescription,
      closeEditDescriptionDialog,
      updateDescription,
      updatePhotos,
      capitalizeFirstLetter: localCapitalizeFirstLetter,
      updateSettingField,
      debounceUpdateSingle,
      fetchedHomeData,
      shareHome,
      debouncedUpdate,
      imagesReordered,
      sharePrivateHome,
      privateHomeAdjustedPriceDialog,
      userWantToAdjustPrice,
      privateHomeAdjustedPriceDialogData,
      clickOnPrivateHomeAdjustedPrice,
      saveAdjustedPrice,
      copyPrivateHomeLink,
      privateHomeCreatePasswordDialog,
      passwordChangedDialog,
      password,
      confirmPassword,
      createPassword,
      resetPassword,
      resetPasswordDialog,
      emailDropdown,
      emailPreviewDialog,
      emailSentDialog,
      email,
      sendEmail,
      frontendUrl,
      sharableLink,
      updateCancellationPolicy,
      updateCheckInOut,
      updateHouseRules,
      showTwimoPolicies,
      menuCheckIn,
      menuCheckOut,
      formattedCheckIn,
      formattedCheckOut,
      saveCheckIn,
      saveCheckOut,
      formattedQuietHoursStart,
      formattedQuietHoursEnd,
      menuQuietHoursStart,
      menuQuietHoursEnd,
      saveQuietHoursStart,
      saveQuietHoursEnd,
      autogenerateSharableLink,
      isFriendsAndFamilyOnly,
      isPublicRental,
      isLongTermRental,
      showFinalizeCalendar: false,
      CANCELLATION_POLICY_TYPE,
      updateNoEvents,
      updateNoSmoking,
      updateQuietHours,
      updateAdditionalRules,
      uploadProgress,
      isUploading,
      maxPhotos,
      archiveHome: handleArchiveHome,
      unarchiveHome: handleUnarchiveHome,
      decreaseBedrooms,
      decreaseBeds,
      decreaseBathrooms,
      increaseBathrooms,
      decreaseGuests,
      uiToggleActive,
      toggleHomeStatus: handleToggleHomeStatus,
      isSafetyAmenity,
      verificationStore: subscriptionStore,
      closeDialog,
      closeResetPasswordDialog,
      closePasswordChangedDialog,
      handleMenuCheckInInput,
      handleMenuCheckOutInput,
      handleMenuQuietHoursStartInput,
      handleMenuQuietHoursEndInput,
      handleQuietHoursStartChange,
      handleQuietHoursEndChange,
      showPaywallModal,
      subscriptionStore,
      homeImages,
      hasImageChanges,
      isUploadingImages,
      handleFilesSelected,
      handleDeleteImage,
      handleReorderImages,
      savePhotoChanges,
      handlePhotoRefresh,
      preventCustomPricingLoad,
      isStatusLoading,
      disableImageEditing,
      archiveHome: handleArchiveHome,
      unarchiveHome: handleUnarchiveHome,
    }
  },

  computed: {
    ...mapGetters({
      user: 'auth/getuser',
    }),
  },

  methods: {
    triggerUploader() {
      ;(this.$refs.imageUploader as any)?.triggerFileInput()
    },
    preventAutoFocus(event) {
      // This method prevents any automatic focus behavior
      // and ensures the clicked input field maintains focus
      event.stopPropagation()
    },

  },
})
</script>

<template>
  <v-row no-gutters>
    <v-col v-if="isLoading" cols="12" class="tw-mt-4">
      <v-progress-circular indeterminate color="primary" size="64" />
    </v-col>

    <v-col v-else cols="12" class="tw-mt-4">
      <v-row class="tw-mt-4 tw-row-align-top">
        <!--				Left side-->
        <v-col cols="12" md="8" :class="{ 'tw-order-2': !isDesktop, 'tw-order-1': isDesktop }" class="tw-h-100">
          <v-row>
            <v-col cols="12" class="tw-text-2xl tw-font-bold tw-pt-0"> Your Home Details </v-col>

            <!-- Home Completion Notification -->
            <v-col cols="12">
              <HomeCompletionNotification
                :is-from-airbnb="isFromAirbnb"
                :missing-info="missingInfo"
                :has-missing-info="hasMissingInfo"
                :total-fields="totalFields"
              />
            </v-col>

            <!-- Photo Gallery -->
            <v-col cols="12">
              <GoodCard2 class="tw-cursor-pointer tw-flex tw-flex-col tw-gap-4">
                <div class="tw-flex tw-justify-between tw-items-center">
                  <div class="tw-text-xl tw-font-semibold tw-text-zinc-600">Photo Gallery</div>
                  <div class="tw-flex tw-items-center tw-text-zinc-500">
                    <SmallDot class="tw-mr-2" :color-class="getStatusColor" />
                    {{ capitalizeFirstLetter(`${editHomeData?.status || ''}`) }}
                    <v-switch
                      v-if="editHomeData && editHomeData.status !== 'archived'"
                      v-model="uiToggleActive"
                      :loading="isStatusLoading"
                      color="primary"
                      class="tw-ml-4"
                      :disabled="isStatusLoading"
                      @change="toggleHomeStatus"
                    />
                  </div>
                </div>

                <!-- Image guidelines -->
                <div
v-if="isPublicRental && homeImages.length < 3"
                     class="tw-p-3 tw-bg-red-50 tw-border tw-border-red-200 tw-rounded-md tw-text-red-600 tw-mb-3">
                  <strong>Required:</strong> At least 3 images are needed for public rental listings. Your
                  home won't be accessible to the public until you add at least 3 images.
                </div>

                <div class="tw-p-3 tw-bg-blue-50 tw-border tw-border-blue-200 tw-rounded-md tw-text-blue-700 tw-mb-3">
                  <div class="tw-font-medium tw-mb-1">Image Guidelines:</div>
                  <ul class="tw-list-disc tw-pl-5 tw-text-sm">
                    <li>Use high-quality, well-lit photos that showcase your property</li>
                    <li>Supported formats: JPG, PNG (max 10MB per image)</li>
                    <li>Recommended dimensions: 800x600 to 2000x1500 pixels</li>
                    <li>Include photos of all main rooms and exterior views</li>
                    <li>The first image will be your cover photo (drag to reorder)</li>
                  </ul>
                </div>

                <ImagesUploader
                  ref="imageUploader"
                  class="tw-cursor-pointer tw-h-[500px]"
                  :images="homeImages"
                  :is-uploading="isUploadingImages || disableImageEditing"
                  :has-unsaved-changes="hasImageChanges"
                  @files-selected="handleFilesSelected"
                  @delete-image="handleDeleteImage"
                  @images-reordered="handleReorderImages"
                  @save-changes="savePhotoChanges"
                  @error="toast.error($event)"
                >
                </ImagesUploader>

                <!-- Photo processing status indicator - component will handle visibility based on status -->
                <PhotoProcessingStatus
                  v-if="editHomeData && editHomeData.id"
                  :home-id="editHomeData.id"
                  @refresh-requested="handlePhotoRefresh"
                />
              </GoodCard2>
            </v-col>

            <!--						Home Title-->
            <v-col cols="12" md="6" @click="startEditTitle">
              <GoodCard2 class="tw-cursor-pointer tw-h-40 tw-flex tw-flex-col tw-gap-4">
                <div
                  class="tw-text-xl tw-font-semibold tw-text-zinc-600 tw-flex tw-flex-row tw-justify-between"
                >
                  <span>Home Title</span>
                  <v-icon color="primary" small dark>mdi-pencil</v-icon>
                </div>
                <div class="tw-text-lg tw-text-zinc-500">
                  {{ editHomeData.title || 'Add a title for your home' }}
                </div>
              </GoodCard2>
            </v-col>

            <!--						Guests and Pets-->
            <v-col cols="12" md="6" @click="startEditAmount">
              <GoodCard2 class="tw-cursor-pointer tw-h-40 tw-flex tw-flex-col tw-gap-4">
                <div
                  class="tw-text-xl tw-font-semibold tw-text-zinc-600 tw-flex tw-flex-row tw-justify-between"
                >
                  <span>Number of Guests</span>
                  <v-icon color="primary" small dark>mdi-pencil</v-icon>
                </div>
                <div class="tw-text-lg tw-text-zinc-500 tw-flex tw-gap-4">
                  <div class="tw-flex tw-items-center tw-gap-2">
                    <span>{{ editHomeData.guests }}</span>
                    <v-icon color="primary" size="24" dark>mdi-account</v-icon>
                  </div>
                  <div class="tw-flex tw-items-center tw-gap-2">
                    <span>{{ displayedPets }}</span>
                    <v-icon color="primary" size="24" dark>mdi-paw</v-icon>
                  </div>
                </div>
              </GoodCard2>
            </v-col>
            <!--						Type of space-->
            <v-col cols="12" md="6" @click="startEditTypeOfSpace">
              <GoodCard2 class="tw-cursor-pointer tw-h-40 tw-flex tw-flex-col tw-gap-4">
                <div
                  class="tw-text-xl tw-font-semibold tw-text-zinc-600 tw-flex tw-flex-row tw-justify-between"
                >
                  <span>Type of Space</span>
                  <v-icon color="primary" small dark>mdi-pencil</v-icon>
                </div>
                <template v-if="editHomeData?.typeOfSpace && editHomeData.typeOfSpace.length > 0">
                  <div class="tw-grid tw-gap-y-8 tw-grid-cols-2">
                    <div
                      v-for="(type, index) in editHomeData.typeOfSpace"
                      :key="index"
                      class="tw-text-lg tw-text-zinc-500"
                    >
                      {{ type.title }}
                    </div>
                  </div>
                </template>

                <template v-else>
                  <div class="tw-text-lg tw-text-zinc-500">Select type of space for your home</div>
                </template>
              </GoodCard2>
            </v-col>
            <!--						Bedrooms-->
            <v-col cols="12" md="6" @click="startEditAmount">
              <GoodCard2 class="tw-cursor-pointer tw-h-40 tw-flex tw-flex-col tw-gap-4">
                <div
                  class="tw-text-xl tw-font-semibold tw-text-zinc-600 tw-flex tw-flex-row tw-justify-between"
                >
                  <span>Number of Bedrooms</span>
                  <v-icon color="primary" small dark>mdi-pencil</v-icon>
                </div>
                <div class="tw-text-lg tw-text-zinc-500 tw-flex tw-gap-4">
                  {{ editHomeData.bedrooms }} Bedrooms
                </div>
              </GoodCard2>
            </v-col>
            <!--						Address-->
            <v-col cols="12" md="6" @click="startEditAddress">
              <GoodCard2 class="tw-cursor-pointer tw-h-40 tw-flex tw-flex-col tw-gap-4">
                <div
                  class="tw-text-xl tw-font-semibold tw-text-zinc-600 tw-flex tw-flex-row tw-justify-between"
                >
                  <span>Home Address</span>
                  <v-icon color="primary" small dark>mdi-pencil</v-icon>
                </div>
                <div class="tw-text-lg tw-text-zinc-500 tw-flex tw-gap-4">
                  {{ editHomeData.address }}
                </div>
              </GoodCard2>
            </v-col>
            <!--		Beds & Bathrooms-->
            <v-col cols="12" md="6" @click="startEditAmount">
              <GoodCard2 class="tw-cursor-pointer tw-h-40 tw-flex tw-flex-col tw-gap-4">
                <div
                  class="tw-text-xl tw-font-semibold tw-text-zinc-600 tw-flex tw-flex-row tw-justify-between"
                >
                  <span>Bed and Bath</span>
                  <v-icon color="primary" small dark>mdi-pencil</v-icon>
                </div>
                <div class="tw-text-lg tw-text-zinc-500 tw-flex tw-gap-4">
                  <div class="tw-flex tw-items-center tw-gap-2">
                    <span>{{ editHomeData.beds }} Beds</span>
                    <v-icon color="primary" size="24" dark>mdi-bed</v-icon>
                  </div>
                  <div class="tw-flex tw-items-center tw-gap-2">
                    <span>{{ Math.floor(editHomeData.bathrooms) }} Bathrooms</span>
                    <v-icon color="primary" size="24" dark>mdi-shower</v-icon>
                  </div>
                </div>
              </GoodCard2>
            </v-col>
            <!--						Amenities-->
            <v-col cols="12" @click="startEditAmenities">
              <GoodCard2 class="tw-cursor-pointer tw-flex tw-flex-col tw-gap-4">
                <div
                  class="tw-text-lg tw-font-semibold tw-text-zinc-600 tw-flex tw-flex-row tw-justify-between"
                >
                  <span>Amenities</span>
                  <v-icon color="primary" small dark>mdi-pencil</v-icon>
                </div>
                <template v-if="editHomeData?.amenities?.length > 0">
                  <div class="tw-grid tw-gap-4 tw-grid-cols-2 md:tw-grid-cols-4">
                    <div
                      v-for="(amenity, index) in editHomeData.amenities"
                      :key="index"
                      class="tw-flex tw-items-center tw-gap-2"
                    >
                      <v-icon color="primary" small>mdi-check</v-icon>
                      <span class="tw-text-zinc-500">{{ amenity }}</span>
                    </div>
                  </div>
                </template>

                <template v-else>
                  <div class="tw-text-lg tw-text-zinc-500">Select amenities for your home</div>
                </template>
              </GoodCard2>
            </v-col>
            <!--						Description-->
            <v-col cols="12" @click="startEditDescription">
              <GoodCard2 class="tw-cursor-pointer tw-flex tw-flex-col tw-gap-4">
                <div
                  class="tw-text-xl tw-font-semibold tw-text-zinc-600 tw-flex tw-flex-row tw-justify-between"
                >
                  <span>Home Description</span>
                  <v-icon color="primary" small dark>mdi-pencil</v-icon>
                </div>
                <div class="tw-text-lg tw-text-zinc-500">
                  {{ editHomeData.description }}
                </div>
              </GoodCard2>
            </v-col>

            <!-- Twimo Policies -->
            <v-col cols="12">
              <GoodCard2 class="tw-pb-0">
                <div class="tw-flex tw-justify-between tw-flex-col tw-gap-4">
                  <div class="tw-text-xl tw-font-semibold tw-text-zinc-600">Twimo Policies</div>
                  <div class="tw-text-lg tw-text-zinc-500">
                    Twimo has default policy's in place, to adjust these settings feel free to edit
                    as needed.
                  </div>
                  <v-btn icon class="tw-mx-auto" @click="showTwimoPolicies = !showTwimoPolicies">
                    <v-icon large
                      >{{ showTwimoPolicies ? 'mdi-chevron-up' : 'mdi-chevron-down' }}
                    </v-icon>
                  </v-btn>
                  <v-divider v-if="showTwimoPolicies" class="tw-mb-4"></v-divider>
                </div>

                <v-expand-transition>
                  <div v-if="showTwimoPolicies" class="tw-pb-4">
                    <v-row>
                      <!-- Cancellation Policy -->
                      <v-col cols="12" md="6" class="tw-flex tw-flex-col tw-gap-4">
                        <div class="tw-text-lg tw-font-semibold tw-text-zinc-600">
                          Cancellation Policy
                        </div>
                        <div class="tw-text-sm tw-text-zinc-500 tw-italic">
                          Guests have 24 hours to cancel reservations in California for a full
                          refund
                        </div>

                        <!-- Friends & Family Policy -->
                        <div class="tw-flex tw-flex-col tw-gap-2">
                          <div class="tw-text-lg tw-font-semibold tw-text-zinc-500">
                            {{ CANCELLATION_POLICY_TYPE.PRIVATE.TITLE }}
                          </div>
                          <div class="tw-flex tw-flex-row tw-gap-4 tw-items-center">
                            <div class="tw-text-zinc-500">
                              If you need to cancel your visit please contact your Host as soon as
                              possible so they can Share their home with another guest, coordinate
                              with the cleaners, or use themselves!
                            </div>
                            <v-switch
                              v-model="editHomeData.cancellationPolicies.friendsAndFamily"
                              color="primary"
                              disabled
                              @change="updateCancellationPolicy('friendsAndFamily', api)"
                            ></v-switch>
                          </div>
                        </div>

                        <!-- Standard Public Rental Policy -->
                        <div class="tw-flex tw-flex-col tw-gap-2">
                          <div class="tw-text-lg tw-font-semibold tw-text-zinc-500">
                            {{ CANCELLATION_POLICY_TYPE.PUBLIC.TITLE }}
                          </div>
                          <div class="tw-flex tw-flex-row tw-gap-4 tw-items-center">
                            <div class="tw-text-zinc-500">
                              {{ CANCELLATION_POLICY_TYPE.PUBLIC.DESCRIPTION }}
                            </div>
                            <v-switch
                              v-model="editHomeData.cancellationPolicies.publicRental"
                              :disabled="!isPublicRental"
                              color="primary"
                              @change="updateCancellationPolicy('publicRental', api)"
                            ></v-switch>
                          </div>
                        </div>

                        <!-- Flexible Public Rental Policy  -->
                        <div class="tw-flex tw-flex-col tw-gap-2">
                          <div class="tw-text-lg tw-font-semibold tw-text-zinc-500">
                            {{ CANCELLATION_POLICY_TYPE.FLEXIBLE.TITLE }}
                          </div>
                          <div class="tw-flex tw-flex-row tw-gap-4 tw-items-center">
                            <div class="tw-text-zinc-500">
                              {{ CANCELLATION_POLICY_TYPE.FLEXIBLE.DESCRIPTION }}
                            </div>
                            <v-switch
                              v-model="editHomeData.cancellationPolicies.flexiblePublicRental"
                              :disabled="!isPublicRental"
                              color="primary"
                              @change="updateCancellationPolicy('flexiblePublicRental', api)"
                            ></v-switch>
                          </div>
                        </div>

                        <!-- Long Term Stay Policy -->
                        <div class="tw-flex tw-flex-col tw-gap-2">
                          <div class="tw-text-lg tw-font-semibold tw-text-zinc-500">
                            {{ CANCELLATION_POLICY_TYPE.LONG_TERM.TITLE }}
                          </div>
                          <div class="tw-flex tw-flex-row tw-gap-4 tw-items-center">
                            <div class="tw-text-zinc-500">
                              {{ CANCELLATION_POLICY_TYPE.LONG_TERM.DESCRIPTION }}
                            </div>
                            <v-switch
                              v-model="editHomeData.cancellationPolicies.longTermStay"
                              :disabled="!isPublicRental"
                              color="primary"
                              @change="updateCancellationPolicy('longTermStay', api)"
                            ></v-switch>
                          </div>
                        </div>

                        <!-- Non-Refundable -->
                        <div class="tw-flex tw-flex-col tw-gap-2">
                          <div class="tw-text-lg tw-font-semibold tw-text-zinc-500">
                            {{ CANCELLATION_POLICY_TYPE.NON_REFUNDABLE.TITLE }}
                          </div>
                          <div class="tw-flex tw-flex-row tw-gap-4 tw-items-center">
                            <div class="tw-text-zinc-500">
                              {{ CANCELLATION_POLICY_TYPE.NON_REFUNDABLE.DESCRIPTION }}
                            </div>
                            <v-switch
                              v-model="editHomeData.cancellationPolicies.nonRefundable"
                              color="primary"
                              @change="updateCancellationPolicy('nonRefundable', api)"
                            ></v-switch>
                          </div>
                        </div>
                      </v-col>

                      <!-- Check-in and Check-Out -->
                      <v-col cols="12" md="6" class="tw-flex tw-flex-col tw-gap-4">
                        <div class="tw-text-lg tw-font-semibold tw-text-zinc-600">
                          Check-in and Check-Out
                        </div>

                        <div class="tw-text-sm tw-text-zinc-500 tw-italic">
                          You can edit both the check-in and check-out windows if needed
                        </div>

                        <div class="tw-flex tw-flex-row tw-gap-4">
                          <div class="tw-flex tw-flex-col tw-gap-2">
                            <div class="tw-text-lg tw-font-semibold tw-text-zinc-500">
                              Check-in:
                            </div>
                            <div class="tw-flex tw-flex-row tw-gap-4">
                              <v-menu
                                ref="menuCheckIn"
                                :close-on-content-click="false"
                                transition="scale-transition"
                                offset-y
                                max-width="290px"
                                min-width="290px"
                                @input="handleMenuCheckInInput"
                              >
                                <template #activator="{ on, attrs }">
                                  <v-text-field
                                    :value="formattedCheckIn"
                                    label="Time"
                                    readonly
                                    v-bind="attrs"
                                    dense
                                    outlined
                                    v-on="on"
                                    @click:append="menuCheckIn = !menuCheckIn"
                                  ></v-text-field>
                                </template>
                                <v-time-picker
                                  v-if="menuCheckIn"
                                  v-model="editHomeData.checkInOut.checkinTime"
                                  full-width
                                  format="24hr"
                                  @change="saveCheckIn(api)"
                                ></v-time-picker>
                              </v-menu>
                            </div>
                          </div>

                          <div class="tw-flex tw-flex-col tw-gap-2">
                            <div class="tw-text-lg tw-font-semibold tw-text-zinc-500">
                              Check-out:
                            </div>
                            <v-menu
                              ref="menuCheckOut"
                              :close-on-content-click="false"
                              transition="scale-transition"
                              offset-y
                              max-width="290px"
                              min-width="290px"
                              @input="handleMenuCheckOutInput"
                            >
                              <template #activator="{ on, attrs }">
                                <v-text-field
                                  :value="formattedCheckOut"
                                  label="Time"
                                  readonly
                                  v-bind="attrs"
                                  dense
                                  outlined
                                  v-on="on"
                                  @click:append="menuCheckOut = !menuCheckOut"
                                ></v-text-field>
                              </template>
                              <v-time-picker
                                v-if="menuCheckOut"
                                v-model="editHomeData.checkInOut.checkoutTime"
                                full-width
                                format="24hr"
                                @change="saveCheckOut(api)"
                              ></v-time-picker>
                            </v-menu>
                          </div>
                        </div>

                        <!-- House Rules -->
                        <div class="tw-flex tw-flex-col tw-gap-4">
                          <div class="tw-text-lg tw-font-semibold tw-text-zinc-600">
                            House Rules
                          </div>

                          <div class="tw-flex tw-flex-row tw-gap-4">
                            <v-checkbox
                              v-model="editHomeData.houseRules.noEvents"
                              label="No Events"
                              hide-details
                              dense
                              @change="updateNoEvents(api)"
                            ></v-checkbox>
                            <v-checkbox
                              v-model="editHomeData.houseRules.noSmoking"
                              label="No Smoking"
                              hide-details
                              dense
                              @change="updateNoSmoking(api)"
                            ></v-checkbox>
                            <v-checkbox
                              v-model="editHomeData.houseRules.quietHours"
                              label="Quiet Hours"
                              hide-details
                              dense
                              @change="updateQuietHours(api)"
                            ></v-checkbox>
                          </div>

                          <div
                            v-if="editHomeData.houseRules.quietHours"
                            class="tw-flex tw-flex-col tw-gap-2"
                          >
                            <div class="tw-text-lg tw-font-semibold tw-text-zinc-500">
                              Quiet Hours:
                            </div>
                            <div class="tw-flex tw-gap-2">
                              <v-menu
                                ref="menuQuietHoursStart"
                                :close-on-content-click="false"
                                transition="scale-transition"
                                offset-y
                                max-width="290px"
                                min-width="290px"
                                @input="handleMenuQuietHoursStartInput"
                              >
                                <template #activator="{ on, attrs }">
                                  <v-text-field
                                    :value="formattedQuietHoursStart"
                                    label="Start Time"
                                    readonly
                                    v-bind="attrs"
                                    dense
                                    outlined
                                    v-on="on"
                                    @click:append="menuQuietHoursStart = !menuQuietHoursStart"
                                  ></v-text-field>
                                </template>
                                <v-time-picker
                                  v-if="
                                    menuQuietHoursStart &&
                                    editHomeData.houseRules &&
                                    editHomeData.houseRules.quietHoursTime
                                  "
                                  :value="editHomeData.houseRules.quietHoursTime.startTime"
                                  full-width
                                  format="24hr"
                                  @change="handleQuietHoursStartChange"
                                />
                              </v-menu>

                              <v-menu
                                ref="menuQuietHoursEnd"
                                :close-on-content-click="false"
                                transition="scale-transition"
                                offset-y
                                max-width="290px"
                                min-width="290px"
                                @input="handleMenuQuietHoursEndInput"
                              >
                                <template #activator="{ on, attrs }">
                                  <v-text-field
                                    :value="formattedQuietHoursEnd"
                                    label="End Time"
                                    readonly
                                    v-bind="attrs"
                                    dense
                                    outlined
                                    v-on="on"
                                    @click:append="menuQuietHoursEnd = !menuQuietHoursEnd"
                                  ></v-text-field>
                                </template>
                                <v-time-picker
                                  v-if="
                                    menuQuietHoursEnd &&
                                    editHomeData.houseRules &&
                                    editHomeData.houseRules.quietHoursTime
                                  "
                                  :value="editHomeData.houseRules.quietHoursTime.endTime"
                                  full-width
                                  format="24hr"
                                  @change="handleQuietHoursEndChange"
                                />
                              </v-menu>
                            </div>
                          </div>

                          <v-textarea
                            v-model="editHomeData.houseRules.additionalRules"
                            label="Anything specific you want to share with your guests?"
                            outlined
                            @input="updateHouseRules(api)"
                          ></v-textarea>
                        </div>
                      </v-col>
                    </v-row>
                  </div>
                </v-expand-transition>
              </GoodCard2>
            </v-col>
          </v-row>
        </v-col>
        <!--				Right side-->
        <v-col
          cols="12"
          md="4"
          :class="{ 'tw-order-1': !isDesktop, 'tw-order-2': isDesktop }"
          class="tw-flex tw-flex-col tw-gap-8 tw-h-100"
        >
          <div class="tw-text-2xl tw-font-bold tw-mb-6 tw-pt-0">Booking Settings and Pricing</div>

          <!--						Booking Settings-->
          <GoodCard2 class="tw-flex tw-flex-col tw-gap-12">
            <!--						Booking-->
            <div class="tw-flex tw-flex-col tw-gap-4">
              <!--						Private-->
              <div class="tw-flex tw-flex-col tw-gap-4 tw-border-b-2 tw-border-zinc-300 tw-pb-8">
                <div class="tw-text-sm tw-text-zinc-500">Friends & Family Private Booking Link</div>

                <div class="tw-text-zinc-500 tw-flex tw-flex-row tw-gap-2 tw-items-center">
                  <span>Friends & Family Private Booking Link</span>
                  <v-icon
                    v-if="editHomeData.sharablePassword"
                    color="primary"
                    small
                    class="tw-cursor-pointer"
                    @click="resetPasswordDialog = true"
                    >mdi-lock
                  </v-icon>
                </div>
                <div
                  v-if="!editHomeData.sharablePassword"
                  class="tw-text-sm tw-text-primary tw-font-semibold tw-cursor-pointer tw-underline"
                  @click="privateHomeCreatePasswordDialog = true"
                >
                  Must Create Booking Link Password Before Sharing
                </div>
                <good-button
                  v-if="editHomeData.sharablePassword"
                  class="tw-w-fit"
                  @click="sharePrivateHome(api)"
                  >Share Link
                </good-button>
              </div>

              <!--						Swap-->
              <div
                class="tw-flex tw-flex-col tw-gap-4 tw-border-b-2 tw-border-zinc-300 tw-pb-8 tw-pt-4"
              >
                <div
                  class="tw-text-xl tw-text-zinc-500 tw-font-semibold tw-flex tw-items-center tw-justify-between"
                >
                  <div>Home Swap</div>
                  <div>
                    <v-switch
                      v-model="editHomeData.isSwap"
                      color="primary"
                      dense
                      class="tw-m-0 tw-p-0"
                      hide-details
                      @change="updateSettingField(api, 'allow_swaps', editHomeData.isSwap)"
                    />
                  </div>
                </div>
              </div>

              <!--						Rental-->
              <div
                class="tw-flex tw-flex-col tw-gap-4 tw-border-b-2 tw-border-zinc-300 tw-pb-8 tw-pt-4"
              >
                <div
                  class="tw-text-xl tw-text-zinc-500 tw-font-semibold tw-flex tw-items-center tw-justify-between"
                >
                  <div>Rent</div>
                  <v-switch
                    v-model="editHomeData.isBooking"
                    color="primary"
                    dense
                    class="tw-m-0 tw-p-0"
                    hide-details
                    @change="updateSettingField(api, 'allow_booking', editHomeData.isBooking)"
                  />
                </div>
                <div class="tw-text-sm tw-text-zinc-500">Public Rental Booking Link</div>
                <good-button class="tw-w-fit" @click="shareHome(api)">Share Link </good-button>
              </div>

              <!--						Seasonal-->
              <div class="tw-flex tw-flex-col tw-gap-4 tw-pb-8 tw-pt-4">
                <div
                  class="tw-text-xl tw-text-zinc-500 tw-font-semibold tw-flex tw-items-center tw-justify-between"
                >
                  <div>Seasonal Rental</div>
                  <v-tooltip bottom color="primary">
                    <template #activator="{ on, attrs }">
                      <div v-bind="attrs" v-on="on">
                        <v-switch
                          v-model="editHomeData.offerAsSeasonalLease"
                          color="primary"
                          dense
                          class="tw-m-0 tw-p-0"
                          hide-details
                          :disabled="!editHomeData.isBooking"
                          @change="handleSeasonalRentalToggle"
                        />
                      </div>
                    </template>
                    <span v-if="!editHomeData.isBooking" class="tw-text-xs">
                      Regular rental must be enabled first to use seasonal rental
                    </span>
                    <span v-else class="tw-text-xs">
                      Toggle seasonal rental option
                    </span>
                  </v-tooltip>
                </div>
              </div>
            </div>
          </GoodCard2>

          <!--						Pricing-->
          <div class="tw-flex tw-flex-col tw-gap-4">
            <div class="tw-text-2xl tw-font-semibold tw-text-zinc-600">Pricing</div>
            <GoodCard2 class="tw-flex tw-flex-col tw-gap-12">
              <div class="tw-flex tw-flex-col tw-gap-4">
                <div class="tw-text-xl tw-text-zinc-500 tw-font-semibold">Nightly Rate*</div>
                <v-text-field
                  v-model="editHomeData.nightlyRate"
                  outlined
                  dense
                  prefix="$"
                  type="number"
                  required
                  class="tw-w-full"
                  :rules="[(v) => !!v || 'Nightly Rate is required']"
                  @input="updateSettingField(api, 'nightly_rate', editHomeData.nightlyRate)"
                />
                <div class="tw-text-xl tw-text-zinc-500 tw-font-semibold">Cleaning Fee</div>
                <v-text-field
                  v-model="editHomeData.cleaningFee"
                  outlined
                  dense
                  prefix="$"
                  hide-details
                  type="number"
                  required
                  class="tw-w-full"
                  @input="updateSettingField(api, 'cleaning_fee', editHomeData.cleaningFee)"
                />
                <div class="tw-text-xl tw-text-zinc-500 tw-font-semibold">Taxes</div>
                <v-text-field
                  v-model="editHomeData.taxRate"
                  outlined
                  dense
                  suffix="%"
                  hide-details
                  type="number"
                  required
                  class="tw-w-full"
                  @input="updateSettingField(api, 'tax_rate', editHomeData.taxRate)"
                />
                <div class="tw-text-xl tw-text-zinc-500 tw-font-semibold">Pet Fee</div>
                <v-text-field
                  v-model="editHomeData.petFee"
                  outlined
                  dense
                  prefix="$"
                  hide-details
                  type="number"
                  required
                  class="tw-w-full"
                  @input="updateSettingField(api, 'pet_fee', editHomeData.petFee)"
                />
                <div class="tw-text-xl tw-text-zinc-500 tw-font-semibold">Minimum Nightly Stay</div>
                <v-text-field
                  v-model="editHomeData.minimumStay"
                  outlined
                  dense
                  hide-details
                  type="number"
                  required
                  class="tw-w-full"
                  @input="updateSettingField(api, 'minimum_stay_rentals', editHomeData.minimumStay)"
                />
              </div>
            </GoodCard2>
          </div>

          <!--						Advanced Pricing-->
          <div class="tw-flex tw-flex-col tw-gap-4">
            <div class="tw-text-2xl tw-font-semibold tw-text-zinc-600">Advanced Pricing</div>
            <GoodCard2 class="tw-flex tw-flex-col tw-gap-12">
              <div class="tw-flex tw-flex-col tw-gap-4">
                <!--						Add form-->
                <div class="tw-flex tw-flex-col tw-gap-4 tw-border-b-2 tw-border-zinc-300 tw-pb-8">
                  <div class="tw-text-sm tw-text-zinc-500">
                    Advanced Pricing allows you to optimize your Nightly Rate. Whether it be
                    increased weekend rates, or customized rates for holidays, select your date
                    range and Add.
                  </div>

                  <AddCustomPricingForm
                    v-if="editHomeData && editHomeData.id"
                    :home-id-prop="editHomeData.id"
                  />
                </div>

                <div class="tw-flex tw-flex-col tw-gap-4">
                  <div class="tw-text-xl tw-text-zinc-500 tw-font-semibold">
                    Advanced Pricing Dates
                  </div>

                  <CustomPricingList
                    v-if="editHomeData && editHomeData.id"
                    :home-id-prop="editHomeData.id"
                    :prevent-load="preventCustomPricingLoad"
                  />
                </div>
              </div>
            </GoodCard2>
          </div>

          <!-- Archive Button -->
          <GoodButton
            v-if="editHomeData && editHomeData.status !== 'archived'"
            class="tw-w-fit tw-ml-auto"
            color="error"
            @click="archiveHome"
          >
            Archive Home
            <v-icon right>mdi-archive</v-icon>
          </GoodButton>

          <GoodButton
            v-if="editHomeData && editHomeData.status === 'archived'"
            class="tw-w-fit tw-ml-auto"
            color="primary"
            @click="unarchiveHome"
          >
            Unarchive Home
            <v-icon right>mdi-archive-off</v-icon>
          </GoodButton>
        </v-col>
      </v-row>
    </v-col>

    <!--		Create Password Dialog-->
    <v-dialog
      key="create-password-dialog"
      v-model="privateHomeCreatePasswordDialog"
      :max-width="isDesktop ? '50%' : '100%'"
      :retain-focus="false"
      persistent
      content-class="password-dialog"
    >
      <GoodCard2 class="tw-flex tw-flex-col tw-gap-4 tw-p-8">
        <div class="tw-text-2xl tw-font-semibold tw-text-zinc-600">
          Shareable Booking Link Password
        </div>

        <div class="tw-text-lg tw-font-medium tw-text-zinc-500">
          To keep your booking private and secure, your booking page will be password protected.
          Your guest will need to enter a password to access the booking page. Please create the
          password you would like them to enter below.
        </div>

        <v-text-field
          key="password-field"
          v-model="password"
          outlined
          dense
          label="Password*"
          hide-details
          type="password"
          required
          :autofocus="false"
        />

        <v-text-field
          key="confirm-password-field"
          v-model="confirmPassword"
          outlined
          dense
          label="Confirm Password*"
          hide-details
          type="password"
          required
          :autofocus="false"
        />

        <div class="tw-flex tw-justify-end tw-mt-8 tw-gap-4">
          <GoodButtonReverted @click="() => (privateHomeCreatePasswordDialog = false)">
            Exit
          </GoodButtonReverted>

          <GoodButton @click="createPassword(api)"> Save</GoodButton>
        </div>
      </GoodCard2>
    </v-dialog>

    <!--		Reset Password Dialog-->
    <v-dialog
      key="reset-password-dialog"
      v-model="resetPasswordDialog"
      :max-width="isDesktop ? '50%' : '100%'"
      :retain-focus="false"
    >
      <GoodCard2 class="tw-flex tw-flex-col tw-gap-4 tw-p-8">
        <div class="tw-text-2xl tw-font-semibold tw-text-zinc-600">Reset Booking Link Password</div>

        <v-text-field
          key="reset-password-field"
          v-model="password"
          outlined
          dense
          label="Password*"
          hide-details
          type="password"
          required
          :autofocus="false"
        />

        <v-text-field
          key="reset-confirm-password-field"
          v-model="confirmPassword"
          outlined
          dense
          label="Confirm Password*"
          hide-details
          type="password"
          required
          :autofocus="false"
        />

        <div class="tw-flex tw-justify-end tw-mt-8 tw-gap-4">
          <GoodButtonReverted @click="closeResetPasswordDialog"> Exit </GoodButtonReverted>

          <GoodButton @click="resetPassword(api)"> Save</GoodButton>
        </div>
      </GoodCard2>
    </v-dialog>

    <!--		Password Changed Dialog-->
    <v-dialog
      key="password-changed-dialog"
      v-model="passwordChangedDialog"
      :max-width="isDesktop ? '50%' : '100%'"
      :retain-focus="false"
    >
      <GoodCard2 class="tw-flex tw-flex-col tw-gap-4 tw-p-8">
        <div class="tw-text-2xl tw-font-semibold tw-text-primary">Thank you!</div>

        <div class="tw-text-lg tw-font-medium tw-text-zinc-500">
          Your password has been created for
          {{ editHomeData.title }} Shareable Booking Link.
        </div>

        <div class="tw-text-lg tw-font-medium tw-text-zinc-500">
          When you share your booking link directly with family & friends make sure to notify them
          of this password. They will need the password to access the booking page.
        </div>

        <div class="tw-text-lg tw-font-medium tw-text-zinc-500">
          If for any reason you want to reset your password, or forget your password, click the lock
          icon next to the Shareable Link to reset.
        </div>

        <div class="tw-flex tw-justify-end tw-mt-8 tw-gap-4">
          <GoodButtonReverted @click="closePasswordChangedDialog"> Exit </GoodButtonReverted>
        </div>
      </GoodCard2>
    </v-dialog>

    <!--		Price adjustment dialog-->
    <v-dialog v-model="privateHomeAdjustedPriceDialog" :max-width="isDesktop ? '60%' : '100%'" :retain-focus="false" :persistent="false">
      <GoodCard2 class="tw-flex tw-flex-col tw-gap-6 tw-p-8">
        <div class="tw-text-2xl tw-font-semibold tw-text-zinc-600">
          Do You to Want to Update Pricing for Friends & Family?
        </div>
        <div class="tw-text-lg tw-font-medium tw-text-zinc-500">
          Before you Share your Link, you have the ability to set an Adjusted Rate.
        </div>
        <div class="tw-text-lg tw-font-medium tw-text-zinc-500">
          If you want to add a discounted rate before sharing your Friends & Family Booking Link
          click Set Adjustment Rates below, after editing your current rates, if needed.
        </div>
        <div class="tw-flex tw-flex-col sm:tw-flex-row tw-gap-2">
          <div class="tw-flex tw-flex-col tw-gap-1 tw-flex-1">
            <div class="tw-font-medium tw-text-zinc-500">Nightly Rate*</div>
            <v-text-field
              v-model="privateHomeAdjustedPriceDialogData.nightlyRate"
              outlined
              dense
              prefix="$"
              type="number"
              required
              class="tw-flex-1"
              :rules="[(v) => !!v || 'Nightly Rate is required']"
              :autofocus="false"
              @focus="preventAutoFocus"
              @click="preventAutoFocus"
            />
          </div>
          <div class="tw-flex tw-flex-col tw-gap-1 tw-flex-1">
            <div class="tw-font-medium tw-text-zinc-500">Cleaning Fee</div>
            <v-text-field
              v-model="privateHomeAdjustedPriceDialogData.cleaningFee"
              outlined
              dense
              prefix="$"
              hide-details
              type="number"
              required
              :autofocus="false"
              @focus="preventAutoFocus"
              @click="preventAutoFocus"
            />
          </div>
          <div class="tw-flex tw-flex-col tw-gap-1 tw-flex-1">
            <div class="tw-font-medium tw-text-zinc-500">Pet Fee</div>
            <v-text-field
              v-model="privateHomeAdjustedPriceDialogData.petFee"
              outlined
              dense
              prefix="$"
              hide-details
              type="number"
              required
              :autofocus="false"
              @focus="preventAutoFocus"
              @click="preventAutoFocus"
            />
          </div>
          <div class="tw-flex tw-flex-col tw-gap-1 tw-flex-1">
            <div class="tw-font-medium tw-text-zinc-500">Taxes</div>
            <v-text-field
              v-model="privateHomeAdjustedPriceDialogData.taxRate"
              outlined
              dense
              suffix="%"
              hide-details
              type="number"
              required
              :autofocus="false"
              @focus="preventAutoFocus"
              @click="preventAutoFocus"
            />
          </div>
          <div class="tw-flex tw-flex-col tw-gap-1 tw-flex-1">
            <div class="tw-font-medium tw-text-zinc-500">Min Stay</div>
            <v-text-field
              v-model="privateHomeAdjustedPriceDialogData.minimumStay"
              outlined
              dense
              hide-details
              type="number"
              required
              :autofocus="false"
              @focus="preventAutoFocus"
              @click="preventAutoFocus"
            />
          </div>
        </div>
        <GoodButton class="tw-w-fit tw-ml-auto" @click="saveAdjustedPrice(api)">
          Set Adjustment Rates
        </GoodButton>

        <v-divider />
        <div class="tw-text-2xl tw-font-semibold tw-text-zinc-600">Share Link</div>
        <div class="tw-text-lg tw-font-medium tw-text-zinc-500">
          Friends & Family Private Booking Link
        </div>
        <div class="tw-text-sm tw-text-zinc-500">
          When Sharing, please remember to provide your guest with your private booking link
          password
        </div>
        <div class="tw-flex tw-flex-col sm:tw-flex-row tw-gap-4">
          <ChillButton class="tw-flex-1 tw-min-w-0" @click="copyPrivateHomeLink(api)">
            <div class="tw-flex tw-items-center tw-justify-center tw-gap-2">
              <v-icon color="#858585" small>mdi-content-copy</v-icon>
              <span class="tw-whitespace-nowrap">Copy Link</span>
            </div>
          </ChillButton>
          <ChillButton class="tw-flex-1 tw-min-w-0" @click="emailDropdown = !emailDropdown">
            <div class="tw-flex tw-items-center tw-justify-center tw-gap-2">
              <v-icon color="#858585" small>mdi-email</v-icon>
              <span class="tw-whitespace-nowrap">Email</span>
            </div>
          </ChillButton>
          <ChillButton class="tw-flex-1 tw-min-w-0">
            <div class="tw-flex tw-items-center tw-justify-center tw-gap-2">
              <v-icon color="#858585" small>mdi-code-tags</v-icon>
              <span class="tw-whitespace-nowrap">Embed</span>
            </div>
          </ChillButton>
        </div>

        <div v-if="emailDropdown" class="tw-flex tw-flex-col tw-gap-4">
          <div
            class="tw-text-2xl tw-font-semibold tw-text-zinc-600 tw-flex tw-flex-row tw-items-center tw-gap-2"
          >
            <span>Email</span>
            <v-icon color="#858585" small>mdi-email</v-icon>
          </div>

          <div class="tw-text-sm tw-text-zinc-500">
            Send your guest their custom Private Booking Link by entering their email below. The
            email is inclusive of the Home password.
          </div>

          <div class="tw-text-sm tw-text-zinc-500">
            Once your guests receive the email they can access the booking page and request to book
            the home. You will get a notification to approve and finalize the booking.
          </div>

          <div class="tw-flex sm:tw-flex-row tw-flex-col tw-gap-4">
            <v-text-field
              v-model="email"
              outlined
              dense
              label="Email*"
              hide-details
              type="email"
              required
              class="sm:tw-w-2/3 tw-w-full"
            />
            <GoodButtonReverted
              class="sm:tw-w-1/6 tw-w-full"
              @click="
                emailPreviewDialog = true
                autogenerateSharableLink(api)
              "
            >
              <v-icon color="primary" small>mdi-email</v-icon>
              <span>Preview Email</span>
            </GoodButtonReverted>
            <GoodButton class="sm:tw-w-1/6 tw-w-full" @click="sendEmail(api)">
              <v-icon color="white" small>mdi-send</v-icon>
              <span>Send</span>
            </GoodButton>
          </div>
        </div>

        <div class="tw-flex tw-justify-end tw-mt-8 tw-gap-4">
          <GoodButtonReverted @click="privateHomeAdjustedPriceDialog = false">
            Exit
          </GoodButtonReverted>
        </div>
      </GoodCard2>
    </v-dialog>

    <!--		Email Preview Dialog-->
    <v-dialog v-model="emailPreviewDialog" :max-width="isDesktop ? '60%' : '100%'">
      <GoodCard2 class="tw-flex tw-flex-col tw-gap-4 tw-p-8">
        <BeastImage
          :src="require('~/assets/logo.webp')"
          alt="twimo-logo"
          class="tw-mr-auto tw-w-48"
        />

        <div class="tw-text-lg tw-font-medium tw-text-primary">Hi There!</div>

        <div class="tw-text-lg tw-font-medium tw-text-primary">
          {{ user.first_name }} has sent you a Family & Friends Booking Link, a private link custom
          for you to book their Home directly on Twimo!
        </div>

        <div class="tw-text-lg tw-font-medium tw-text-zinc-500">
          To access please follow the steps outlined below:
        </div>

        <div class="tw-text-lg tw-font-medium tw-text-zinc-500">
          1. Click the Booking Link provided below
        </div>

        <div class="tw-text-lg tw-font-medium tw-text-zinc-500">
          2. Once you click the Booking Link enter the password you have been provided
        </div>

        <div class="tw-text-lg tw-font-medium tw-text-zinc-500">
          3. Once you enter the password you will be directed to create a Guest Twimo account. This
          is a simple step, to ease your booking process in the future. Simply enter your Name,
          Email, and Create your own password for your account
        </div>

        <div class="tw-text-lg tw-font-medium tw-text-zinc-500">
          4. Once this is done you will be taken directly to the booking page! All Host on Twimo
          must Approve Bookings, so the host will get back to you with Booking Approval. You will
          receive this approval via email and on your Twimo account
        </div>

        <div>
          <div class="tw-text-lg tw-font-semibold tw-text-zinc-500">Booking Link:</div>

          <div class="tw-text-lg tw-font-medium tw-text-zinc-500">
            {{ frontendUrl }}/{{ editHomeData.slug }}?sharable_link={{ sharableLink?.link }}
          </div>
        </div>

        <div>
          <div class="tw-text-lg tw-font-semibold tw-text-zinc-500">Password:</div>

          <div class="tw-text-lg tw-font-medium tw-text-zinc-500">
            {{ editHomeData.sharablePassword }}
          </div>
        </div>

        <div>
          <div class="tw-text-lg tw-font-medium tw-text-zinc-500">Thanks!</div>

          <div class="tw-text-lg tw-font-medium tw-text-zinc-500">Twimo</div>
        </div>

        <div class="tw-flex tw-justify-end tw-mt-8 tw-gap-4">
          <GoodButtonReverted @click="emailPreviewDialog = false"> Exit </GoodButtonReverted>
        </div>
      </GoodCard2>
    </v-dialog>

    <!--		Edit modals-->
    <!--		Title-->
    <v-dialog v-model="editTitleDialog" :max-width="isDesktop ? '30%' : '100%'">
      <GoodCard2>
        <div class="tw-text-2xl tw-font-semibold tw-text-zinc-600 tw-mb-4">Home Title</div>
        <div class="tw-text-xl tw-text-zinc-500 tw-mb-4">
          The title of your home will be what you and your guests see on your Twimo custom URL
        </div>
        <v-text-field
          v-model="editTitleDialogData"
          outlined
          dense
          label="Title*"
          type="text"
          required
          :rules="[(v) => !!v || 'Title is required']"
        />
        <div class="tw-flex tw-justify-end tw-mt-8 tw-gap-4">
          <GoodButtonReverted @click="closeEditTitleDialog"> Exit </GoodButtonReverted>

          <GoodButton @click="updateTitle(api)"> Save</GoodButton>
        </div>
      </GoodCard2>
    </v-dialog>

    <!--		Amount (Guests, Beds, Bathrooms, ...)-->
    <v-dialog v-model="editAmountDialog" :max-width="isDesktop ? '30%' : '100%'">
      <GoodCard2>
        <div class="tw-text-2xl tw-font-semibold tw-text-zinc-600 tw-mb-4">
          Guests, Bedroom and Bathroom Count
        </div>
        <div class="tw-text-xl tw-text-zinc-500 tw-mb-4">
          Input your guest count, bedrooms, beds, and bathrooms below. Pets if applicable.
        </div>
        <v-col cols="12" class="tw-flex tw-flex-col tw-gap-3">
          <div class="tw-flex tw-flex-row tw-gap-2">
            <GoodButton class="tw-w-1/2 tw-text-lg tw-py-[29px]"> Bedrooms </GoodButton>
            <GoodButtonIncreaseNumber
              class="tw-w-1/2 tw-text-lg tw-py-[28px]"
              :value="editAmountDialogData.bedrooms"
              @decrease="decreaseBedrooms"
              @increase="editAmountDialogData.bedrooms++"
            ></GoodButtonIncreaseNumber>
          </div>

          <div class="tw-flex tw-flex-row tw-gap-2">
            <GoodButton class="tw-w-1/2 tw-text-lg tw-py-[29px]"> Beds </GoodButton>
            <GoodButtonIncreaseNumber
              class="tw-w-1/2 tw-text-lg tw-py-[28px]"
              :value="editAmountDialogData.beds"
              @decrease="decreaseBeds"
              @increase="editAmountDialogData.beds++"
            ></GoodButtonIncreaseNumber>
          </div>

          <div class="tw-flex tw-flex-row tw-gap-2">
            <GoodButton class="tw-w-1/2 tw-text-lg tw-py-[29px]"> Bathrooms </GoodButton>
            <GoodButtonIncreaseNumber
              class="tw-w-1/2 tw-text-lg tw-py-[28px]"
              :value="Math.floor(editAmountDialogData.bathrooms)"
              @decrease="decreaseBathrooms"
              @increase="increaseBathrooms"
            ></GoodButtonIncreaseNumber>
          </div>

          <div class="tw-flex tw-flex-row tw-gap-2">
            <GoodButton class="tw-w-1/2 tw-text-lg tw-py-[29px]"> Guests </GoodButton>
            <GoodButtonIncreaseNumber
              class="tw-w-1/2 tw-text-lg tw-py-[28px]"
              :value="editAmountDialogData.guests"
              @decrease="editAmountDialogData.guests > 1 ? editAmountDialogData.guests-- : editAmountDialogData.guests"
              @increase="editAmountDialogData.guests++"
            ></GoodButtonIncreaseNumber>
          </div>
        </v-col>

        <v-col cols="12" class="tw-flex tw-flex-row tw-gap-2 tw-pr-0 tw-pt-0">
          <GoodButton class="tw-w-1/2 tw-text-lg tw-py-[29px]"> Pet Friendly? </GoodButton>
          <div class="tw-w-1/2 tw-flex tw-flex-row tw-gap-2">
            <v-select
              v-model="editAmountDialogData.pets.enabled"
              :items="petFriendlyOptions"
              label="Y / N"
              outlined
              hide-details
              class="tw-w-1/2 tw-rounded-xl tw-border tw-h-fit"
            ></v-select>
          </div>
        </v-col>

        <div class="tw-flex tw-justify-end tw-mt-8 tw-gap-4">
          <GoodButtonReverted @click="closeEditAmountDialog"> Exit </GoodButtonReverted>

          <GoodButton @click="updateAmount(api)"> Save</GoodButton>
        </div>
      </GoodCard2>
    </v-dialog>
    <!--		Type of space-->
    <v-dialog v-model="editTypeOfSpaceDialog" :max-width="isDesktop ? '30%' : '100%'">
      <GoodCard2>
        <div class="tw-text-2xl tw-font-semibold tw-text-zinc-600 tw-mb-4">Type of Space</div>
        <div class="tw-text-xl tw-text-zinc-500 tw-mb-4">What type of space is your home?</div>
        <v-col
          v-for="(spaceType, index) in spaceTypes"
          :key="index"
          cols="12"
          @click="clickOnTypeOfSpace(spaceType)"
        >
          <SpaceType :space-type="spaceType" :is-activated="isSpaceTypeSelected(spaceType)" />
        </v-col>
        <div class="tw-flex tw-justify-end tw-mt-8 tw-gap-4">
          <GoodButtonReverted @click="closeEditTypeOfSpaceDialog"> Exit </GoodButtonReverted>

          <GoodButton @click="updateTypeOfSpace(api)"> Save</GoodButton>
        </div>
      </GoodCard2>
    </v-dialog>
    <!--		Address-->
    <v-dialog v-model="editAddressDialog" :max-width="isDesktop ? '500px' : '100%'">
      <GoodCard2>
        <div class="tw-text-2xl tw-font-semibold tw-text-zinc-600 tw-mb-4">Home Address</div>
        <div class="tw-text-xl tw-text-zinc-500 tw-mb-4">Enter Home Address Below</div>

        <!-- Address input section with more space -->
        <div class="tw-px-4 tw-mb-16">
          <OpenStreetAutocomplete
            :address="editAddressDialogData || ''"
            placeholder="Enter your home address"
            label="Address"
            helper-text="For best results, enter your complete address including house/building number, street, city, and country"
            input-border
            @address-selection="
              (address, details) => {
                editAddressDialogData = address
                // Store the selected address details if needed
                if (details) {
                  selectedAddressDetails.value = details
                  // Check if the address has a house number
                  const hasHouseNumber = details.address && details.address.house_number
                  addressMissingHouseNumber.value = !hasHouseNumber
                }
              }
            "
          />

          <!-- Selected address preview -->
          <div
            v-if="editAddressDialogData"
            class="tw-mt-4 tw-p-3 tw-bg-gray-50 tw-rounded-lg tw-border tw-border-gray-200"
          >
            <div class="tw-flex tw-items-start tw-gap-2">
              <v-icon class="tw-text-primary tw-mt-0.5 tw-shrink-0">mdi-map-marker</v-icon>
              <div>
                <div class="tw-font-medium">Selected Address:</div>
                <div class="tw-text-gray-700">{{ editAddressDialogData }}</div>
              </div>
            </div>
          </div>

          <!-- Address Warning -->
          <div
            v-if="addressMissingHouseNumber && editAddressDialogData"
            class="tw-mt-4 tw-p-3 tw-bg-amber-50 tw-border-l-4 tw-border-amber-500 tw-rounded-lg"
          >
            <div class="tw-flex tw-items-start tw-gap-2">
              <v-icon color="warning" class="tw-mt-0.5">mdi-alert-circle</v-icon>
              <div>
                <div class="tw-font-medium tw-text-amber-800">House/building number missing</div>
                <div class="tw-text-sm tw-text-amber-700">
                  Your address appears to be missing a house/building number. Please make sure to include the complete address with house/building number to activate your home.
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Fixed footer with action buttons -->
        <div
          class="tw-absolute tw-bottom-0 tw-left-0 tw-right-0 tw-p-4 tw-bg-white tw-border-t tw-border-gray-200 tw-flex tw-justify-end tw-gap-4"
        >
          <GoodButtonReverted @click="closeEditAddressDialog"> Cancel </GoodButtonReverted>
          <GoodButton :disabled="!editAddressDialogData" @click="updateAddress(api)">
            Save Address
          </GoodButton>
        </div>
      </GoodCard2>
    </v-dialog>
    <!--		Amenities-->
    <v-dialog v-model="editAmenitiesDialog" :max-width="isDesktop ? '60%' : '100%'">
      <GoodCard2>
        <div class="tw-text-2xl tw-font-semibold tw-text-zinc-600 tw-mb-4">Amenities</div>
        <div class="tw-text-xl tw-text-zinc-500 tw-mb-4">Check all applicable amenities below</div>
        <v-row v-for="(amenityGroup, groupName) in amenities" :key="groupName">
          <v-col :key="groupName" cols="12">
            <div class="tw-text-xl tw-font-semibold tw-text-zinc-600 tw-capitalize tw-mb-4">
              {{ groupName.replace(/([A-Z])/g, ' $1').toLowerCase() }}
            </div>
          </v-col>
          <v-col
            v-for="(amenity, index) in amenityGroup"
            :key="groupName + '-' + index"
            cols="6"
            md="4"
            @click="clickOnAmenity(amenity)"
          >
            <Amenity
              :amenity="amenity"
              :is-activated="isAmenitySelected(amenity)"
              :disabled="isSafetyAmenity(amenity)"
            />
          </v-col>
        </v-row>
        <div class="tw-flex tw-justify-end tw-mt-8 tw-gap-4">
          <GoodButtonReverted @click="closeEditAmenitiesDialog"> Exit </GoodButtonReverted>

          <GoodButton @click="updateAmenities(api)"> Save</GoodButton>
        </div>
      </GoodCard2>
    </v-dialog>
    <!--		Description-->
    <v-dialog v-model="editDescriptionDialog" :max-width="isDesktop ? '30%' : '100%'">
      <GoodCard2>
        <div class="tw-text-2xl tw-font-semibold tw-text-zinc-600 tw-mb-4">Home Description</div>
        <div class="tw-text-xl tw-text-zinc-500 tw-mb-4">
          In a brief sentence or two describe what makes your home your home and your neighborhood
          unique!
        </div>
        <v-col cols="12">
          <v-textarea
            v-model="editDescriptionDialogData"
            label="Description*"
            outlined
            placeholder="Type Here"
            dense
            class="tw-mt-4"
            rows="8"
            counter="500"
            maxlength="500"
          />
        </v-col>
        <div class="tw-flex tw-justify-end tw-mt-8 tw-gap-4">
          <GoodButtonReverted @click="closeEditDescriptionDialog"> Exit </GoodButtonReverted>

          <GoodButton @click="updateDescription(api)"> Save</GoodButton>
        </div>
      </GoodCard2>
    </v-dialog>

    <!--		Paywall Modal-->
    <PaywallModal :model-value="showPaywallModal" @update:model-value="showPaywallModal = $event" />

    <!-- Host Activation Modal -->
    <HostActivationModal />

    <!-- Seasonal Rental Confirmation Dialog -->
    <ConfirmationDialog
      v-model="showSeasonalRentalConfirmation"
      title="Enable Regular Rental"
      message="Seasonal rental requires regular rental to be enabled first. Would you like to enable both regular rental and seasonal rental?"
      confirm-text="Enable Both"
      cancel-text="Cancel"
      @confirm="enableBothRentals"
    />
  </v-row>
</template>

<style lang="scss" scoped>
.tw-flex-col {
  display: flex;
  flex-direction: column;
}

// Ensure equal height for cards in the same row
.tw-h-40 {
  height: 10rem;
  display: flex;
  flex-direction: column;
}

// Ensure proper height for the right column
.tw-h-100 {
  height: 100%;
}

// Ensure consistent spacing between cards
.v-card {
  margin-bottom: 1.5rem;
}

// Ensure proper vertical alignment for column headers
@media (min-width: 960px) {
  .tw-order-1, .tw-order-2 {
    display: flex;
    flex-direction: column;
    align-self: flex-start;
  }

  // Fix for the main row alignment
  .tw-mt-4 > .tw-mt-4 {
    display: flex;
    align-items: flex-start;
  }
}

// Ensure top alignment for the main row
.tw-row-align-top {
  align-items: flex-start !important;
}

// Responsive adjustments for mobile
@media (max-width: 959px) {
  .tw-flex-col.tw-gap-8 {
    gap: 1.5rem;
  }
}
</style>
