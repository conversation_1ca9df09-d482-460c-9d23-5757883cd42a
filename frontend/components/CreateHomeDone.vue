<script lang="ts">
// @ts-nocheck

import { defineComponent, useRouter , computed } from '@nuxtjs/composition-api'
import { storeToRefs } from 'pinia'

import GoodButton from '~/components/GoodButton.vue'
import { useCreateHomeProgressStore } from '~/composables/useCreateHomeStore'
import { additionalInfos } from '~/constants'
import HomeEditActivationWrapper from '~/components/HomeEditActivationWrapper.vue'
import PhotoProcessingStatus from '~/components/PhotoProcessingStatus.vue'
import { useCreateHomeImageStore } from '~/composables/useCreateHomeImageStore'

export default defineComponent({
  name: 'CreateHomeDone',

  components: {
    GoodButton,
    HomeEditActivationWrapper,
    PhotoProcessingStatus,
  },

  setup() {
    const store = useCreateHomeProgressStore()
    const router = useRouter()
    const imageStore = useCreateHomeImageStore()

    const isFromAirbnb = computed(() => imageStore.isFromAirbnb)
    const processingStatus = computed(() => imageStore.processingStatus)
    const createdHomeId = computed(() => store.createdHomeId)

    const {
      createHomeData,
      clickOnOfferAsSeasonalLease,
      isAdditionalInfoSelected,
      clickOnAdditionalInfo,
    } = store

    const { createdHomeSlug } = storeToRefs(store)

    const setupYourCalendarRoute = '/my-calendar'

    const goToHomeEdit = () => {
      if (createdHomeSlug.value) {
        router.push(`/${createdHomeSlug.value}/edit`)
      }
    }

    return {
      createHomeData,
      additionalInfos,
      clickOnOfferAsSeasonalLease,
      isAdditionalInfoSelected,
      clickOnAdditionalInfo,
      setupYourCalendarRoute,
      createdHomeSlug,
      goToHomeEdit,
      isFromAirbnb,
      processingStatus,
      createdHomeId,
    }
  },
})
</script>

<template>
  <v-row>
      <v-col cols="12" md="8" lg="6" class="tw-flex tw-flex-col tw-gap-4 tw-px-4 sm:tw-px-6">
        <div class="tw-text-2xl tw-font-semibold tw-text-zinc-600">
          <div class="tw-flex tw-items-center">Your Home is complete!</div>
        </div>

        <div class="tw-text-lg tw-font-medium tw-text-zinc-500">
          Now it's time to setup your calendar! Twimo syncs Bookings, Swaps and MyCrew Jobs straight
          into your Calendar.
        </div>

        <div class="tw-text-lg tw-font-medium tw-text-zinc-500">
          We recommend inputting direct Owner Use Dates, Blocked Dates or syncing your iCal to get
          started
        </div>

        <!-- Photo processing status for all photo uploads - component will handle visibility based on status -->
        <PhotoProcessingStatus
          v-if="createdHomeId"
          :home-id="createdHomeId"
        />

        <div
v-if="processingStatus && processingStatus !== 'completed' && !(typeof processingStatus === 'string' && processingStatus.startsWith('completed:'))"
             class="tw-text-md tw-font-medium tw-text-zinc-500 tw-mb-4">
          Your photos are being processed in the background. You can continue using the site.
        </div>

        <div class="tw-flex tw-flex-col sm:tw-flex-row tw-gap-4">
          <GoodButton class="tw-text-lg" @click="$router.push(setupYourCalendarRoute)">
            Setup Your Calendar
          </GoodButton>

          <GoodButton
            v-if="createdHomeSlug"
            class="tw-text-lg"
            color="secondary"
            @click="goToHomeEdit"
          >
            Edit Your Home
          </GoodButton>
        </div>
      </v-col>

      <v-col cols="12" class="tw-px-4 sm:tw-px-6">
        <HomeEditActivationWrapper :prop-slug="createdHomeSlug" />
      </v-col>
    </v-row>
</template>

<style scoped>
/* Mobile-specific styles */
@media (max-width: 600px) {
  /* Ensure text doesn't overflow on small screens */
  :deep(.tw-text-2xl) {
    font-size: 1.375rem;
    line-height: 1.75rem;
  }

  :deep(.tw-text-lg) {
    font-size: 1rem;
    line-height: 1.5rem;
  }

  /* Ensure buttons stack properly on mobile */
  :deep(.v-btn) {
    width: 100%;
    margin-bottom: 0.5rem;
  }
}

/* Ensure proper spacing for the activation wrapper */
:deep(.host-activation-tracker) {
  margin-top: 1rem;
  margin-bottom: 1rem;
  width: 100%;
  overflow-x: hidden;
}
</style>
