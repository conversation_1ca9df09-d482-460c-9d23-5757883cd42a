<script lang="ts">
// @ts-nocheck

import { defineComponent, PropType } from '@nuxtjs/composition-api'

import { HomeTo } from '~/types'

export default defineComponent({
  name: 'HomeSecondBasicInfo',

  props: {
    data: {
      type: Object as PropType<HomeTo>,
      required: true,
    },
    card: {
      type: Boolean,
      default: true,
    },
  },

  computed: {
    hasAnyBasicInfo(): boolean {
      return (
        this.data.offer_as_seasonal_lease ||
        this.data.minimum_stay_rentals > 1
      )
    },
  },
})
</script>

<template>
  <div
    v-if="hasAnyBasicInfo"
    :class="
      card
        ? 'tw-flex tw-flex-row tw-items-center tw-px-[3px] tw-w-fit'
        : 'tw-flex tw-flex-col tw-w-fit'
    "
  >
    <template v-if="card">
      <div v-if="data.minimum_stay_rentals > 1" class="tw-mx-2">
        <v-tooltip bottom color="primary">
          <template #activator="{ on, attrs }">
            <div v-bind="attrs" v-on="on">
              <div class="tw-flex tw-flex-row">
                <v-icon small color="black">mdi-calendar-clock</v-icon>
                <div class="tw-text-black tw-text-sm">
                  {{ data.minimum_stay_rentals }}
                </div>
              </div>
            </div>
          </template>
          <span class="tw-text-xs">Minimum stay</span>
        </v-tooltip>
      </div>
    </template>

    <template v-else>
      <div v-if="data.offer_as_seasonal_lease" class="tw-flex tw-items-center tw-py-1">
        <v-icon small color="black">mdi-calendar-remove</v-icon>
        <div class="tw-ml-2 tw-my-1 tw-text-black tw-text-base">Seasonal lease</div>
      </div>

      <div
        v-if="
          data.offer_as_seasonal_lease &&
          data.seasonal_lease_description
        "
        class="tw-flex tw-items-center tw-py-1"
      >
        <div class="tw-ml-6 tw-my-1 tw-text-gray-500 tw-text-sm">
          {{ data.seasonal_lease_description }}
        </div>
      </div>

      <div v-if="data.minimum_stay_rentals > 1" class="tw-my-1 tw-flex tw-items-center tw-py-1">
        <v-icon small color="black">mdi-calendar-clock</v-icon>
        <div class="tw-ml-2 tw-text-black tw-text-base">
          Minimum {{ data.minimum_stay_rentals }} days stay
        </div>
      </div>
    </template>
  </div>
</template>
