<template>
  <div :class="id">
    <h3 v-if="heading" class="heading">
      {{ heading }}
    </h3>
    <div v-if="content" class="content" v-html="content"></div>
    <div v-if="buttonName" class="button_container">
      <a :href="buttonLink" class="button" :class="bgColor" :target="target">{{ buttonName }}</a>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    src: String,
    alt: String,
    heading: String,
    content: String,
    buttonName: String,
    buttonLink: String,
    target: {
      type: String,
      default: '_self',
    },
    bgColor: {
      default: 'bg-white',
    },
  },
}
</script>
<style scoped>
.heading {
  color: #7e7e7e;
  font-weight: 800;
  font-size: 30px;
  line-height: 30px;
  margin-bottom: 20px;
}
.content,
.content p {
  color: #7b7b7b;
  line-height: 20px;
  font-size: 16px;
  margin-bottom: 20px;
}
.button_container {
  margin-top: 50px;
}
.button {
  border-radius: 30px;
  /* font-size:18x; */
  line-height: 22px;
  padding: 12px 35px;
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
  display: inline-block;
  font-weight: 800;
  transition: all 300ms ease;
}
.bg-purple {
  background-color: #7c0cb1;
  color: white;
}
.bg-purple:hover {
  color: #7c0cb1;
  background-color: #fff;
}
.bg-white {
  color: #7c0cb1;
  background-color: white;
}
.bg-white:hover {
  background-color: #7c0cb1;
  color: #fff;
}
@media screen and (max-width: 767px) {
  .image__content__container--image img {
    max-width: 400px;
    display: block;
    margin: auto;
  }
  .image__content--container {
    flex-direction: column;
  }
  .image__content__container--image,
  .image__content__container--content {
    width: 100%;
  }
  .image__content__container--content {
    padding: 0;
  }
}
</style>
