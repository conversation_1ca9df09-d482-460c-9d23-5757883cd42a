<script setup>
import SwapHomes from '~/assets/swap-homes.jpg'
defineProps({
  title: {
    type: String,
    required: true,
  },
  description: {
    type: String,
    required: true,
  },
  image: {
    default: SwapHomes,
  },
  animate_delay: {
    type: String,
    default: 1,
  },
})
</script>

<template>
  <div
    class="tw-min-h-[300px] md:tw-min-h-[450px] tw-flex tw-items-start tw-flex-col tw-justify-end tw-bg-no-repeat tw-relative tw-group animate new_home_card"
    :class="`animate-${animate_delay}`"
  >
    <img :src="image" :alt="title">

    <h3 class="tw-text-xl md:tw-text-2xl tw-mt-5 md:tw-text-xl tw-mb-2 md:tw-mb-3 tw-relative tw-text-[#2C005B] tw-font-medium tw-font-[Poppins]" v-html="title">
     
    </h3>
    <p class="tw-text-[0.9rem] tw-relative tw-mb-0 tw-pr-[1.2rem] tw-text-[#2C005B] tw-font-light tw-font-[Poppins]">
      {{ description }}
    </p>
  </div>
</template>
<style scoped>
.animate-1 {
  transition-delay: 0.1s !important;
}

.animate-2 {
  transition-delay: 0.3s !important;
}

.animate-3 {
  transition-delay: 0.5s !important;
}
.new_home_card {
  background-size: 140%;
  background-position: center;
  transition: all 0.3s !important;
}
.new_home_card:hover {
  background-size: 150% !important;
}

.new_home_card img {
  width: 100%;
  min-height: 415px;
  object-fit: cover;
  z-index: -1;

  box-shadow: 0px 2px 3px rgba(0, 0, 0, 0.25), 0px 2px 4px rgba(0, 0, 0, 0.25);
  border-radius: 10px;
}

@media screen and (max-width: 767px) {
  .new_home_card {
    background-size: 100%;
    margin-bottom: 35px;
  }
  .new_home_card:hover {
    background-size: 110% !important;
  }

  .new_home_card img{
    min-height: 190px;
  }
}
</style>
