<script>
export default {
  data() {
    return {
      openMenu: false,
      host_tool_submenus: [
        { name: 'Share and Swap', link: '/share-and-swap' },
        { name: 'Rent', link: '/rent' },
        { name: 'Track', link: '/track' },
        { name: 'Service', link: '/service' },
      ],
      why_twimo_submenus: [
        { name: 'About Us', link: '/about' },
        { name: 'FAQs', link: '/faqs' },
      ],
    }
  },
  methods: {
    hamburger() {
      this.openMenu = !this.openMenu
    },
  },
}
</script>
<template>
  <header>
    <div class="header__inner">
      <div class="header__logo--section">
        <NuxtLink to="/">
          <img src="~/assets/newhome/twimo-header-logo.png" alt="Twimo Logo" />
        </NuxtLink>
      </div>
      <div class="header__menus--section">
        <nav class="main-menu-container" :class="openMenu ? 'active' : ''">
          <ul class="main-menu" @click="openMenu = false">
            <li class="menu-item-haschildren">
              <NuxtLink to="/#features"> Features </NuxtLink>
            </li>
            <li class="menu-item-haschildren">
              <NuxtLink to="/#why_twimo"> Why Twimo </NuxtLink>
            </li>
            <li class="menu-item-haschildren">
              <NuxtLink to="/#testimonials"> Testimonials </NuxtLink>
            </li>
            <li class="menu-item-haschildren">
              <NuxtLink to="/#pricing"> Pricing </NuxtLink>
            </li>
            <li class="menu-item-haschildren">
              <NuxtLink to="/explore"> Explore Homes </NuxtLink>
            </li>
          </ul>

          <div class="header__button">
            <NuxtLink
              class="btn tw-bg-white tw-rounded-full tw-font-bold tw-px-5 tw-py-2 tw-mr-2 hover:tw-bg-[#7c0cb1] tw-transition-all hover:tw-text-white header__btn"
              to="/login"
            >
              Log In
            </NuxtLink>

            <NuxtLink
              class="btn tw-rounded-full tw-px-5 tw-font-bold tw-py-2 hover:tw-bg-[#7c0cb1] tw-transition-all hover:tw-text-white header__join-btn"
              to="/signup"
            >
              Sign Up Now
            </NuxtLink>
          </div>
        </nav>
        <div class="hamburger__menu" :class="openMenu ? 'active' : ''" @click="hamburger">
          <span></span>
          <span></span>
          <span></span>
        </div>
      </div>
    </div>
  </header>
</template>
<style scoped>
header {
  padding: 0;
  margin: 0;
  position: fixed;
  width: 100%;
  left: 0;
  top: 0;
  right: 0;
  z-index: 10;
  background: #3a314673;
}
.header__inner {
  max-width: 96%;
  margin: auto;
  padding: 20px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
}

.header__logo--section {
  width: 25%;
}
.header__logo--section img {
  width: 190px;
  object-fit: contain;
}
.header__menus--section {
  width: 75%;
  display: flex;
  justify-content: end;
  align-items: center;
}
.main-menu,
.sub-menus {
  list-style: none;
  padding: 0;
  margin: 0;
}
.header__inner a {
  font-weight: 500;
  font-family: 'Figtree', sans-serif;
}
.sub-menus li {
  margin: 10px 0;
}
.main-menu {
  display: flex;
}
.main-menu a {
  padding: 10px 15px;
  margin-left: 10px;
  color: white;
  font-size: 15px;
  cursor: pointer;
}
.menu-item-haschildren {
  position: relative;
}
.sub-menus {
  background-color: #6c6c6c;
}
.menu-item-haschildren .sub-menus {
  position: absolute;
  top: 30px;
  text-wrap: nowrap;
  display: none;
  min-width: 200px;
  text-align: end;
  padding: 5px 10px;
  right: 0;
}
.menu-item-haschildren:hover .sub-menus {
  display: block;
}
.hamburger__menu {
  display: none;
  margin-right: 20px;
  cursor: pointer;
}
.hamburger__menu span {
  width: 28px;
  height: 2px;
  background-color: #72359c;
  margin-bottom: 7px;
  display: block;
  transition: all 200ms;
}
.hamburger__menu.active span:first-of-type {
  transform: rotate(45deg);
  transform-origin: 8% -1px;
}
.hamburger__menu.active span:last-of-type {
  transform: rotate(-45deg);
  transform-origin: 10px 7px;
}
.hamburger__menu.active span:nth-child(2) {
  display: none;
}
header .router-link-active {
  text-decoration: underline;
}
.header__btn {
  font-size: 14px !important;
}

.main-menu-container {
  display: flex;
  gap: 30px;
}

.header__join-btn {
  background: conic-gradient(
    from 90deg at 50% 50%,
    rgba(56, 11, 115, 0.9) -124.46deg,
    #7c0cb1 73.63deg,
    rgba(56, 11, 115, 0.9) 235.54deg,
    #7c0cb1 433.63deg
  );
  color: #fff;
  font-size: 14px;
}

.header__btn:hover {
  background: conic-gradient(
    from 90deg at 50% 50%,
    rgba(56, 11, 115, 0.9) -124.46deg,
    #7c0cb1 73.63deg,
    rgba(56, 11, 115, 0.9) 235.54deg,
    #7c0cb1 433.63deg
  );
  color: #fff;
}

@media screen and (max-width: 1230px) {
  .main-menu-container {
    flex-direction: column;
    align-items: end;
  }
  .main-menu a {
    padding: 10px 8px;
  }
}

@media screen and (min-width: 1025px) {
  .header__btn {
    margin-left: 15px;
  }
}
@media screen and (max-width: 767px) {
  .header__logo--section {
    width: 60%;
  }
  .main-menu-container {
    display: none !important;
  }
  .main-menu-container.active {
    display: block !important;
  }
  .hamburger__menu {
    display: block;
    margin-top: 10px;
  }
  .main-menu-container {
    display: flex;
    flex-direction: column;
    position: absolute;
    width: 100%;
    top: 100%;
    left: 0;
    background-color: #270f0f99;
    padding: 20px;
  }
  .main-menu-container.active {
    display: flex;
    flex-direction: column;
    z-index: -1;
  }
  .main-menu {
    flex-direction: column;
    gap: 15px;
  }
  .main-menu a {
    font-size: 25px;
  }
  .menu-item-haschildren .sub-menus {
    position: static;
    background-color: unset;
  }
  .header__button {
    margin-top: 25px;
  }
  .main-menu a {
    padding: 5px;
    font-size: 16px;
  }
  .menu-item-haschildren .sub-menus {
    text-align: left;
    padding-left: 5px;
  }
  header {
    background: rgb(255 255 255 / 80%);
  }
}
</style>
