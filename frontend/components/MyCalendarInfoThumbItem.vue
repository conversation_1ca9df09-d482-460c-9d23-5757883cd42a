<script lang="ts">
// @ts-nocheck
import { defineComponent } from '@nuxtjs/composition-api'

export default defineComponent({
  name: 'MyCalendarInfoThumbItem',

  props: {
    title: {
      type: String,
      default: '',
    },
    value: {
      type: [String, Number],
      default: '',
    },
    description: {
      type: String,
      default: '',
    },
    tag: {
      type: String,
      default: '',
    },
  },
})
</script>

<template>
  <!--<div
		:class="`tw-w-[140px] md:tw-w-[18%] tw-min-w-[140px] tw-h-[280px] tw-rounded-tl-[54px] tw-rounded-tr-[54px] tw-px-[25px] tw-py-[40px] mc-thumb-item tag-${tag}`"
	>
		<div class="tw-text-2xl tw-font-semibold tw-text-left tw-text-white">
			{{ title }}
		</div>
		<div class="tw-text-white tw-text-6xl tw-font-bold tw-mt-5">
			{{ value }}
		</div>
		<div class="tw-text-white tw-text-xl tw-mt-5 small-description">
			{{ description }}
		</div>
	</div>-->

  <div
    :class="`tw-w-[100px] md:tw-w-[13%] tw-min-w-[100px] tw-rounded-[25px] tw-px-[15px] tw-py-[15px] mc-thumb-item-v2 tag-${tag}`"
  >
    <div class="tw-text-3xl tw-font-semibold tw-text-left text-title">
      <span class="info__indicator"
        ><svg
          version="1.1"
          xmlns="http://www.w3.org/2000/svg"
          xmlns:xlink="http://www.w3.org/1999/xlink"
          x="0px"
          y="0px"
          width="10px"
          height="10px"
          viewBox="0 0 122.881 89.842"
          enable-background="new 0 0 122.881 89.842"
          xml:space="preserve"
        >
          <g>
            <path
              fill="#DDD"
              d="M1.232,55.541c-1.533-1.388-1.652-3.756-0.265-5.289c1.388-1.534,3.756-1.652,5.29-0.265l34.053,30.878l76.099-79.699 c1.429-1.501,3.804-1.561,5.305-0.132c1.502,1.428,1.561,3.803,0.133,5.305L43.223,88.683l-0.005-0.005 c-1.396,1.468-3.716,1.563-5.227,0.196L1.232,55.541L1.232,55.541z"
            />
          </g></svg
      ></span>
      {{ title }} <br />
      <span class="text-title__small">Events</span>
    </div>
    <div class="tw-text-6xl tw-font-bold tw-mt-2 tw-mb-2 tw-ml-5">
      {{ value }}
    </div>
    <!--<div class="tw-text-white tw-text-xl tw-mt-1 small-description" v-html="description">
		</div>-->
  </div>
</template>

<style scoped>
/*.mc-thumb-item{
	background: rgb(121,121,121);
background: linear-gradient(28deg, rgba(121,121,121,1) 0%, rgba(209,209,209,1) 100%);
}

.tag-owner_user{ box-shadow: 3px 0px 0px 0px #B13DBC; }
.tag-bookings{ box-shadow: 3px 0px 0px 0px #C48A41; }
.tag-swaps{ box-shadow: 3px 0px 0px 0px #63963D; }
.tag-mycrew_jobs{ box-shadow: 3px 0px 0px 0px #3E49CB; }
.tag-blocked_dates{ box-shadow: 3px 0px 0px 0px #CD6D6D; }*/

.mc-thumb-item-v2 .info__indicator {
  position: absolute;
  top: 18px;
  left: 15px;
  height: 15px;
  width: 15px;
  background: #e6e6e6;
  border-radius: 3px;
  padding: 2px 0px 0px 3px;
}

.tag-owner_user .info__indicator {
  background: #b13dbc;
}
.tag-public_booking .info__indicator {
  background: #c48a41;
}
.tag-swaps .info__indicator {
  background: #63963d;
}
.tag-mycrew_jobs .info__indicator {
  background: #3e49cb;
}
.tag-blocked_dates .info__indicator {
  background: #cd6d6d;
}
.tag-friends_family .info__indicator {
  background: #562f91;
}
.tag-ical_events .info__indicator {
  background: #666666;
}

.mc-thumb-item-v2 {
  position: relative;
  border-radius: 15px;
  color: #3f3f3f;

  background: rgba(233, 233, 233, 0.5);
  box-shadow:
    0px 4px 4px rgba(243, 234, 234, 0.25),
    0px 4px 4px rgba(0, 0, 0, 0.25);
  border-radius: 15px;
}

.text-title {
  font-size: 0.9em !important;
  font-weight: 600 !important;
  padding-left: 20px;
  line-height: 20px !important;
}

.text-title__small {
  font-weight: 200;
}

.small-description {
  font-size: 0.8em !important;
  line-height: 1.4em !important;
}
</style>
