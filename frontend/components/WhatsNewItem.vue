<script lang="ts">
// @ts-nocheck
import { defineComponent } from '@nuxtjs/composition-api'

export default defineComponent({
  name: 'WhatsNewItem',

  props: {
    title: {
      type: String,
      default: '',
    },
    description: {
      type: [String, Number],
      default: '',
    },
    button_text: {
      type: String,
      default: '',
    },
    link: {
      type: String,
      default: '',
    },
  },

  methods: {
    gotoLink() {
      this.$router.push({ path: this.link })
    },
  },
})
</script>

<template>
  <div
    class="indiv_item tw-cursor-pointer tw-min-w-[100%] tw-w-[100%] md:tw-min-w-[280px] md:tw-w-[300px] tw-min-h-[190px] tw-mb-5 md:tw-mb-0 tw-pt-5 tw-pb-1 tw-pl-5 tw-pr-2 tw-relative hover:tw-bg-grey"
    @click="gotoLink()"
  >
    <div class="tw-text-xl tw-font-semibold tw-text-left tw-text-[#672093]">
      {{ title }}
    </div>
    <div class="tw-text-[#5E5E5E] tw-text-base tw-font-light tw-mt-3 tw-pr-3">
      {{ description }}
    </div>
    <div
      class="tw-text-[#6C6C6C] tw-text-sm tw-font-light tw-mt-8 tw-text-right tw-absolute tw-bottom-[5px] tw-right-[5px]"
    >
      <span v-html="button_text"></span>
      <v-icon color="#7C0CB1" size="20" class="tw-mr-2"> mdi-arrow-right </v-icon>
    </div>
  </div>
</template>

<style scoped>
.indiv_item {
  /*background: linear-gradient(0deg, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2)), linear-gradient(0deg, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2)), conic-gradient(from 180deg at 50% 50%, #989898a3 0deg, rgb(75 75 75 / 52%) 221.4deg, #989898ab 360deg);*/
  box-shadow:
    0px 2px 4px rgba(0, 0, 0, 0.25),
    0px 2px 4px rgba(0, 0, 0, 0.25);
  border-radius: 15px;
  transition: all 0.2s;
}
</style>
