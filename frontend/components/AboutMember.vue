<script setup>
import { ref } from 'vue'
defineProps({
  name: {
    type: String,
    required: true,
  },
  image: {
    type: String,
    required: true,
  },
  role: {
    type: String,
    required: true,
  },
  description: {
    type: String,
    required: true,
  },
  reveseOrder: {
    type: Boolean,
    default: false,
  },
})
</script>
<template>
  <div class="tw-my-8 tw-py-5 md:tw-py-10 animate">
    <div
      class="tw-max-w-[940px] tw-ml-auto tw-mr-auto tw-flex tw-flex-col md:tw-flex-row tw-gap-7 md:tw-gap-14 tw-justify-between tw-container tw-px-5 tw-mx-auto"
    >
      <div
        class="md:tw-w-4/12 tw-text-left md:tw-text-left"
        :class="reveseOrder && 'md:tw-order-1 md:tw-text-right'"
      >
        <img
          :src="image"
          :alt="name"
          class="tw-max-w-[50%] md:tw-max-w-[80%] tw-aspect-square tw-inline tw-rounded-full tw-object-cover tw-block"
        />
      </div>
      <div class="md:tw-w-8/12" :class="reveseOrder && 'md:tw-order-none'">
        <h3 class="tw-text-[#360877] tw-text-2xl tw-text-left tw-mb-0 tw-font-medium tw-font-[Poppins]">
          {{ name }}
        </h3>
        <h4 class="tw-text-[#360877] tw-text-xl tw-text-left tw-font-light tw-mb-5 md:tw-mb-5 tw-font-[Poppins]">
          {{ role }}
        </h4>
        <p class="tw-text-[#360877] tw-text-base tw-font-light tw-font-[Poppins]">
          {{ description }}
        </p>
      </div>
    </div>
  </div>
</template>
