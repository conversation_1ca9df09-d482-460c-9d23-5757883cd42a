<template>
  <div class="host-activation-tracker">
    <!-- Progress Bar -->
    <div class="tw-mb-4">
      <div class="tw-flex tw-justify-between tw-items-center tw-mb-2">
        <div class="tw-text-3xl tw-font-normal tw-text-left tw-text-[#5E5E5E]">Host Activation Progress</div>
        <div class="tw-text-sm tw-font-medium tw-text-primary">{{ progress }}% Complete</div>
      </div>
      <div class="tw-bg-gray-200 tw-rounded-full tw-h-2.5">
        <div
          class="tw-bg-primary tw-h-2.5 tw-rounded-full"
          :style="{ width: `${progress}%` }"
        ></div>
      </div>
    </div>

    <div class="tw-mt-[10px] tw-mb-[10px] tw-text-xl tw-font-normal tw-text-left tw-text-[#5E5E5E] md:tw-max-w-[90%]">
      <div>Complete these steps to activate your host account</div>
    </div>

    <!-- Steps -->
    <div class="tw-flex tw-flex-col tw-gap-3">
      <div
        v-for="(step, index) in steps"
        :key="step.id"
        class="tw-flex tw-items-center tw-gap-3 tw-p-3 tw-rounded-lg tw-transition-colors tw-duration-200"
        :class="{
          'tw-bg-gray-50 hover:tw-bg-gray-100 tw-cursor-pointer': !step.completed,
          'tw-bg-green-50': step.completed,
        }"
        @click="onStepClick(index)"
      >
        <div
          class="tw-flex tw-items-center tw-justify-center tw-w-10 tw-h-10 tw-rounded-full tw-flex-shrink-0"
          :class="step.completed ? 'tw-bg-green-100 tw-text-green-600' : 'tw-bg-primary tw-bg-opacity-10 tw-text-primary'"
        >
          <v-icon v-if="step.completed">mdi-check</v-icon>
          <v-icon v-else>{{ step.icon }}</v-icon>
        </div>
        <div class="tw-flex-grow">
          <div class="tw-font-medium tw-text-zinc-800">{{ step.title }}</div>
          <div class="tw-text-sm tw-text-zinc-500">{{ step.description }}</div>
        </div>
        <div v-if="!step.completed" class="tw-flex-shrink-0">
          <v-btn
            small
            color="primary"
            class="tw-rounded-full tw-px-4"
            @click.stop="onActionClick(index)"
          >
            {{ step.action }}
          </v-btn>
        </div>
        <div v-else class="tw-flex-shrink-0 tw-text-green-600 tw-text-sm tw-font-medium">
          Completed
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, computed, onMounted } from '@nuxtjs/composition-api'

import { useHostActivationStore } from '~/composables/useHostActivationStore'
import { useApi } from '~/composables/useCommon'

export default defineComponent({
  name: 'HostActivationTracker',

  props: {
    compact: {
      type: Boolean,
      default: false
    }
  },

  setup(props, { emit }) {
    const hostActivationStore = useHostActivationStore()
    const api = useApi()

    const progress = computed(() => hostActivationStore.activationProgress)
    const steps = computed(() => hostActivationStore.steps)
    const isFullyActivated = computed(() => hostActivationStore.isFullyActivated)

    // We don't need to check activation status on component mount anymore
    // This is now handled by the HomeEditActivationWrapper component
    // onMounted(async () => {
    //   await hostActivationStore.checkActivationStatus(api)
    // })

    const onStepClick = (index: number) => {
      hostActivationStore.openActivationModal(index)
    }

    const onActionClick = (index: number) => {
      const step = steps.value[index]

      if (step.route) {
        // If the step has a route, navigate to it
        emit('navigate', step.route)
      } else {
        // Otherwise open the activation modal
        hostActivationStore.openActivationModal(index)
      }
    }

    return {
      progress,
      steps,
      isFullyActivated,
      onStepClick,
      onActionClick
    }
  }
})
</script>

<style scoped>
.host-activation-tracker {
  @apply tw-bg-white tw-rounded-[15px] tw-p-4;
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.25), 0px 2px 4px rgba(0, 0, 0, 0.25);
}
</style>
