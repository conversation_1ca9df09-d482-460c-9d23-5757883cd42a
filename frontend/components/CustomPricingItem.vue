<script setup lang="ts">
import { computed, PropType, ref } from '@nuxtjs/composition-api'

import type { HomeCustomNightlyRate } from '~/types'
import { useApi, useToast } from '~/composables/useCommon'
import { TOAST_DURATION } from '~/constants'
import { useAdvancedPricesStore } from '~/composables/useStore'
import { convertDateStringFromDatePicker, formatNumberToDisplay } from '~/helpers'
import GoodCard from '~/components/GoodCard.vue'
import GoodCardTitle from '~/components/GoodCardTitle.vue'
import GoodCancelButton from '~/components/GoodCancelButton.vue'
import GoodOkButton from '~/components/GoodOkButton.vue'
import GoodCard2 from '~/components/GoodCard2.vue'

const toast = useToast()
const api = useApi()
const store = useAdvancedPricesStore()

const dialog = ref(false)
const deleteDialog = ref(false)
const valid = ref(true)

const props = defineProps({
  advancedPrice: {
    type: Object as PropType<HomeCustomNightlyRate>,
    required: true,
  },
  homeIdProp: {
    type: null,
    required: false,
    default: null,
  },
} as const)

const homeIdProp = props.homeIdProp

const advancedPrice = ref<HomeCustomNightlyRate>(props.advancedPrice)
const updateDataPrice = ref(advancedPrice.value.nightly_rate)

const isAppliedForAllFridays = computed(() => {
  const daysOfWeek = advancedPrice.value.conditions.days_of_week
  return daysOfWeek?.every(day => day === 5 || day === 6) && daysOfWeek?.includes(5)
})

const isAppliedForAllSaturdays = computed(() => {
  const daysOfWeek = advancedPrice.value.conditions.days_of_week
  return daysOfWeek?.every(day => day === 5 || day === 6) && daysOfWeek?.includes(6)
})

const formattedStartDateComputed = computed(() => {
  const date = advancedPrice.value.conditions.from_date

  if (!date) return ''

  return convertDateStringFromDatePicker(date)
})

const formattedEndDateComputed = computed(() => {
  const date = advancedPrice.value.conditions.to_date

  if (!date) return ''

  return convertDateStringFromDatePicker(date)
})

const updatePrice = () => {
  store
    .updateAdvancedPrice(api, advancedPrice.value.id, {
      nightly_rate: +updateDataPrice.value,
      is_active: advancedPrice.value.is_active,
    })
    .then(() => {
      advancedPrice.value.nightly_rate = updateDataPrice.value

      toast.success('Price updated successfully').goAway(TOAST_DURATION)
    })
}

const updateData = () => {
  updatePrice()

  dialog.value = false
}

const cancelUpdate = () => {
  // store.getAdvancedPrices(api)

  dialog.value = false
}

const deleteAdvancedPrice = async () => {
  try {
    await store.deleteAdvancedPrice(api, advancedPrice.value.id)
    toast.success('Advanced pricing removed successfully').goAway(TOAST_DURATION)
    deleteDialog.value = false
  } catch (error) {
    toast.error('Error removing advanced pricing').goAway(TOAST_DURATION)
  }
}
</script>

<template>
  <good-card2 class="tw-px-4">
    <v-row>
      <v-col v-if="!homeIdProp" cols="12" md="2" class="tw-text-zinc-500 tw-font-semibold">
        <div
          v-if="$vuetify.breakpoint.mdAndDown && !homeIdProp"
          class="tw-font-semibold tw-text-lg"
        >
          Home
        </div>

        <NuxtLink
          v-if="!homeIdProp"
          :to="`/home/<USER>/${advancedPrice.home?.id}`"
          class="tw-text-primary"
        >
          {{ advancedPrice.home?.title }}
        </NuxtLink>
      </v-col>

      <v-col
        cols="6"
        :md="!homeIdProp ? 2 : 6"
        class="tw-text-zinc-500 tw-font-normal tw-tracking-wide tw-flex tw-flex-col tw-justify-start"
      >
        <div class="tw-flex tw-flex-col">
          <div
            v-if="$vuetify.breakpoint.mdAndDown || homeIdProp"
            class="tw-font-semibold tw-text-lg tw-mt-4"
          >
            Check-In
          </div>
          <div>
            {{ formattedStartDateComputed }}
          </div>
        </div>
        <div v-if="isAppliedForAllFridays">
          <v-icon color="#000000" small>mdi-check</v-icon>
          <span class="tw-text-sm">Apply to Friday's Only</span>
        </div>
      </v-col>

      <v-col
        cols="6"
        :md="!homeIdProp ? 2 : 6"
        class="tw-text-zinc-500 tw-font-normal tw-tracking-wide tw-flex tw-flex-col tw-justify-start"
      >
        <div class="tw-flex tw-flex-col">
          <div
            v-if="$vuetify.breakpoint.mdAndDown || homeIdProp"
            class="tw-font-semibold tw-text-lg tw-mt-4"
          >
            Check-Out
          </div>
          <div>
            {{ formattedEndDateComputed }}
          </div>
        </div>
        <div v-if="isAppliedForAllSaturdays">
          <v-icon color="#000000" small>mdi-check</v-icon>
          <span class="tw-text-sm">Apply to Saturday's Only</span>
        </div>
      </v-col>

      <v-col
        cols="6"
        :md="!homeIdProp ? 2 : 6"
        class="tw-text-zinc-500 tw-font-normal tw-tracking-wide tw-flex tw-items-center"
      >
        <div class="tw-flex tw-flex-col">
          <div
            v-if="$vuetify.breakpoint.mdAndDown || homeIdProp"
            class="tw-font-semibold tw-text-lg tw-mt-4"
          >
            Price
          </div>
          <div>${{ formatNumberToDisplay(advancedPrice.nightly_rate) }}</div>
          <div class="tw-text-xs">
            Original Price: ${{ formatNumberToDisplay(advancedPrice.home?.nightly_rate || 0) }}
          </div>
        </div>
      </v-col>

      <v-col
        cols="6"
        :md="!homeIdProp ? 2 : 6"
        class="tw-text-zinc-500 tw-font-normal tw-tracking-wide tw-flex tw-items-center"
      >
        <div class="tw-flex tw-flex-col">
          <div
            v-if="$vuetify.breakpoint.mdAndDown || homeIdProp"
            class="tw-font-semibold tw-text-lg tw-mt-4"
          >
            Availability
          </div>
          <div class="tw-flex tw-flex-row tw-items-center tw-w-full tw-justify-start">
            <span class="tw-mr-2">Active</span>
            <v-switch
              v-model="advancedPrice.is_active"
              color="primary"
              hide-details
              class="ma-0 pa-0 py-4"
              @change="updatePrice"
            />
          </div>
        </div>
      </v-col>

      <v-col
        cols="12"
        :md="!homeIdProp ? 2 : 12"
        class="tw-text-zinc-500 tw-font-normal tw-tracking-wide"
      >
        <div
          v-if="$vuetify.breakpoint.mdAndDown || homeIdProp"
          class="tw-font-semibold tw-text-lg tw-mt-4 tw-flex tw-flex-row tw-items-center tw-w-full tw-justify-start"
        >
          Action
        </div>
        <div class="tw-flex tw-flex-row tw-items-center tw-w-full tw-justify-start">
          <div class="tw-cursor-pointer tw-py-4 tw-mr-4" @click="dialog = true">
            <span class="tw-mr-2">Edit</span>
            <v-icon color="grey" class="mx-1" small>mdi-pencil</v-icon>
          </div>
          <div class="tw-cursor-pointer tw-py-4 tw-text-red-500" @click="deleteDialog = true">
            <span class="tw-mr-2">Delete</span>
            <v-icon color="error" class="mx-1" small>mdi-delete</v-icon>
          </div>
        </div>
      </v-col>
    </v-row>

    <v-dialog v-model="dialog" :max-width="$vuetify.breakpoint.smAndDown ? '100%' : '30%'">
      <good-card card-text-classes="tw-p-5">
        <good-card-title title="Edit Advanced Pricing" />

        <v-form ref="form" v-model="valid">
          <v-text-field
            v-model="updateDataPrice"
            label="Nightly Rate"
            prefix="$"
            outlined
            dense
            :rules="[v => !!v || 'Price is required']"
          />
        </v-form>

        <div class="tw-flex tw-justify-end tw-mt-4 tw-gap-4">
          <good-cancel-button @click="cancelUpdate">Cancel</good-cancel-button>
          <good-ok-button @click="updateData">Update</good-ok-button>
        </div>
      </good-card>
    </v-dialog>

    <!-- Confirmation Dialog for Deletion -->
    <v-dialog v-model="deleteDialog" :max-width="$vuetify.breakpoint.smAndDown ? '100%' : '30%'">
      <good-card2 class="tw-p-5">
        <div class="tw-text-xl tw-font-semibold tw-mb-4">Remove Advanced Pricing</div>

        <div class="tw-mb-6 tw-text-zinc-600">
          Are you sure you want to remove this advanced pricing setting? This will revert the
          pricing for this date range back to the standard rate.
        </div>

        <div class="tw-flex tw-justify-end tw-gap-4">
          <v-btn text color="grey darken-1" @click="deleteDialog = false"> Cancel </v-btn>
          <v-btn color="error" @click="deleteAdvancedPrice"> Remove </v-btn>
        </div>
      </good-card2>
    </v-dialog>
  </good-card2>
</template>
