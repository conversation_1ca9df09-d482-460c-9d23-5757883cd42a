<script>
export default {
  props: {
    data: {
      data: Array,
      required: true,
    },
    direction: {
      data: String,
      required: false,
    },
  },
}
</script>
<template>
  <div class="testimonials-container">
    <div class="testimonials-container--slider tw-flex" :class="direction">
      <div
        v-for="(item, index) in data"
        class="indiv-testimonial tw-min-w-[310px] md:tw-min-w-[450px] tw-p-5 tw-mr-3 tw-ml-3 tw-mb-5"
      >
        <div>
          <div class="tw-flex tw-flex-row md:tw-flex-row">
            <div class="tw-w-5/6 tw-text-wrap md:tw-w-5/6 tw-p-0 tw-pb-5 tw-text-[#360877] tw-mt-3">
              {{ item.content }}
            </div>
            <div class="tw-w-1/6 md:tw-w-1/6 p-4">
              <img :src="item.image" width="tw-w-full tw-h-auto" />
            </div>
          </div>
        </div>
        <p class="tw-text-right tw-p-0 tw-m-0 tw-text-[#360877]">
          {{ item.name }}
        </p>
      </div>

      <div
        v-for="(item, index) in data"
        :key="index"
        class="indiv-testimonial tw-min-w-[310px] md:tw-min-w-[450px] tw-p-5 tw-mr-3 tw-ml-3 tw-mb-5 tw-shadow-md"
      >
        <div>
          <div class="tw-flex tw-flex-row md:tw-flex-row">
            <div class="tw-w-5/6 tw-text-wrap md:tw-w-5/6 tw-p-0 tw-pb-5 tw-text-[#360877] tw-mt-3">
              {{ item.content }}
            </div>
            <div class="tw-w-1/6 md:tw-w-1/6 p-4">
              <img :src="item.image" width="tw-w-full tw-h-auto" />
            </div>
          </div>
        </div>
        <p class="tw-text-right tw-p-0 tw-m-0 tw-text-[#360877]">
          {{ item.name }}
        </p>
      </div>
    </div>
  </div>
</template>
<style scoped>
.testimonials-container {
  position: relative;
  overflow: hidden;
  width: 100%;
}

.testimonials-container:hover .testimonials-container--slider,
.testimonials-container:hover .testimonials-container--slider.reverse {
  animation-play-state: paused !important;
}

.testimonials-container--slider {
  white-space: nowrap;
  animation: slide 35s linear infinite;
}

.testimonials-container--slider.reverse {
  animation: slideReverse 35s linear infinite !important;
}

/* .testimonials-container--slider img {
    min-height: 50px;
} */

.indiv-testimonial {
  /* background: rgba(255, 255, 255, 0.42); */
  background-color: white;
  /* box-shadow: 0px 2px 2px #360877; */
  border-radius: 25px;
}

@keyframes slide {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-100%);
  }
}

@keyframes slideReverse {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(0%);
  }
}

@media screen and (max-width: 600px) {
  .testimonials-container .testimonials-container--slider,
  .testimonials-container .testimonials-container--slider.reverse {
    animation-play-state: paused !important;
    animation: none !important;
  }
  .testimonials-container {
    overflow-x: auto;
  }
}
</style>
