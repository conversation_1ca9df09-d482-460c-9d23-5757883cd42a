<script lang="ts">
// @ts-nocheck
import { defineComponent, ref, watch, onUnmounted, computed } from '@nuxtjs/composition-api'
import debounce from 'lodash/debounce'

export default defineComponent({
  name: 'OpenStreetAutocomplete',

  props: {
    address: {
      type: String,
      default: '',
    },
    placeholder: {
      type: String,
      default: 'Enter address',
    },
    label: {
      type: [String, null],
      default: 'Address',
    },
    helperText: {
      type: [String, null],
      default: 'For best results, enter your complete address including street, city, and country',
    },
    inputBorder: {
      type: Boolean,
      default: true,
    },
  },

  setup(props, { emit }) {
    const searchQuery = ref(props.address || '')
    const suggestions = ref([])
    const isFetching = ref(false)
    const showSuggestions = ref(false)
    const hasFocus = ref(false)
    const inputElement = ref(null)
    const selectedAddress = ref(props.address || '')
    const lastQuery = ref(props.address || '')

    // Watch for external changes to address prop
    watch(
      () => props.address,
      newValue => {
        if (newValue !== searchQuery.value) {
          searchQuery.value = newValue || ''
          // Update selectedAddress to match the new address
          selectedAddress.value = newValue || ''
          lastQuery.value = newValue || ''
        }
      }
    )

    // Clear input and emit event
    const clearInput = () => {
      searchQuery.value = ''
      suggestions.value = []
      selectedAddress.value = ''
      lastQuery.value = ''
      emit('address-selection', '', null)

      // Focus the input after clearing
      if (inputElement.value) {
        inputElement.value.focus()
      }
    }

    // Handle input focus
    const handleFocus = () => {
      hasFocus.value = true
      // Only show suggestions if the query is different from the selected address
      // and it's long enough to be meaningful
      if (searchQuery.value &&
          searchQuery.value.length > 2 &&
          searchQuery.value !== selectedAddress.value) {
        showSuggestions.value = true
        // Only fetch suggestions if we don't already have them
        if (suggestions.value.length === 0) {
          fetchSuggestions()
        }
      }
    }

    // Handle input click - prevent dropdown from showing when clicking on input after selection
    const handleInputClick = () => {
      // If the current query is the same as the selected address, don't show suggestions
      if (searchQuery.value === selectedAddress.value) {
        showSuggestions.value = false
        return
      }

      // Otherwise, treat it like a focus event
      handleFocus()
    }

    // Handle input blur
    const handleBlur = () => {
      // Delay hiding suggestions to allow for click event on suggestions
      setTimeout(() => {
        hasFocus.value = false
        showSuggestions.value = false
      }, 200)
    }

    // Fetch address suggestions from Nominatim
    const fetchSuggestions = debounce(async () => {
      // Don't fetch if the query is the same as the selected address
      // This prevents the dropdown from showing after selection
      if (!searchQuery.value ||
          searchQuery.value.length < 3 ||
          searchQuery.value === selectedAddress.value) {
        suggestions.value = []
        showSuggestions.value = false
        return
      }

      // Check if we're in a "just selected" state
      if (selectedAddress.value && searchQuery.value === selectedAddress.value) {
        suggestions.value = []
        showSuggestions.value = false
        return
      }

      isFetching.value = true
      showSuggestions.value = true

      try {
        // First try a more specific search with addressdetails
        const response = await fetch(
          `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(searchQuery.value)}&limit=8&addressdetails=1`
        )

        // Check for response status
        if (!response.ok) {
          // If we hit rate limit or server error
          if (response.status === 429 || response.status >= 500) {
            console.error(`API Error: ${response.status}`)
            throw new Error('Rate limit exceeded or server error')
          }
        }

        const data = await response.json()

        if (data && data.length > 0) {
          // Map the data to our format
          const mappedSuggestions = data.map(item => {
            // Create a better formatted address for display
            const formattedAddress = formatAddressWithoutCounty(item.address) || item.display_name

            // Determine the type of location
            let locationType = 'unknown'
            if (item.type === 'natural' || item.class === 'natural' ||
                item.type === 'lake' || item.class === 'water' ||
                (item.address && (item.address.natural || item.address.lake))) {
              locationType = 'natural'
            } else if (item.type === 'administrative' || item.class === 'boundary') {
              locationType = 'administrative'
            } else if (item.type === 'city' ||
                      (item.address && (item.address.city || item.address.town || item.address.village))) {
              locationType = 'city'
            }

            return {
              display_name: item.display_name,
              lat: parseFloat(item.lat),
              lng: parseFloat(item.lon),
              address: item.address,
              type: locationType,
              importance: item.importance || 0,
              // Create a formatted address for deduplication and display
              formatted_address: formattedAddress
            }
          })

          // Sort by importance (higher values first) to prioritize more relevant results
          mappedSuggestions.sort((a, b) => b.importance - a.importance)

          // Filter out duplicates based on the formatted address
          const uniqueAddresses = new Set()
          const filteredSuggestions = mappedSuggestions.filter(item => {
            // If we've already seen this formatted address, skip it
            if (uniqueAddresses.has(item.formatted_address)) {
              return false
            }
            // Otherwise, add it to our set and keep it
            uniqueAddresses.add(item.formatted_address)
            return true
          })

          // Ensure we have a good mix of result types
          // Prioritize natural features and cities when searching for landmarks

          // Special handling for lakes and water features
          const lakes = filteredSuggestions.filter(item =>
            item.address && item.address.lake &&
            item.address.lake.toLowerCase().includes(searchQuery.value.toLowerCase()))

          const naturalFeatures = filteredSuggestions.filter(item =>
            item.type === 'natural' &&
            !(item.address && item.address.lake &&
              item.address.lake.toLowerCase().includes(searchQuery.value.toLowerCase())))

          const cities = filteredSuggestions.filter(item => item.type === 'city')
          const administrative = filteredSuggestions.filter(item => item.type === 'administrative')
          const others = filteredSuggestions.filter(item =>
            item.type !== 'natural' && item.type !== 'city' && item.type !== 'administrative')

          // Combine the results with priority to lakes, natural features, and cities
          suggestions.value = [
            ...lakes,
            ...naturalFeatures,
            ...cities,
            ...administrative,
            ...others
          ].slice(0, 5) // Limit to 5 results for better UX

          // Log the suggestions for debugging
          console.log('Suggestions:', suggestions.value)
        } else {
          suggestions.value = []
        }
      } catch (error) {
        console.error('Error fetching address suggestions:', error)
        suggestions.value = []
      } finally {
        isFetching.value = false
      }
    }, 500) // 500ms debounce

    // Handle changes to search query
    watch(searchQuery, newQuery => {
      // If the query exactly matches the selected address, don't show suggestions
      // This is the key to preventing the dropdown after selection
      if (newQuery === selectedAddress.value) {
        suggestions.value = []
        showSuggestions.value = false
        return
      }

      // Don't fetch suggestions if the query is too short or the same as the last query
      if (newQuery && newQuery.length > 2 && newQuery !== lastQuery.value) {
        fetchSuggestions()
      } else {
        suggestions.value = []
        showSuggestions.value = false
      }

      // If the user is clearing the input, emit empty address
      if (!newQuery || newQuery.length === 0) {
        emit('address-selection', '', null)
        selectedAddress.value = ''
        lastQuery.value = ''
      }

      // Update lastQuery to track what the user is typing
      if (newQuery !== lastQuery.value) {
        lastQuery.value = newQuery
      }
    })

    // Check if address has a house number
    const hasHouseNumber = (addressComponents) => {
      return Boolean(addressComponents && addressComponents.house_number)
    }

    // Format address without county
    const formatAddressWithoutCounty = (addressComponents) => {
      // If address components are missing or empty, return an empty string
      if (!addressComponents || Object.keys(addressComponents).length === 0) {
        return ''
      }

      // Special case for lakes - always prioritize the lake name
      if (addressComponents.lake) {
        return `${addressComponents.lake}, ${addressComponents.state || ''}, ${addressComponents.country || ''}`.replace(/, ,/g, ',').replace(/,$/,'')
      }

      // Extract the components we want to include
      const components = []

      // Check for natural features, lakes, or tourist attractions first
      if (addressComponents.natural || addressComponents.tourism || addressComponents.attraction ||
          addressComponents.water) {
        const featureName = addressComponents.natural ||
                           addressComponents.water || addressComponents.tourism ||
                           addressComponents.attraction
        components.push(featureName)

        // For natural features, we want to prioritize the feature name
        // and potentially skip other components to make the display cleaner
        if (addressComponents.natural) {
          // If it's a well-known natural feature, we might want to just show
          // the feature name and the state/country
          return `${featureName}, ${addressComponents.state || ''}, ${addressComponents.country || ''}`.replace(/, ,/g, ',').replace(/,$/,'')
        }
      }

      // Add house number and street if available
      if (addressComponents.house_number && addressComponents.road) {
        components.push(`${addressComponents.house_number} ${addressComponents.road}`)
      } else if (addressComponents.road) {
        components.push(addressComponents.road)
      }

      // Add neighborhood/suburb if available
      if (addressComponents.neighbourhood || addressComponents.suburb) {
        components.push(addressComponents.neighbourhood || addressComponents.suburb)
      }

      // Add city/town/village (use the first available)
      const locality = addressComponents.city ||
                      addressComponents.town ||
                      addressComponents.village ||
                      addressComponents.hamlet ||
                      addressComponents.municipality
      if (locality) {
        components.push(locality)
      }

      // Add county if it's the only geographic identifier available
      if (addressComponents.county && !locality && !addressComponents.state) {
        components.push(addressComponents.county)
      }

      // Add state/province if available
      if (addressComponents.state) {
        components.push(addressComponents.state)
      }

      // Add country if available
      if (addressComponents.country) {
        components.push(addressComponents.country)
      }

      // If we couldn't extract any components, fall back to the original display_name if available
      if (components.length === 0 && addressComponents.display_name) {
        return addressComponents.display_name
      }

      // Join with commas
      return components.join(', ')
    }

    // Warning message for incomplete address
    const addressWarning = ref('')

    // Handle selection of a suggestion
    const selectSuggestion = suggestion => {
      // Check if the address has a house number
      const hasNumber = hasHouseNumber(suggestion.address)

      // Format the address without county
      const formattedAddress = formatAddressWithoutCounty(suggestion.address) || suggestion.display_name

      // Update the search query with the formatted address
      searchQuery.value = formattedAddress
      showSuggestions.value = false

      // Store the selected address to prevent showing the same suggestion again
      selectedAddress.value = formattedAddress
      lastQuery.value = formattedAddress

      // Set warning message if no house number (only for home creation/editing)
      if (!hasNumber && props.helperText && props.helperText.includes('house')) {
        addressWarning.value = 'Address must include a house/building number to activate your home'
      } else {
        addressWarning.value = ''
      }

      // Process address components for better city/state/country extraction
      const processedComponents = processAddressComponents(suggestion.address, suggestion.type)

      // Emit the formatted address and the full details
      emit('address-selection', formattedAddress, {
        lat: suggestion.lat,
        lng: suggestion.lng,
        full_address: formattedAddress,
        address_components: processedComponents,
        has_house_number: hasNumber,
        location_type: suggestion.type || 'unknown'
      })
    }

    // Process address components to ensure city, state, country are properly set
    const processAddressComponents = (addressComponents, locationType) => {
      if (!addressComponents) return {}

      // Create a copy to avoid modifying the original
      const processed = { ...addressComponents }

      // For natural features like "Lake Tahoe", ensure city/state/country are properly set
      if (locationType === 'natural' && (processed.natural || processed.lake || processed.water || processed.tourism)) {
        // Use the natural feature name as the "city" for search purposes
        const featureName = processed.natural || processed.lake || processed.water || processed.tourism

        // For lakes and natural features, we want to prioritize the feature name
        // Set it as the city for search purposes
        processed.city = featureName

        // Also store the original feature name for display purposes
        processed.feature_name = featureName
      }

      // Ensure city is set from town/village/hamlet if needed
      processed.city = processed.city ||
                      processed.town ||
                      processed.village ||
                      processed.hamlet ||
                      processed.municipality

      // For administrative areas without a city, use the name as the city
      if (locationType === 'administrative' && !processed.city && processed.state) {
        processed.city = processed.state
      }

      return processed
    }

    // Cleanup debounce on component unmount
    onUnmounted(() => {
      fetchSuggestions.cancel()
    })

    // Show clear button when we have input
    const showClearButton = computed(() => {
      return searchQuery.value && searchQuery.value.length > 0
    })

    return {
      searchQuery,
      suggestions,
      isFetching,
      showSuggestions,
      selectSuggestion,
      handleFocus,
      handleBlur,
      handleInputClick,
      hasFocus,
      clearInput,
      showClearButton,
      inputElement,
      formatAddressWithoutCounty,
      hasHouseNumber,
      addressWarning,
      processAddressComponents,
      selectedAddress,
      lastQuery,
    }
  },
})
</script>

<template>
  <div class="address-autocomplete">
    <label v-if="label" class="tw-block tw-text-sm tw-font-medium tw-text-gray-700 tw-mb-1">
      {{ label }}
    </label>

    <div
      class="tw-relative tw-rounded-lg tw-transition-all"
      :class="[
        inputBorder ? 'tw-border' : '',
        hasFocus && inputBorder
          ? 'tw-border-primary tw-ring-2 tw-ring-primary-100'
          : inputBorder
            ? 'tw-border-gray-300'
            : '',
        isFetching ? 'tw-bg-gray-50' : 'tw-bg-white',
      ]"
    >
      <!-- Input with location icon -->
      <div class="tw-flex tw-items-center tw-relative">
        <div class="tw-absolute tw-left-3 tw-text-gray-400">
          <v-icon color="primary"> mdi-map-marker </v-icon>
        </div>

        <input
          ref="inputElement"
          v-model="searchQuery"
          type="text"
          :placeholder="placeholder"
          :class="[
            'tw-w-full tw-py-3 tw-pl-10 tw-pr-10 tw-rounded-lg tw-bg-transparent focus:tw-outline-none',
          ]"
          @focus="handleFocus"
          @blur="handleBlur"
          @click="handleInputClick"
        />

        <!-- Loading indicator or clear button -->
        <div
          v-if="isFetching || showClearButton"
          class="tw-absolute tw-right-3 tw-flex tw-items-center"
        >
          <v-progress-circular
            v-if="isFetching"
            indeterminate
            size="20"
            width="2"
            color="primary"
          />

          <button
            v-else-if="showClearButton"
            type="button"
            class="tw-text-gray-400 hover:tw-text-gray-600 tw-transition"
            @click.prevent="clearInput"
          >
            <v-icon>mdi-close</v-icon>
          </button>
        </div>
      </div>

      <!-- Suggestions dropdown -->
      <div
        v-if="showSuggestions && suggestions.length > 0"
        class="tw-absolute tw-z-10 tw-mt-1 tw-w-full tw-bg-white tw-shadow-lg tw-rounded-md tw-border tw-border-gray-200 tw-max-h-60 tw-overflow-auto"
      >
        <ul class="tw-py-1">
          <li
            v-for="(suggestion, index) in suggestions"
            :key="index"
            class="tw-px-4 tw-py-2 hover:tw-bg-gray-100 tw-cursor-pointer tw-transition-colors tw-text-sm"
            @mousedown.prevent="selectSuggestion(suggestion)"
          >
            <div class="tw-flex tw-items-start tw-gap-2">
              <!-- Use different icons based on location type -->
              <v-icon
                class="tw-mt-0.5 tw-shrink-0"
                :color="suggestion.type === 'natural' ? 'green' : 'primary'"
              >
                {{
                  suggestion.address && suggestion.address.lake ? 'mdi-waves' :
                  suggestion.type === 'natural' ? 'mdi-pine-tree' :
                  suggestion.type === 'city' ? 'mdi-city' :
                  suggestion.type === 'administrative' ? 'mdi-map' :
                  'mdi-map-marker'
                }}
              </v-icon>
              <div class="tw-flex tw-flex-col">
                <span class="tw-font-medium">{{ formatAddressWithoutCounty(suggestion.address) || suggestion.display_name }}</span>
                <!-- Show additional location type info -->
                <span v-if="suggestion.address" class="tw-text-xs tw-text-gray-500">
                  {{
                    suggestion.address && suggestion.address.lake ? 'Lake' :
                    suggestion.type === 'natural' ? 'Natural Feature' :
                    suggestion.type === 'city' ? 'City' :
                    suggestion.type === 'administrative' ? 'Region' :
                    'Location'
                  }}
                </span>
              </div>
            </div>
          </li>
        </ul>
      </div>

      <!-- No results message -->
      <div
        v-else-if="showSuggestions && searchQuery.length > 2 && !isFetching"
        class="tw-absolute tw-z-10 tw-mt-1 tw-w-full tw-bg-white tw-shadow-lg tw-rounded-md tw-border tw-border-gray-200"
      >
        <div class="tw-p-4 tw-text-sm tw-text-gray-500">
          No addresses found. Try entering a more complete address including street, city, and
          country.
        </div>
      </div>
    </div>

    <!-- Warning message for incomplete address -->
    <div v-if="addressWarning" class="tw-mt-1 tw-text-xs tw-text-amber-600">
      <v-icon small color="warning">mdi-alert-circle</v-icon>
      {{ addressWarning }}
    </div>

    <!-- Helper text -->
    <div v-else-if="helperText" class="tw-mt-1 tw-text-xs tw-text-gray-500">
      {{ helperText }}
    </div>
  </div>
</template>

<style scoped>
.address-autocomplete {
  position: relative;
}

input {
  transition: all 0.2s ease;
}

button:focus {
  outline: none;
}
</style>
