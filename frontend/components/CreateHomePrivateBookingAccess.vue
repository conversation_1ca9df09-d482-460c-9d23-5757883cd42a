<script lang="ts">
// @ts-nocheck
import { computed, defineComponent, onMounted } from '@nuxtjs/composition-api'

import BoldPurpleText from '~/components/BoldPurpleText.vue'
import { useCreateHomeProgressStore } from '~/composables/useCreateHomeStore'
import { rentalPlans } from '~/constants'

export default defineComponent({
  name: 'CreateHomePrivateBookingAccess',

  components: {
    BoldPurpleText,
  },

  setup() {
    const store = useCreateHomeProgressStore()

    const { createHomeData, validateCreateHomeData, clickOnRentalPlan } = store

    const selectedRentalPlans = computed(() => {
      return createHomeData.rentalPlan.map(plan => plan.title).join(', ')
    })

    onMounted(() => {
      createHomeData.isBooking = false
      createHomeData.isSwap = false
      createHomeData.isPrivate = true
    })

    return {
      createHomeData,
      validateCreateHomeData,
      rentalPlans,
      clickOnRentalPlan,
      selectedRentalPlans,
    }
  },
})
</script>

<template>
  <v-row>
    <v-col cols="12" class="tw-mt-8 tw-flex tw-flex-col tw-gap-4">
      <div class="tw-text-2xl tw-font-semibold tw-text-zinc-600">Booking Access</div>

      <div class="tw-text-lg tw-font-medium tw-text-zinc-500">
        Your Home Status is
        <BoldPurpleText>Private</BoldPurpleText>
      </div>

      <div class="tw-w-full md:tw-w-[30%]">
        <img :src="require('~/assets/PrivateIcon.png')" alt="private home" />
      </div>

      <div class="tw-text-lg tw-font-medium tw-text-zinc-500">
        You selected {{ selectedRentalPlans || 'nothing' }} in terms of Home Rentals.
      </div>

      <div class="tw-text-xl tw-font-semibold tw-mt-4 tw-text-zinc-500">
        This indicates you want to utilize our Private Homes feature, where you will create a direct
        booking link that you can share directly! That’s the only way anyone can book your home.
      </div>

      <div class="tw-text-xl tw-font-semibold tw-mt-4 tw-text-zinc-500">
        Your Booking Access is turned
        <BoldPurpleText>OFF</BoldPurpleText>
      </div>
    </v-col>

    <v-col cols="12" class="tw-text-zinc-500">
      If you ever want to edit the status of your Home in the future you can do so in the Homes
      section.
    </v-col>
  </v-row>
</template>

<style scoped></style>
