# Twimo Frontend

## Description

This repository contains the frontend source code for Twimo, a home listing and booking platform. It provides user interfaces for property browsing, user accounts, and booking functionalities.

## Technologies used

- NuxtJS 2
- Nuxt Composition API
- TailwindCSS

## Installation

### Prerequisites

- Node.js

### Installation Steps

Follow these simple steps to set up the development environment:

1. <PERSON>lone the repository and navigate to the frontend directory.
2. Set up environment variables by copying the development environment file:
   ```sh
   cp .env_example .env
   ```
3. Install dependencies:
   ```sh
   bun i
   ```
4. Run the development server:
   ```sh
   bun dev
   ```

## Usage

The frontend application running at: http://localhost:3000

### Login Credentials

- Email: <EMAIL>
- Password: secret

## Environment Variables

### Stripe Configuration

The application uses environment variables for Stripe integration:

- `STRIPE_PUBLISHABLE_KEY`: Your Stripe publishable key for the frontend.
- `STRIPE_PRICE_MONTHLY`: The Stripe price ID for monthly subscription plan.
- `STRIPE_PRICE_YEARLY`: The Stripe price ID for yearly subscription plan.
