// eslint-disable-next-line import/named
import { RentalPlan, SpaceType } from '~/types'

export const TOAST_DURATION = 3000 as const

export const POLLING_INTERVAL = 3000 as const

export const URL_CHECKER_REGEX = new RegExp(
  '^(https?:\\/\\/)?' + // protocol
    '((([a-z\\d]([a-z\\d-]*[a-z\\d])*)\\.)+[a-z]{2,}|' + // domain name
    '((\\d{1,3}\\.){3}\\d{1,3}))' + // OR ip (v4) address
    '(\\:\\d+)?(\\/[-a-z\\d%_.~+]*)*' + // port and path
    '(\\?[;&a-z\\d%_.~+=-]*)?' + // query string
    '(\\#[-a-z\\d_]*)?$',
  'i'
)

export const spaceTypes: SpaceType[] = [
  {
    title: 'An Entire Space',
    description:
      'Guests will have the entire space to themselves, including a private entrance with no shared spaces',
  },
  {
    title: 'Shared Interior Space',
    description:
      'Guests will have a room with private door for sleeping. Other interior areas can be shared such as kitchen, bathrooms, etc. ',
  },
]

export const HOME_STATUSES = {
  PRIVATE: 'PRIVATE',
  SWAP: 'SWAP',
  RENT: 'RENT',
  SEASONAL_RENTAL: 'SEASONAL_RENTAL',
}

export const rentalPlans: RentalPlan[] = [
  {
    title: 'Friends & Family',
    description:
      'Private Booking, only accessible to guests direclty from Twimo Host via shareable booking link. Ideal for fractional-ownership sharing as well!',
  },
  {
    title: 'Swap',
    description:
      'Your home will be visible to other Twimo Hosts within our Home Swap Network. Swap with like minded vacation home owners around the world.',
  },
  {
    title: 'Rent',
    description:
      'Allows public visibility, offering direct booking access with ability to approve or decline booking requests.',
  },
  {
    title: 'Seasonal Rental',
    description:
      'Allows public visibility, offering direct booking access for 30 Days+ with ability to approve or decline booking requests.',
  },
]

export const HOME_STATUS_RENTAL_PLAN_MAPPING = {
  [HOME_STATUSES.PRIVATE]: rentalPlans.find(plan => plan.title === 'Friends & Family'),
  [HOME_STATUSES.SWAP]: rentalPlans.find(plan => plan.title === 'Swap'),
  [HOME_STATUSES.RENT]: rentalPlans.find(plan => plan.title === 'Rent'),
  [HOME_STATUSES.SEASONAL_RENTAL]: rentalPlans.find(plan => plan.title === 'Seasonal Rental'),
}

export const petFriendlyOptions = ['yes', 'no', 'service animal only']

export const petFriendlyTypes = ['dog', 'cat', 'goldfish']

export const petFriendlyNumbers = [1, 2, 3]

export const amenities = {
  exterior: [
    'Lake Access',
    'Pool',
    'Firepit',
    'Outdoor Dining',
    'Beach Access',
    'Hot Tub',
    'Deck',
    'BBQ',
    'Waterfront',
    'View',
    'Patio',
    'Gym',
  ],
  interior: [
    'Extra Linens',
    'Free Parking',
    'Fireplace',
    'Laundry',
    'Extra Towels',
    'Paid Parking',
    'Toiletries',
    'AC',
    'Office Space',
    'Street Parking',
    'Hair Dryer',
    'Heating',
  ],
  kitchen: [
    'Blender',
    'Pantry Staples',
    'Cooking Utensils',
    'Oven',
    'Dishwasher',
    'Coffee Maker',
    'Toaster',
    'Grill',
  ],
  security: [
    'Doorman',
    'Gated Community',
    'Fenced Yard',
    'Cellular',
    'High Speed Wifi',
    'Internet Connection',
  ],
  entertainment: [
    'TV',
    'Game Console',
    'Gym Equipment',
    'Pool Table',
    'Wine Cellar',
    'High Speed Wifi',
    'Internet Connection',
    'Cellular',
  ],
  seasonal: [
    'Golf Cart',
    'Kayaks',
    'Paddle Boards',
    'Skiis',
    'Snow Shoes',
    'High Speed Wifi',
    'Internet Connection',
    'Cellular',
  ],
  kidsAndPets: ['Pet Friendly', "Nursery or Kid's Room"],
  additional: ['Non-Smoking', 'Wheelchair Accessibility'],
  safety: ['Smoke Alarm', 'CO2 Alarm', 'Fire Extinguisher'],
}

export const MANDATORY_SAFETY_AMENITIES = ['Smoke Alarm', 'CO2 Alarm', 'Fire Extinguisher']

export const additionalInfos = ['Outdoor Security Cameras']

export const CREATE_HOME_STEPS_NOR = {
  TITLE: 'TITLE',
  AIRBNB_URL: 'AIRBNB_URL',
  RENTAL_PLAN: 'RENTAL_PLAN',
  SPACE_TYPE: 'SPACE_TYPE',
  ADDRESS: 'ADDRESS',
  ROOM_AMOUNT: 'ROOM_AMOUNT',
  AMENITY: 'AMENITY',
  PHOTOS: 'PHOTOS',
  DESCRIPTION: 'DESCRIPTION',
  PRICING: 'PRICING',
  TERM_AGREEMENT: 'TERM_AGREEMENT',
  DONE: 'DONE',
}

export const CREATE_HOME_STEP_AIRBNB = {
  TITLE: 'TITLE',
  AIRBNB_URL: 'AIRBNB_URL',
  AIRBNB_WAITING: 'AIRBNB_WAITING',
  RENTAL_PLAN: 'RENTAL_PLAN',
  PRICING: 'PRICING',
  TERM_AGREEMENT: 'TERM_AGREEMENT',
  DONE: 'DONE',
}

export const CREATE_HOME_COMPONENTS = {
  [CREATE_HOME_STEPS_NOR.TITLE]: 'CreateHomeTitle',
  [CREATE_HOME_STEPS_NOR.RENTAL_PLAN]: 'CreateHomeRentalPlan',
  [CREATE_HOME_STEPS_NOR.SPACE_TYPE]: 'CreateHomeSpaceType',
  // [CREATE_HOME_STEPS_NOR.BOOKING_ACCESS]: 'CreateHomeBookingAccess',
  // [CREATE_HOME_STEPS_NOR.SWAP]: 'CreateHomeSwap',
  // [CREATE_HOME_STEPS_NOR.SKIP_TO]: 'CreateHomeSkipTo',
  [CREATE_HOME_STEPS_NOR.ADDRESS]: 'CreateHomeAddress',
  [CREATE_HOME_STEPS_NOR.ROOM_AMOUNT]: 'CreateHomeRoomAmount',
  [CREATE_HOME_STEPS_NOR.AMENITY]: 'CreateHomeAmenity',
  [CREATE_HOME_STEPS_NOR.PHOTOS]: 'CreateHomePhotos',
  [CREATE_HOME_STEPS_NOR.DESCRIPTION]: 'CreateHomeDescription',
  [CREATE_HOME_STEPS_NOR.PRICING]: 'CreateHomePricing',
  [CREATE_HOME_STEPS_NOR.TERM_AGREEMENT]: 'CreateHomeTermAgreement',
  [CREATE_HOME_STEPS_NOR.DONE]: 'CreateHomeDone',

  [CREATE_HOME_STEP_AIRBNB.AIRBNB_URL]: 'CreateHomeAirBnbUrl',
  [CREATE_HOME_STEP_AIRBNB.AIRBNB_WAITING]: 'CreateHomeAirBnbWaiting',
}

export const DEFAULT_SERVICE_FEE_RATIO = 0
export const TRUVI_PROTECTION_PRICE = 7
export const POINTS_MULTIPLIER = 1.5
export const SwapPaymentTypeEnum = Object.freeze({
  MONEY: 'money',
  FREE: 'free',
})

export const cookieOptions = {
  path: '/',
  maxAge: 7 * 24 * 60 * 60,
}

export const MESSAGES_POLLING_INTERVAL = 1000

export const CANCELLATION_POLICY_TYPE = {
  PRIVATE: {
    TITLE: 'Friends & Family Policy',
    DESCRIPTION:
      'If you need to cancel your visit please contact your Host as soon as possible so they can Share their home with another guest, coordinate with the cleaners, or use themselves! Guest pay in full on day of booking.',
    GUEST_DESCRIPTION:
      'If you need to cancel your visit please contact your Host as soon as possible so they can Share their home with another guest, coordinate with the cleaners, or use themselves! Guest pay in full on day of booking.',
  },
  PUBLIC: {
    TITLE: 'Standard Public Rental Policy',
    DESCRIPTION:
      'Guests have the ability to cancel for a full refund up until 14 days before the booking. If they cancel after 14 days they will receive 50% of their booking fee. After 7 days no refunds. Guest have choice to pay in full on day of booking. Alternatively, guest can pay half on the day of booking, and the other half three weeks prior to arrival.',
    GUEST_DESCRIPTION:
      'Guests have the ability to cancel for a full refund up until 14 days before the booking. If they cancel after 14 days they will receive 50% of their booking fee. After 7 days no refunds. Guest have choice to pay in full on day of booking. Alternatively, guest can pay half on the day of booking, and the other half three weeks prior to arrival.',
  },
  FLEXIBLE: {
    TITLE: 'Flexible Public Rental Policy',
    DESCRIPTION:
      'Guests have the ability to cancel for a full refund up until 72 hours before the booking. If they cancel after 72 hours before the booking they will receive a 50% of their booking fee. After 24 hours no refunds. Guest have choice to pay in full on day of booking. Alternatively, guest can pay half on the day of booking, and the other half three weeks prior to arrival.',
    GUEST_DESCRIPTION:
      'Guests have the ability to cancel for a full refund up until 72 hours before the booking. If they cancel after 72 hours before the booking they will receive a 50% of their booking fee. After 24 hours no refunds. Guest have choice to pay in full on day of booking. Alternatively, guest can pay half on the day of booking, and the other half three weeks prior to arrival.',
  },
  LONG_TERM: {
    TITLE: 'Long-Term Stay Policy',
    DESCRIPTION:
      'Guests can cancel anytime 30 days before check-in for a full refund. If guests cancel within the 30 days before check-in they are financially responsible for the first month of stay. Guest have choice to pay in full on day of booking. Alternatively, guest can pay half on the day of booking, and the other half three weeks prior to arrival.',
    GUEST_DESCRIPTION:
      'Guests can cancel anytime 30 days before check-in for a full refund. If guests cancel within the 30 days before check-in they are financially responsible for the first month of stay. Guest have choice to pay in full on day of booking. Alternatively, guest can pay half on the day of booking, and the other half three weeks prior to arrival.',
  },
  NON_REFUNDABLE: {
    TITLE: 'Non-Refundable Policy',
    DESCRIPTION:
      'Guests opt into a non-refundable cancellation policy. Guest pay in full on day of booking.',
    GUEST_DESCRIPTION:
      'Guests opt into a non-refundable cancellation policy. Guest pay in full on day of booking.',
  },
}

// Increased timeout to 8 hours to minimize disruption to users
export const INACTIVITY_TIMEOUT = 8 * 60 * 60 * 1000

export const FETCH_NOTIFICATIONS_INTERVAL = 3000
export const PROCESS_NOTIFICATIONS_INTERVAL = 30000
