{"name": "twimo/backend", "type": "project", "description": "<PERSON><PERSON><PERSON>", "keywords": ["twimo", "backend"], "license": "MIT", "require": {"php": "^8.4", "aws/aws-sdk-php": "^3.277.9", "doctrine/dbal": "^3.1", "filament/filament": "^3.0-stable", "guzzlehttp/guzzle": "^7.0.1", "halaxa/json-machine": "^0.8.0", "laravel/cashier": "*", "laravel/framework": "^12", "laravel/pulse": "1.x-dev", "laravel/telescope": "5.x-dev", "laravel/tinker": "^2.5", "laravel/ui": "^4", "league/flysystem": "^3", "league/flysystem-aws-s3-v3": "^3", "livewire/livewire": "^3.1", "php-open-source-saver/jwt-auth": "dev-main", "pxlrbt/filament-excel": "dev-main", "spatie/icalendar-generator": "^2.3", "spatie/laravel-medialibrary": "^11", "spatie/laravel-sluggable": "^3.5", "yieldstudio/laravel-brevo-notifier": "dev-main"}, "require-dev": {"fakerphp/faker": "^1", "larastan/larastan": "^3.0", "laravel/pint": "^1.18", "mockery/mockery": "^1", "nunomaduro/collision": "^v8.1.1", "roave/security-advisories": "dev-latest"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"], "format": ["./vendor/bin/pint"], "analyse": ["./vendor/bin/phpstan analyse --memory-limit=2G"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"php-http/discovery": true}}, "minimum-stability": "dev", "prefer-stable": false}