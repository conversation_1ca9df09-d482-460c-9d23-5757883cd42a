<?php

use App\Enums\HomeStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update all homes to have status 'active' and enable both allow_booking and allow_swaps
        DB::table('homes')->update([
            'status' => 'active', // Using string value directly as per HomeStatus enum
            'allow_booking' => true,
            'allow_swaps' => true,
        ]);
    }

    /**
     * Reverse the migrations.
     *
     * Note: This is a destructive operation and cannot be truly reversed
     * as we don't know the original values. This is just a placeholder.
     */
    public function down(): void
    {
        // No down migration as we can't know the original values
    }
};
