<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('homes', function (Blueprint $table) {
            $table->dropColumn('country_short');
            $table->dropColumn('state_short');
            $table->dropColumn('city_short');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('homes', function (Blueprint $table) {
            $table->string('country_short')->nullable();
            $table->string('state_short')->nullable();
            $table->string('city_short')->nullable();
        });
    }
};
