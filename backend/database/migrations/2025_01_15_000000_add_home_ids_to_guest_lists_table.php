<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('guest_lists', function (Blueprint $table) {
            // Add home_ids JSON column to track which specific homes each guest can access
            $table->json('home_ids')->nullable()->after('guest_id');
        });

        // Migrate existing data: For each existing guest-host relationship,
        // populate home_ids with all homes owned by that host (maintaining current behavior)
        $this->migrateExistingGuestLists();
    }

    /**
     * Migrate existing guest list entries to include all host's homes in home_ids
     */
    private function migrateExistingGuestLists(): void
    {
        // Get all existing guest list entries
        $existingGuestLists = DB::table('guest_lists')->get();
        
        foreach ($existingGuestLists as $guestList) {
            // Get all homes owned by this host
            $hostHomes = DB::table('homes')
                ->where('user_id', $guestList->host_id)
                ->pluck('id')
                ->toArray();
            
            // Update the guest list entry with all host's home IDs
            DB::table('guest_lists')
                ->where('id', $guestList->id)
                ->update(['home_ids' => json_encode($hostHomes)]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('guest_lists', function (Blueprint $table) {
            $table->dropColumn('home_ids');
        });
    }
};
