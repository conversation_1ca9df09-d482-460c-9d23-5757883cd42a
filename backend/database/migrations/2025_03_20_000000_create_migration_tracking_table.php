<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('migration_tracking', function (Blueprint $table) {
            $table->id();
            $table->string('migration_name')->unique();
            $table->unsignedBigInteger('last_processed_id')->default(0);
            $table->json('metadata')->nullable();
            $table->timestamps();
        });

        // Remove all current user stripe info and subscriptions in the database
        DB::table('users')->update(['stripe_id' => null, 'pm_type' => null, 'pm_last_four' => null]);

        // Remove all current subscriptions in the database
        DB::table('subscriptions')->delete();
        DB::table('subscription_items')->delete();
    }

    public function down(): void
    {
        Schema::dropIfExists('migration_tracking');
    }
};
