<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->boolean('identity_verified')->default(false);
            $table->string('stripe_identity_session_id')->nullable();
            $table->timestamp('identity_verified_at')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('identity_verified');
            $table->dropColumn('stripe_identity_session_id');
            $table->dropColumn('identity_verified_at');
        });
    }
};
