<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('action_tokens', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->timestamps();
            $table->string('action');
            $table->unsignedInteger('user_id');
            $table->string('token');
            $table->timestamp('expired_at')->nullable();
        });

        Schema::create('blocked_dates', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->timestamps();
            $table->unsignedBigInteger('home_id')->index('blocked_dates_home_id_foreign');
            $table->date('start_at')->nullable();
            $table->date('end_at')->nullable();
            $table->text('type')->nullable();
        });

        Schema::create('bookings', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->timestamps();
            $table->unsignedBigInteger('user_id')->nullable();
            $table->unsignedBigInteger('request_user_home');
            $table->unsignedInteger('user_home')->nullable();
            $table->date('start_at');
            $table->date('end_at');
            $table->string('status');
            $table->json('extra_info')->nullable();
            $table->string('code')->nullable();
            $table->string('from_sharable_link')->nullable();
            $table->string('booking_type')->default('RENT');
            $table->boolean('guest_cleaning_fee_enabled')->default(false);
            $table->boolean('host_cleaning_fee_enabled')->default(false);
            $table->text('comment')->nullable();
        });

        Schema::create('events', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('event_title', 100);
            $table->dateTime('start_date');
            $table->dateTime('end_date');
            $table->unsignedTinyInteger('recurrence');
            $table->unsignedTinyInteger('block_dates')->default(0);
            $table->text('notes')->nullable();
            $table->unsignedBigInteger('event_ref_id')->nullable()->comment('ID of the reference table where booking is store');
            $table->unsignedBigInteger('events_categories_id')->index('events_events_categories_id_foreign');
            $table->unsignedBigInteger('home_id')->index('events_home_id_foreign');
            $table->unsignedBigInteger('user_id')->index('events_user_id_foreign');
            $table->unsignedBigInteger('ical_id')->nullable()->index('events_ical_id_foreign');
            $table->timestamps();
        });

        Schema::create('events_categories', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('category_name', 100);
            $table->string('color', 10);
            $table->unsignedTinyInteger('sort_order');
            $table->enum('status', ['active', 'inactive'])->default('active');
            $table->timestamps();
        });

        Schema::create('events_ical_imports', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('ical_url');
            $table->string('ical_name', 100);
            $table->unsignedBigInteger('home_id')->index('events_ical_imports_home_id_foreign');
            $table->unsignedBigInteger('user_id')->index('events_ical_imports_user_id_foreign');
            $table->timestamps();
        });

        Schema::create('failed_jobs', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('uuid')->unique();
            $table->text('connection');
            $table->text('queue');
            $table->longText('payload');
            $table->longText('exception');
            $table->timestamp('failed_at')->useCurrent();
        });

        Schema::create('guest_lists', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('host_id');
            $table->unsignedBigInteger('guest_id')->index('guest_lists_guest_id_foreign');
            $table->timestamps();
            $table->softDeletes();

            $table->unique(['host_id', 'guest_id']);
        });

        Schema::create('home_availables', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->timestamp('start_at')->nullable();
            $table->timestamp('end_at')->nullable();
            $table->integer('home_id');
        });

        Schema::create('home_custom_nightly_rates', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('home_id')->index();
            $table->decimal('nightly_rate', 10);
            $table->json('conditions');
            $table->timestamps();
            $table->boolean('is_active')->default(true);
        });

        Schema::create('home_saved_category', function (Blueprint $table) {
            $table->unsignedBigInteger('home_id');
            $table->unsignedBigInteger('saved_home_category_id')->index('home_saved_category_saved_home_category_id_foreign');

            $table->primary(['home_id', 'saved_home_category_id']);
        });

        Schema::create('home_user', function (Blueprint $table) {
            $table->comment('Pivot table to storing matches of user preferences and homes with ACTIVE status');
            $table->bigIncrements('id');
            $table->unsignedBigInteger('home_id')->index('home_user_home_id_foreign');
            $table->unsignedBigInteger('user_id')->index('home_user_user_id_foreign');
            $table->timestamps();
        });

        Schema::create('homes', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('slug')->nullable();
            $table->timestamps();
            $table->string('title');
            $table->string('address');
            $table->string('country_short')->nullable();
            $table->string('country_long')->nullable();
            $table->string('state_long')->nullable();
            $table->string('state_short')->nullable();
            $table->string('city_short')->nullable();
            $table->string('city_long')->nullable();
            $table->string('street')->nullable();
            $table->integer('beds');
            $table->double('baths');
            $table->text('location');
            $table->text('description');
            $table->integer('user_id')->nullable();
            $table->string('status');
            $table->string('ical_url')->nullable();
            $table->decimal('nightly_rate', 10)->nullable();
            $table->boolean('allow_booking')->default(false);
            $table->boolean('allow_swaps')->default(false);
            $table->unsignedSmallInteger('cleaning_fee')->default(150);
            $table->decimal('tax_rate', 5)->default(0);
            $table->integer('number_of_beds')->nullable();
            $table->integer('guests')->nullable();
            $table->json('amenities')->nullable();
            $table->boolean('offer_as_seasonal_lease')->default(false);
            $table->integer('minimum_stay_rentals')->default(1);
            $table->integer('minimum_stay_swaps')->default(1);
            $table->string('sharable_password')->nullable();
            $table->json('extra_info')->nullable();
        });

        Schema::create('indefinite_approvals', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('guest_id');
            $table->unsignedBigInteger('home_id')->index('indefinite_approvals_home_id_foreign');
            $table->timestamps();

            $table->unique(['guest_id', 'home_id']);
        });

        Schema::create('job_invitation_messages', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('job_invitation_id')->index();
            $table->unsignedBigInteger('sender_id')->nullable()->index();
            $table->text('content');
            $table->timestamps();
        });

        Schema::create('job_invitation_proposes', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('job_invitation_id')->index();
            $table->unsignedBigInteger('sender_id')->nullable()->index();
            $table->decimal('alternate_price')->default(0);
            $table->timestamp('alternate_start_time')->nullable();
            $table->json('extra_info')->nullable();
            $table->timestamps();
        });

        Schema::create('jobs', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('queue')->index();
            $table->longText('payload');
            $table->unsignedTinyInteger('attempts');
            $table->unsignedInteger('reserved_at')->nullable();
            $table->unsignedInteger('available_at');
            $table->unsignedInteger('created_at');
        });

        Schema::create('media', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('model_type');
            $table->unsignedBigInteger('model_id');
            $table->char('uuid', 36)->nullable()->unique();
            $table->string('collection_name');
            $table->string('name');
            $table->string('file_name');
            $table->string('mime_type')->nullable();
            $table->string('disk');
            $table->string('conversions_disk')->nullable();
            $table->unsignedBigInteger('size');
            $table->json('manipulations');
            $table->json('custom_properties');
            $table->json('generated_conversions');
            $table->json('responsive_images');
            $table->unsignedInteger('order_column')->nullable();
            $table->timestamps();

            $table->index(['model_type', 'model_id']);
        });

        Schema::create('messages', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('sender_id')->index('messages_v2_sender_id_foreign');
            $table->unsignedBigInteger('receiver_id')->index('messages_v2_receiver_id_foreign');
            $table->string('subject')->nullable();
            $table->text('body');
            $table->string('messageable_type')->nullable();
            $table->unsignedBigInteger('messageable_id')->nullable();
            $table->boolean('viewed')->default(false);
            $table->timestamps();
            $table->boolean('archived_by_sender')->default(false);
            $table->boolean('archived_by_receiver')->default(false);

            $table->index(['messageable_type', 'messageable_id'], 'messages_v2_messageable_type_messageable_id_index');
        });

        Schema::create('mycrew_categories', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('category_name', 200);
            $table->integer('sort_order')->default(0);
            $table->timestamps();
        });

        Schema::create('mycrew_jobs', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('job_title', 250);
            $table->dateTime('due_date');
            $table->text('details')->nullable();
            $table->string('payment_type', 100)->nullable();
            $table->float('payment_amount');
            $table->enum('priority_level', ['low', 'medium', 'high', 'none'])->default('low');
            $table->enum('job_status', ['pending', 'accepted', 'rejected', 'completed', 'cancelled'])->default('pending');
            $table->enum('payment_status', ['pending', 'completed', 'cancelled', 'refunded'])->default('pending');
            $table->string('vendor_task_status', 3)->nullable();
            $table->text('vendor_task_description')->nullable();
            $table->string('host_task_status', 3)->nullable();
            $table->text('host_task_description')->nullable();
            $table->dateTime('vendor_completion_date')->nullable();
            $table->dateTime('host_completion_date')->nullable();
            $table->unsignedBigInteger('vendor_id')->nullable()->index('mycrew_jobs_vendor_id_foreign');
            $table->unsignedBigInteger('vendor_user_id')->nullable()->index('mycrew_jobs_vendor_user_id_foreign');
            $table->unsignedBigInteger('category_id')->index('mycrew_jobs_category_id_foreign');
            $table->unsignedBigInteger('home_id')->index('mycrew_jobs_home_id_foreign');
            $table->unsignedBigInteger('user_id')->index('mycrew_jobs_user_id_foreign');
            $table->timestamps();
        });

        Schema::create('mycrew_vendor_invitations', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('email_id', 200);
            $table->string('unique_code', 200);
            $table->enum('status', ['pending', 'accepted', 'cancelled'])->default('pending');
            $table->unsignedBigInteger('vendor_user_id')->nullable()->index('mycrew_vendor_invitations_vendor_user_id_foreign');
            $table->unsignedBigInteger('user_id')->nullable()->index('mycrew_vendor_invitations_user_id_foreign');
            $table->timestamps();
        });

        Schema::create('notifications', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('type');
            $table->string('notifiable_type');
            $table->unsignedBigInteger('notifiable_id');
            $table->json('data');
            $table->timestamp('read_at')->nullable();
            $table->timestamps();

            $table->index(['notifiable_type', 'notifiable_id'], 'notifications_v2_notifiable_type_notifiable_id_index');
        });

        Schema::create('password_resets', function (Blueprint $table) {
            $table->string('email')->index();
            $table->string('token');
            $table->timestamp('created_at')->nullable();
        });

        Schema::create('reviews', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('booking_id');
            $table->unsignedBigInteger('reviewer_id')->index('reviews_reviewer_id_foreign');
            $table->unsignedBigInteger('reviewee_id')->index('reviews_reviewee_id_foreign');
            $table->unsignedBigInteger('home_id')->index('reviews_home_id_foreign');
            $table->unsignedTinyInteger('rating');
            $table->text('feedback')->nullable();
            $table->enum('type', ['guest_review', 'host_review']);
            $table->timestamps();

            $table->unique(['booking_id', 'reviewer_id', 'type']);
        });

        Schema::create('roles', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('name');
        });

        Schema::create('saved_home_categories', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('user_id')->index('saved_home_categories_user_id_foreign');
            $table->string('name');
            $table->string('slug')->unique();
            $table->timestamps();
        });

        Schema::create('service_job_invitations', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('service_job_id')->index();
            $table->unsignedBigInteger('service_provider_id')->index();
            $table->boolean('is_accepted')->default(false);
            $table->dateTime('alternate_start_time')->nullable();
            $table->decimal('alternate_price')->nullable();
            $table->string('slug')->unique();
            $table->json('extra_info')->nullable();
            $table->timestamps();
            $table->string('payment_key')->nullable();
        });

        Schema::create('service_jobs', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('title');
            $table->text('description')->nullable();
            $table->unsignedBigInteger('home_id')->index();
            $table->unsignedBigInteger('service_provider_id')->nullable()->index();
            $table->dateTime('start_time')->nullable();
            $table->dateTime('end_time')->nullable();
            $table->integer('status')->default(0)->index();
            $table->json('extra_info')->nullable();
            $table->unsignedBigInteger('created_by_id')->index();
            $table->string('slug')->unique();
            $table->decimal('price')->default(0);
            $table->timestamps();
        });

        Schema::create('service_providers', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('name');
            $table->string('type')->nullable();
            $table->string('email')->unique();
            $table->string('mobile_number')->nullable();
            $table->string('slug')->unique();
            $table->json('extra_info')->nullable();
            $table->unsignedBigInteger('created_by_id')->index();
            $table->timestamps();
            $table->string('stripe_connect_id')->nullable();
        });

        Schema::create('sharable_link_block_access', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('sharable_link_id');
            $table->unsignedBigInteger('user_id')->index('sharable_link_block_access_user_id_foreign');
            $table->timestamps();

            $table->unique(['sharable_link_id', 'user_id']);
        });

        Schema::create('sharable_links', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('home_id')->index('sharable_links_home_id_foreign');
            $table->string('link')->unique();
            $table->json('price_info')->nullable();
            $table->timestamps();
        });

        Schema::create('subscription_items', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('subscription_id');
            $table->string('stripe_id')->unique();
            $table->string('stripe_product');
            $table->string('stripe_price');
            $table->integer('quantity')->nullable();
            $table->timestamps();

            $table->index(['subscription_id', 'stripe_price']);
        });

        Schema::create('subscriptions', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('user_id');
            $table->string('type');
            $table->string('stripe_id')->unique();
            $table->string('stripe_status');
            $table->string('stripe_price')->nullable();
            $table->integer('quantity')->nullable();
            $table->timestamp('trial_ends_at')->nullable();
            $table->timestamp('ends_at')->nullable();
            $table->timestamps();

            $table->index(['user_id', 'stripe_status']);
        });

        Schema::create('temporary_uploads', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->timestamps();
        });

        Schema::create('users', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('first_name');
            $table->string('last_name')->nullable();
            $table->mediumText('about')->nullable();
            $table->string('email')->unique();
            $table->timestamp('email_verified_at')->nullable();
            $table->string('password');
            $table->integer('role_id')->default(1);
            $table->string('user_type')->default('traveler');
            $table->string('phone_number', 22)->nullable();
            $table->string('avatar')->nullable();
            $table->rememberToken();
            $table->timestamps();
            $table->string('shared_url')->nullable()->index();
            $table->string('canonical_url')->nullable();
            $table->string('host_logo')->nullable();
            $table->string('host_landing_page')->nullable();
            $table->timestamp('last_seen')->nullable();
            $table->string('stripe_id')->nullable()->index();
            $table->string('pm_type')->nullable();
            $table->string('pm_last_four', 4)->nullable();
            $table->timestamp('trial_ends_at')->nullable();
            $table->string('stripe_connect_id')->nullable();
            $table->json('extra_info')->nullable();
            $table->string('venmo_username')->nullable();
        });

        Schema::table('blocked_dates', function (Blueprint $table) {
            $table->foreign(['home_id'])->references(['id'])->on('homes')->onUpdate('no action')->onDelete('no action');
        });

        Schema::table('events', function (Blueprint $table) {
            $table->foreign(['events_categories_id'])->references(['id'])->on('events_categories')->onUpdate('cascade')->onDelete('cascade');
            $table->foreign(['home_id'])->references(['id'])->on('homes')->onUpdate('no action')->onDelete('cascade');
            $table->foreign(['ical_id'])->references(['id'])->on('events_ical_imports')->onUpdate('no action')->onDelete('cascade');
            $table->foreign(['user_id'])->references(['id'])->on('users')->onUpdate('no action')->onDelete('cascade');
        });

        Schema::table('events_ical_imports', function (Blueprint $table) {
            $table->foreign(['home_id'])->references(['id'])->on('homes')->onUpdate('no action')->onDelete('cascade');
            $table->foreign(['user_id'])->references(['id'])->on('users')->onUpdate('no action')->onDelete('cascade');
        });

        Schema::table('guest_lists', function (Blueprint $table) {
            $table->foreign(['guest_id'])->references(['id'])->on('users')->onUpdate('no action')->onDelete('no action');
            $table->foreign(['host_id'])->references(['id'])->on('users')->onUpdate('no action')->onDelete('no action');
        });

        Schema::table('home_saved_category', function (Blueprint $table) {
            $table->foreign(['home_id'])->references(['id'])->on('homes')->onUpdate('no action')->onDelete('cascade');
            $table->foreign(['saved_home_category_id'])->references(['id'])->on('saved_home_categories')->onUpdate('no action')->onDelete('cascade');
        });

        Schema::table('home_user', function (Blueprint $table) {
            $table->foreign(['home_id'])->references(['id'])->on('homes')->onUpdate('no action')->onDelete('cascade');
            $table->foreign(['user_id'])->references(['id'])->on('users')->onUpdate('no action')->onDelete('cascade');
        });

        Schema::table('indefinite_approvals', function (Blueprint $table) {
            $table->foreign(['guest_id'])->references(['id'])->on('users')->onUpdate('no action')->onDelete('no action');
            $table->foreign(['home_id'])->references(['id'])->on('homes')->onUpdate('no action')->onDelete('no action');
        });

        Schema::table('messages', function (Blueprint $table) {
            $table->foreign(['receiver_id'], 'messages_v2_receiver_id_foreign')->references(['id'])->on('users')->onUpdate('no action')->onDelete('cascade');
            $table->foreign(['sender_id'], 'messages_v2_sender_id_foreign')->references(['id'])->on('users')->onUpdate('no action')->onDelete('cascade');
        });

        Schema::table('mycrew_jobs', function (Blueprint $table) {
            $table->foreign(['category_id'])->references(['id'])->on('mycrew_categories')->onUpdate('no action')->onDelete('cascade');
            $table->foreign(['home_id'])->references(['id'])->on('homes')->onUpdate('no action')->onDelete('cascade');
            $table->foreign(['user_id'])->references(['id'])->on('users')->onUpdate('no action')->onDelete('cascade');
            $table->foreign(['vendor_id'])->references(['id'])->on('mycrew_vendor_invitations')->onUpdate('no action')->onDelete('cascade');
            $table->foreign(['vendor_user_id'])->references(['id'])->on('users')->onUpdate('no action')->onDelete('set null');
        });

        Schema::table('mycrew_vendor_invitations', function (Blueprint $table) {
            $table->foreign(['user_id'])->references(['id'])->on('users')->onUpdate('no action')->onDelete('cascade');
            $table->foreign(['vendor_user_id'])->references(['id'])->on('users')->onUpdate('no action')->onDelete('set null');
        });

        Schema::table('reviews', function (Blueprint $table) {
            $table->foreign(['booking_id'])->references(['id'])->on('bookings')->onUpdate('no action')->onDelete('cascade');
            $table->foreign(['home_id'])->references(['id'])->on('homes')->onUpdate('no action')->onDelete('cascade');
            $table->foreign(['reviewee_id'])->references(['id'])->on('users')->onUpdate('no action')->onDelete('cascade');
            $table->foreign(['reviewer_id'])->references(['id'])->on('users')->onUpdate('no action')->onDelete('cascade');
        });

        Schema::table('saved_home_categories', function (Blueprint $table) {
            $table->foreign(['user_id'])->references(['id'])->on('users')->onUpdate('no action')->onDelete('cascade');
        });

        Schema::table('sharable_link_block_access', function (Blueprint $table) {
            $table->foreign(['sharable_link_id'])->references(['id'])->on('sharable_links')->onUpdate('no action')->onDelete('cascade');
            $table->foreign(['user_id'])->references(['id'])->on('users')->onUpdate('no action')->onDelete('cascade');
        });

        Schema::table('sharable_links', function (Blueprint $table) {
            $table->foreign(['home_id'])->references(['id'])->on('homes')->onUpdate('no action')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('sharable_links', function (Blueprint $table) {
            $table->dropForeign('sharable_links_home_id_foreign');
        });

        Schema::table('sharable_link_block_access', function (Blueprint $table) {
            $table->dropForeign('sharable_link_block_access_sharable_link_id_foreign');
            $table->dropForeign('sharable_link_block_access_user_id_foreign');
        });

        Schema::table('saved_home_categories', function (Blueprint $table) {
            $table->dropForeign('saved_home_categories_user_id_foreign');
        });

        Schema::table('reviews', function (Blueprint $table) {
            $table->dropForeign('reviews_booking_id_foreign');
            $table->dropForeign('reviews_home_id_foreign');
            $table->dropForeign('reviews_reviewee_id_foreign');
            $table->dropForeign('reviews_reviewer_id_foreign');
        });

        Schema::table('mycrew_vendor_invitations', function (Blueprint $table) {
            $table->dropForeign('mycrew_vendor_invitations_user_id_foreign');
            $table->dropForeign('mycrew_vendor_invitations_vendor_user_id_foreign');
        });

        Schema::table('mycrew_jobs', function (Blueprint $table) {
            $table->dropForeign('mycrew_jobs_category_id_foreign');
            $table->dropForeign('mycrew_jobs_home_id_foreign');
            $table->dropForeign('mycrew_jobs_user_id_foreign');
            $table->dropForeign('mycrew_jobs_vendor_id_foreign');
            $table->dropForeign('mycrew_jobs_vendor_user_id_foreign');
        });

        Schema::table('messages', function (Blueprint $table) {
            $table->dropForeign('messages_v2_receiver_id_foreign');
            $table->dropForeign('messages_v2_sender_id_foreign');
        });

        Schema::table('indefinite_approvals', function (Blueprint $table) {
            $table->dropForeign('indefinite_approvals_guest_id_foreign');
            $table->dropForeign('indefinite_approvals_home_id_foreign');
        });

        Schema::table('home_user', function (Blueprint $table) {
            $table->dropForeign('home_user_home_id_foreign');
            $table->dropForeign('home_user_user_id_foreign');
        });

        Schema::table('home_saved_category', function (Blueprint $table) {
            $table->dropForeign('home_saved_category_home_id_foreign');
            $table->dropForeign('home_saved_category_saved_home_category_id_foreign');
        });

        Schema::table('guest_lists', function (Blueprint $table) {
            $table->dropForeign('guest_lists_guest_id_foreign');
            $table->dropForeign('guest_lists_host_id_foreign');
        });

        Schema::table('events_ical_imports', function (Blueprint $table) {
            $table->dropForeign('events_ical_imports_home_id_foreign');
            $table->dropForeign('events_ical_imports_user_id_foreign');
        });

        Schema::table('events', function (Blueprint $table) {
            $table->dropForeign('events_events_categories_id_foreign');
            $table->dropForeign('events_home_id_foreign');
            $table->dropForeign('events_ical_id_foreign');
            $table->dropForeign('events_user_id_foreign');
        });

        Schema::table('blocked_dates', function (Blueprint $table) {
            $table->dropForeign('blocked_dates_home_id_foreign');
        });

        Schema::dropIfExists('users');

        Schema::dropIfExists('temporary_uploads');

        Schema::dropIfExists('subscriptions');

        Schema::dropIfExists('subscription_items');

        Schema::dropIfExists('sharable_links');

        Schema::dropIfExists('sharable_link_block_access');

        Schema::dropIfExists('service_providers');

        Schema::dropIfExists('service_jobs');

        Schema::dropIfExists('service_job_invitations');

        Schema::dropIfExists('saved_home_categories');

        Schema::dropIfExists('roles');

        Schema::dropIfExists('reviews');

        Schema::dropIfExists('password_resets');

        Schema::dropIfExists('notifications');

        Schema::dropIfExists('mycrew_vendor_invitations');

        Schema::dropIfExists('mycrew_jobs');

        Schema::dropIfExists('mycrew_categories');

        Schema::dropIfExists('messages');

        Schema::dropIfExists('media');

        Schema::dropIfExists('jobs');

        Schema::dropIfExists('job_invitation_proposes');

        Schema::dropIfExists('job_invitation_messages');

        Schema::dropIfExists('indefinite_approvals');

        Schema::dropIfExists('homes');

        Schema::dropIfExists('home_user');

        Schema::dropIfExists('home_saved_category');

        Schema::dropIfExists('home_custom_nightly_rates');

        Schema::dropIfExists('home_availables');

        Schema::dropIfExists('guest_lists');

        Schema::dropIfExists('failed_jobs');

        Schema::dropIfExists('events_ical_imports');

        Schema::dropIfExists('events_categories');

        Schema::dropIfExists('events');

        Schema::dropIfExists('bookings');

        Schema::dropIfExists('blocked_dates');

        Schema::dropIfExists('action_tokens');
    }
};
