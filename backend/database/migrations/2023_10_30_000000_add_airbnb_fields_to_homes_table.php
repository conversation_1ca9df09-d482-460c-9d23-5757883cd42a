<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('homes', function (Blueprint $table) {
            $table->string('airbnb_url')->nullable()->after('ical_url');
            $table->boolean('is_from_airbnb')->default(false)->after('airbnb_url');
            $table->string('photo_processing_status')->nullable()->after('is_from_airbnb');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('homes', function (Blueprint $table) {
            $table->dropColumn('airbnb_url');
            $table->dropColumn('is_from_airbnb');
            $table->dropColumn('photo_processing_status');
        });
    }
};
