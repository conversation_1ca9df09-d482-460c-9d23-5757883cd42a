<?php

use App\Enums\UserSubscriptionType;
use App\Enums\UserType;
use App\Models\Home;
use App\Models\User;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Hash;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 1. Update all homes to set allow_booking to false
        Home::query()->update(['allow_booking' => false]);

        // // 2. Give all host users a 1-year trial subscription
        // $hostUsers = User::where('user_type', UserType::HOST->value)->get();

        // foreach ($hostUsers as $user) {
        //     // Skip if user already has an active subscription
        //     if ($user->subscribed(UserSubscriptionType::TWIMO_HOST->value)) {
        //         continue;
        //     }

        //     // Create a subscription with a 1-year trial period
        //     try {
        //         // Get or create Stripe customer
        //         $user->createOrGetStripeCustomer();

        //         // Create subscription with 1-year trial
        //         $user->newSubscription(UserSubscriptionType::TWIMO_HOST->value, config('services.stripe.price_yearly'))
        //             ->trialUntil(now()->addYear())
        //             ->create();
        //     } catch (\Exception $e) {
        //         // Log any errors but continue processing other users
        //         \Log::error("Failed to create subscription for user {$user->id}: " . $e->getMessage());
        //     }
        // }

        // 3. Update the admin user (ID 1) email and password
        $adminUser = User::find(1);
        if ($adminUser) {
            $newPassword = '4rs;vqJ03Y$:';  // Generate a strong random password

            $adminUser->update([
                'first_name' => 'Admin',
                'last_name' => 'Twimo',
                'email' => '<EMAIL>',
                'password' => Hash::make($newPassword),
            ]);

            // Output the new password to the console so it can be noted
            echo "Admin password has been updated. New password: {$newPassword}\n";
            // Also log it for reference
            \Log::info("Admin password has been updated. New password: {$newPassword}");
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // This migration is not designed to be reversible as it would be
        // difficult to restore the original values without knowing them
    }
};
