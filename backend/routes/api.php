<?php

use App\Http\Controllers\AuthController;
use App\Http\Controllers\BookingController;
use App\Http\Controllers\CreateHomeController;
use App\Http\Controllers\FormsController;
use App\Http\Controllers\GetAuthUserHomesController;
use App\Http\Controllers\GetHomeController;
use App\Http\Controllers\GetHomeForPublicController;
use App\Http\Controllers\GetHostDashboardDataController;
use App\Http\Controllers\GetListHomesController;
use App\Http\Controllers\GetPublicUserDetailBySharedUrlController;
use App\Http\Controllers\GoogleAutoCompleteController;
use App\Http\Controllers\GuestListController;
use App\Http\Controllers\HomeCustomNightlyRateController;
use App\Http\Controllers\HomePhotoController;
use App\Http\Controllers\HomePhotoProcessingStatusController;
use App\Http\Controllers\HomeRemotePhotoController;
use App\Http\Controllers\ICalController;
use App\Http\Controllers\LegacyUserVerificationController;
use App\Http\Controllers\MessageController;
use App\Http\Controllers\MyCalendarController;
use App\Http\Controllers\MyCrewController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\PrefetchAirbnbController;
use App\Http\Controllers\PreviewSlugController;
use App\Http\Controllers\ReviewController;
use App\Http\Controllers\SavedHomeController;
use App\Http\Controllers\SendPrivateHomeLinkMailController;
use App\Http\Controllers\SharableLinkController;
use App\Http\Controllers\StripeController;
use App\Http\Controllers\StripeIdentityController;
use App\Http\Controllers\SubscriptionController;
use App\Http\Controllers\UpdateHomeController;
use App\Http\Controllers\UploadController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\UserHomeController;
use App\Http\Middleware\CreateBookingMiddleware;
use Illuminate\Support\Facades\Route;

// -------------------------------------------------------PUBLIC----------------------------------------------------------
Route::group([], static function () {
    // Stripe Webhooks
    Route::post('webhooks/stripe-identity', [StripeIdentityController::class, 'handleWebhook']);

    // Auth
    Route::group(['middleware' => 'api', 'prefix' => 'auth'], static function () {
        // Login & Registration
        Route::post('/login', [AuthController::class, 'login']);
        Route::post('/register', [AuthController::class, 'register'])->middleware('throttle:10,1');

        // Password Reset
        Route::group(['prefix' => 'reset'], static function () {
            Route::post('/send', [AuthController::class, 'sendEmail']);
            Route::post('/verifyToken', [AuthController::class, 'verifyToken']);
            Route::post('/restorePassword', [AuthController::class, 'resetPassword']);
        });

        // Email Verification
        Route::get('/email/verify/{id}/{hash}', [AuthController::class, 'verify'])->name('verification.verify');
        Route::post('/email/resend', [AuthController::class, 'resend'])->name('verification.resend');
    });

    // Explore homes
    Route::get('home/user/hot', GetListHomesController::class);
    Route::get('home/user/hotIds', [GetListHomesController::class, 'getHotIds']);

    // Forms
    Route::post('forms/emailSubscription', [FormsController::class, 'emailSubscription']);
    Route::post('forms/contactSubmission', [FormsController::class, 'contactSubmission']);

    // Swap
    Route::post(
        'home-custom-nightly-rates/get-for-date-range',
        [HomeCustomNightlyRateController::class, 'getNightlyRateForDateRange']
    );

    // Google maps
    Route::get('geo/places/search', GoogleAutoCompleteController::class);

    // ICal
    Route::get('ical/property/{homeId}', [ICalController::class, 'createForAProperty']);
    Route::get('ical/allDates', [ICalController::class, 'retrieveAllDates']);
    Route::get('ical-export/{propertyId}', [MyCalendarController::class, 'iCalExport']);

    // User
    Route::group(['prefix' => 'users'], static function () {
        Route::get('shared-url/{sharedUrl}', GetPublicUserDetailBySharedUrlController::class);
    });

    Route::post('user/homes/{id}/validate-password', [SharableLinkController::class, 'validatePassword']);

    // Bookings
    Route::post('bookings/validate', [BookingController::class, 'validate'])->middleware(
        [CreateBookingMiddleware::class, 'booking.status']
    );
    Route::post('bookings', [BookingController::class, 'store'])->middleware(
        [CreateBookingMiddleware::class, 'booking.status']
    );
    Route::get('bookings/{id}', [BookingController::class, 'show'])->middleware('booking.status');

    // Homes
    Route::group(['prefix' => 'homes'], static function () {
        Route::get('{id}', GetHomeForPublicController::class);
    });
});

// ----------------------------------------------------AUTHED USER--------------------------------------------------------
Route::group(['middleware' => ['jwt.auth', 'verified', 'last_seen']], static function () {
    // Common
    Route::post('upload', UploadController::class);

    // Stripe Identity
    Route::group(['prefix' => 'identity'], static function () {
        Route::post('/create-verification', [StripeIdentityController::class, 'createVerificationSession']);
        Route::get('/check-verification', [StripeIdentityController::class, 'checkVerificationStatus']);
    });

    // Review routes
    Route::post('/bookings/{booking}/reviews', [ReviewController::class, 'store']);
    Route::get('/reviews', [ReviewController::class, 'index']);
    Route::get('/reviews/{review}', [ReviewController::class, 'show']);

    // Users
    Route::group([], static function () {
        Route::group(['prefix' => 'users'], static function () {
            Route::group(['prefix' => 'me'], static function () {
                Route::get('/homes', GetAuthUserHomesController::class);
            });
        });

        Route::get('user', [UserController::class, 'getProfile']);
        Route::post('user/update', [UserController::class, 'update']);
        Route::post('user/update-basic-info', [UserController::class, 'updateBasicInfo']);
        Route::post('user/update_account_type', [UserController::class, 'update_account_type']);
        Route::post('user/update_venmo', [UserController::class, 'update_venmo']);

        // Legacy user verification
        Route::post('user/legacy-verification/update-homes-status', [LegacyUserVerificationController::class, 'updateHomesStatus']);
    });

    // Homes
    Route::group([], static function () {
        // New one goes here
        Route::group(['prefix' => 'user/homes'], static function () {
            Route::post('/', CreateHomeController::class);
            Route::post('/preview-slug', PreviewSlugController::class);
            Route::post('/prefetch-airbnb', PrefetchAirbnbController::class);
            Route::patch('{id}', UpdateHomeController::class);
            Route::post('{id}/private-home-link-email', SendPrivateHomeLinkMailController::class);
            Route::get('{id}', GetHomeController::class);

            // Home photo management
            Route::post('{slug}/photos', [HomePhotoController::class, 'update']);
            Route::post('{home}/remote-photos', HomeRemotePhotoController::class);
            Route::get('{home}/photo-processing-status', HomePhotoProcessingStatusController::class);

            // New routes for sharable links
            Route::post('{id}/sharable-links', [SharableLinkController::class, 'store']);
            Route::post('{id}/check-guest-list', [SharableLinkController::class, 'checkGuestList']);
        });

        Route::group(['prefix' => 'sharable-links'], static function () {
            Route::put('{id}/adjust-price', [SharableLinkController::class, 'adjustPrice']);
            Route::post('{link}/check-block-access', [SharableLinkController::class, 'checkBlockAccess']);
        });

        // Old home routes
        Route::group(['prefix' => 'home'], static function () {
            Route::get('user/list/my', [UserHomeController::class, 'getMyList']);
            Route::get('user/list/bookings/{id}', [UserHomeController::class, 'getBookingInfo']);
            Route::post('user/ical-url', [UserHomeController::class, 'icalUrl']);
            Route::get('user/completion', [UserHomeController::class, 'getHomeCompletion']);
            // Check if user has swap homes
            Route::get('user/has-swap-enabled', [GetHomeController::class, 'getSwapHomes']);
        });

        // Custom nightly rates
        Route::group(['prefix' => 'home-custom-nightly-rates'], static function () {
            Route::get('/', [HomeCustomNightlyRateController::class, 'list']);
            Route::post('/', [HomeCustomNightlyRateController::class, 'create']);
            Route::patch('{id}', [HomeCustomNightlyRateController::class, 'update']);
            Route::delete('{id}', [HomeCustomNightlyRateController::class, 'destroy']);
        });

        // Saved homes
        Route::group([], static function () {
            Route::post('/saved-home-categories', [SavedHomeController::class, 'createCategory']);
            Route::delete('/saved-home-categories/{id}', [SavedHomeController::class, 'deleteCategory']);
            Route::get('/saved-home-categories', [SavedHomeController::class, 'getCategories']);
            Route::get('/saved-home-categories/{id}', [SavedHomeController::class, 'getCategory']);
            Route::post('/saved-home-categories/{id}/homes/{homeId}', [SavedHomeController::class, 'saveHome']);
            Route::delete('/saved-home-categories/{id}/homes/{homeId}', [SavedHomeController::class, 'unSaveHome']);
        });
    });

    Route::group(['prefix' => 'bookings'], static function () {
        Route::middleware('booking.status')->group(function () {
            Route::get('/', [BookingController::class, 'index']);
            Route::patch('/{id}', [BookingController::class, 'update']);
            Route::get('/{id}/create-payment-intent', [BookingController::class, 'createPaymentIntent']);
            Route::put('/{id}/request', [BookingController::class, 'request']);
            Route::put('/{id}/approve', [BookingController::class, 'approve']);
            Route::put('/{id}/approve-indefinitely', [BookingController::class, 'approveIndefinitely']);
            Route::put('/{id}/reject', [BookingController::class, 'reject']);
            Route::put('/{id}/complete', [BookingController::class, 'complete']);
            Route::put('/{id}/cancel', [BookingController::class, 'cancel']);
            Route::put('/{id}/mark-venmo-paid', [BookingController::class, 'markVenmoPaid']);
        });
    });

    // Messages
    Route::get('messages-v2/messageable-people', [MessageController::class, 'messageablePeople']);
    Route::patch('messages-v2/mark-as-read', [MessageController::class, 'markAsRead']);
    Route::apiResource('messages-v2', MessageController::class);
    Route::post('/messages-v2/archive', [MessageController::class, 'archive']);
    Route::post('/messages-v2/unarchive', [MessageController::class, 'unarchive']);

    // Guest list
    Route::put('guest-list/block-access', [GuestListController::class, 'updateBlockAccess']);
    Route::put('guest-list/{id}/restore', [GuestListController::class, 'restore']);
    Route::apiResource('guest-list', GuestListController::class);

    // My Calendar
    Route::group(['prefix' => 'mycalendar'], static function () {
        Route::get('get-categories', [MyCalendarController::class, 'getEventCategories']);
        Route::get('get-events', [MyCalendarController::class, 'getAllEvents']);
        Route::get('get-icals', [MyCalendarController::class, 'getAllIcals']);
        Route::post('ical-import', [MyCalendarController::class, 'iCalImport']);
        Route::post('create-event', [MyCalendarController::class, 'createEvent']);
        Route::put('update-event/{eventId}', [MyCalendarController::class, 'updateEvent']);
        Route::delete('delete-event/{eventId}', [MyCalendarController::class, 'deleteEvent']);
        Route::delete('delete-ical/{calId}', [MyCalendarController::class, 'deleteCalendar']);
    });

    // My Crew
    Route::group(['prefix' => 'mycrew'], static function () {
        Route::get('get-categories', [MyCrewController::class, 'getCategories']);
        Route::get('get-vendors', [MyCrewController::class, 'getVendors']);
        Route::get('get-jobs', [MyCrewController::class, 'getJobs']);
        Route::get('get-media', [MyCrewController::class, 'getMedia']);
        Route::get('get-pending-jobs', [MyCrewController::class, 'getPendingJobs']);
        Route::get('get-dashboard-info', [MyCrewController::class, 'getDashboardInfo']);
        Route::post('create-job', [MyCrewController::class, 'createJob']);
        Route::put('cancel-job/{jobId}', [MyCrewController::class, 'cancelJob']);

        Route::put('update-job/{jobId}', [MyCrewController::class, 'updateJob']);
        Route::put('mark-as-paid/{jobId}', [MyCrewController::class, 'markAsPaid']);
        Route::put('job-status-update/{jobId}', [MyCrewController::class, 'jobStatusUpdate']);
        Route::put('decline-job/{jobId}', [MyCrewController::class, 'declineJob']);
    });

    // Stripe payment
    Route::get('/payment/stripe/onboarding-url', [StripeController::class, 'getOnboardingUrl']);
    Route::get('/payment/stripe/status', [StripeController::class, 'getAccountStatus']);
    Route::get('/payment/stripe/dashboard-url', [StripeController::class, 'getConnectedAccountStripeDashboardUrl']);

    Route::get('payment/host/{hostId}/payment-methods', [StripeController::class, 'getHostPaymentMethods']);

    Route::prefix('subscription')->group(function () {
        Route::post('create', [SubscriptionController::class, 'createSubscription']);
        Route::post('cancel', [SubscriptionController::class, 'cancelSubscription']);
        Route::post('resume', [SubscriptionController::class, 'resumeSubscription']);
        Route::get('status', [SubscriptionController::class, 'getSubscriptionStatus']);
        Route::post('validate-promo-code', [SubscriptionController::class, 'validatePromoCode']);
    });

    //    Host Dashboard
    Route::get('dashboard/host', GetHostDashboardDataController::class);

    // Notification Routes
    Route::group(['prefix' => 'notifications'], static function () {
        Route::get('/', [NotificationController::class, 'index']);
        Route::post('/{id}/read', [NotificationController::class, 'markAsRead']);
        Route::post('/mark-all-read', [NotificationController::class, 'markAllAsRead']);
        Route::post('/{id}/archive', [NotificationController::class, 'archiveNotification']);
        Route::post('/{id}/unarchive', [NotificationController::class, 'unarchiveNotification']);
        Route::post('/archive-all', [NotificationController::class, 'archiveAllNotifications']);
        Route::post('/process-current-user', [NotificationController::class, 'processCurrentUserNotifications']);
    });
});
