<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */
    'adminEmail' => env('ADMIN_EMAIL', '<EMAIL>'),

    'frontendUrl' => env('FRONT_URL', 'https://twimo.com'),

    'media' => [
        'uploadDisk' => env('UPLOAD_DISK', 's3'),
    ],

    'mailgun' => [
        'domain' => env('MAILGUN_DOMAIN'),
        'secret' => env('MAILGUN_SECRET'),
        'endpoint' => env('MAILGUN_ENDPOINT', 'api.mailgun.net'),
    ],

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'stripe' => [
        'secret' => env('STRIPE_SECRET'),
        'connect_onboarding_refresh_url' => env('STRIPE_CONNECT_ONBOARDING_REFRESH_URL'),
        'connect_onboarding_return_url' => env('STRIPE_CONNECT_ONBOARDING_RETURN_URL'),
        'webhook_secret' => env('STRIPE_WEBHOOK_SECRET'),
        'application_fee_amount_fixed' => env('STRIPE_APPLICATION_FEE_AMOUNT_FIXED', 0),
        'application_fee_amount_percent' => env('STRIPE_APPLICATION_FEE_AMOUNT_PERCENT', 2),
        'price_monthly' => env('STRIPE_PRICE_MONTHLY'),
        'price_yearly' => env('STRIPE_PRICE_YEARLY'),
        'promo_codes' => [
            'TWIMO30' => env('STRIPE_COUPON_TWIMO30', ''),
            'FRIENDS' => env('STRIPE_COUPON_FRIENDS', ''),
        ],
        'promo_discounts' => [
            'TWIMO30' => env('STRIPE_DISCOUNT_TWIMO30', 30),
            'FRIENDS' => env('STRIPE_DISCOUNT_FRIENDS', 100),
        ],
        'yearly_price_amount' => env('STRIPE_YEARLY_PRICE_AMOUNT', 144),
    ],

    'recaptcha' => [
        'secret_key' => env('RECAPTCHA_SECRET_KEY', '6LdBNc8qAAAAADGOCqGe5ht1dtjSReISE5uqgyf1'),
    ],

    'mailchimp' => [
        'baseUrl' => env('MAILCHIMP_BASE_URL', 'us6'),
        'apiKey' => env('MAILCHIMP_API_KEY', '************************************'),
        'audienceId' => env('MAILCHIMP_AUDIENCE_ID', '66abeb1a8e'),
    ],

    'brevo' => [
        'api_key' => env('BREVO_KEY', ''),
        'list_id' => env('BREVO_LIST_ID', 12),
    ],

    'google' => [
        'apiKey' => env('GOOGLE_API_KEY', 'AIzaSyDZxV0b44NqAABTCqyBiOG4QUhZoYORr2c'),
        'geoCodeTextSearchUrl' => env(
            'GOOGLE_GEO_CODE_TEXT_SEARCH_URL',
            'https://maps.google.com/maps/api/geocode/json'
        ),
    ],

    'aws' => [
        'accessKey' => env('AWS_ACCESS_KEY_ID'),
        'secretKey' => env('AWS_SECRET_ACCESS_KEY'),
    ],

    'geoForAllHomes' => [
        'endpoint' => env(
            'GEO_FOR_ALL_HOMES_ENDPOINT',
            'https://6wrsig04e5.execute-api.us-east-1.amazonaws.com/v1/query?lat=-18.282211332597548&lng=-144.46077390551758&zoom=1&filter=1000'
        ),
    ],

    'airbnb' => [
        'apiKey' => env('AIRBNB_API_KEY', '**********************************************'),
    ],

    'truvi' => [
        'subscription_key' => env('TRUVI_SUBSCRIPTION_KEY', ''),
        'environment' => env('TRUVI_ENVIRONMENT', ''),
        'endpoint' => env('TRUVI_ENDPOINT', ''),
    ],

];
