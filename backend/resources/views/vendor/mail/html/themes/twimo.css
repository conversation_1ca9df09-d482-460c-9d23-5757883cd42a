/* Base */

body,
body *:not(html):not(style):not(br):not(tr):not(code) {
    box-sizing: border-box;
    font-family: 'Figtree', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif,
        'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol';
    position: relative;
}

body {
    -webkit-text-size-adjust: none;
    background-color: #ffffff;
    color: #4a4a4a;
    height: 100%;
    line-height: 1.5;
    margin: 0;
    padding: 0;
    width: 100% !important;
}

p,
ul,
ol,
blockquote {
    line-height: 1.5;
    text-align: left;
}

a {
    color: #672093;
}

a img {
    border: none;
}

/* Typography */

h1 {
    color: #333333;
    font-size: 20px;
    font-weight: bold;
    margin-top: 0;
    text-align: left;
}

h2 {
    font-size: 18px;
    font-weight: bold;
    margin-top: 0;
    text-align: left;
}

h3 {
    font-size: 16px;
    font-weight: bold;
    margin-top: 0;
    text-align: left;
}

p {
    font-size: 16px;
    line-height: 1.5em;
    margin-top: 0;
    text-align: left;
}

p.sub {
    font-size: 13px;
}

img {
    max-width: 100%;
}

/* Layout */

.wrapper {
    -premailer-cellpadding: 0;
    -premailer-cellspacing: 0;
    -premailer-width: 100%;
    background-color: #f5f5f5;
    margin: 0;
    padding: 0;
    width: 100%;
}

.content {
    -premailer-cellpadding: 0;
    -premailer-cellspacing: 0;
    -premailer-width: 100%;
    margin: 0;
    padding: 0;
    width: 100%;
}

/* Header */

.header {
    padding: 25px 0;
    text-align: center;
}

.header a {
    color: #333333;
    font-size: 19px;
    font-weight: bold;
    text-decoration: none;
}

/* Logo */

.logo {
    height: auto;
    max-height: 60px;
    width: auto;
    max-width: 200px;
}

/* Body */

.body {
    -premailer-cellpadding: 0;
    -premailer-cellspacing: 0;
    -premailer-width: 100%;
    background-color: #f5f5f5;
    border-bottom: 1px solid #f5f5f5;
    border-top: 1px solid #f5f5f5;
    margin: 0;
    padding: 0;
    width: 100%;
}

.inner-body {
    -premailer-cellpadding: 0;
    -premailer-cellspacing: 0;
    -premailer-width: 570px;
    background-color: #ffffff;
    border-color: #e8e5ef;
    border-radius: 12px;
    border-width: 1px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
    margin: 0 auto;
    padding: 0;
    width: 570px;
}

.inner-body a {
    word-break: break-all;
}

/* Subcopy */

.subcopy {
    border-top: 1px solid #e8e5ef;
    margin-top: 25px;
    padding-top: 25px;
}

.subcopy p {
    font-size: 14px;
}

/* Footer */

.footer {
    -premailer-cellpadding: 0;
    -premailer-cellspacing: 0;
    -premailer-width: 570px;
    margin: 0 auto;
    padding: 0;
    text-align: center;
    width: 570px;
}

.footer p {
    color: #888888;
    font-size: 12px;
    text-align: center;
}

.footer a {
    color: #672093;
    text-decoration: underline;
}

/* Tables */

.table table {
    -premailer-cellpadding: 0;
    -premailer-cellspacing: 0;
    -premailer-width: 100%;
    margin: 30px auto;
    width: 100%;
}

.table th {
    border-bottom: 1px solid #edeff2;
    margin: 0;
    padding-bottom: 8px;
}

.table td {
    color: #4a4a4a;
    font-size: 15px;
    line-height: 18px;
    margin: 0;
    padding: 10px 0;
}

.content-cell {
    max-width: 100vw;
    padding: 32px;
}

/* Buttons */

.action {
    -premailer-cellpadding: 0;
    -premailer-cellspacing: 0;
    -premailer-width: 100%;
    margin: 30px auto;
    padding: 0;
    text-align: center;
    width: 100%;
    float: unset;
}

.button {
    -webkit-text-size-adjust: none;
    border-radius: 50px;
    color: #fff;
    display: inline-block;
    overflow: hidden;
    text-decoration: none;
    font-weight: 600;
    box-shadow: 0 4px 6px rgba(112, 25, 153, 0.2);
}

.button-blue,
.button-primary {
    background: linear-gradient(90deg, #672093 0%, #7c0cb1 100%);
    border-bottom: 8px solid #672093;
    border-left: 18px solid #672093;
    border-right: 18px solid #672093;
    border-top: 8px solid #672093;
}

.button-green,
.button-success {
    background-color: #4CAF50;
    border-bottom: 8px solid #4CAF50;
    border-left: 18px solid #4CAF50;
    border-right: 18px solid #4CAF50;
    border-top: 8px solid #4CAF50;
}

.button-red,
.button-error {
    background-color: #FF5252;
    border-bottom: 8px solid #FF5252;
    border-left: 18px solid #FF5252;
    border-right: 18px solid #FF5252;
    border-top: 8px solid #FF5252;
}

/* Panels */

.panel {
    border-left: #672093 solid 4px;
    margin: 21px 0;
}

.panel-content {
    background-color: #f8f4fb;
    color: #4a4a4a;
    padding: 16px;
}

.panel-content p {
    color: #4a4a4a;
}

.panel-item {
    padding: 0;
}

.panel-item p:last-of-type {
    margin-bottom: 0;
    padding-bottom: 0;
}

/* Utilities */

.break-all {
    word-break: break-all;
}

/* Steps */
ol {
    padding-left: 20px;
}

ol li {
    margin-bottom: 10px;
}

/* Links */
.link-highlight {
    background-color: #f8f4fb;
    padding: 12px;
    border-radius: 8px;
    margin: 15px 0;
    display: block;
    text-align: center;
}
