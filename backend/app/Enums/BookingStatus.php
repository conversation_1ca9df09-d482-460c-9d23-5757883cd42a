<?php

namespace App\Enums;

enum BookingStatus: string
{
    case CREATED = 'created'; // Guest created booking
    case REQUESTED = 'requested'; // Guest send request to host

    case ACCEPTED = 'accepted'; // Host accepted request
    case REJECTED = 'rejected'; // Host rejected request
    case CANCELED = 'canceled'; // Guest canceled request
    case COMPLETED = 'completed'; // Booking completed

    // Old status
    case PENDING = 'pending'; // Booking is pending
}
