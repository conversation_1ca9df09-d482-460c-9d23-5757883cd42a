<?php

namespace App\Mail;

use App\Models\Home;
use App\Models\SharableLink;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

final class PrivateHomeLinkEmail extends Mailable
{
    use Queueable, SerializesModels;

    public function __construct(
        private readonly Home $home,
        private readonly SharableLink $sharableLink,
    ) {}

    public function build(): self
    {
        $hostName = $this->home->user->first_name;
        $password = $this->home->sharable_password;
        $link = config('services.frontendUrl').'/'.$this->home->slug.'?sharable_link='.$this->sharableLink->link;

        return $this->markdown('emails.home.privateHomeLink')
            ->subject("Exclusive Access: Family & Friends Booking Link from $hostName")
            ->with([
                'hostName' => $hostName,
                'link' => $link,
                'password' => $password,
            ]);
    }
}
