<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

final class ContactSubmission extends Mailable
{
    use Queueable, SerializesModels;

    public string $message;

    public string $subj;

    public string $email;

    public string $fname;

    public function __construct(string $subject, string $message, string $email, string $fname)
    {
        $this->subject = $subject;
        $this->subj = $subject;
        $this->message = $message;
        $this->email = $email;
        $this->fname = $fname;
    }

    public function build(): ContactSubmission
    {
        return $this->markdown('emails.contactSubmission');
    }
}
