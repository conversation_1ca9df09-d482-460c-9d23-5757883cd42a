<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

final class VendorInvite extends Mailable
{
    use Queueable, SerializesModels;

    public $subject = 'Your vendor account sign up link';

    public mixed $data;

    public function __construct($data)
    {
        $this->data = $data;
    }

    public function build(): VendorInvite
    {
        return $this->markdown('emails.vendorInvite')->with('data', $this->data);
    }
}
