<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

final class NewsletterSignup extends Mailable
{
    use Queueable, SerializesModels;

    public string $message;

    public string $subj;

    public string $email;

    public function __construct(string $subject, string $message, string $email)
    {
        $this->subject = $subject;
        $this->subj = $subject;
        $this->message = $message;
        $this->email = $email;
    }

    public function build(): NewsletterSignup
    {
        return $this->markdown('emails.newsletterSignup');
    }
}
