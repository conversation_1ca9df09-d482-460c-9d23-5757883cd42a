<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

final class ResetPassword extends Mailable
{
    use Queueable, SerializesModels;

    public $subject = 'Password reset confirmation';

    public string $token;

    public function __construct(string $token)
    {
        $this->token = $token;
    }

    public function build(): ResetPassword
    {
        return $this->markdown('emails.auth.passwordReset');
    }
}
