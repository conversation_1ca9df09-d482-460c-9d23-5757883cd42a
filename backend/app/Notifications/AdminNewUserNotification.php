<?php

namespace App\Notifications;

use App\Models\Notification as NotificationModel;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;

class AdminNewUserNotification extends BaseNotification implements ShouldQueue
{
    use Queueable;

    protected User $newUser;

    /**
     * Create a new notification instance.
     */
    public function __construct(User $user)
    {
        $this->newUser = $user;

        parent::__construct(
            title: 'New User Registration',
            message: 'A new user has registered on Twimo: '.$user->first_name.' '.$user->last_name,
            actionText: 'View User Profile',
            link: '/u/'.$user->shared_url,
            recurrenceType: self::RECURRENCE_ONCE,
            additionalData: [
                'user_id' => $user->id,
                'user_name' => $user->first_name.' '.$user->last_name,
                'user_email' => $user->email,
                'user_type' => $user->user_type->value,
                'created_at' => $user->created_at->format('Y-m-d H:i:s'),
            ]
        );
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via($notifiable): array
    {
        return ['database', 'mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail($notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject('New User Registration on Twimo')
            ->line('A new user has registered on Twimo.')
            ->line('Name: '.$this->newUser->first_name.' '.$this->newUser->last_name)
            ->line('Email: '.$this->newUser->email)
            ->line('User Type: '.$this->newUser->user_type->value)
            ->line('Registration Time: '.$this->newUser->created_at->format('Y-m-d H:i:s'))
            ->action('View User Profile', config('services.frontendUrl').'/u/'.$this->newUser->shared_url);
    }

    /**
     * Always valid for admin notifications
     */
    public function isStillValid($notifiable): bool
    {
        return true;
    }

    /**
     * Override to check for specific user ID to prevent duplicate notifications
     */
    public function findExistingNotification($notifiable): ?NotificationModel
    {
        return $notifiable->notifications()
            ->where('type', get_class($this))
            ->where('data->user_id', $this->newUser->id)
            ->latest()
            ->first();
    }
}
