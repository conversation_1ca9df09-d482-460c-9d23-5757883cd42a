<?php

namespace App\Notifications;

use App\Models\User;

final class ProfileCompletionReminderNotification extends BaseNotification
{
    private string $userType;

    public function __construct(User $user)
    {
        $this->userType = $user->user_type->value;

        $message = match ($this->userType) {
            'host' => "You haven't yet finished your profile, finish now to Share your home!",
            'vendor' => 'Complete your profile to begin offering services!',
            default => 'Complete your profile to finish signing up!'
        };

        parent::__construct(
            'Complete Your Profile',
            $message,
            'Complete Profile',
            '/profile',
            self::RECURRENCE_DAILY_UNTIL_COMPLETE,
            ['user_id' => $user->id, 'user_type' => $this->userType]
        );
    }

    /**
     * Override to provide SMS-specific formatting
     */
    protected function formatSmsMessage(): string
    {
        return "Twimo: You haven't yet finished your profile, finish now! to opt out of SMS updates reply STOP.";
    }

    public function via($notifiable): array
    {
        // Only include database and mail channels by default
        return ['database', 'mail'];
    }

    public function isStillValid($notifiable): bool
    {
        return $notifiable->profile_completion < 100;
    }
}
