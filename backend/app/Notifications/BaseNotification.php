<?php

namespace App\Notifications;

use App\Models\Notification as NotificationModel;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use YieldStudio\LaravelBrevoNotifier\BrevoSmsMessage;

abstract class BaseNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public const string RECURRENCE_ONCE = 'once';

    public const string RECURRENCE_UNTIL_COMPLETE = 'until_complete';

    public const string RECURRENCE_DAILY_UNTIL_COMPLETE = 'daily_until_complete';

    protected array $data;

    protected ?NotificationModel $existingNotification = null;

    public function __construct(
        protected string $title,
        protected string $message,
        protected string $actionText,
        protected string $link,
        protected string $recurrenceType = self::RECURRENCE_ONCE,
        array $additionalData = []
    ) {
        $this->data = [
            'title' => $title,
            'message' => $message,
            'action_text' => $actionText,
            'link' => $link,
            'recurrence_type' => $recurrenceType,
            ...$additionalData,
        ];
    }

    public function via($notifiable): array
    {
        return ['database'];
    }

    public function toArray($notifiable): array
    {
        return $this->data;
    }

    public function toMail($notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject($this->title)
            ->line($this->message)
            ->action($this->actionText, config('services.frontendUrl').$this->link);
    }

    /**
     * Find the existing notification for the current notification type.
     * Override this in child classes that need specific lookup logic.
     */
    public function findExistingNotification($notifiable): ?NotificationModel
    {
        return $notifiable->notifications()
            ->where('type', get_class($this))
            ->latest()
            ->first();
    }

    /**
     * Determine if the notification should be sent or updated.
     *
     * @return array{shouldSend: bool, shouldUpdate: bool}
     */
    public function shouldSendOrUpdate($notifiable): array
    {
        $isValid = $this->isStillValid($notifiable);

        if (! $isValid) {
            return ['shouldSend' => false, 'shouldUpdate' => false];
        }

        $this->existingNotification = $this->findExistingNotification($notifiable);

        if (! $this->existingNotification) {
            return ['shouldSend' => true, 'shouldUpdate' => false];
        }

        // For ONCE notifications, never send again
        if ($this->recurrenceType === self::RECURRENCE_ONCE) {
            return ['shouldSend' => false, 'shouldUpdate' => false];
        }

        // For UNTIL_COMPLETE, update existing if condition not met
        if ($this->recurrenceType === self::RECURRENCE_UNTIL_COMPLETE) {
            return [
                'shouldSend' => false,
                'shouldUpdate' => true,
            ];
        }

        // For DAILY_UNTIL_COMPLETE, check if 24 hours passed and condition not met
        if ($this->recurrenceType === self::RECURRENCE_DAILY_UNTIL_COMPLETE) {
            $dayPassed = $this->existingNotification->created_at->diffInHours(now()) >= 24;

            return [
                'shouldSend' => false,
                'shouldUpdate' => $dayPassed,
            ];
        }

        return ['shouldSend' => false, 'shouldUpdate' => false];
    }

    /**
     * Check if the notification condition is still valid.
     * Override this in child classes that use UNTIL_COMPLETE or DAILY_UNTIL_COMPLETE
     */
    public function isStillValid($notifiable): bool
    {
        return true;
    }

    /**
     * Format message specifically for SMS
     * This can be overridden in child classes to provide SMS-specific formatting
     */
    protected function formatSmsMessage(): string
    {
        // Default format: "Twimo: [message] to opt out of SMS updates reply STOP."
        return "Twimo: {$this->message} to opt out of SMS updates reply STOP.";
    }

    /**
     * Prepare for Brevo SMS channel.
     */
    public function toBrevoSms($notifiable): ?BrevoSmsMessage
    {
        if (! $notifiable->phone_number) {
            return null;
        }

        // Ensure phone number has the international format with + prefix
        $phoneNumber = $notifiable->phone_number;
        if (! str_starts_with($phoneNumber, '+')) {
            $phoneNumber = '+'.$phoneNumber;
        }

        return new BrevoSmsMessage()
            ->to($phoneNumber)
            ->content($this->formatSmsMessage());
    }

    /**
     * Process the notification - determine if it should be sent or updated and handle accordingly
     */
    public function process($notifiable): void
    {
        $result = $this->shouldSendOrUpdate($notifiable);

        if ($result['shouldSend']) {
            $notifiable->notify($this);

            return;
        }

        if ($result['shouldUpdate'] && $this->existingNotification) {
            $this->existingNotification->update([
                'created_at' => now(),
                'read_at' => null,
            ]);
        }
    }
}
