<?php

namespace App\Notifications;

final class AddFirstHomeReminderNotification extends BaseNotification
{
    public function __construct()
    {
        parent::__construct(
            'Share Your Vacation Home',
            'Want to start sharing your vacation home? Add your home to get started!',
            'Add Home',
            '/create',
            self::RECURRENCE_UNTIL_COMPLETE
        );
    }

    public function isStillValid($notifiable): bool
    {
        return $notifiable->is_host &&
               $notifiable->homes->isEmpty() &&
               $notifiable->profile_completion === 100;
    }
}
