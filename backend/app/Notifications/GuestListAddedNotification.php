<?php

namespace App\Notifications;

use App\Models\Notification as NotificationModel;
use App\Models\User;

final class GuestListAddedNotification extends BaseNotification
{
    public function __construct(
        private readonly User $guest,
    ) {
        parent::__construct(
            'Guest List',
            "{$guest->first_name} has been added to your guest list",
            'View Guest List',
            '/guests',
            self::RECURRENCE_ONCE,
            [
                'guest_id' => $guest->id,
                'guest_name' => $guest->first_name.' '.$guest->last_name,
            ]
        );
    }

    public function findExistingNotification($notifiable): ?NotificationModel
    {
        return $notifiable->notifications()
            ->where('type', self::class)
            ->where('data->guest_id', $this->guest->id)
            ->first();
    }
}
