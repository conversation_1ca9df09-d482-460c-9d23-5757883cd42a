<?php

namespace App\Notifications\MyCrew;

use App\Models\Jobs;
use App\Models\User;
use App\Notifications\BaseNotification;

class HostJobApprovedNotification extends BaseNotification
{
    public function __construct(
        string $vendorName,
        private readonly int $jobId
    ) {
        parent::__construct(
            title: 'MyCrew Job Approved',
            message: "Your Vendor {$vendorName} has approved your MyCrew Job! Check the Status of the Job Here",
            actionText: 'MyCrew Jobs',
            link: '/my-crews',
            recurrenceType: self::RECURRENCE_UNTIL_COMPLETE
        );
    }

    public function via($notifiable): array
    {
        // Only include database and mail channels by default
        $channels = ['database', 'mail'];

        // This notification is not in the SMS-allowed list
        return $channels;
    }

    /**
     * Override to provide SMS-specific formatting
     */
    protected function formatSmsMessage(): string
    {
        return 'Twimo: Your Vendor has approved your MyCrew Job! Check the Status of the Job Here twimo.com to opt out of SMS updates reply STOP.';
    }

    public function isStillValid($notifiable): bool
    {
        if (! $notifiable instanceof User) {
            return false;
        }

        // Check if the job still exists and is still in accepted status
        return Jobs::query()
            ->where('id', $this->jobId)
            ->where('user_id', $notifiable->id)
            ->where('job_status', 'accepted')
            ->exists();
    }
}
