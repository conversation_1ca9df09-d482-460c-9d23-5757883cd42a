<?php

namespace App\Notifications\MyCrew;

use App\Models\Jobs;
use App\Models\User;
use App\Notifications\BaseNotification;

class HostJobCompletedNotification extends BaseNotification
{
    public function __construct(
        string $vendorName,
        private readonly int $jobId
    ) {
        parent::__construct(
            title: 'MyCrew Job Completed',
            message: "Your Vendor {$vendorName} has completed your MyCrew Job! Review the job and finalize here with payment",
            actionText: 'MyCrew Jobs Make Payment',
            link: '/my-crews',
            recurrenceType: self::RECURRENCE_UNTIL_COMPLETE
        );
    }

    public function via($notifiable): array
    {
        // Only include SMS channel if the user has a phone number
        $channels = ['database', 'mail'];

        if ($notifiable->phone_number) {
            $channels[] = \YieldStudio\LaravelBrevoNotifier\BrevoSmsChannel::class;
        }

        return $channels;
    }

    /**
     * Override to provide SMS-specific formatting
     */
    protected function formatSmsMessage(): string
    {
        return 'Twimo: Your Vendor has completed your MyCrew Job! Review the job and finalize here with payment twimo.com to opt out of SMS updates reply STOP.';
    }

    public function isStillValid($notifiable): bool
    {
        if (! $notifiable instanceof User) {
            return false;
        }

        return Jobs::query()
            ->where('id', $this->jobId)
            ->where('user_id', $notifiable->id)
            ->where('job_status', 'completed')
            ->where('payment_status', '!=', 'completed')
            ->exists();
    }
}
