<?php

namespace App\Notifications\MyCrew;

use App\Models\Jobs;
use App\Models\User;
use App\Notifications\BaseNotification;

class VendorPendingJobNotification extends BaseNotification
{
    public function __construct(
        string $hostName,
        private readonly int $jobId
    ) {
        parent::__construct(
            title: 'New MyCrew Job Request',
            message: "You have received a MyCrew Job Request from {$hostName}. Click here to accept or deny the Job",
            actionText: 'MyCrew Pending Jobs',
            link: '/my-crews',
            recurrenceType: self::RECURRENCE_UNTIL_COMPLETE
        );
    }

    public function via($notifiable): array
    {
        // Only include database and mail channels by default
        $channels = ['database', 'mail'];

        // This notification is not in the SMS-allowed list
        return $channels;
    }

    /**
     * Override to provide SMS-specific formatting
     */
    protected function formatSmsMessage(): string
    {
        return 'Twimo: You have received a MyCrew Job Request. Click here to accept or decline the MyCrew Job twimo.com to opt out of SMS updates reply STOP.';
    }

    public function isStillValid($notifiable): bool
    {
        if (! $notifiable instanceof User) {
            return false;
        }

        // Check if the job still exists and is still pending
        return Jobs::query()
            ->where('id', $this->jobId)
            ->where('vendor_user_id', $notifiable->id)
            ->where('job_status', 'pending')
            ->exists();
    }
}
