<?php

namespace App\Notifications\Booking;

use App\Enums\BookingStatus;
use App\Models\Booking;
use App\Models\User;

final class BookingDraftNotification extends BaseBookingNotification
{
    public function __construct(Booking $booking, User $recipient)
    {
        parent::__construct(
            $booking,
            $recipient,
            'Booking Draft Saved',
            'Your booking for '.$booking->toHome?->title.' has been saved as draft'
        );
    }

    public function isStillValid($notifiable): bool
    {
        return $this->booking->status === BookingStatus::REQUESTED;
    }
}
