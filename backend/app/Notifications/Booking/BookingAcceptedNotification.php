<?php

namespace App\Notifications\Booking;

use App\Enums\BookingStatus;
use App\Enums\BookingType;
use App\Models\Booking;
use App\Models\User;

final class BookingAcceptedNotification extends BaseBookingNotification
{
    protected bool $isIndefinite = false;

    public function __construct(Booking $booking, User $recipient)
    {
        $isSwap = $booking->booking_type === BookingType::SWAP;
        $title = $isSwap ? 'Swap Request Approved ✨' : 'Payment Required 💳';
        $message = 'Your '.($isSwap ? 'swap' : 'booking').' for '.$booking->toHome?->title;

        if ($isSwap) {
            if ($booking->fromHome) {
                $message .= ' with '.$booking->fromHome?->title;
            }
            $message .= ' was approved!';
            if ($booking->host_cleaning_fee_enabled || $booking->guest_cleaning_fee_enabled) {
                $message .= ' Complete payment for cleaning fees.';
            }
        } else {
            $message .= ' was approved! Complete payment now';
        }

        // For accepted bookings that require payment, we still want to direct to the payment page
        // rather than the bookings list
        $needsPayment = $booking->host_cleaning_fee_enabled || $booking->guest_cleaning_fee_enabled || ! $isSwap;
        $link = $needsPayment ? '/bookings/'.$booking->id.'/payment' : null; // null will use smart link

        parent::__construct(
            $booking,
            $recipient,
            $title,
            $message,
            ($isSwap && ! $booking->host_cleaning_fee_enabled && ! $booking->guest_cleaning_fee_enabled)
                ? 'View Swap Details'
                : 'Complete Payment',
            $link,
            self::RECURRENCE_DAILY_UNTIL_COMPLETE
        );
    }

    /**
     * Override to provide SMS-specific formatting
     */
    protected function formatSmsMessage(): string
    {
        if ($this->isIndefinite) {
            return 'Twimo: Your Twimo Booking Request has been approved indefinitely! Your booking will not be complete until your payment has been is made. Login to twimo.com to finalize your booking! to opt out of SMS updates reply STOP.';
        }

        return 'Twimo: Your Twimo Booking Request has been approved! Your booking will not be complete until your payment is made. Login to twimo.com to finalize your booking! to opt out of SMS updates reply STOP.';
    }

    public function isStillValid($notifiable): bool
    {
        return $this->booking->status === BookingStatus::ACCEPTED;
    }
}
