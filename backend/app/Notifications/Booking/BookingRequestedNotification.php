<?php

namespace App\Notifications\Booking;

use App\Enums\BookingStatus;
use App\Enums\BookingType;
use App\Models\Booking;
use App\Models\User;

final class BookingRequestedNotification extends BaseBookingNotification
{
    public function __construct(Booking $booking, User $recipient)
    {
        $isSwap = $booking->booking_type === BookingType::SWAP;
        $title = $isSwap ? 'New Swap Request' : 'New Booking Request';
        $message = 'You have a new '.($isSwap ? 'swap' : 'booking').' request for '.$booking->toHome?->title;
        if ($isSwap && $booking->fromHome) {
            $message .= ' with '.$booking->fromHome?->title;
        }

        parent::__construct(
            $booking,
            $recipient,
            $title,
            $message,
            'Review Request',
            null, // Use the smart link from BaseBookingNotification
            self::RECURRENCE_DAILY_UNTIL_COMPLETE
        );
    }

    /**
     * Override to provide SMS-specific formatting
     */
    protected function formatSmsMessage(): string
    {
        return "Twimo: You've received a Twimo booking request! Login to twimo.com and let your guest know if their Trip is approved! to opt out of SMS updates reply STOP.";
    }

    public function isStillValid($notifiable): bool
    {
        return $this->booking->status === BookingStatus::REQUESTED;
    }
}
