<?php

namespace App\Notifications\Booking;

use App\Enums\BookingStatus;
use App\Models\Booking;
use App\Models\User;

final class SwapPartnerPaymentCompletedNotification extends BaseBookingNotification
{
    public function __construct(Booking $booking, User $recipient)
    {
        $isHost = $recipient->id === $booking->toHome?->user_id;
        $partnerName = $isHost ? $booking->user->first_name : $booking->toHome?->user->first_name;

        $title = 'Swap Partner Payment Completed 💰';
        $message = "{$partnerName} has completed their payment for the swap. ";

        if ($booking->host_cleaning_fee_enabled && $isHost && ! $booking->guest_cleaning_fee_enabled) {
            // Host receiving notification about guest payment, no host payment needed
            $message .= 'Your swap is now confirmed!';
        } elseif ($booking->guest_cleaning_fee_enabled && ! $isHost && ! $booking->host_cleaning_fee_enabled) {
            // Guest receiving notification about host payment, no guest payment needed
            $message .= 'Your swap is now confirmed!';
        } else {
            // Both payments required, but only one is completed
            $message .= "It's now your turn to complete your payment to finalize the swap.";
        }

        parent::__construct(
            $booking,
            $recipient,
            $title,
            $message,
            ($booking->host_cleaning_fee_enabled && $isHost) || ($booking->guest_cleaning_fee_enabled && ! $isHost)
                ? 'Complete Your Payment'
                : 'View Swap Details',
            ($booking->host_cleaning_fee_enabled && $isHost) || ($booking->guest_cleaning_fee_enabled && ! $isHost)
                ? '/bookings/'.$booking->id.'/payment'
                : '/bookings/'.$booking->id,
            self::RECURRENCE_DAILY_UNTIL_COMPLETE
        );
    }

    /**
     * Override to provide SMS-specific formatting
     */
    protected function formatSmsMessage(): string
    {
        return 'Twimo: Your swap partner has completed their payment for your home swap. Login to twimo.com to complete your payment and finalize the swap! To opt out of SMS updates reply STOP.';
    }

    public function isStillValid($notifiable): bool
    {
        // This notification is valid as long as the booking is accepted and not completed
        return $this->booking->status === BookingStatus::ACCEPTED;
    }
}
