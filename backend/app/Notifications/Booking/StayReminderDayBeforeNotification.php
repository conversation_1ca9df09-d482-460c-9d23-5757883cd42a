<?php

namespace App\Notifications\Booking;

use App\Enums\BookingStatus;
use App\Models\Booking;
use App\Models\User;
use Carbon\Carbon;

final class StayReminderDayBeforeNotification extends BaseBookingNotification
{
    public function __construct(Booking $booking, User $recipient)
    {
        parent::__construct(
            $booking,
            $recipient,
            'Your Stay is Tomorrow!',
            'Your stay at '.$booking->toHome?->title.' is tomorrow! Review details or message your host for any last-minute questions.',
            'View Stay Details',
            '/bookings/'.$booking->id,
            self::RECURRENCE_ONCE,
        );
    }

    /**
     * Override to provide SMS-specific formatting
     */
    protected function formatSmsMessage(): string
    {
        return 'Twimo: Your Stay is Tomorrow! If you need to message your Host or check details about your trip go to My Stays at twimo.com to opt out of SMS updates reply STOP.';
    }

    /**
     * Override to specify which channels to use
     * This notification should only be sent via SMS if it's one of the specified types
     */
    public function via($notifiable): array
    {
        // Only include database and mail channels by default
        $channels = ['database', 'mail'];

        // This notification is not in the SMS-allowed list
        return $channels;
    }

    public function isStillValid($notifiable): bool
    {
        // Only valid for completed bookings
        if ($this->booking->status !== BookingStatus::COMPLETED) {
            return false;
        }

        // Check if we're exactly 1 day before start date
        $startDate = Carbon::parse($this->booking->start_at);
        $daysUntilStart = now()->startOfDay()->diffInDays($startDate->startOfDay(), false);

        return $daysUntilStart == 1;
    }
}
