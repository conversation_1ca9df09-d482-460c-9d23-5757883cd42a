<?php

namespace App\Notifications\Booking;

use App\Enums\BookingStatus;
use App\Models\Booking;
use App\Models\Notification as NotificationModel;
use App\Models\User;
use App\Notifications\BaseNotification;
use YieldStudio\LaravelBrevoNotifier\BrevoSmsChannel;

abstract class BaseBookingNotification extends BaseNotification
{
    /**
     * Generate a smart link for a booking notification that will direct the user to the booking
     * The frontend will handle determining the correct tab and booking type
     */
    protected function generateSmartBookingLink(Booking $booking, User $recipient): string
    {
        // For cancelled bookings, direct to the cancelled tab (index 3)
        if ($booking->status === BookingStatus::CANCELED) {
            return "/bookings?tab=3&booking_id={$booking->id}";
        }

        // For other statuses, just include the booking_id
        // The frontend will determine the correct tab and booking type based on the booking details
        // We keep the recipient parameter for potential future use with user-specific routing
        return "/bookings?booking_id={$booking->id}";
    }

    public function __construct(
        protected Booking $booking,
        protected User $recipient,
        string $title,
        string $message,
        string $actionText = 'View Booking',
        ?string $link = null,
        string $recurrenceType = self::RECURRENCE_ONCE
    ) {
        // If no link is provided, generate a smart link
        $smartLink = $link ?? $this->generateSmartBookingLink($booking, $recipient);

        parent::__construct(
            $title,
            $message,
            $actionText,
            $smartLink,
            $recurrenceType,
            [
                'booking_id' => $booking->id,
                'booking_status' => $booking->status,
                'home_title' => $booking->toHome?->title,
            ]
        );
    }

    /**
     * Override to check for specific booking ID
     */
    public function findExistingNotification($notifiable): ?NotificationModel
    {
        return $notifiable->notifications()
            ->where('type', get_class($this))
            ->where('data->booking_id', $this->booking->id)
            ->latest()
            ->first();
    }

    /**
     * Determine if this notification type should be sent via SMS
     * Only specific notification types should be sent via SMS
     */
    protected function shouldSendSms(): bool
    {
        // List of notification types that should be sent via SMS
        $smsEnabledNotifications = [
            'App\\Notifications\\Booking\\BookingRequestedNotification',
            'App\\Notifications\\Booking\\BookingAcceptedNotification',
            'App\\Notifications\\Booking\\AutoApprovedBookingNotification',
            'App\\Notifications\\Booking\\BookingRejectedNotification',
            'App\\Notifications\\Booking\\BookingCanceledNotification',
            'App\\Notifications\\MyCrew\\HostJobCompletedNotification',
        ];

        return in_array(get_class($this), $smsEnabledNotifications);
    }

    public function via($notifiable): array
    {
        // Only include database and mail channels by default
        $channels = ['database', 'mail'];

        // Add SMS channel only if the user has a phone number AND this notification type should be sent via SMS
        if ($notifiable->phone_number && $this->shouldSendSms()) {
            $channels[] = BrevoSmsChannel::class;
        }

        return $channels;
    }
}
