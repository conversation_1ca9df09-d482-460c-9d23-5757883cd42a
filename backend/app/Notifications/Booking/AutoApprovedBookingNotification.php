<?php

namespace App\Notifications\Booking;

use App\Enums\BookingStatus;
use App\Enums\BookingType;
use App\Models\Booking;
use App\Models\User;

final class AutoApprovedBookingNotification extends BaseBookingNotification
{
    public function __construct(Booking $booking, User $recipient)
    {
        $isSwap = $booking->booking_type === BookingType::SWAP;
        $title = $isSwap ? 'New Auto-Approved Swap' : 'New Auto-Approved Booking';
        $message = 'A pre-approved '.($isSwap ? 'swap' : 'booking').' request for '.$booking->toHome?->title;
        if ($isSwap && $booking->fromHome) {
            $message .= ' with '.$booking->fromHome?->title;
        }
        $message .= ' has been automatically approved.';

        parent::__construct(
            $booking,
            $recipient,
            $title,
            $message,
            'View Details',
            null, // Use the smart link from BaseBookingNotification
            self::RECURRENCE_ONCE
        );
    }

    /**
     * Override to provide SMS-specific formatting
     */
    protected function formatSmsMessage(): string
    {
        $isSwap = $this->booking->booking_type === BookingType::SWAP;

        return 'Twimo: A pre-approved '.($isSwap ? 'swap' : 'booking').' request for '.$this->booking->toHome?->title.' has been automatically approved. Login to twimo.com to view details. To opt out of SMS updates reply STOP.';
    }

    public function isStillValid($notifiable): bool
    {
        return $this->booking->status === BookingStatus::ACCEPTED;
    }
}
