<?php

namespace App\Notifications\Booking;

use App\Enums\BookingStatus;
use App\Models\Booking;
use App\Models\User;

final class BookingCanceledNotification extends BaseBookingNotification
{
    public function __construct(Booking $booking, User $recipient)
    {
        // Check who cancelled the booking using the cancelled_by field in extra_info
        $cancelledById = $booking->extra_info['cancelled_by'] ?? null;
        $isRecipientHost = $recipient->id === $booking->toHome?->user_id;
        $isHostCancelling = $cancelledById === $booking->toHome?->user_id;

        // Determine the appropriate message based on who cancelled and who is receiving
        if ($isRecipientHost) {
            // Message for host (who cancelled)
            $message = 'Your booking has been cancelled. A refund will be sent to the guest\'s original payment method following the rental policy in place.';
        } else {
            // Message for traveler/guest
            if ($isHostCancelling) {
                // Host cancelled, message to guest
                $message = 'Your booking has been cancelled. A refund will be sent to your original payment method following the rental policy in place.';
            } else {
                // Guest cancelled, message to guest (shouldn't normally happen as guest gets this via controller)
                $message = 'Your booking has been cancelled. A refund will be sent to your original payment method following the rental policy in place.';
            }
        }

        parent::__construct(
            $booking,
            $recipient,
            'Booking Canceled',
            $message,
            'View Details',
            '/bookings?tab=3' // Direct to the Cancelled Bookings tab (index 3)
        );
    }

    /**
     * Override to provide SMS-specific formatting
     */
    protected function formatSmsMessage(): string
    {
        // Check who cancelled the booking using the cancelled_by field in extra_info
        $cancelledById = $this->booking->extra_info['cancelled_by'] ?? null;
        $isRecipientHost = $this->recipient->id === $this->booking->toHome?->user_id;
        $isHostCancelling = $cancelledById === $this->booking->toHome?->user_id;

        // Determine the appropriate message based on who cancelled and who is receiving
        if ($isRecipientHost) {
            // Message for host (who cancelled)
            $message = 'Your booking has been cancelled. A refund will be sent to the guest\'s original payment method following the rental policy in place.';
        } else {
            // Message for traveler/guest
            if ($isHostCancelling) {
                // Host cancelled, message to guest
                $message = 'Your booking has been cancelled. A refund will be sent to your original payment method following the rental policy in place.';
            } else {
                // Guest cancelled, message to guest
                $message = 'Your booking has been cancelled. A refund will be sent to your original payment method following the rental policy in place.';
            }
        }

        return "Twimo: {$message} to opt out of SMS updates reply STOP.";
    }

    public function isStillValid($notifiable): bool
    {
        return $this->booking->status === BookingStatus::CANCELED;
    }
}
