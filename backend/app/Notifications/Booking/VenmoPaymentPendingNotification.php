<?php

namespace App\Notifications\Booking;

use App\Enums\BookingStatus;
use App\Models\Booking;
use App\Models\User;

final class VenmoPaymentPendingNotification extends BaseBookingNotification
{
    public function __construct(Booking $booking, User $recipient)
    {
        $title = 'Venmo Payment Pending Verification 💰';
        $message = "Guest {$booking->user->first_name} {$booking->user->last_name} has marked their Venmo payment as completed for {$booking->toHome?->title}. Please verify the payment and complete the booking.";

        parent::__construct(
            $booking,
            $recipient,
            $title,
            $message,
            'Verify Payment',
            null, // Use the smart link from BaseBookingNotification
            self::RECURRENCE_DAILY_UNTIL_COMPLETE
        );
    }

    public function isStillValid($notifiable): bool
    {
        // The notification is valid if:
        // 1. Booking is still in ACCEPTED status
        // 2. Has venmo_payment info
        // 3. Venmo payment is not yet confirmed by host
        return $this->booking->status === BookingStatus::ACCEPTED
            && isset($this->booking->extra_info['venmo_payment'])
            && ! isset($this->booking->extra_info['venmo_payment']['host_confirmed_at']);
    }
}
