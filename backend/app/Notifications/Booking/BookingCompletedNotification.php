<?php

namespace App\Notifications\Booking;

use App\Enums\BookingStatus;
use App\Models\Booking;
use App\Models\User;

final class BookingCompletedNotification extends BaseBookingNotification
{
    public function __construct(Booking $booking, User $recipient)
    {
        parent::__construct(
            $booking,
            $recipient,
            'Booking Completed ✅',
            'Payment received for '.$booking->toHome?->title
        );
    }

    public function isStillValid($notifiable): bool
    {
        return $this->booking->status === BookingStatus::COMPLETED;
    }
}
