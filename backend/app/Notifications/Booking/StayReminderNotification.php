<?php

namespace App\Notifications\Booking;

use App\Enums\BookingStatus;
use App\Models\Booking;
use App\Models\User;
use Carbon\Carbon;

final class StayReminderNotification extends BaseBookingNotification
{
    public function __construct(Booking $booking, User $recipient)
    {
        parent::__construct(
            $booking,
            $recipient,
            'Upcoming Stay Reminder',
            'Your stay at '.$booking->toHome?->title.' is coming up in 7 days! Review details or message your host.',
            'View Stay Details',
            '/bookings/'.$booking->id,
            self::RECURRENCE_ONCE,
        );
    }

    /**
     * Override to provide SMS-specific formatting
     */
    protected function formatSmsMessage(): string
    {
        return "Twimo: You're Stay is coming up! 1 week from today you will be on vacation. If you need to message your Host or check details about your trip go to My Stays at twimo.com to opt out of SMS updates reply STOP.";
    }

    /**
     * Override to specify which channels to use
     * This notification should not be sent via SMS
     */
    public function via($notifiable): array
    {
        // Only include database and mail channels
        return ['database', 'mail'];
    }

    public function isStillValid($notifiable): bool
    {
        // Only valid for completed bookings
        if ($this->booking->status !== BookingStatus::COMPLETED) {
            return false;
        }

        // Check if we're exactly 7 days before start date
        $startDate = Carbon::parse($this->booking->start_at);
        $daysUntilStart = now()->startOfDay()->diffInDays($startDate->startOfDay(), false);

        return $daysUntilStart == 7;
    }
}
