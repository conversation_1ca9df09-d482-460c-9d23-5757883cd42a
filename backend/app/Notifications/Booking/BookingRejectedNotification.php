<?php

namespace App\Notifications\Booking;

use App\Enums\BookingStatus;
use App\Enums\BookingType;
use App\Models\Booking;
use App\Models\User;

final class BookingRejectedNotification extends BaseBookingNotification
{
    public function __construct(Booking $booking, User $recipient)
    {
        $isSwap = $booking->booking_type === BookingType::SWAP;
        $title = $isSwap ? 'Swap Declined' : 'Booking Declined';
        $message = 'Your '.($isSwap ? 'swap' : 'booking').' request for '.$booking->toHome?->title;
        if ($isSwap && $booking->fromHome) {
            $message .= ' with '.$booking->fromHome?->title;
        }
        $message .= ' was declined';

        parent::__construct(
            $booking,
            $recipient,
            $title,
            $message,
            'View Details',
            '/bookings?booking_type='.($isSwap ? 'swap' : 'rent')
        );
    }

    /**
     * Override to provide SMS-specific formatting
     */
    protected function formatSmsMessage(): string
    {
        return "Twimo: Your Booking Request has been denied. Sorry this trip didn't work out there, but there's many great homes within Twimo's network to visit! Keep exploring twimo.com to opt out of SMS updates reply STOP.";
    }

    public function isStillValid($notifiable): bool
    {
        return $this->booking->status === BookingStatus::REJECTED;
    }
}
