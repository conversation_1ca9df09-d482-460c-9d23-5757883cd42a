<?php

namespace App\Notifications;

use App\Models\Message;
use App\Models\Notification as NotificationModel;
use App\Models\User;

final class MessageReceivedNotification extends BaseNotification
{
    protected Message $messageModel;

    public function __construct(Message $message, User $recipient)
    {
        $title = $this->generateTitle($message);
        $body = $this->generateBody($message);
        $link = $this->generateLink($message);

        parent::__construct(
            title: $title,
            message: $body,
            actionText: 'View Message',
            link: $link,
            recurrenceType: self::RECURRENCE_ONCE,
            additionalData: [
                'message_id' => $message->id,
                'sender_id' => $message->sender_id,
                'receiver_id' => $message->receiver_id,
                'is_booking_message' => $message->isBookingMessage(),
            ]
        );

        $this->messageModel = $message;
        $this->recipient = $recipient;
    }

    public function findExistingNotification($notifiable): ?NotificationModel
    {
        return $notifiable->notifications()
            ->where('type', self::class)
            ->where('data->message_id', $this->messageModel->id)
            ->first();
    }

    private function generateTitle(Message $message): string
    {
        $senderName = "{$message->sender->first_name} {$message->sender->last_name}";

        if ($message->isBookingMessage()) {
            return "New Booking Message from {$senderName}";
        }

        return "New Message from {$senderName}";
    }

    private function generateBody(Message $message): string
    {
        if ($message->isBookingMessage()) {
            $booking = $message->messageable;

            return "You have a new message about {$booking->toHome?->title} from {$message->sender->first_name}";
        }

        // For direct messages, use a snippet of the message body (first 100 chars)
        $messagePreview = strlen($message->body) > 100
            ? substr($message->body, 0, 97).'...'
            : $message->body;

        return "Message: {$messagePreview}";
    }

    private function generateLink(Message $message): string
    {
        // Both direct messages and booking messages will go to the messages page
        // The frontend can handle showing the correct conversation
        return '/messages';
    }

    public function shouldProcess($notifiable): bool
    {
        // Only process if:
        // 1. The message hasn't been viewed
        // 2. The recipient is the intended receiver
        // 3. The message isn't archived by the receiver
        return ! $this->messageModel->viewed
            && $this->messageModel->receiver_id === $this->recipient->id
            && ! $this->messageModel->archived_by_receiver;
    }
}
