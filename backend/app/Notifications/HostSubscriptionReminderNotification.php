<?php

namespace App\Notifications;

final class HostSubscriptionReminderNotification extends BaseNotification
{
    public function __construct()
    {
        parent::__construct(
            'Please Subscribe to Twimo',
            'Please subscribe to Twimo to start sharing your vacation home!',
            'Subscribe Now',
            '/profile',
            self::RECURRENCE_DAILY_UNTIL_COMPLETE
        );
    }

    public function isStillValid($notifiable): bool
    {
        return ! $notifiable->is_subscribed &&
            $notifiable->is_host &&
            $notifiable->profile_completion === 100 &&
            $notifiable->homes->isNotEmpty();
    }

    public function via($notifiable): array
    {
        return ['database', 'mail'];
    }
}
