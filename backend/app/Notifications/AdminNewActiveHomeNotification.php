<?php

namespace App\Notifications;

use App\Models\Home;
use App\Models\Notification as NotificationModel;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;

class AdminNewActiveHomeNotification extends BaseNotification implements ShouldQueue
{
    use Queueable;

    protected Home $home;

    /**
     * Create a new notification instance.
     */
    public function __construct(Home $home)
    {
        $this->home = $home;

        parent::__construct(
            title: 'New Home Added',
            message: 'A new home has been added and is now active: '.$home->title,
            actionText: 'View Home',
            link: '/'.$home->slug,
            recurrenceType: self::RECURRENCE_ONCE,
            additionalData: [
                'home_id' => $home->id,
                'home_title' => $home->title,
                'home_slug' => $home->slug,
                'user_id' => $home->user_id,
                'user_name' => $home->user->first_name.' '.$home->user->last_name,
            ]
        );
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via($notifiable): array
    {
        return ['database', 'mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail($notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject('New Home Added on Twimo')
            ->line('A new home has been added and is now active on Twimo.')
            ->line('Home Title: '.$this->home->title)
            ->line('Location: '.$this->home->address)
            ->line('Owner: '.($this->home->user->first_name.' '.$this->home->user->last_name))
            ->line('Owner Email: '.$this->home->user->email)
            ->line('Activation Time: '.now()->format('Y-m-d H:i:s'))
            ->action('View Home', config('services.frontendUrl').'/'.$this->home->slug);
    }

    /**
     * Always valid for admin notifications
     */
    public function isStillValid($notifiable): bool
    {
        return true;
    }

    /**
     * Override to check for specific home ID to prevent duplicate notifications
     */
    public function findExistingNotification($notifiable): ?NotificationModel
    {
        return $notifiable->notifications()
            ->where('type', get_class($this))
            ->where('data->home_id', $this->home->id)
            ->latest()
            ->first();
    }
}
