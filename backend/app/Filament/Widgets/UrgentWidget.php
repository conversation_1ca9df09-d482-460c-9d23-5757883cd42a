<?php

namespace App\Filament\Widgets;

use App\Enums\BookingStatus;
use App\Filament\Resources\BookingResource;
use App\Filament\Resources\CommunicationResource;
use App\Filament\Resources\HomeResource;
use App\Filament\Resources\UserResource;
use App\Models\Booking;
use App\Models\Home;
use App\Models\Message;
use App\Models\User;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\DB;

class UrgentWidget extends BaseWidget
{
    protected static ?string $pollingInterval = '30s';

    protected static ?int $sort = 1;

    protected int|string|array $columnSpan = 'full';

    protected function getStats(): array
    {
        // Recently created listings
        $recentlyCreatedListings = Home::query()
            ->where('created_at', '>=', now()->subDay())
            ->count();

        $yesterdayListings = Home::query()
            ->whereBetween('created_at', [now()->subDays(2), now()->subDay()])
            ->count();

        $listingsTrend = $this->calculateTrend($recentlyCreatedListings, $yesterdayListings);

        // Incomplete listings that need attention
        $incompleteListings = Home::query()
            ->where(function ($query) {
                $query->whereNull('description')
                    ->orWhereNull('address')
                    ->orWhereNull('nightly_rate')
                    ->orWhere('status', '!=', 'active');
            })
            ->count();

        // Pending swaps
        $pendingSwaps = Booking::query()
            ->where('status', BookingStatus::REQUESTED)
            ->count();

        $olderPendingSwaps = Booking::query()
            ->where('status', BookingStatus::REQUESTED)
            ->where('created_at', '<=', now()->subDays(3))
            ->count();

        // Recent user registrations
        $userRegistrations = User::query()
            ->where('created_at', '>=', now()->subDay())
            ->count();

        $yesterdayRegistrations = User::query()
            ->whereBetween('created_at', [now()->subDays(2), now()->subDay()])
            ->count();

        $registrationsTrend = $this->calculateTrend($userRegistrations, $yesterdayRegistrations);

        // Urgent messages
        $urgentMessages = Message::query()
            ->where('created_at', '>=', now()->subDay())
            ->where('viewed', false)
            ->count();

        $oldUrgentMessages = Message::query()
            ->where('created_at', '<', now()->subDay())
            ->where('viewed', false)
            ->count();

        // User incomplete profiles
        $incompleteProfiles = User::query()
            ->where(function ($query) {
                $query->whereNull('first_name')
                    ->orWhereNull('last_name')
                    ->orWhereNull('about');
            })
            ->count();

        // Coming soon bookings
        $upcomingBookings = Booking::query()
            ->where('status', BookingStatus::COMPLETED)
            ->where('start_at', '>=', now())
            ->where('start_at', '<=', now()->addDays(7))
            ->count();

        // Homes with calendar conflicts
        $homesWithConflicts = Home::query()
            ->whereHas('bookings', function ($query) {
                $query->where('status', BookingStatus::COMPLETED)
                    ->where(function ($q) {
                        $q->whereRaw('start_at < end_at')
                            ->whereExists(function ($subquery) {
                                $subquery->select(DB::raw(1))
                                    ->from('bookings as b2')
                                    ->whereRaw('bookings.request_user_home = b2.request_user_home')
                                    ->whereRaw('bookings.id != b2.id')
                                    ->whereRaw('bookings.start_at < b2.end_at')
                                    ->whereRaw('bookings.end_at > b2.start_at')
                                    ->where('b2.status', BookingStatus::COMPLETED);
                            });
                    });
            })
            ->count();

        // Revenue metrics (last 30 days)
        $revenueData = Booking::query()
            ->where('status', BookingStatus::COMPLETED)
            ->where('created_at', '>=', now()->subDays(30))
            ->select(DB::raw('SUM(JSON_EXTRACT(extra_info, "$.total_price")) as total_revenue'))
            ->first();

        $totalRevenue = $revenueData ? round($revenueData->total_revenue ?? 0, 2) : 0;

        return [
            // Critical attention items (red/danger)
            Stat::make('Calendar Conflicts', $homesWithConflicts)
                ->description('Homes with overlapping bookings')
                ->icon('heroicon-o-exclamation-circle')
                ->color('danger')
                ->extraAttributes([
                    'class' => 'border-l-4 border-danger-500',
                ])
                ->url(HomeResource::getUrl('index', ['tableFilters[has_conflicts][values][0]' => '1'])),

            Stat::make('Urgent Messages', $urgentMessages + $oldUrgentMessages)
                ->description($oldUrgentMessages > 0 ? "{$oldUrgentMessages} older messages need attention" : 'Unread user messages')
                ->icon('heroicon-o-chat-bubble-bottom-center-text')
                ->color($oldUrgentMessages > 0 ? 'danger' : 'warning')
                ->extraAttributes([
                    'class' => $oldUrgentMessages > 0 ? 'border-l-4 border-danger-500' : 'border-l-4 border-warning-500',
                ])
                ->url(CommunicationResource::getUrl('index', ['tableFilters[viewed][values][0]' => '0'])),

            Stat::make('Pending Swaps', $pendingSwaps)
                ->description($olderPendingSwaps > 0 ? "{$olderPendingSwaps} older than 3 days" : 'Swaps awaiting approval')
                ->icon('heroicon-o-clock')
                ->color($olderPendingSwaps > 0 ? 'danger' : 'warning')
                ->extraAttributes([
                    'class' => $olderPendingSwaps > 0 ? 'border-l-4 border-danger-500' : 'border-l-4 border-warning-500',
                ])
                ->url(BookingResource::getUrl('index', ['tableFilters[status][values][0]' => BookingStatus::PENDING->value])),

            // Attention needed items (yellow/warning)
            Stat::make('Incomplete Listings', $incompleteListings)
                ->description('Listings needing completion')
                ->icon('heroicon-o-home-modern')
                ->color('warning')
                ->extraAttributes([
                    'class' => 'border-l-4 border-warning-500',
                ])
                ->url(HomeResource::getUrl('index', ['tableFilters[status][values][0]' => 'draft'])),

            Stat::make('User Incomplete Profiles', $incompleteProfiles)
                ->description('Users with missing profile information')
                ->icon('heroicon-o-user')
                ->color('warning')
                ->extraAttributes([
                    'class' => 'border-l-4 border-warning-500',
                ])
                ->url(UserResource::getUrl('index', ['tableFilters[incomplete_profile][values][0]' => '1'])),

            // Positive metrics (green/success)
            Stat::make('Recently Created Listings', $recentlyCreatedListings)
                ->description($listingsTrend['description'])
                ->descriptionIcon($listingsTrend['icon'])
                ->icon('heroicon-o-plus-circle')
                ->color('success')
                ->extraAttributes([
                    'class' => 'border-l-4 border-success-500',
                ])
                ->url(HomeResource::getUrl('index', ['tableFilters[created_at][from]' => now()->subDay()->format('Y-m-d')])),

            Stat::make('Recent User Registrations', $userRegistrations)
                ->description($registrationsTrend['description'])
                ->descriptionIcon($registrationsTrend['icon'])
                ->icon('heroicon-o-user-plus')
                ->color('success')
                ->extraAttributes([
                    'class' => 'border-l-4 border-success-500',
                ])
                ->url(UserResource::getUrl('index', ['tableFilters[created_at][from]' => now()->subDay()->format('Y-m-d')])),

            Stat::make('Upcoming Bookings (7 days)', $upcomingBookings)
                ->description('Bookings starting within a week')
                ->icon('heroicon-o-calendar')
                ->color('success')
                ->extraAttributes([
                    'class' => 'border-l-4 border-success-500',
                ])
                ->url(BookingResource::getUrl('index', [
                    'tableFilters[status][values][0]' => BookingStatus::COMPLETED->value,
                    'tableFilters[start_at][from]' => now()->format('Y-m-d'),
                    'tableFilters[start_at][to]' => now()->addDays(7)->format('Y-m-d'),
                ])),

            Stat::make('30-Day Revenue', '$'.number_format($totalRevenue, 2))
                ->description('Total revenue from confirmed bookings')
                ->icon('heroicon-o-currency-dollar')
                ->color('success')
                ->extraAttributes([
                    'class' => 'border-l-4 border-success-500',
                ])
                ->url(BookingResource::getUrl('index', [
                    'tableFilters[status][values][0]' => BookingStatus::COMPLETED->value,
                    'tableFilters[created_at][from]' => now()->subDays(30)->format('Y-m-d'),
                ])),
        ];
    }

    private function calculateTrend(int $current, int $previous): array
    {
        if ($previous == 0) {
            return [
                'description' => $current > 0 ? '+'.$current.' new' : 'No change',
                'icon' => $current > 0 ? 'heroicon-m-arrow-trending-up' : 'heroicon-m-minus',
                'color' => $current > 0 ? 'success' : 'gray',
            ];
        }

        $percentChange = round((($current - $previous) / $previous) * 100);

        return [
            'description' => ($percentChange >= 0 ? '+' : '').$percentChange.'% vs previous day',
            'icon' => $percentChange > 0 ? 'heroicon-m-arrow-trending-up' : ($percentChange < 0 ? 'heroicon-m-arrow-trending-down' : 'heroicon-m-minus'),
            'color' => $percentChange > 0 ? 'success' : ($percentChange < 0 ? 'danger' : 'gray'),
        ];
    }
}
