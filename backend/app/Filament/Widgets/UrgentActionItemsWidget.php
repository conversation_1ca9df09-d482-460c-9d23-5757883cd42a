<?php

namespace App\Filament\Widgets;

use App\Enums\BookingStatus;
use App\Filament\Resources\BookingResource;
use App\Filament\Resources\CommunicationResource;
use App\Filament\Resources\HomeResource;
use App\Filament\Resources\UserResource;
use App\Models\Booking;
use App\Models\Home;
use App\Models\Message;
use App\Models\User;
use Filament\Tables;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Filters\SelectFilter;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Contracts\Pagination\Paginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class UrgentActionItemsWidget extends BaseWidget
{
    protected static ?string $heading = 'Action Items Requiring Attention';

    protected static ?int $sort = 2;

    protected int|string|array $columnSpan = 'full';

    protected function getTableQuery(): Builder
    {
        // We'll combine several queries to create a single urgent items list

        // Start with bookings that need immediate attention
        $pendingBookings = Booking::query()
            ->where('status', BookingStatus::PENDING)
            ->select([
                'id',
                'created_at',
                'user_id',
                'user_home',
                'start_at',
                'status',
                DB::raw("'booking' as item_type"),
                DB::raw("'Booking Request' as item_title"),
                DB::raw("CONCAT('Pending booking from ', DATE_FORMAT(created_at, '%m/%d/%Y')) as item_description"),
                DB::raw('DATEDIFF(NOW(), created_at) as days_pending'),
            ])
            ->orderBy('created_at', 'desc')
            ->limit(10);

        // Homes with missing information
        $incompleteHomes = Home::query()
            ->where(function ($query) {
                $query->whereNull('description')
                    ->orWhereNull('address')
                    ->orWhereNull('nightly_rate')
                    ->orWhere('status', '!=', 'active');
            })
            ->select([
                'id',
                'created_at',
                'user_id',
                DB::raw('NULL as user_home'),
                DB::raw('NULL as start_at'),
                DB::raw('NULL as status'),
                DB::raw("'home' as item_type"),
                DB::raw("CONCAT('Incomplete Home: ', title) as item_title"),
                DB::raw("CASE 
                    WHEN description IS NULL THEN 'Missing description'
                    WHEN address IS NULL THEN 'Missing address'
                    WHEN nightly_rate IS NULL THEN 'Missing nightly rate'
                    ELSE 'Not active'
                END as item_description"),
                DB::raw('DATEDIFF(NOW(), created_at) as days_pending'),
            ])
            ->orderBy('created_at', 'desc')
            ->limit(10);

        // Unverified users
        $incompleteUsers = User::query()
            ->where(function ($query) {
                $query->whereNull('first_name')
                    ->orWhereNull('last_name')
                    ->orWhereNull('about');
            })
            ->select([
                'id',
                'created_at',
                DB::raw('id as user_id'),
                DB::raw('NULL as user_home'),
                DB::raw('NULL as start_at'),
                DB::raw('NULL as status'),
                DB::raw("'user' as item_type"),
                DB::raw("CONCAT('Incomplete Profile: ', email) as item_title"),
                DB::raw("CASE 
                    WHEN first_name IS NULL THEN 'Missing first name'
                    WHEN last_name IS NULL THEN 'Missing last name'
                    WHEN about IS NULL THEN 'Missing about section'
                    ELSE 'Incomplete profile'
                END as item_description"),
                DB::raw('DATEDIFF(NOW(), created_at) as days_pending'),
            ])
            ->orderBy('created_at', 'desc')
            ->limit(10);

        // Urgent messages (unread and over 2 days old)
        $urgentMessages = Message::query()
            ->where('viewed', false)
            ->where('created_at', '<=', now()->subDays(2))
            ->select([
                'id',
                'created_at',
                'sender_id as user_id',
                DB::raw('NULL as user_home'),
                DB::raw('NULL as start_at'),
                DB::raw('NULL as status'),
                DB::raw("'message' as item_type"),
                DB::raw("CONCAT('Unread Message: ', subject) as item_title"),
                DB::raw("CONCAT('From: ', sender_id, ' - ', SUBSTRING(body, 1, 50), '...') as item_description"),
                DB::raw('DATEDIFF(NOW(), created_at) as days_pending'),
            ])
            ->orderBy('created_at', 'desc')
            ->limit(10);

        // Combine all queries
        $combinedQuery = $pendingBookings
            ->union($incompleteHomes)
            ->union($incompleteUsers)
            ->union($urgentMessages);

        return $combinedQuery;
    }

    protected function getTableColumns(): array
    {
        return [
            IconColumn::make('priority')
                ->label('')
                ->tooltip('Priority Level')
                ->icon(function ($record): string {
                    // Handle both array and object types
                    $daysPending = is_array($record) ? $record['days_pending'] : $record->days_pending;

                    return match (true) {
                        $daysPending >= 7 => 'heroicon-o-exclamation-circle',
                        $daysPending >= 3 => 'heroicon-o-exclamation-triangle',
                        default => 'heroicon-o-clock',
                    };
                })
                ->color(function ($record): string {
                    // Handle both array and object types
                    $daysPending = is_array($record) ? $record['days_pending'] : $record->days_pending;

                    return match (true) {
                        $daysPending >= 7 => 'danger',
                        $daysPending >= 3 => 'warning',
                        default => 'gray',
                    };
                })
                ->size('lg'),

            IconColumn::make('item_type')
                ->label('')
                ->tooltip('Item Type')
                ->icon(function ($record): string {
                    // Handle both array and object types
                    $itemType = is_array($record) ? $record['item_type'] : $record->item_type;

                    return match ($itemType) {
                        'booking' => 'heroicon-o-calendar',
                        'home' => 'heroicon-o-home-modern',
                        'user' => 'heroicon-o-user',
                        'message' => 'heroicon-o-chat-bubble-bottom-center-text',
                        default => 'heroicon-o-document',
                    };
                })
                ->color(function ($record): string {
                    // Handle both array and object types
                    $itemType = is_array($record) ? $record['item_type'] : $record->item_type;

                    return match ($itemType) {
                        'booking' => 'primary',
                        'home' => 'success',
                        'user' => 'info',
                        'message' => 'warning',
                        default => 'gray',
                    };
                })
                ->size('lg'),

            TextColumn::make('item_title')
                ->label('Item')
                ->searchable()
                ->weight('bold')
                ->wrap(),

            TextColumn::make('item_description')
                ->label('Description')
                ->searchable()
                ->wrap()
                ->limit(50),

            TextColumn::make('created_at')
                ->label('Date')
                ->date('M d, Y')
                ->sortable(),

            TextColumn::make('days_pending')
                ->label('Age')
                ->formatStateUsing(function ($state): string {
                    $days = is_object($state) ? $state->days_pending : $state;

                    return $days.' '.($days == 1 ? 'day' : 'days');
                })
                ->sortable()
                ->color(function ($record): string {
                    // Handle both array and object types
                    $daysPending = is_array($record) ? $record['days_pending'] : $record->days_pending;

                    return match (true) {
                        $daysPending >= 7 => 'danger',
                        $daysPending >= 3 => 'warning',
                        default => 'gray',
                    };
                }),
        ];
    }

    protected function getTableFilters(): array
    {
        return [
            SelectFilter::make('item_type')
                ->label('Item Type')
                ->options([
                    'booking' => 'Bookings',
                    'home' => 'Homes',
                    'user' => 'Users',
                    'message' => 'Messages',
                ]),

            SelectFilter::make('priority')
                ->label('Priority')
                ->options([
                    'high' => 'High (7+ days)',
                    'medium' => 'Medium (3-6 days)',
                    'low' => 'Low (1-2 days)',
                ])
                ->query(function (Builder $query, array $data): Builder {
                    return $query
                        ->when($data['value'] === 'high', function (Builder $query) {
                            $query->where('days_pending', '>=', 7);
                        })
                        ->when($data['value'] === 'medium', function (Builder $query) {
                            $query->where('days_pending', '>=', 3)
                                ->where('days_pending', '<', 7);
                        })
                        ->when($data['value'] === 'low', function (Builder $query) {
                            $query->where('days_pending', '<', 3);
                        });
                }),
        ];
    }

    protected function getTableFiltersLayout(): ?FiltersLayout
    {
        return FiltersLayout::AboveContent;
    }

    protected function getTableActions(): array
    {
        return [
            Tables\Actions\Action::make('view')
                ->label('View & Resolve')
                ->icon('heroicon-o-eye')
                ->color('primary')
                ->button()
                ->url(function ($record) {
                    $itemType = is_array($record) ? $record['item_type'] : $record->item_type;
                    $id = is_array($record) ? $record['id'] : $record->id;

                    return match ($itemType) {
                        'booking' => BookingResource::getUrl('edit', ['record' => $id]),
                        'home' => HomeResource::getUrl('edit', ['record' => $id]),
                        'user' => UserResource::getUrl('edit', ['record' => $id]),
                        'message' => CommunicationResource::getUrl('edit', ['record' => $id]),
                        default => '#',
                    };
                }),
        ];
    }

    protected function getTableRecordsPerPageSelectOptions(): array
    {
        return [5, 10, 25, 50];
    }

    protected function getDefaultTableSortColumn(): ?string
    {
        return 'days_pending';
    }

    protected function getDefaultTableSortDirection(): ?string
    {
        return 'desc';
    }

    protected function getTableHeading(): string
    {
        return 'Action Items Requiring Immediate Attention';
    }

    protected function isTablePaginationEnabled(): bool
    {
        return true;
    }

    public function paginateTableQuery(Builder $query): Paginator
    {
        return $query->paginate(
            $this->getTableRecordsPerPage() == -1 ? $query->count() : $this->getTableRecordsPerPage(),
            ['*'],
            $this->getTablePaginationPageName()
        );
    }

    public function getTablePaginationPageName(): string
    {
        return 'page';
    }

    protected function configureTableAction(Tables\Actions\Action $action): void
    {
        $action->openUrlInNewTab();
    }
}
