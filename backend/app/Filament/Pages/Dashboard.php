<?php

namespace App\Filament\Pages;

use App\Filament\Resources\BookingResource\Widgets\BookingWidget;
use App\Filament\Resources\BookingResource\Widgets\LatestBookingsWidget;
use App\Filament\Resources\BookingResource\Widgets\RevenueChart;
use App\Filament\Resources\CommunicationResource\Widgets\CommunicationWidget;
use App\Filament\Resources\CommunicationResource\Widgets\RecentMessagesWidget;
use App\Filament\Resources\HomeResource\Widgets\HomeWidget;
use App\Filament\Resources\HomeResource\Widgets\OccupancyHomesChart;
use App\Filament\Resources\HomeResource\Widgets\PopularHomesWidget;
use App\Filament\Resources\UserResource\Widgets\NewUsersWidget;
use App\Filament\Widgets\UrgentActionItemsWidget;
use App\Filament\Widgets\UrgentWidget;
use Filament\Pages\Dashboard as BaseDashboard;

class Dashboard extends BaseDashboard
{
    protected static ?string $navigationIcon = 'heroicon-o-presentation-chart-bar';

    protected static ?string $navigationLabel = 'Dashboard';

    protected static ?int $navigationSort = -1;

    protected function getHeaderWidgets(): array
    {
        return [
            UrgentWidget::class,
        ];
    }

    public function getWidgets(): array
    {
        return [
            // Critical action items requiring immediate attention
            UrgentActionItemsWidget::class,

            // Property management section
            HomeWidget::class,
            PopularHomesWidget::class,
            OccupancyHomesChart::class,

            // Booking management section
            BookingWidget::class,
            LatestBookingsWidget::class,
            RevenueChart::class,

            // User engagement section
            NewUsersWidget::class,
            CommunicationWidget::class,
            RecentMessagesWidget::class,
        ];
    }

    public function getColumns(): int|array
    {
        return [
            'default' => 1,
            'sm' => 2,
            'md' => 3,
            'lg' => 4,
            'xl' => 4,
        ];
    }
}
