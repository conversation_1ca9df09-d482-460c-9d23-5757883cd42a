<?php

namespace App\Filament\Resources;

use App\Filament\Resources\UserResource\Pages;
use App\Models\User;
use Exception;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Support\Enums\IconPosition;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use pxlrbt\FilamentExcel\Actions\Tables\ExportBulkAction;

class UserResource extends Resource
{
    protected static ?string $model = User::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Basic Information')
                    ->schema([
                        Forms\Components\TextInput::make('first_name')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('last_name')
                            ->maxLength(255),
                        Forms\Components\TextInput::make('email')
                            ->email()
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('phone_number')
                            ->tel()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('venmo_username')
                            ->maxLength(255),
                        Forms\Components\Select::make('user_type')
                            ->options(\App\Enums\UserType::class)
                            ->required(),
                        Forms\Components\Select::make('role_id')
                            ->relationship('role', 'name')
                            ->required(),
                    ])->columns(2),

                Forms\Components\Section::make('About')
                    ->schema([
                        Forms\Components\Textarea::make('about')
                            ->maxLength(16777215)
                            ->columnSpanFull(),
                    ]),

                Forms\Components\Section::make('Business Information')
                    ->schema([
                        Forms\Components\TextInput::make('host_logo')
                            ->maxLength(255),
                        Forms\Components\TextInput::make('host_landing_page')
                            ->maxLength(255),
                        Forms\Components\TextInput::make('stripe_connect_id')
                            ->maxLength(255)
                            ->disabled(),
                    ])->columns(2),

                Forms\Components\Section::make('Additional Information')
                    ->schema([
                        Forms\Components\KeyValue::make('extra_info')
                            ->keyLabel('Property')
                            ->valueLabel('Value')
                            ->disabled()
                            ->columnSpanFull(),
                    ]),
            ]);
    }

    /**
     * @throws Exception
     */
    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->sortable(),
                Tables\Columns\TextColumn::make('first_name')
                    ->wrap()
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('last_name')
                    ->wrap()
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('email')
                    ->wrap()
                    ->sortable()
                    ->searchable(),
                Tables\Columns\IconColumn::make('email_verified_at')
                    ->label('Verified')
                    ->boolean()
                    ->sortable(),
                Tables\Columns\TextColumn::make('user_type')
                    ->badge()
                    ->sortable(),
                Tables\Columns\TextColumn::make('role.name')
                    ->badge()
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('phone_number')
                    ->searchable(),
                Tables\Columns\TextColumn::make('venmo_username')
                    ->searchable(),
                Tables\Columns\TextColumn::make('homes_count')
                    ->icon('heroicon-o-home')
                    ->iconPosition(IconPosition::After)
                    ->label('Homes')
                    ->numeric()
                    ->color('info')
                    ->sortable()
                    ->url(fn (User $user) => HomeResource::getUrl('index', ['tableSearch' => $user->email, 'activeTab' => 'all'])),
                Tables\Columns\IconColumn::make('host_logo')
                    ->label('Whitelabel')
                    ->state(fn (User $user) => $user->host_logo && $user->host_landing_page)
                    ->boolean(),
                Tables\Columns\IconColumn::make('stripe_connect_id')
                    ->label('Stripe Connected')
                    ->boolean()
                    ->sortable(),
                Tables\Columns\TextColumn::make('last_seen')
                    ->label('Last online')
                    ->since()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->since()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('user_type')
                    ->multiple()
                    ->label('User Type')
                    ->options(\App\Enums\UserType::class),
                Tables\Filters\SelectFilter::make('role')
                    ->multiple()
                    ->relationship('role', 'name'),
                Tables\Filters\Filter::make('verified')
                    ->label('Email Verified')
                    ->toggle()
                    ->query(fn (Builder $query): Builder => $query->whereNotNull('email_verified_at')),
                Tables\Filters\Filter::make('not_verified')
                    ->label('Email Not Verified')
                    ->toggle()
                    ->query(fn (Builder $query): Builder => $query->whereNull('email_verified_at')),
                Tables\Filters\Filter::make('has_phone')
                    ->toggle()
                    ->query(fn (Builder $query): Builder => $query->whereNotNull('phone_number')),
                Tables\Filters\Filter::make('has_venmo')
                    ->toggle()
                    ->query(fn (Builder $query): Builder => $query->whereNotNull('venmo_username')),
                Tables\Filters\Filter::make('has_homes')
                    ->toggle()
                    ->query(fn (Builder $query): Builder => $query->has('homes')),
                Tables\Filters\Filter::make('stripe_connected')
                    ->toggle()
                    ->query(fn (Builder $query): Builder => $query->whereNotNull('stripe_connect_id')),
                Tables\Filters\Filter::make('whitelabel')
                    ->toggle()
                    ->query(fn (Builder $query): Builder => $query->whereNotNull('host_logo')->whereNotNull('host_landing_page')),
                Tables\Filters\Filter::make('registration_complete')
                    ->toggle()
                    ->query(fn (Builder $query): Builder => $query->whereRaw("JSON_EXTRACT(extra_info, '$.registration_progress') = 100")),
                Tables\Filters\Filter::make('opted_in')
                    ->label('Opted In To Communications')
                    ->toggle()
                    ->query(fn (Builder $query): Builder => $query->whereRaw("JSON_EXTRACT(extra_info, '$.opted_in_to_communications') = true")),
                Tables\Filters\Filter::make('recent')
                    ->label('Created Last 30 Days')
                    ->toggle()
                    ->query(fn (Builder $query): Builder => $query->where('created_at', '>=', now()->subDays(30))),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->defaultSort('id', 'desc')
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
                ExportBulkAction::make(),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUsers::route('/'),
            'create' => Pages\CreateUser::route('/create'),
            'edit' => Pages\EditUser::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withCount(['homes']);
    }
}
