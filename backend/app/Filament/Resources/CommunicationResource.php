<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CommunicationResource\Pages;
use App\Models\Message;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use pxlrbt\FilamentExcel\Actions\Tables\ExportBulkAction;

class CommunicationResource extends Resource
{
    protected static ?string $model = Message::class;

    protected static ?string $modelLabel = 'Communication';

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('sender_id')
                    ->relationship('sender', 'first_name')
                    ->searchable()
                    ->required(),
                Select::make('receiver_id')
                    ->relationship('receiver', 'first_name')
                    ->searchable()
                    ->required(),
                Select::make('messageable_type')
                    ->options([
                        \App\Models\Booking::class => 'Booking',
                    ])
                    ->required(),
                Select::make('messageable_id')
                    ->relationship('messageable', 'id')
                    ->required(),
                TextInput::make('subject'),
                Textarea::make('body')
                    ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->groups([
                Tables\Grouping\Group::make('messageable_id')
                    ->label('Booking'),
            ])
            ->defaultGroup('messageable_id')
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->sortable(),
                Tables\Columns\TextColumn::make('sender.first_name')
                    ->wrap()
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('receiver.first_name')
                    ->wrap()
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('messageable_id')
                    ->label('Booking ID')
                    ->url(fn (Message $message) => $message->isBookingMessage()
                        ? BookingResource::getUrl('edit', ['record' => $message->messageable_id])
                        : null)
                    ->color('success')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('body')
                    ->label('Message')
                    ->wrap()
                    ->words(10)
                    ->searchable()
                    ->sortable(),
                Tables\Columns\ToggleColumn::make('viewed')
                    ->disabled()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Sent At')
                    ->sortable()
                    ->since(),
            ])
            ->filters([
                //
            ])
            ->defaultSort('created_at', 'desc')
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
                ExportBulkAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCommunications::route('/'),
            'create' => Pages\CreateCommunication::route('/create'),
            'edit' => Pages\EditCommunication::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery();
    }
}
