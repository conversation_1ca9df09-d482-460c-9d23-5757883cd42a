<?php

namespace App\Filament\Resources\CommunicationResource\Widgets;

use App\Filament\Resources\CommunicationResource;
use App\Models\Message;
use Filament\Tables;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Database\Eloquent\Builder;

class RecentMessagesWidget extends BaseWidget
{
    protected static ?string $heading = 'Recent Messages';

    protected int|string|array $columnSpan = 'full';

    protected static ?int $sort = 15;

    protected function getTableQuery(): Builder
    {
        return Message::query()
            ->with(['sender', 'receiver'])
            ->latest()
            ->limit(8);
    }

    protected function getTableColumns(): array
    {
        return [
            Tables\Columns\TextColumn::make('sender.email')
                ->label('From')
                ->searchable(),

            Tables\Columns\TextColumn::make('receiver.email')
                ->label('To')
                ->searchable(),

            Tables\Columns\TextColumn::make('subject')
                ->limit(40)
                ->searchable(),

            Tables\Columns\TextColumn::make('body')
                ->label('Message')
                ->limit(50)
                ->html(),

            Tables\Columns\IconColumn::make('viewed')
                ->boolean()
                ->label('Read')
                ->trueIcon('heroicon-o-check-circle')
                ->falseIcon('heroicon-o-x-circle')
                ->trueColor('success')
                ->falseColor('danger'),

            Tables\Columns\TextColumn::make('created_at')
                ->label('Date')
                ->dateTime('M d, H:i')
                ->sortable(),
        ];
    }

    protected function getTableActions(): array
    {
        return [
            Tables\Actions\Action::make('view')
                ->label('View')
                ->icon('heroicon-o-eye')
                ->url(fn (Message $record): string => CommunicationResource::getUrl('edit', ['record' => $record])),

            Tables\Actions\Action::make('mark_read')
                ->label('Mark Read')
                ->icon('heroicon-o-check')
                ->action(fn (Message $record) => $record->update(['viewed' => true]))
                ->hidden(fn (Message $record) => $record->viewed)
                ->color('success'),
        ];
    }

    protected function getTableFilters(): array
    {
        return [
            Tables\Filters\SelectFilter::make('viewed')
                ->options([
                    '0' => 'Unread',
                    '1' => 'Read',
                ])
                ->attribute('viewed'),
        ];
    }

    protected function getTableEmptyStateIcon(): ?string
    {
        return 'heroicon-o-chat-bubble-bottom-center-text';
    }

    protected function getTableEmptyStateHeading(): ?string
    {
        return 'No messages yet';
    }
}
