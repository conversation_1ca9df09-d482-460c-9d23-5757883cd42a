<?php

namespace App\Filament\Resources\CommunicationResource\Widgets;

use App\Filament\Resources\CommunicationResource;
use App\Models\Booking;
use App\Models\Message;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class CommunicationWidget extends BaseWidget
{
    protected function getStats(): array
    {
        $totalMessages = Message::query()->count();

        $averageMessagesPerBooking = round(Message::query()
            ->where('messageable_type', Booking::class)
            ->selectRaw('COUNT(*) / COUNT(DISTINCT messageable_id) as avg_messages')
            ->value('avg_messages')) ?: 0;

        $totalBookingsWithAtLeastTwoDifferentSenders = Message::query()
            ->where('messageable_type', Booking::class)
            ->select('messageable_id')
            ->groupBy('messageable_id')
            ->havingRaw('COUNT(DISTINCT sender_id) >= 2')
            ->count();

        $totalBookings = Booking::query()->count();

        if ($totalBookings > 0) {
            $bookingEngagementRate = round($totalBookingsWithAtLeastTwoDifferentSenders / $totalBookings * 100);
        } else {
            $bookingEngagementRate = 0;
        }

        $messageLength = round(Message::query()
            ->selectRaw('AVG(LENGTH(body)) as avg_message_length')
            ->value('avg_message_length')) ?: 0;

        return [
            Stat::make('Total Messages', $totalMessages)
                ->description('The total number of messages sent')
                ->icon('heroicon-o-chat-bubble-left-ellipsis')
                ->url(CommunicationResource::getUrl()),
            Stat::make('Average Messages Per Booking', $averageMessagesPerBooking)
                ->description('The average number of messages sent per booking')
                ->icon('heroicon-o-chat-bubble-left-right')
                ->url(CommunicationResource::getUrl()),
            Stat::make('Booking Engagement Rate', $bookingEngagementRate.'%')
                ->description('The percentage of bookings with messages from at least two different users')
                ->icon('heroicon-o-users')
                ->url(CommunicationResource::getUrl()),
            Stat::make('Average Message Length', $messageLength)
                ->description('The average length of a message in characters')
                ->icon('heroicon-o-document-text')
                ->url(CommunicationResource::getUrl()),
        ];
    }
}
