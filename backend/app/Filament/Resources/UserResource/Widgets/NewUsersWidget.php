<?php

namespace App\Filament\Resources\UserResource\Widgets;

use App\Filament\Resources\UserResource;
use App\Models\User;
use Filament\Tables;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Database\Eloquent\Builder;

class NewUsersWidget extends BaseWidget
{
    protected static ?string $heading = 'New Users';

    protected int|string|array $columnSpan = 'full';

    protected static ?int $sort = 20;

    protected function getTableQuery(): Builder
    {
        return User::query()
            ->where('created_at', '>=', now()->subDays(7))
            ->latest();
    }

    protected function getTableColumns(): array
    {
        return [
            Tables\Columns\ImageColumn::make('avatar')
                ->label('Photo')
                ->circular()
                ->defaultImageUrl(fn (User $record): string => 'https://ui-avatars.com/api/?name='.urlencode($record->first_name.' '.$record->last_name).'&color=FFFFFF&background=111827'),

            Tables\Columns\TextColumn::make('first_name')
                ->label('Name')
                ->formatStateUsing(fn (User $record): string => $record->first_name.' '.$record->last_name)
                ->searchable(['first_name', 'last_name']),

            Tables\Columns\TextColumn::make('email')
                ->searchable(),

            Tables\Columns\BadgeColumn::make('profile_status')
                ->label('Profile Status')
                ->formatStateUsing(function (User $record): string {
                    if (! $record->first_name || ! $record->last_name || ! $record->about) {
                        return 'Incomplete';
                    }

                    if (! $record->email_verified_at) {
                        return 'Unverified Email';
                    }

                    if (! $record->identity_verified) {
                        return 'Unverified Identity';
                    }

                    return 'Complete';
                })
                ->colors([
                    'danger' => fn ($state): bool => $state === 'Incomplete',
                    'warning' => fn ($state): bool => $state === 'Unverified Email' || $state === 'Unverified Identity',
                    'success' => fn ($state): bool => $state === 'Complete',
                ]),

            Tables\Columns\TextColumn::make('created_at')
                ->label('Joined')
                ->dateTime('M d, H:i')
                ->sortable(),

            Tables\Columns\TextColumn::make('last_seen')
                ->dateTime('M d, H:i')
                ->sortable(),
        ];
    }

    protected function getTableActions(): array
    {
        return [
            Tables\Actions\Action::make('view')
                ->label('View')
                ->icon('heroicon-o-eye')
                ->url(fn (User $record): string => UserResource::getUrl('edit', ['record' => $record])),

            Tables\Actions\Action::make('verify_email')
                ->label('Verify Email')
                ->icon('heroicon-o-envelope')
                ->action(fn (User $record) => $record->update(['email_verified_at' => now()]))
                ->hidden(fn (User $record) => $record->email_verified_at !== null)
                ->color('success'),

            Tables\Actions\Action::make('verify_identity')
                ->label('Verify Identity')
                ->icon('heroicon-o-shield-check')
                ->action(fn (User $record) => $record->update(['identity_verified' => true, 'identity_verified_at' => now()]))
                ->hidden(fn (User $record) => $record->identity_verified)
                ->color('success'),
        ];
    }

    protected function getTableEmptyStateIcon(): ?string
    {
        return 'heroicon-o-user';
    }

    protected function getTableEmptyStateHeading(): ?string
    {
        return 'No new users in the past week';
    }
}
