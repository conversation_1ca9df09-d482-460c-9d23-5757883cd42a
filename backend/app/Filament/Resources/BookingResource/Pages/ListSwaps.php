<?php

namespace App\Filament\Resources\BookingResource\Pages;

use App\Enums\BookingStatus;
use App\Filament\Resources\BookingResource;
use App\Models\Booking;
use Filament\Actions;
use Filament\Resources\Components\Tab;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;

class ListSwaps extends ListRecords
{
    protected static string $resource = BookingResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    public function getDefaultActiveTab(): string|int|null
    {
        return 'pending';
    }

    public function getTabs(): array
    {
        return [
            'all' => Tab::make(),
            'draft' => Tab::make('Draft')
                ->badge(
                    fn () => Booking::query()
                        ->where('status', BookingStatus::CREATED)
                        ->count()
                )
                ->badgeColor('warning')
                ->modifyQueryUsing(fn (Builder $query) => $query
                    ->where('status', BookingStatus::CREATED)),
            'requested' => Tab::make('Requested')
                ->badge(
                    fn () => Booking::query()
                        ->where('status', BookingStatus::REQUESTED)
                        ->count()
                )
                ->badgeColor('success')
                ->modifyQueryUsing(fn (Builder $query) => $query
                    ->where('status', BookingStatus::REQUESTED)),
            'accepted' => Tab::make('Accepted')
                ->badge(
                    fn () => Booking::query()
                        ->where('status', BookingStatus::ACCEPTED)
                        ->count()
                )
                ->badgeColor('danger')
                ->modifyQueryUsing(fn (Builder $query) => $query
                    ->where('status', BookingStatus::ACCEPTED)),
            'rejected' => Tab::make('Rejected')
                ->badge(
                    fn () => Booking::query()
                        ->where('status', BookingStatus::REJECTED)
                        ->count()
                )
                ->badgeColor('danger')
                ->modifyQueryUsing(fn (Builder $query) => $query
                    ->where('status', BookingStatus::REJECTED)),
            'completed' => Tab::make('Completed')
                ->badge(
                    fn () => Booking::query()
                        ->where('status', BookingStatus::COMPLETED)
                        ->count()
                )
                ->badgeColor('success')
                ->modifyQueryUsing(fn (Builder $query) => $query
                    ->where('status', BookingStatus::COMPLETED)),
            'canceled' => Tab::make('Canceled')
                ->badge(
                    fn () => Booking::query()
                        ->where('status', BookingStatus::CANCELED)
                        ->count()
                )
                ->badgeColor('danger')
                ->modifyQueryUsing(fn (Builder $query) => $query
                    ->where('status', BookingStatus::CANCELED)),
        ];
    }
}
