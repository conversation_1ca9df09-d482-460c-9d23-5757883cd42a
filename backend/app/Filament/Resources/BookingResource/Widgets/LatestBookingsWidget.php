<?php

namespace App\Filament\Resources\BookingResource\Widgets;

use App\Enums\BookingStatus;
use App\Filament\Resources\BookingResource;
use App\Models\Booking;
use Filament\Tables;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Database\Eloquent\Builder;

class LatestBookingsWidget extends BaseWidget
{
    protected static ?string $heading = 'Recent Booking Activity';

    protected int|string|array $columnSpan = 'full';

    protected static ?int $sort = 7;

    protected function getTableHeading(): string
    {
        return 'Recent Booking Activity';
    }

    protected function getTableQuery(): Builder
    {
        return Booking::query()
            ->with(['user', 'user_home_relation'])
            ->latest()
            ->limit(10);
    }

    protected function getTableColumns(): array
    {
        return [
            Tables\Columns\TextColumn::make('code')
                ->label('Booking Code')
                ->searchable()
                ->copyable()
                ->weight('bold')
                ->color('primary'),

            Tables\Columns\TextColumn::make('user.email')
                ->label('Guest')
                ->searchable()
                ->icon('heroicon-o-user')
                ->tooltip(fn (Booking $record): string => $record->user?->first_name.' '.$record->user?->last_name),

            Tables\Columns\TextColumn::make('user_home_relation.title')
                ->label('Property')
                ->limit(30)
                ->icon('heroicon-o-home-modern')
                ->tooltip(fn (Booking $record): string => $record->user_home_relation->title ?? 'N/A'),

            Tables\Columns\TextColumn::make('start_at')
                ->label('Check In')
                ->date('M d, Y')
                ->icon('heroicon-o-arrow-right-circle')
                ->sortable(),

            Tables\Columns\TextColumn::make('end_at')
                ->label('Check Out')
                ->date('M d, Y')
                ->icon('heroicon-o-arrow-left-circle')
                ->sortable(),

            Tables\Columns\BadgeColumn::make('status')
                ->colors([
                    'danger' => fn ($state): bool => $state === BookingStatus::REJECTED->value || $state === BookingStatus::CANCELED->value,
                    'warning' => fn ($state): bool => $state === BookingStatus::REQUESTED->value,
                    'success' => fn ($state): bool => $state === BookingStatus::COMPLETED->value,
                    'primary' => fn ($state): bool => $state === BookingStatus::ACCEPTED->value,
                ])
                ->icons([
                    'heroicon-o-x-circle' => fn ($state): bool => $state === BookingStatus::REJECTED->value || $state === BookingStatus::CANCELED->value,
                    'heroicon-o-clock' => fn ($state): bool => $state === BookingStatus::REQUESTED->value,
                    'heroicon-o-check-circle' => fn ($state): bool => $state === BookingStatus::COMPLETED->value,
                    'heroicon-o-check' => fn ($state): bool => $state === BookingStatus::ACCEPTED->value,
                ]),

            Tables\Columns\TextColumn::make('created_at')
                ->label('Created')
                ->dateTime('M d, H:i')
                ->sortable()
                ->icon('heroicon-o-calendar'),
        ];
    }

    protected function getTableActions(): array
    {
        return [
            Tables\Actions\Action::make('view')
                ->label('View Details')
                ->icon('heroicon-o-eye')
                ->color('primary')
                ->button()
                ->url(fn (Booking $record): string => BookingResource::getUrl('edit', ['record' => $record])),
        ];
    }

    protected function getTableRecordsPerPageSelectOptions(): array
    {
        return [5, 10, 25, 50];
    }

    protected function isTablePaginationEnabled(): bool
    {
        return true;
    }
}
