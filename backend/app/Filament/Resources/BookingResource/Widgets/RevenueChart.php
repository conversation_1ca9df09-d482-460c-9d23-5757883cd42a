<?php

namespace App\Filament\Resources\BookingResource\Widgets;

use App\Enums\BookingStatus;
use App\Models\Booking;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Facades\DB;

class RevenueChart extends ChartWidget
{
    protected static ?string $heading = 'Revenue & Booking Metrics';

    protected int|string|array $columnSpan = 'full';

    protected static ?int $sort = 8;

    protected static ?string $maxHeight = '400px';

    public ?string $filter = 'month';

    protected function getData(): array
    {
        $activeFilter = $this->filter;

        // Initialize data based on filter
        [$dateFormat, $groupingFormat, $startDate, $labels] = $this->getFilterParameters($activeFilter);

        // Get revenue data
        $revenueData = Booking::query()
            ->where('status', BookingStatus::COMPLETED)
            ->where('created_at', '>=', $startDate)
            ->select([
                DB::raw("DATE_FORMAT(created_at, '{$dateFormat}') as date"),
                DB::raw("COALESCE(SUM(JSON_EXTRACT(extra_info, '$.total_price')), 0) as revenue"),
                DB::raw('COUNT(*) as count'),
            ])
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        // Prepare datasets
        $revenueByDate = [];
        $countByDate = [];

        foreach ($labels as $label) {
            $revenueByDate[$label] = 0;
            $countByDate[$label] = 0;
        }

        foreach ($revenueData as $data) {
            if (isset($revenueByDate[$data->date])) {
                $revenueByDate[$data->date] = (float) $data->revenue;
                $countByDate[$data->date] = $data->count;
            }
        }

        // Calculate the total revenue across all time
        $totalRevenue = array_sum(array_values($revenueByDate));
        $totalBookings = array_sum(array_values($countByDate));
        $averageRevenuePerBooking = $totalBookings > 0 ? $totalRevenue / $totalBookings : 0;

        return [
            'datasets' => [
                [
                    'label' => 'Revenue',
                    'data' => array_values($revenueByDate),
                    'borderColor' => '#10B981',
                    'backgroundColor' => 'rgba(16, 185, 129, 0.1)',
                    'fill' => true,
                    'tension' => 0.2,
                    'yAxisID' => 'y',
                ],
                [
                    'label' => 'Bookings',
                    'data' => array_values($countByDate),
                    'borderColor' => '#6366F1',
                    'backgroundColor' => 'rgba(99, 102, 241, 0.1)',
                    'type' => 'bar',
                    'yAxisID' => 'y1',
                ],
            ],
            'labels' => array_keys($revenueByDate),
            'metadata' => [
                'total_revenue' => '$'.number_format($totalRevenue, 2),
                'total_bookings' => $totalBookings,
                'average_revenue' => '$'.number_format($averageRevenuePerBooking, 2),
                'period' => $this->getFilterLabel($activeFilter),
            ],
        ];
    }

    protected function getType(): string
    {
        return 'line';
    }

    protected function getOptions(): array
    {
        $metadata = $this->getData()['metadata'] ?? [];

        return [
            'scales' => [
                'y' => [
                    'beginAtZero' => true,
                    'position' => 'left',
                    'title' => [
                        'display' => true,
                        'text' => 'Revenue ($)',
                        'font' => [
                            'weight' => 'bold',
                        ],
                    ],
                    'ticks' => [
                        'callback' => '(value) => "$" + value.toLocaleString()',
                        'font' => [
                            'weight' => 'bold',
                        ],
                    ],
                    'grid' => [
                        'color' => 'rgba(200, 200, 200, 0.2)',
                    ],
                ],
                'y1' => [
                    'beginAtZero' => true,
                    'position' => 'right',
                    'title' => [
                        'display' => true,
                        'text' => 'Number of Bookings',
                        'font' => [
                            'weight' => 'bold',
                        ],
                    ],
                    'grid' => [
                        'drawOnChartArea' => false,
                    ],
                    'ticks' => [
                        'font' => [
                            'weight' => 'bold',
                        ],
                    ],
                ],
                'x' => [
                    'grid' => [
                        'color' => 'rgba(200, 200, 200, 0.2)',
                    ],
                    'ticks' => [
                        'font' => [
                            'weight' => 'bold',
                        ],
                    ],
                ],
            ],
            'plugins' => [
                'tooltip' => [
                    'callbacks' => [
                        'label' => '(context) => {
                            let label = context.dataset.label || "";
                            let value = context.parsed.y;
                            if (label === "Revenue") {
                                return label + ": $" + value.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2});
                            } else {
                                return label + ": " + value;
                            }
                        }',
                    ],
                    'backgroundColor' => 'rgba(0, 0, 0, 0.8)',
                    'titleFont' => [
                        'size' => 14,
                    ],
                    'bodyFont' => [
                        'size' => 13,
                    ],
                    'padding' => 10,
                    'cornerRadius' => 6,
                ],
                'legend' => [
                    'position' => 'top',
                    'labels' => [
                        'font' => [
                            'weight' => 'bold',
                            'size' => 13,
                        ],
                        'padding' => 20,
                    ],
                ],
                'title' => [
                    'display' => true,
                    'text' => [
                        'Revenue & Bookings ('.$this->getFilterLabel($this->filter).')',
                        'Total: '.($metadata['total_revenue'] ?? '$0').' | Bookings: '.($metadata['total_bookings'] ?? '0').' | Avg: '.($metadata['average_revenue'] ?? '$0'),
                    ],
                    'font' => [
                        'size' => 16,
                        'weight' => 'bold',
                    ],
                    'padding' => [
                        'top' => 10,
                        'bottom' => 20,
                    ],
                ],
                'subtitle' => [
                    'display' => true,
                    'text' => 'Comparison of revenue and number of bookings over time',
                    'font' => [
                        'size' => 14,
                        'style' => 'italic',
                    ],
                    'padding' => [
                        'bottom' => 20,
                    ],
                ],
            ],
            'responsive' => true,
            'maintainAspectRatio' => false,
            'interaction' => [
                'mode' => 'index',
                'intersect' => false,
            ],
        ];
    }

    protected function getFilters(): ?array
    {
        return [
            'week' => 'Last 7 days',
            'month' => 'Last 30 days',
            'quarter' => 'Last 90 days',
            'year' => 'This year',
        ];
    }

    /**
     * Get date format, group format, start date, and labels based on filter
     */
    private function getFilterParameters(string $filter): array
    {
        $labels = [];

        switch ($filter) {
            case 'week':
                $dateFormat = '%Y-%m-%d';
                $groupingFormat = 'Y-m-d';
                $startDate = now()->subDays(7);

                // Generate last 7 days labels
                for ($i = 6; $i >= 0; $i--) {
                    $date = now()->subDays($i)->format('Y-m-d');
                    $labels[$date] = 0;
                }
                break;

            case 'month':
                $dateFormat = '%Y-%m-%d';
                $groupingFormat = 'Y-m-d';
                $startDate = now()->subDays(30);

                // Group by 3 days to avoid too many labels
                $days = 30;
                $interval = 3;
                $currentDate = now()->startOfDay();

                for ($i = 0; $i < $days; $i += $interval) {
                    $date = $currentDate->copy()->subDays($i)->format('Y-m-d');
                    $labels[$date] = 0;
                }
                $labels = array_reverse($labels, true);
                break;

            case 'quarter':
                $dateFormat = '%Y-%m-%d';
                $groupingFormat = 'Y-m-d';
                $startDate = now()->subDays(90);

                // Group by week
                $weeks = 13;
                $currentDate = now()->startOfDay();

                for ($i = 0; $i < $weeks; $i++) {
                    $date = $currentDate->copy()->subWeeks($i)->startOfWeek()->format('Y-m-d');
                    $labels[$date] = 0;
                }
                $labels = array_reverse($labels, true);
                break;

            case 'year':
                $dateFormat = '%Y-%m';
                $groupingFormat = 'Y-m';
                $startDate = now()->startOfYear();

                // Generate monthly labels
                $currentMonth = now()->month;

                for ($i = 1; $i <= $currentMonth; $i++) {
                    $date = now()->setMonth($i)->startOfMonth()->format('Y-m');
                    $labels[$date] = 0;
                }
                break;

            default:
                $dateFormat = '%Y-%m-%d';
                $groupingFormat = 'Y-m-d';
                $startDate = now()->subDays(30);

                // Generate last 30 days labels
                for ($i = 29; $i >= 0; $i--) {
                    $date = now()->subDays($i)->format('Y-m-d');
                    $labels[$date] = 0;
                }
                break;
        }

        return [$dateFormat, $groupingFormat, $startDate, $labels];
    }

    /**
     * Get human-readable label for filter
     */
    private function getFilterLabel(string $filter): string
    {
        return match ($filter) {
            'week' => 'Last 7 days',
            'month' => 'Last 30 days',
            'quarter' => 'Last 90 days',
            'year' => 'This year',
            default => 'Last 30 days',
        };
    }
}
