<?php

namespace App\Filament\Resources\BookingResource\Widgets;

use App\Enums\BookingStatus;
use App\Filament\Resources\BookingResource;
use App\Models\Booking;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\DB;

class BookingWidget extends BaseWidget
{
    protected ?string $heading = 'Booking Overview';

    protected static ?int $sort = 6;

    protected function getStats(): array
    {
        $totalBookings = Booking::query()->count();
        $pendingBookings = Booking::query()->where('status', BookingStatus::REQUESTED)->count();
        $acceptedBookings = Booking::query()->where('status', BookingStatus::ACCEPTED)->count();
        $rejectedBookings = Booking::query()->where('status', BookingStatus::REJECTED)->count();
        $unpaidBookings = Booking::query()
            ->where('status', BookingStatus::ACCEPTED)
            ->count();
        $upcomingBookings = Booking::query()
            ->where('status', BookingStatus::COMPLETED)
            ->where('start_at', '>=', now())
            ->where('start_at', '<=', now()->addDays(7))
            ->count();
        $upcomingCheckoutBookings = Booking::query()
            ->where('status', BookingStatus::COMPLETED)
            ->where('end_at', '>=', now())
            ->where('end_at', '<=', now()->addDays(7))
            ->count();
        $averageBookingDuration = round(
            Booking::query()
                ->where('status', BookingStatus::COMPLETED)
                ->select(DB::raw('AVG(DATEDIFF(end_at, start_at)) as avg_duration'))
                ->value('avg_duration') ?? 0);
        $activeBookings = Booking::query()
            ->where('status', BookingStatus::COMPLETED)
            ->where('start_at', '<=', now())
            ->where('end_at', '>=', now())
            ->count();

        return [
            Stat::make('Total Bookings', $totalBookings)
                ->description('The total number of bookings made')
                ->icon('heroicon-o-document-text')
                ->color('gray')
                ->chart([5, 10, 15, 20, 15, 25, $totalBookings])
                ->url(BookingResource::getUrl('index', ['activeTab' => 'all'])),

            Stat::make('Pending Bookings', $pendingBookings)
                ->description('Bookings requiring approval or payment')
                ->icon('heroicon-o-clock')
                ->color($pendingBookings > 0 ? 'warning' : 'gray')
                ->chart([2, 4, 3, 5, 4, 3, $pendingBookings])
                ->url(BookingResource::getUrl('index', ['activeTab' => 'pending'])),

            Stat::make('Accepted Bookings', $acceptedBookings)
                ->description('Bookings confirmed by both parties')
                ->icon('heroicon-o-check-circle')
                ->color('success')
                ->chart([3, 5, 7, 12, 10, 8, $acceptedBookings])
                ->url(BookingResource::getUrl('index', ['activeTab' => 'accepted'])),

            Stat::make('Rejected Bookings', $rejectedBookings)
                ->description('Rejected or cancelled bookings')
                ->icon('heroicon-o-x-circle')
                ->color($rejectedBookings > 0 ? 'danger' : 'gray')
                ->chart([1, 3, 2, 4, 3, 2, $rejectedBookings])
                ->url(BookingResource::getUrl('index', ['activeTab' => 'rejected'])),

            Stat::make('Unpaid Bookings', $unpaidBookings)
                ->description('Swaps with pending payments')
                ->icon('heroicon-o-credit-card')
                ->color($unpaidBookings > 0 ? 'warning' : 'gray')
                ->chart([1, 2, 3, 2, 1, 2, $unpaidBookings])
                ->url(BookingResource::getUrl()),

            Stat::make('Active Bookings Now', $activeBookings)
                ->description('Currently active bookings')
                ->icon('heroicon-o-home-modern')
                ->color('primary')
                ->chart([2, 3, 4, 5, 6, 5, $activeBookings])
                ->url(BookingResource::getUrl()),

            Stat::make('Upcoming Check-ins', $upcomingBookings)
                ->description('Check-ins in the next 7 days')
                ->icon('heroicon-o-arrow-right-circle')
                ->color('info')
                ->chart([0, 2, 1, 3, 2, 4, $upcomingBookings])
                ->url(BookingResource::getUrl()),

            Stat::make('Upcoming Check-outs', $upcomingCheckoutBookings)
                ->description('Check-outs in the next 7 days')
                ->icon('heroicon-o-arrow-left-circle')
                ->color('info')
                ->chart([0, 1, 3, 2, 4, 3, $upcomingCheckoutBookings])
                ->url(BookingResource::getUrl()),

            Stat::make('Avg. Booking Duration', $averageBookingDuration.' days')
                ->description('Average stay duration')
                ->icon('heroicon-o-calendar-days')
                ->color('success')
                ->chart([3, 4, 5, 6, 7, 6, $averageBookingDuration])
                ->url(BookingResource::getUrl()),
        ];
    }
}
