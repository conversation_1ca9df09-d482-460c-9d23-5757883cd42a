<?php

namespace App\Filament\Resources;

use App\Enums\BookingStatus;
use App\Filament\Resources\BookingResource\Pages;
use App\Models\Booking;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Support\Enums\FontWeight;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use pxlrbt\FilamentExcel\Actions\Tables\ExportBulkAction;

class BookingResource extends Resource
{
    protected static ?string $model = Booking::class;

    protected static ?string $modelLabel = 'Booking';

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('user_id')
                    ->relationship('user', 'first_name')
                    ->required(),
                Select::make('user_home')
                    ->relationship('fromHome', 'title'),
                Select::make('request_user_home')
                    ->relationship('toHome', 'title')
                    ->required(),
                DatePicker::make('start_at')
                    ->required(),
                DatePicker::make('end_at')
                    ->required(),
                Textarea::make('comment')
                    ->columnSpanFull(),
            ]);
    }

    /**
     * @throws \Exception
     */
    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->sortable(),
                Tables\Columns\TextColumn::make('user.first_name')
                    ->label('User')
                    ->weight(FontWeight::Bold)
                    ->wrap()
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('fromHome.title')
                    ->label('From Home')
                    ->words(5)
                    ->wrap()
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('toHome.title')
                    ->label('To Home')
                    ->words(5)
                    ->wrap()
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->formatStateUsing(fn (BookingStatus $state): string => $state->value)
                    ->color(fn (BookingStatus $state): string => match ($state) {
                        BookingStatus::CREATED => 'gray',
                        BookingStatus::REQUESTED => 'warning',
                        BookingStatus::ACCEPTED => 'success',
                        BookingStatus::REJECTED => 'danger',
                        BookingStatus::CANCELED => 'danger',
                        BookingStatus::COMPLETED => 'success',
                        default => 'warning',
                    }),
                Tables\Columns\TextColumn::make('start_at')
                    ->label('Start Date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('end_at')
                    ->label('End Date')
                    ->date()
                    ->sortable(),
                Tables\Columns\IconColumn::make('payment_status')
                    ->label('Paid')
                    ->icon(fn ($state) => $state ? 'heroicon-s-check-circle' : 'heroicon-s-x-circle')
                    ->color(fn ($state) => $state ? 'success' : 'danger'),
                Tables\Columns\TextColumn::make('swap_type')
                    ->state(function (Booking $booking) {
                        return $booking->fromHome ? 'Swap' : 'Rent';
                    })
                    ->color(fn (string $state) => $state === 'Swap' ? 'primary' : 'success')
                    ->badge()
                    ->label('Type'),
                Tables\Columns\TextColumn::make('extra_info')
                    ->formatStateUsing(fn ($state) => json_encode($state, JSON_PRETTY_PRINT))
                    ->badge()
                    ->wrap()
                    ->color('gray')
                    ->label('Payment Info')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Created At')
                    ->since()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('swap_type')
                    ->multiple()
                    ->options([
                        'swap' => 'Swap',
                        'rent' => 'Rent',
                    ])
                    ->query(function (Builder $query, array $data) {
                        return $query
                            ->when(
                                in_array('swap', $data['values'] ?? []),
                                fn (Builder $query) => $query->whereHas('fromHome')
                            )
                            ->when(
                                in_array('rent', $data['values'] ?? []),
                                fn (Builder $query) => $query->whereDoesntHave('fromHome')
                            );
                    }),
                Tables\Filters\Filter::make('paid')
                    ->label('Paid Bookings')
                    ->toggle()
                    ->query(fn (Builder $query): Builder => $query->where('payment_status', true)),
                Tables\Filters\Filter::make('unpaid')
                    ->label('Unpaid Bookings')
                    ->toggle()
                    ->query(fn (Builder $query): Builder => $query->where('payment_status', false)),
                Tables\Filters\Filter::make('waiting_payment')
                    ->label('Waiting for Payment')
                    ->toggle()
                    ->query(fn (Builder $query): Builder => $query->where('is_waiting_on_payment', true)),
                Tables\Filters\Filter::make('recent')
                    ->label('Created Last 30 Days')
                    ->toggle()
                    ->query(fn (Builder $query): Builder => $query->where('created_at', '>=', now()->subDays(30))),
                Tables\Filters\Filter::make('upcoming')
                    ->toggle()
                    ->query(fn (Builder $query): Builder => $query->where('start_at', '>=', now())),
                Tables\Filters\Filter::make('past')
                    ->toggle()
                    ->query(fn (Builder $query): Builder => $query->where('end_at', '<', now())),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->defaultSort('id', 'desc')
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
                ExportBulkAction::make(),
            ]);
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->with(['user', 'fromHome', 'toHome', 'guestReview', 'hostReview', 'messagesV2']);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSwaps::route('/'),
            'create' => Pages\CreateSwap::route('/create'),
            'edit' => Pages\EditSwap::route('/{record}/edit'),
        ];
    }
}
