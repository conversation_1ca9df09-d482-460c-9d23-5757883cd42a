<?php

namespace App\Filament\Resources\HomeResource\Widgets;

use App\Filament\Resources\HomeResource;
use App\Models\Booking;
use App\Models\Home;
use Filament\Tables;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class PopularHomesWidget extends BaseWidget
{
    protected static ?string $heading = 'Popular Homes';

    protected int|string|array $columnSpan = 'full';

    protected static ?int $sort = 25;

    public ?string $filter = 'bookings';

    protected function getTableQuery(): Builder
    {
        // Get homes with booking counts
        $query = Home::query()
            ->select([
                'homes.id',
                'homes.title',
                'homes.address',
                'homes.nightly_rate',
                'homes.status',
                'homes.created_at',
                'homes.user_id',
                DB::raw('COUNT(bookings.id) as booking_count'),
                DB::raw('MAX(bookings.created_at) as latest_booking'),
            ])
            ->leftJoin('bookings', 'homes.id', '=', 'bookings.request_user_home')
            ->groupBy('homes.id', 'homes.title', 'homes.address', 'homes.nightly_rate', 'homes.status', 'homes.created_at', 'homes.user_id');

        if ($this->filter === 'bookings') {
            $query->orderByDesc('booking_count')
                ->orderByDesc('latest_booking');
        } elseif ($this->filter === 'newest') {
            $query->orderByDesc('homes.created_at');
        } elseif ($this->filter === 'highest_rate') {
            $query->orderByDesc('homes.nightly_rate');
        }

        return $query;
    }

    protected function getTableColumns(): array
    {
        return [
            Tables\Columns\TextColumn::make('title')
                ->label('Home')
                ->limit(30)
                ->searchable()
                ->url(fn (Home $record): string => HomeResource::getUrl('edit', ['record' => $record])),

            Tables\Columns\TextColumn::make('user.email')
                ->label('Owner')
                ->limit(25)
                ->searchable(),

            Tables\Columns\TextColumn::make('address')
                ->label('Location')
                ->limit(30)
                ->searchable(),

            Tables\Columns\TextColumn::make('nightly_rate')
                ->money('USD')
                ->label('Price')
                ->sortable(),

            Tables\Columns\TextColumn::make('booking_count')
                ->label('Bookings')
                ->sortable(),

            Tables\Columns\BadgeColumn::make('status')
                ->colors([
                    'danger' => fn ($state): bool => $state === 'inactive',
                    'warning' => fn ($state): bool => $state === 'pending' || $state === 'draft',
                    'success' => fn ($state): bool => $state === 'active',
                ]),

            Tables\Columns\TextColumn::make('created_at')
                ->label('Added')
                ->date('M d, Y')
                ->sortable(),
        ];
    }

    protected function getTableActions(): array
    {
        return [
            Tables\Actions\Action::make('view')
                ->label('View')
                ->icon('heroicon-o-eye')
                ->url(fn (Home $record): string => HomeResource::getUrl('edit', ['record' => $record])),
        ];
    }

    protected function getTableFilters(): array
    {
        return [
            Tables\Filters\SelectFilter::make('status')
                ->options([
                    'active' => 'Active',
                    'inactive' => 'Inactive',
                    'pending' => 'Pending',
                    'draft' => 'Draft',
                ])
                ->attribute('status'),
        ];
    }

    protected function getFilters(): ?array
    {
        return [
            'bookings' => 'Most Booked',
            'newest' => 'Newest Listings',
            'highest_rate' => 'Highest Nightly Rate',
        ];
    }

    protected function getTableEmptyStateIcon(): ?string
    {
        return 'heroicon-o-home';
    }

    protected function getTableEmptyStateHeading(): ?string
    {
        return 'No homes found';
    }
}
