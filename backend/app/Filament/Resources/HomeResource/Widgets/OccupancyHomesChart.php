<?php

namespace App\Filament\Resources\HomeResource\Widgets;

use App\Models\Home;
use Filament\Widgets\ChartWidget;

class OccupancyHomesChart extends ChartWidget
{
    protected static ?string $heading = 'Homes Occupancy Trends';

    protected static ?string $description = 'Number of homes occupied in the last 12 months.';

    protected static ?int $sort = 5;

    protected int|string|array $columnSpan = 'full';

    protected static ?string $maxHeight = '400px';

    protected function getData(): array
    {
        $months = [];
        $monthLabels = [];

        // Get the last 12 months in reverse order (most recent first)
        for ($i = 11; $i >= 0; $i--) {
            $monthDate = now()->subMonths($i);
            $months[] = $monthDate->format('F Y');
            $monthLabels[] = $monthDate->format('M Y');
        }

        $occupiedHomes = [];
        $totalHomes = [];

        foreach ($months as $month) {
            $monthDate = \DateTime::createFromFormat('F Y', $month);
            $monthNum = $monthDate->format('m');
            $yearNum = $monthDate->format('Y');

            // Count occupied homes for this month
            $occupied = Home::query()->whereHas('occupiedHomes', function ($query) use ($monthNum, $yearNum) {
                $query->whereMonth('start_at', $monthNum)
                    ->whereYear('start_at', $yearNum);
            })->count();

            // Count total active homes for this month
            $total = Home::query()
                ->where('status', 'active')
                ->where(function ($query) use ($monthNum, $yearNum) {
                    $query->whereMonth('created_at', '<=', $monthNum)
                        ->whereYear('created_at', '<=', $yearNum);
                })
                ->count();

            $occupiedHomes[] = $occupied;
            $totalHomes[] = $total;
        }

        // Calculate occupancy percentage
        $occupancyPercentage = [];
        foreach ($occupiedHomes as $index => $occupied) {
            $total = $totalHomes[$index] > 0 ? $totalHomes[$index] : 1; // Avoid division by zero
            $occupancyPercentage[] = round(($occupied / $total) * 100, 1);
        }

        return [
            'labels' => $monthLabels,
            'datasets' => [
                [
                    'label' => 'Occupied Homes',
                    'backgroundColor' => 'rgba(59, 130, 246, 0.7)', // Blue
                    'borderColor' => 'rgb(59, 130, 246)',
                    'borderWidth' => 2,
                    'data' => $occupiedHomes,
                    'barPercentage' => 0.6,
                    'categoryPercentage' => 0.7,
                ],
                [
                    'label' => 'Total Active Homes',
                    'backgroundColor' => 'rgba(209, 213, 219, 0.5)', // Gray
                    'borderColor' => 'rgb(156, 163, 175)',
                    'borderWidth' => 2,
                    'data' => $totalHomes,
                    'barPercentage' => 0.6,
                    'categoryPercentage' => 0.7,
                ],
                [
                    'label' => 'Occupancy Rate (%)',
                    'backgroundColor' => 'rgba(16, 185, 129, 0.1)', // Green
                    'borderColor' => 'rgb(16, 185, 129)',
                    'borderWidth' => 2,
                    'type' => 'line',
                    'data' => $occupancyPercentage,
                    'yAxisID' => 'percentage',
                    'fill' => false,
                    'tension' => 0.4,
                ],
            ],
        ];
    }

    protected function getType(): string
    {
        return 'bar';
    }

    protected function getOptions(): array
    {
        return [
            'scales' => [
                'y' => [
                    'beginAtZero' => true,
                    'title' => [
                        'display' => true,
                        'text' => 'Number of Homes',
                        'font' => [
                            'weight' => 'bold',
                        ],
                    ],
                    'grid' => [
                        'color' => 'rgba(200, 200, 200, 0.2)',
                    ],
                    'ticks' => [
                        'font' => [
                            'weight' => 'bold',
                        ],
                    ],
                ],
                'percentage' => [
                    'position' => 'right',
                    'beginAtZero' => true,
                    'max' => 100,
                    'title' => [
                        'display' => true,
                        'text' => 'Occupancy Rate (%)',
                        'font' => [
                            'weight' => 'bold',
                        ],
                    ],
                    'grid' => [
                        'drawOnChartArea' => false,
                    ],
                    'ticks' => [
                        'callback' => '(value) => value + "%"',
                        'font' => [
                            'weight' => 'bold',
                        ],
                    ],
                ],
                'x' => [
                    'grid' => [
                        'color' => 'rgba(200, 200, 200, 0.2)',
                    ],
                    'ticks' => [
                        'font' => [
                            'weight' => 'bold',
                        ],
                    ],
                ],
            ],
            'plugins' => [
                'legend' => [
                    'position' => 'top',
                    'labels' => [
                        'font' => [
                            'weight' => 'bold',
                            'size' => 13,
                        ],
                        'padding' => 20,
                    ],
                ],
                'tooltip' => [
                    'callbacks' => [
                        'label' => '(context) => {
                            let label = context.dataset.label || "";
                            let value = context.parsed.y;
                            if (label === "Occupancy Rate (%)") {
                                return label + ": " + value + "%";
                            } else {
                                return label + ": " + value;
                            }
                        }',
                    ],
                    'backgroundColor' => 'rgba(0, 0, 0, 0.8)',
                    'titleFont' => [
                        'size' => 14,
                    ],
                    'bodyFont' => [
                        'size' => 13,
                    ],
                    'padding' => 10,
                    'cornerRadius' => 6,
                ],
                'title' => [
                    'display' => true,
                    'text' => 'Home Occupancy Trends (Last 12 Months)',
                    'font' => [
                        'size' => 16,
                        'weight' => 'bold',
                    ],
                    'padding' => [
                        'top' => 10,
                        'bottom' => 20,
                    ],
                ],
                'subtitle' => [
                    'display' => true,
                    'text' => 'Comparison of occupied homes vs. total active homes',
                    'font' => [
                        'size' => 14,
                        'style' => 'italic',
                    ],
                    'padding' => [
                        'bottom' => 20,
                    ],
                ],
            ],
            'responsive' => true,
            'maintainAspectRatio' => false,
            'interaction' => [
                'mode' => 'index',
                'intersect' => false,
            ],
        ];
    }
}
