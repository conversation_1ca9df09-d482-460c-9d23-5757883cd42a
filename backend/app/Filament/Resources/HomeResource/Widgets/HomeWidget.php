<?php

namespace App\Filament\Resources\HomeResource\Widgets;

use App\Enums\HomeStatus;
use App\Filament\Resources\HomeResource;
use App\Models\Home;
use App\Services\RoundedIntConverterService;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class HomeWidget extends BaseWidget
{
    protected ?string $heading = 'Property Overview';

    protected static ?int $sort = 3;

    protected function getCards(): array
    {
        $totalListings = Home::query()->count();
        $activeListings = Home::query()->where('status', HomeStatus::ACTIVE->value)->count();
        $pendingListings = Home::query()->where('status', '!=', HomeStatus::ACTIVE->value)->count();
        $upcomingCheckinsHomes = Home::query()->whereHas('upcomingCheckInHomes')->count();
        $upcomingCheckoutsHomes = Home::query()->whereHas('upcomingCheckOutHomes')->count();
        $occupiedHomes = Home::query()->whereHas('occupiedHomes')->count();
        $occupancyRate = $activeListings > 0
            ? (new RoundedIntConverterService)($occupiedHomes / $activeListings * 100)
            : 0;

        return [
            Stat::make('Total Listings', $totalListings)
                ->description('All property listings in the system')
                ->icon('heroicon-o-home')
                ->color('gray')
                ->chart([7, 2, 10, 3, 15, 4, $totalListings])
                ->url(HomeResource::getUrl()),

            Stat::make('Active Listings', $activeListings)
                ->description('Currently active and bookable listings')
                ->icon('heroicon-o-check-badge')
                ->color('success')
                ->chart([3, 5, 7, 12, 10, 8, $activeListings])
                ->url(HomeResource::getUrl('index', ['activeTab' => 'active'])),

            Stat::make('Pending Listings', $pendingListings)
                ->description('Listings awaiting approval or with pending details')
                ->icon('heroicon-o-clock')
                ->color($pendingListings > 0 ? 'warning' : 'gray')
                ->chart([2, 4, 3, 5, 4, 3, $pendingListings])
                ->url(HomeResource::getUrl('index', ['activeTab' => 'inactive'])),

            Stat::make('Occupied Listings', $occupiedHomes)
                ->description('Listings with active bookings')
                ->icon('heroicon-o-user-group')
                ->color('primary')
                ->chart([1, 3, 2, 5, 4, 7, $occupiedHomes])
                ->url(HomeResource::getUrl()),

            Stat::make('Occupancy Rate', "$occupancyRate%")
                ->description('Percentage of listings with active bookings')
                ->icon('heroicon-o-chart-bar')
                ->color($occupancyRate > 75 ? 'success' : ($occupancyRate > 50 ? 'warning' : 'danger'))
                ->chart([20, 30, 40, 50, 60, 70, $occupancyRate])
                ->url(HomeResource::getUrl()),

            Stat::make('Upcoming Check-ins', $upcomingCheckinsHomes)
                ->description('Homes with check-in dates in the next 7 days')
                ->icon('heroicon-o-arrow-right-circle')
                ->color('info')
                ->chart([0, 2, 1, 3, 2, 4, $upcomingCheckinsHomes])
                ->url(HomeResource::getUrl()),

            Stat::make('Upcoming Check-outs', $upcomingCheckoutsHomes)
                ->description('Homes with check-out dates in the next 7 days')
                ->icon('heroicon-o-arrow-left-circle')
                ->color('info')
                ->chart([0, 1, 3, 2, 4, 3, $upcomingCheckoutsHomes])
                ->url(HomeResource::getUrl()),
        ];
    }
}
