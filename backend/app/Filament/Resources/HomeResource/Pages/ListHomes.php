<?php

namespace App\Filament\Resources\HomeResource\Pages;

use App\Enums\HomeStatus;
use App\Filament\Resources\HomeResource;
use App\Models\Home;
use Filament\Actions;
use Filament\Resources\Components\Tab;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;

class ListHomes extends ListRecords
{
    protected static string $resource = HomeResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    public function getDefaultActiveTab(): string|int|null
    {
        return 'inactive';
    }

    public function getTabs(): array
    {
        return [
            'all' => Tab::make(),
            'active' => Tab::make('Active')
                ->badge(
                    fn () => Home::query()
                        ->where('status', HomeStatus::ACTIVE->value)
                        ->count())
                ->badgeColor('success')
                ->modifyQueryUsing(fn (Builder $query) => $query
                    ->with('user')
                    ->where('status', HomeStatus::ACTIVE->value)),
            'inactive' => Tab::make('Draft')
                ->badge(
                    fn () => Home::query()
                        ->where('status', '!=', HomeStatus::ACTIVE->value)
                        ->count())
                ->badgeColor('danger')
                ->modifyQueryUsing(fn (Builder $query) => $query
                    ->with('user')
                    ->where('status', '!=', HomeStatus::ACTIVE->value)),
        ];
    }
}
