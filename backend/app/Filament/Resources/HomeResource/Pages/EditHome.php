<?php

namespace App\Filament\Resources\HomeResource\Pages;

use App\Enums\HomeStatus;
use App\Filament\Resources\HomeResource;
use App\Models\Home;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Database\Eloquent\Model;

class EditHome extends EditRecord
{
    protected static string $resource = HomeResource::class;

    protected function getHeaderActions(): array
    {
        // Get current record
        $record = $this->getRecord();

        /**
         * @var Home $record
         */
        if ($record->status !== HomeStatus::ACTIVE->value) {
            $action = Actions\Action::make('force_active')
                ->label('Activate')
                ->icon('heroicon-o-exclamation-triangle')
                ->color('danger')
                ->action(fn (Home $record) => $record->update(['status' => HomeStatus::ACTIVE->value]))
                ->requiresConfirmation()
                ->modalDescription('Are you sure you want to force this home to be active? This action needs to be done with caution because it will bypass the home validation.');
        } else {
            $action = Actions\Action::make('force_inactive')
                ->label('Deactivate')
                ->icon('heroicon-o-exclamation-triangle')
                ->color('danger')
                ->action(fn (Home $record) => $record->update(['status' => HomeStatus::DRAFT->value]))
                ->requiresConfirmation()
                ->modalDescription('Are you sure you want to force this home to be inactive? This action needs to be done with caution because it will bypass the home validation.');
        }

        return [
            $action,
            Actions\DeleteAction::make(),
        ];
    }

    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        unset($record->photos, $record->owner);

        $record->update($data);

        return $record;
    }
}
