<?php

namespace App\Filament\Resources;

use App\Filament\Resources\HomeResource\Pages;
use App\Models\Home;
use Exception;
use Filament\Forms\Components\MultiSelect;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Support\Enums\FontWeight;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use pxlrbt\FilamentExcel\Actions\Tables\ExportBulkAction;

class HomeResource extends Resource
{
    protected static ?string $model = Home::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('title')
                    ->required(),
                Textarea::make('description'),
                TextInput::make('location'),
                TextInput::make('address'),
                TextInput::make('country_long')
                    ->label('Country'),
                TextInput::make('state_long')
                    ->label('State'),
                TextInput::make('city_long')
                    ->label('City'),
                TextInput::make('street'),
                TextInput::make('beds')
                    ->numeric(),
                TextInput::make('baths')
                    ->numeric(),
                TextInput::make('number_of_beds')
                    ->numeric(),
                TextInput::make('guests')
                    ->numeric(),
                MultiSelect::make('amenities')
                    ->options([
                        // Provide key-value pairs for amenities.
                    ]),
                Toggle::make('allow_booking'),
                Toggle::make('allow_swaps'),
                Toggle::make('on_site_booking'),
                Toggle::make('allow_points'),
                TextInput::make('points_price')
                    ->numeric()->dehydrated(fn ($state) => $state !== null),
                TextInput::make('cleaning_fee')
                    ->numeric(),
                TextInput::make('tax_rate')
                    ->numeric(),
                Toggle::make('offer_as_seasonal_lease'),
                Textarea::make('seasonal_lease_description'),
                TextInput::make('minimum_stay_rentals')
                    ->numeric(),
                TextInput::make('minimum_stay_swaps')
                    ->numeric(),
                TextInput::make('sharable_link'),
                TextInput::make('nightly_rate')
                    ->numeric(),
                // Booking URLs and ratings
                TextInput::make('airbnb_url')
                    ->url(),
                TextInput::make('vrbo_url')
                    ->url(),
                TextInput::make('other_url')
                    ->url(),
                TextInput::make('search_score')
                    ->numeric(),
                // Other fields here if necessary
            ]);
    }

    /**
     * @throws Exception
     */
    public static function table(Table $table): Table
    {
        return $table
            ->actions([
                Tables\Actions\Action::make('frontpage')
                    ->label('')
                    ->icon('heroicon-o-eye')
                    ->url(fn (Home $home): string => $home->front_page_view_as_url)
                    ->openUrlInNewTab(),
                Tables\Actions\EditAction::make()->label(''),
            ])
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->sortable(),
                Tables\Columns\TextColumn::make('title')
                    ->wrap()
                    ->searchable()
                    ->sortable()
                    ->weight(FontWeight::Bold),
                Tables\Columns\TextColumn::make('user.first_name')
                    ->wrap()
                    ->label('Owner')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('user.email')
                    ->wrap()
                    ->label('Owner Email')
                    ->searchable()
                    ->wrap()
                    ->copyable()
                    ->copyMessage('Email copied to clipboard')
                    ->sortable(),
                Tables\Columns\TextColumn::make('country_long')
                    ->label('Country')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('state_long')
                    ->label('State')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('city_long')
                    ->label('City')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\ToggleColumn::make('allow_booking')
                    ->label('Book')
                    ->sortable(),
                Tables\Columns\ToggleColumn::make('allow_swaps')
                    ->label('Swap')
                    ->sortable(),
                Tables\Columns\ToggleColumn::make('allow_points')
                    ->label('Pay with points')
                    ->sortable(),
                Tables\Columns\ToggleColumn::make('on_site_booking')
                    ->label('On site')
                    ->sortable(),
                Tables\Columns\ToggleColumn::make('featured')
                    ->sortable(),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label('Last Updated')
                    ->since()
                    ->sortable(),
            ])
            ->defaultSort('id', 'desc')
            ->filters([
                Tables\Filters\Filter::make('allow_booking')
                    ->toggle()
                    ->query(fn (Builder $query): Builder => $query->where('allow_booking', true)),
                Tables\Filters\Filter::make('allow_points')
                    ->toggle()
                    ->query(fn (Builder $query): Builder => $query->where('allow_points', true)),
                Tables\Filters\Filter::make('allow_swaps')
                    ->toggle()
                    ->query(fn (Builder $query): Builder => $query->where('allow_swaps', true)),
                Tables\Filters\Filter::make('on_site_booking')
                    ->toggle()
                    ->query(fn (Builder $query): Builder => $query->where('on_site_booking', true)),
                Tables\Filters\Filter::make('featured')
                    ->toggle()
                    ->query(fn (Builder $query): Builder => $query->where('featured', true)),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
                ExportBulkAction::make(),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListHomes::route('/'),
            'create' => Pages\CreateHome::route('/create'),
            'edit' => Pages\EditHome::route('/{record}/edit'),
        ];
    }
}
