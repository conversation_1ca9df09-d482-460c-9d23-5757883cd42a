<?php

namespace App\Http\Resources;

use App\Models\GuestList;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Collection;

/**
 * @property int $id
 * @property int $guest_id
 * @property User|null $guest
 * @property int $host_id
 * @property User|null $host
 * @property array $home_ids
 * @property Carbon $created_at
 * @property Carbon|null $updated_at
 * @property Carbon|null $deleted_at
 * @property array|Collection|null $accessible_sharable_links
 */
final class GuestListResource extends JsonResource
{
    public function toArray($request): array
    {
        /* @var GuestList|self $this */
        return [
            'id' => $this->id,

            'guest_id' => $this->guest_id,
            'guest' => $this->whenLoaded('guest', fn () => $this->guest),

            'host_id' => $this->host_id,
            'host' => $this->whenLoaded('host', fn () => $this->host),

            'home_ids' => $this->home_ids,

            'created_at' => $this->created_at->toDateTimeString(),
            'updated_at' => $this->updated_at?->toDateTimeString(),
            'deleted_at' => $this->deleted_at?->toDateTimeString(),

            'accessible_sharable_links' => is_array($this->accessible_sharable_links)
                ? $this->accessible_sharable_links
                : $this->accessible_sharable_links?->values()?->all() ?? [],
        ];
    }
}
