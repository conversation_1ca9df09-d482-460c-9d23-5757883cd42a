<?php

namespace App\Http\Resources;

use App\Models\Home;
use App\Models\User;
use App\Services\GetUserProfileCompletionService;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @property string $first_name
 * @property string|null $about
 * @property string $shared_url
 * @property string $canonical_url
 * @property string|null $avatar
 * @property \Illuminate\Database\Eloquent\Collection<\App\Models\Home> $homes
 * @property string|null $host_logo
 * @property string|null $host_landing_page
 */
final class GetPublicUserDetailBySharedUrlResource extends JsonResource
{
    public function toArray($request): array
    {
        /* @var User|self $this */
        return [
            'first_name' => $this->first_name,
            'about' => $this->about,
            'shared_url' => $this->shared_url,
            'canonical_url' => $this->canonical_url,
            'avatar' => $this->avatar,
            'profile_completion' => (new GetUserProfileCompletionService)($this->resource),
            'homes' => $this->whenLoaded('homes', fn () => $this->mapHomeData($this->homes), []),
            'host_logo' => $this->host_logo,
            'host_landing_page' => $this->host_landing_page,
        ];
    }

    private function mapHomeData(Collection $homes): array
    {
        return $homes->map(static function (Model $home, $key) {
            if (! $home instanceof Home) {
                return $home;
            }
            $home->setAppends(['photos']);

            return $home;
        })->values()->all();
    }
}
