<?php

namespace App\Http\Resources;

use App\Models\Booking;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @property int $id
 * @property int $sender_id
 * @property int $receiver_id
 * @property string $messageable_type
 * @property int $messageable_id
 * @property string|null $subject
 * @property string $body
 * @property bool $viewed
 * @property bool $archived_by_sender
 * @property bool $archived_by_receiver
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property User|null $sender
 * @property User|null $receiver
 * @property Model|null $messageable
 *
 * @method bool isDirectMessage()
 * @method bool isBookingMessage()
 */
final class MessageResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'sender' => $this->whenLoaded('sender', $this->sender),
            'sender_id' => $this->sender_id,
            'receiver' => $this->whenLoaded('receiver', $this->receiver),
            'receiver_id' => $this->receiver_id,
            'messageable' => $this->whenLoaded(
                'messageable',
                $this->messageable instanceof Booking ? new BookingResource($this->messageable) : $this->messageable
            ),
            'messageable_type' => $this->messageable_type,
            'messageable_id' => $this->messageable_id,
            'subject' => $this->getMessageableTitle(),
            'body' => $this->body,
            'viewed' => $this->viewed,
            'created_at' => $this->created_at->toDateString(),
            'updated_at' => $this->updated_at->toDateString(),
            'archived_by_sender' => $this->archived_by_sender,
            'archived_by_receiver' => $this->archived_by_receiver,
        ];
    }

    private function getMessageableTitle(): string
    {
        if ($this->isDirectMessage()) {
            return $this->subject ?? 'Unknown';
        }

        if ($this->isBookingMessage()) {
            return $this->messageable->toHome->title ?? 'Unknown';
        }

        return 'Unknown';
    }
}
