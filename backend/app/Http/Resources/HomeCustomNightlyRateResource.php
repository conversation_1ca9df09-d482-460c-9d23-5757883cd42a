<?php

namespace App\Http\Resources;

use App\Models\HomeCustomNightlyRate;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @property int $id
 * @property int $home_id
 * @property float $nightly_rate
 * @property array $conditions
 * @property bool $is_active
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property \App\Models\Home|null $home
 */
final class HomeCustomNightlyRateResource extends JsonResource
{
    public static $wrap;

    public function toArray($request): array
    {
        /* @var HomeCustomNightlyRate|self $this */
        return [
            'id' => $this->id,
            'home_id' => $this->home_id,
            'nightly_rate' => $this->nightly_rate,
            'conditions' => $this->conditions,
            'is_active' => $this->is_active,

            'created_at' => $this->created_at->toDateTimeString(),
            'updated_at' => $this->updated_at->toDateTimeString(),

            'home' => $this->whenLoaded('home'),
        ];
    }
}
