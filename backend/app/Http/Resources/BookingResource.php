<?php

namespace App\Http\Resources;

use App\Models\Booking;
use App\Models\Home;
use App\Models\IndefiniteApproval;
use App\Models\Message;
use App\Models\Review;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @property int $id
 * @property Carbon $start_at
 * @property Carbon $end_at
 * @property int $user_home
 * @property int $request_user_home
 * @property int $user_id
 * @property Home|null $fromHome
 * @property Home|null $toHome
 * @property User|null $user
 * @property Collection<Message> $messages
 * @property Collection<Review> $reviews
 * @property string $booking_type
 * @property bool $guest_cleaning_fee_enabled
 * @property bool $host_cleaning_fee_enabled
 * @property string|null $comment
 * @property string $status
 * @property array|null $extra_info
 * @property string $code
 * @property bool $from_sharable_link
 * @property Carbon $created_at
 * @property Carbon|null $updated_at
 */
final class BookingResource extends JsonResource
{
    public function toArray($request): array
    {
        /* @var Booking|self $this */
        $approvedIndefinitely = IndefiniteApproval::query()
            ->where([
                'guest_id' => $this->user_id,
                'home_id' => $this->request_user_home,
            ])->exists();

        /* @var Booking|self $this */
        return [
            'id' => $this->id,
            'start_at' => $this->start_at->toDateString(),
            'end_at' => $this->end_at->toDateString(),
            'home_id' => $this->user_home,
            'request_home_id' => $this->request_user_home,
            'user_id' => $this->user_id,
            'fromHome' => $this->whenLoaded('fromHome', fn () => $this->fromHome),
            'toHome' => $this->whenLoaded('toHome', fn () => $this->toHome),
            'user' => $this->whenLoaded('user', fn () => $this->user),
            'messages' => $this->whenLoaded('messages', fn () => $this->messages),
            'reviews' => $this->whenLoaded('reviews', fn () => $this->reviews),
            'booking_type' => $this->booking_type,
            'guest_cleaning_fee_enabled' => $this->guest_cleaning_fee_enabled,
            'host_cleaning_fee_enabled' => $this->host_cleaning_fee_enabled,
            'comment' => $this->comment,
            'status' => $this->status,
            'extra_info' => $this->extra_info,
            'code' => $this->code,
            'approved_indefinitely' => $approvedIndefinitely,
            'from_sharable_link' => $this->from_sharable_link,
            'created_at' => $this->created_at->toDateTimeString(),
            'updated_at' => $this->updated_at?->toDateTimeString(),
        ];
    }
}
