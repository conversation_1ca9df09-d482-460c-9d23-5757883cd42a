<?php

namespace App\Http\Resources;

use App\Models\SharableLink;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @property int $id
 * @property int $home_id
 * @property string $link
 * @property array|null $price_info
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
final class SharableLinkResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        /* @var SharableLink|self $this */

        return [
            'id' => $this->id,
            'home_id' => $this->home_id,
            'link' => $this->link,
            'price_info' => $this->price_info,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
