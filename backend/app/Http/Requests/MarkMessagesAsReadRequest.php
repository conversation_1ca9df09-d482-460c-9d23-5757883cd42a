<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

final class MarkMessagesAsReadRequest extends FormRequest
{
    public function authorize(): true
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'message_ids' => ['required', 'array'],
            'message_ids.*' => ['integer', 'exists:messages,id'],
        ];
    }
}
