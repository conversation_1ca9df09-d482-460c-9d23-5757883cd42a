<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

final class UserProfileUpdateRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'first_name' => 'required|max:255|min:2|regex:/^[\pL\s\-]+$/u',
            'last_name' => 'required|max:255|min:2|regex:/^[\pL\s\-]+$/u',
            'email' => 'required|email',
            'phone_number' => 'required|regex:/^\+1[0-9]{10}$/|size:12',
            'about' => 'nullable|max:500',
            'avatar' => 'nullable|image|max:5120', // 5MB max
        ];
    }
}
