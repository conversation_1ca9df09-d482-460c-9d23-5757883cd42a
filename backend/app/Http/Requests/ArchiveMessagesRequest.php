<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

final class ArchiveMessagesRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'message_ids' => ['required', 'array'],
            'message_ids.*' => ['integer', 'exists:messages,id'],
        ];
    }
}
