<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

final class AdjustSharableLinkPriceRequest extends FormRequest
{
    public function authorize(): true
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'pet_fee' => 'nullable|numeric|min:0',
            'tax_rate' => 'nullable|numeric|min:0|max:100',
            'cleaning_fee' => 'nullable|numeric|min:0',
            'minimum_stay' => 'nullable|integer|min:1',
            'nightly_rate' => 'nullable|numeric|min:0',
        ];
    }
}
