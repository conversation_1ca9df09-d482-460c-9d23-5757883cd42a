<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

final class GetHomeCustomNightlyRateForDateRangeRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'home_id' => 'required|exists:homes,id',
            'from_date' => 'required|date',
            'to_date' => 'required|date|after_or_equal:from_date',
        ];
    }
}
