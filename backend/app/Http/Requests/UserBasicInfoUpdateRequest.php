<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

final class UserBasicInfoUpdateRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'first_name' => 'sometimes|required|max:255|min:2|regex:/^[\pL\s\-]+$/u',
            'last_name' => 'sometimes|required|max:255|min:2|regex:/^[\pL\s\-]+$/u',
            'about' => 'sometimes|nullable|max:500',
        ];
    }
}
