<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

final class CreateSharableLinkRequest extends FormRequest
{
    public function authorize(): true
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'price_info' => 'nullable|array',
            'price_info.pet_fee' => 'nullable|numeric|min:0',
            'price_info.tax_rate' => 'nullable|numeric|min:0|max:100',
            'price_info.cleaning_fee' => 'nullable|numeric|min:0',
            'price_info.minimum_stay' => 'nullable|integer|min:1',
            'price_info.nightly_rate' => 'nullable|numeric|min:0',
        ];
    }
}
