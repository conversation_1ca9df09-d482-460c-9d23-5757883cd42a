<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

final class CreateHomeRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'title' => 'required|string',
            'address' => 'required|string',
            'beds' => 'required|integer',
            'baths' => 'required|numeric',
            'number_of_beds' => 'required|integer',
            'guests' => 'required|integer|min:1',
            'amenities' => 'nullable|array',
            'description' => 'nullable|string',
            'nightly_rate' => 'required|numeric|min:0',
            'cleaning_fee' => 'nullable|numeric',
            'tax_rate' => 'nullable|numeric',
            'pet_fee' => 'nullable|numeric',
            'allow_booking' => 'required|boolean',
            'allow_swaps' => 'required|boolean',
            'offer_as_seasonal_lease' => 'required|boolean',
            'minimum_stay_rentals' => 'nullable|integer',
            'extra_info' => 'nullable|array',
            'photos' => 'nullable|array',
            'photos.*' => 'required',

            'location' => 'sometimes|required',
            'user_id' => 'sometimes|required|exists:users,id',
            'links' => 'sometimes|required|array',
        ];
    }
}
