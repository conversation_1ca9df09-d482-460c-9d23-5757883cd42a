<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

final class UpdateBlockAccessRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'guest_id' => 'required|exists:users,id',
            'sharable_link_id' => 'required|exists:sharable_links,id',
            'is_blocked' => 'required|boolean',
        ];
    }
}
