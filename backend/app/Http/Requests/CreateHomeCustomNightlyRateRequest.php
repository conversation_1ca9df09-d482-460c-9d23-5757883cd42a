<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

final class CreateHomeCustomNightlyRateRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'home_id' => 'required|exists:homes,id',
            'nightly_rate' => 'required|numeric',
            'conditions' => 'required|array',
            'conditions.from_date' => 'nullable|date',
            'conditions.to_date' => 'nullable|date|after_or_equal:conditions.from_date',
            'conditions.days_of_week' => 'nullable|array',
            'conditions.days_of_week.*' => 'nullable|integer|between:0,6',
        ];
    }
}
