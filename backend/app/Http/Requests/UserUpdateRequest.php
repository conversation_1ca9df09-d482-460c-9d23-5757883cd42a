<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

final class UserUpdateRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'title' => 'sometimes|required|max:255',
            'address' => 'sometimes|required|max:255',
            'beds' => 'sometimes|required|min:1|max:5',
            'baths' => 'sometimes|required|min:1|max:5',
            'description' => 'sometimes|required',
            'location' => 'sometimes|required',
            'nightly_rate' => 'sometimes|nullable|integer|min:1',
            'cleaning_fee' => 'sometimes|required|integer|min:0',
            'tax_rate' => 'sometimes|required|numeric|between:0,100',
            'allow_swaps' => 'sometimes|required|boolean',
            'allow_booking' => 'sometimes|required|boolean',
            'booking_from_on_site' => 'sometimes|required|boolean',
            'booking_from_off_site' => 'sometimes|required|boolean',
            'booking_from_white_labeled' => 'sometimes|required|boolean',

            'offer_as_seasonal_lease' => 'sometimes|required|boolean',
            'seasonal_lease_description' => 'sometimes|nullable|string',
            'minimum_stay_rentals' => 'sometimes|required|integer|min:1',
            'minimum_stay_swaps' => 'sometimes|required|integer|min:1',

            'sharable_link' => 'sometimes|required|string',
        ];
    }
}
