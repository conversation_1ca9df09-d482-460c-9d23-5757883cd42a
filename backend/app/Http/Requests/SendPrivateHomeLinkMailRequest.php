<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

final class SendPrivateHomeLinkMailRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'email' => 'required|email',
            'sharable_link_id' => 'required|integer|exists:sharable_links,id',
        ];
    }
}
