<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

final class UpdateHomeRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        $rules = [
            'title' => 'sometimes|required|string',
            'address' => 'sometimes|nullable|string',
            'beds' => 'sometimes|required|integer',
            'baths' => 'sometimes|required|numeric',
            'number_of_beds' => 'sometimes|required|integer',
            'guests' => 'sometimes|required|integer|min:1',
            'amenities' => 'sometimes|nullable|array',
            'description' => 'sometimes|nullable|string',
            'nightly_rate' => 'sometimes|required|numeric|min:0',
            'cleaning_fee' => 'sometimes|nullable|numeric',
            'tax_rate' => 'sometimes|nullable|numeric',
            'pet_fee' => 'sometimes|nullable|numeric',
            'allow_booking' => 'sometimes|required|boolean',
            'allow_swaps' => 'sometimes|required|boolean',
            'on_site_booking' => 'sometimes|required|boolean',
            'booking_redirect_url' => 'sometimes|nullable|string',
            'offer_as_seasonal_lease' => 'sometimes|required|boolean',
            'minimum_stay_rentals' => 'sometimes|nullable|integer',
            'extra_info' => 'sometimes|nullable|array',
            'photos' => 'sometimes|nullable|array',
            'photos.*' => 'sometimes|required|integer|exists:media,id',
            'status' => 'sometimes|required|string',

            'location' => 'sometimes|required',
            'user_id' => 'sometimes|required|exists:users,id',
            'links' => 'sometimes|required|array',

            'sharable_link' => 'sometimes|required|string|min:8|max:24',
            'sharable_password' => 'sometimes|required|string|min:6|max:24',
        ];

        return $rules;
    }
}
