<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

final class CreateBookingRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'start_at' => 'required|date',
            'end_at' => 'required|date|after_or_equal:start_at',
            'user_home' => 'sometimes|nullable|exists:homes,id',
            'request_user_home' => 'required|exists:homes,id',
            'booking_type' => 'required|string',
            'comment' => 'sometimes|nullable|string',
            'extra_info' => 'sometimes|nullable|array',

            'from_sharable_link' => 'sometimes|nullable|string',
        ];
    }
}
