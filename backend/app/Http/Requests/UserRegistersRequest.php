<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

final class UserRegistersRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'first_name' => 'required|max:255|min:2|regex:/^[\pL\s\-]+$/u',
            'last_name' => 'required|max:255|min:2|regex:/^[\pL\s\-]+$/u',
            'email' => 'required|email|unique:users,email',
            'password' => 'required|max:24|min:8',
            'phone_number' => 'required|regex:/^\+1[0-9]{10}$/|size:12',
            'opted_in_to_email' => 'required|boolean',
            'opted_in_to_sms' => 'required|boolean',
            'opted_in_to_communications' => 'boolean',
            'recaptcha_response' => 'required|string',
        ];
    }
}
