<?php

namespace App\Http\Controllers;

use App\Helpers\PhoneNumberHelper;
use App\Http\Requests\UserProfileUpdateRequest;
use App\Http\Requests\UserBasicInfoUpdateRequest;
use App\Http\Resources\UserResource;
use App\Models\User;
use App\Services\UpdateUserService;
use App\Traits\ExceptionLoggableTrait;
use Illuminate\Http\Request;

final readonly class UserController
{
    use ExceptionLoggableTrait;

    public function getProfile(
        Request $request,
    ): UserResource {
        $authUser = $request->user();

        /* @var User $authUser */
        $authUser->load(
            [
                'role',
                'media',
                'homes' => static function ($query) {
                    $query
                        ->setEagerLoads([])
                        ->select(['id', 'status']);
                },
            ]
        );

        return new UserResource($authUser);
    }

    public function update(UserProfileUpdateRequest $request, UpdateUserService $updateUserService): UserResource
    {
        $authUser = $request->user();
        $requestData = $request->validated();

        // Format phone number if present
        if (isset($requestData['phone_number'])) {
            $requestData['phone_number'] = PhoneNumberHelper::formatPhoneNumber($requestData['phone_number']);
        }

        return new UserResource($updateUserService($authUser, $requestData));
    }

    public function updateBasicInfo(UserBasicInfoUpdateRequest $request): UserResource
    {
        $authUser = $request->user();
        $requestData = $request->validated();

        // Only update the fields that were provided
        $authUser->update($requestData);

        /* @var User $authUser */
        $authUser->load([
            'role',
            'media',
            'homes' => static function ($query) {
                $query
                    ->setEagerLoads([])
                    ->select(['id', 'status']);
            },
        ]);

        return new UserResource($authUser);
    }

    public function update_venmo(Request $request): UserResource
    {
        $authUser = $request->user();

        // Validate the incoming request
        $validatedData = $request->validate([
            'venmo_username' => 'nullable|string',
        ]);

        // Update the user's role_id and user_type
        $authUser->update([
            'venmo_username' => $validatedData['venmo_username'],
        ]);

        return new UserResource($authUser);
    }

    public function update_account_type(Request $request): UserResource
    {
        $authUser = $request->user();

        // Validate the incoming request
        $validatedData = $request->validate([
            'role_id' => 'required|integer',
            'user_type' => 'required|string',
        ]);

        // Update the user's role_id and user_type
        $authUser->update([
            'role_id' => $validatedData['role_id'],
            'user_type' => $validatedData['user_type'],
        ]);

        /* @var User $authUser */
        $authUser->load(
            [
                'role',
                'media',
                'homes' => static function ($query) {
                    $query
                        ->setEagerLoads([])
                        ->select(['id', 'status']);
                },
            ]
        );

        return new UserResource($authUser);
    }
}
