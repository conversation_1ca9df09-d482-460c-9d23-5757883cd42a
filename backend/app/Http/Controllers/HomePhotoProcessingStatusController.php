<?php

namespace App\Http\Controllers;

use App\Models\Home;
use App\Traits\ExceptionLoggableTrait;
use Illuminate\Http\JsonResponse;

final class HomePhotoProcessingStatusController
{
    use ExceptionLoggableTrait;

    public function __invoke(Home $home): JsonResponse
    {
        try {
            return response()->json([
                'status' => $home->photo_processing_status,
                'is_from_airbnb' => $home->is_from_airbnb,
                'airbnb_url' => $home->airbnb_url,
            ]);
        } catch (\Exception $e) {
            $this->log('Error fetching photo processing status', [
                'error' => $e->getMessage(),
                'home_id' => $home->id,
            ], 'error');

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to fetch photo processing status',
            ], 500);
        }
    }
}
