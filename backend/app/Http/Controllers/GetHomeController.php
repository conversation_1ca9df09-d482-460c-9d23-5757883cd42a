<?php

namespace App\Http\Controllers;

use App\Models\Home;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

final class GetHomeController
{
    public function __invoke(int|string $id): JsonResponse
    {
        $home = Home::query()
            ->where('id', $id)
            ->orWhere('slug', $id)
            ->without(['user', 'owner'])
            ->firstOrFail();

        $home->makeHidden(['user', 'owner']);

        return response()->json($home);
    }

    public function getSwapHomes(Request $request): JsonResponse
    {
        $user = auth()->user();

        $homes = Home::query()
            ->where('allow_swaps', 1)
            ->where('user_id', '=', $user->id)
            ->where('status', '=', 'active')
            ->without(['user', 'owner'])
            ->get();

        if ($homes->isEmpty()) {
            return response()->json(['hasSwapEnabled' => false, 'message' => 'No homes found with allow_swaps = 1']);
        }

        return response()->json(['hasSwapEnabled' => true, 'message' => 'Homes found with allow_swaps = 1']);
    }
}
