<?php

namespace App\Http\Controllers;

use App\Helpers\PhoneNumberHelper;
use App\Http\Requests\LoginRequest;
use App\Http\Requests\UserRegistersRequest;
use App\Mail\ResetPassword;
use App\Models\ActionToken;
use App\Models\User;
use App\Services\BrevoService;
use App\Services\CreateUserService;
use App\Services\GenerateTokenForAnUserService;
use Carbon\Carbon;
use Illuminate\Auth\Events\Verified;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

final readonly class AuthController
{
    public const int FORGOT_PASSWORD_TOKEN_DURATION = 180; // 3 hours

    private const string RECAPTCHA_VERIFY_URL = 'https://www.google.com/recaptcha/api/siteverify';

    public function __construct(
        private GenerateTokenForAnUserService $generateTokenForAnUserService
    ) {}

    public function register(
        UserRegistersRequest $request,
        CreateUserService $userService,
        BrevoService $brevoService
    ): JsonResponse {
        // Verify reCAPTCHA and get response data
        $recaptchaResponse = Http::asForm()->post(self::RECAPTCHA_VERIFY_URL, [
            'secret' => config('services.recaptcha.secret_key'),
            'response' => $request->get('recaptcha_response'),
            'remoteip' => $request->ip(),
        ])->json();

        if (
            ! isset($recaptchaResponse['success']) || ! $recaptchaResponse['success'] ||
            (isset($recaptchaResponse['score']) && $recaptchaResponse['score'] < 0.5)
        ) {
            Log::warning('reCAPTCHA verification failed for registration:', [
                'ip' => $request->ip(),
                'user_agent' => $request->header('User-Agent'),
                'response' => $recaptchaResponse,
            ]);

            return response()->json([
                'error' => 'Security check failed. Please try again.',
                'code' => 'recaptcha_failed',
            ], 400);
        }

        // Prepare additional info
        $additionalInfo = [
            'opted_in_to_email' => $request->get('opted_in_to_email', false),
            'opted_in_to_sms' => $request->get('opted_in_to_sms', false),
            'opted_in_to_communications' => $request->get(
                'opted_in_to_communications',
                ($request->get('opted_in_to_email', false) || $request->get('opted_in_to_sms', false))
            ),
            'opt_in_timestamp' => now()->toDateTimeString(),
            'opt_in_ip_address' => $request->ip(),
            'recaptcha_score' => $recaptchaResponse['score'] ?? null,
            'recaptcha_action' => $recaptchaResponse['action'] ?? null,
            'registration_user_agent' => $request->header('User-Agent'),
        ];

        $user = $userService(
            $request->get('email'),
            $request->get('first_name'),
            $request->get('last_name'),
            $request->get('phone_number'),
            $request->get('password'),
            $request->get('user_type', 'traveler'),
            $request->get('group_id'),
            $request->get('rewardPointUuid'),
            $request->get('invite_code'),
            false, // welcomeSend
            0,    // interviewsPassed
            null, // about
            null, // avatar
            $additionalInfo
        );

        // Add user to Brevo contact list if they opted in to communications
        if (
            (isset($additionalInfo['opted_in_to_email']) && $additionalInfo['opted_in_to_email'] === true) ||
            (isset($additionalInfo['opted_in_to_sms']) && $additionalInfo['opted_in_to_sms'] === true)
        ) {

            // Prepare any additional attributes to send to Brevo
            $attributes = [
                'FIRSTNAME' => $request->get('first_name'),
                'LASTNAME' => $request->get('last_name'),
                'SMS' => $this->formatPhoneNumber($request->get('phone_number')),
                'OPTIN_TIME' => $additionalInfo['opt_in_timestamp'],
                'OPTIN_IP' => $additionalInfo['opt_in_ip_address'],
                'SOURCE' => 'Website Registration',
                'SMS_OPT_IN' => $additionalInfo['opted_in_to_sms'] ? 'yes' : 'no',
                'EMAIL_OPT_IN' => $additionalInfo['opted_in_to_email'] ? 'yes' : 'no',
            ];

            // Add the user to the Brevo contact list
            // We don't need to wait for a response or handle errors since the BrevoService
            // logs errors internally and doesn't block the registration flow
            $brevoService->addContact($request->get('email'), $attributes);
        }

        return response()->json($user);
    }

    public function login(LoginRequest $request): JsonResponse
    {
        $credentials = $request->validated();

        if (! $token = auth('api')->attempt($credentials)) {
            return response()->json(['error' => 'Your email or password is incorrect'], 401);
        }

        $user = auth('api')->user();

        /**
         * @var User $user
         */
        $user->load('role');

        return response()->json([
            'access_token' => $token,
            'user' => $user,
        ]);
    }

    // Password Reset
    public function sendEmail(Request $request): JsonResponse
    {
        $user = User::where('email', $request->email)->firstOrFail();

        $token = ActionToken::query()->create([
            'action' => ActionToken::RESTORE_PASSWORD,
            'user_id' => $user->id,
            'token' => Str::random(24),
            'expired_at' => ActionToken::getExpiredDate(self::FORGOT_PASSWORD_TOKEN_DURATION),
        ]);
        /** @var ActionToken $token */
        Mail::to($user)->queue(new ResetPassword($token->token));

        return response()->json(['success' => true]);
    }

    public function verifyToken(Request $request): JsonResponse
    {
        $token = ActionToken::where('token', $request->token)->first();
        $is_valid_token = $token !== null;

        if ($token && Carbon::parse($token->expired_at)->isPast()) {
            $is_valid_token = false;
            $token->delete();
        }

        return response()->json(['is_valid_token' => $is_valid_token]);
    }

    public function resetPassword(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'token' => 'required|exists:action_tokens,token',
            'password' => 'required|min:8|confirmed',
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        $data = $validator->validated();

        $token = ActionToken::where('token', $data['token'])->first();

        $user = User::find($token->user_id);

        $user->password = bcrypt($data['password']);
        $user->save();
        $token->delete();

        return response()->json(['success' => true]);
    }

    // Email Verification
    public function verify(Request $request): JsonResponse
    {
        $user = User::query()->findOrFail($request->route('id'));

        if (! hash_equals((string) $request->route('hash'), sha1($user->getEmailForVerification()))) {
            return response()->json(['message' => 'Invalid verification link'], 400);
        }

        if ($user->hasVerifiedEmail()) {
            return response()->json(['message' => 'Email already verified']);
        }

        if ($user->markEmailAsVerified()) {
            event(new Verified($user));
        }

        $user->load('role');

        $token = ($this->generateTokenForAnUserService)($user);

        return response()->json([
            'message' => 'Email verified successfully',
            'token' => $token,
            'user' => $user,
        ]);
    }

    public function resend(Request $request): JsonResponse
    {
        $user = User::query()->where('email', $request->email)->first();

        if (! $user) {
            return response()->json(['message' => 'User not found'], 404);
        }

        if ($user->hasVerifiedEmail()) {
            return response()->json(['message' => 'Email already verified']);
        }

        $user->sendEmailVerificationNotification();

        return response()->json(['message' => 'Verification email sent']);
    }

    /**
     * Format phone number using the PhoneNumberHelper
     */
    private function formatPhoneNumber(?string $phoneNumber): ?string
    {
        return PhoneNumberHelper::formatPhoneNumber($phoneNumber);
    }
}
