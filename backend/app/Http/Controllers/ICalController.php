<?php

namespace App\Http\Controllers;

use App\Enums\BookingStatus;
use App\Models\BlockedDate;
use App\Models\Booking;
use App\Models\CalendarEvent;
use App\Models\Home;
use App\Models\HomeAvailable;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Spatie\IcalendarGenerator\Components\Calendar;
use Spatie\IcalendarGenerator\Components\Event;

final class ICalController extends Controller
{
    /**
     * @throws Exception
     */
    public function createForAProperty(int $homeId): mixed
    {
        $home = Home::query()->findOrFail($homeId);
        /** @var Home $home */
        $calendar = Calendar::create($home->title);

        $events = [];

        foreach ($home->available_dates as $available_date) {
            /** @var HomeAvailable $available_date */
            $events[] = Event::create('48 Dots (BLOCKED)')
                ->fullDay()
                ->period(Carbon::parse($available_date->start_at), Carbon::parse($available_date->end_at));
        }

        $bookings = Booking::query()
            ->select(['start_at', 'end_at'])
            ->where(function ($query) use ($home) {
                $query->where('request_user_home', $home->id)
                    ->orWhere('user_home', $home->id);
            })
            ->whereIn('status', [
                BookingStatus::ACCEPTED->value,
                BookingStatus::COMPLETED->value,
            ])
            ->get();

        foreach ($bookings as $booking) {
            /** @var Booking $booking */
            $events[] = Event::create('48 Dots (BOOKED)')
                ->fullDay()
                ->period(Carbon::parse($booking->start_at), Carbon::parse($booking->end_at));
        }

        $calendar->event($events);

        $fileName = Str::slug($home->title.' '.$home->id).'.ics';

        return response($calendar->get(), 200, [
            'Content-Type' => 'text/calendar; charset=utf-8',
            'Content-Disposition' => 'attachment; filename="'.$fileName.'"',
        ]);
    }

    public function retrieveAllDates(Request $request): array
    {
        $homeId = (int) $request->get('home_id');

        $home = Home::query()->findOrFail($homeId);
        $dates = [];

        // USER CHOSEN DATES FOR THEIR HOMES
        /** @var array $home_available */
        foreach ($home->available_dates->toArray() as $home_available) {
            $dates[] = [
                'type' => 'home_available',
                'start_at' => $home_available['start_at'],
                'end_at' => $home_available['end_at'],
            ];
        }

        // BOOKED DATES
        $swaps = Booking::query()
            ->select(['start_at', 'end_at'])
            ->where(function ($query) use ($home) {
                $query->where('request_user_home', $home->id)
                    ->orWhere('user_home', $home->id);
            })
            ->whereIn('status', [
                BookingStatus::ACCEPTED->value,
                BookingStatus::COMPLETED->value,
            ])
            ->get();

        foreach ($swaps->toArray() as $swap) {
            $dates[] = [
                'type' => 'accepted_swaps',
                'start_at' => $swap['start_at'],
                'end_at' => $swap['end_at'],
            ];
        }

        $blocked_dates = BlockedDate::query()
            ->where('home_id', $homeId)
            ->get();

        foreach ($blocked_dates->toArray() as $blocked_date) {
            $dates[] = [
                'type' => $blocked_date['type'],
                'start_at' => $blocked_date['start_at'],
                'end_at' => $blocked_date['end_at'],
            ];
        }

        // CALENDAR EVENTS
        $calendar_events = CalendarEvent::query()
            ->where('home_id', $homeId)
            ->where('block_dates', 1)
            ->get();
        foreach ($calendar_events->toArray() as $calendar_event) {
            $dates[] = [
                'type' => 'calendar_event',
                'start_at' => $calendar_event['start_date'],
                'end_at' => $calendar_event['end_date'],
            ];
        }

        return $dates;
    }
}
