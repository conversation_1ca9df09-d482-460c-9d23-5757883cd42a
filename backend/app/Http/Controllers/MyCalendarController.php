<?php

namespace App\Http\Controllers;

use App\Models\CalendarEvent;
use App\Models\CalendarImport;
use App\Models\EventCategory;
use App\Models\Home;
use Carbon\Carbon;
use DB;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Spatie\IcalendarGenerator\Components\Calendar;
use Spatie\IcalendarGenerator\Components\Event;
use Throwable;

final class MyCalendarController extends Controller
{
    public function getEventCategories(Request $request): array
    {
        $categories = EventCategory::select('id', 'category_name as label', 'color')
            ->where('status', 'active')
            ->orderBy('sort_order')
            ->get();

        return ['status' => 'success', 'categories' => $categories];
    }

    public function createEvent(Request $request): array
    {
        $user = auth()->user();
        $form_data = $request->post('form_data');
        $ce_id = CalendarEvent::query()->insertGetId(
            [
                'home_id' => $form_data['home'],
                'events_categories_id' => $form_data['category'],

                'start_date' => date('Y-m-d', strtotime($form_data['startDate'])),
                'end_date' => date('Y-m-d', strtotime($form_data['endDate'])),
                'event_title' => $form_data['title'],
                'recurrence' => $form_data['recurrence'],
                'notes' => $form_data['notes'],
                'block_dates' => $form_data['blockDates'],
                'user_id' => $user?->id,
            ]
        );
        $home = Home::query()->findOrFail($form_data['home']);

        return ['status' => 'success', 'calendar_event_id' => $ce_id, 'home_slug' => $home->slug];
    }

    public function updateEvent($eventId, Request $request): array
    {
        $form_data = $request->post('form_data');

        $ce = CalendarEvent::find($eventId);
        $ce->home_id = $form_data['home'];
        $ce->events_categories_id = $form_data['category'];
        $ce->start_date = date('Y-m-d', strtotime($form_data['startDate']));
        $ce->end_date = date('Y-m-d', strtotime($form_data['endDate']));
        $ce->event_title = $form_data['title'];
        $ce->recurrence = $form_data['recurrence'];
        $ce->notes = $form_data['notes'];
        $ce->block_dates = $form_data['blockDates'];
        $ce->save();

        return ['status' => 'success', 'event_id' => $eventId];
    }

    public function deleteEvent($eventId): array
    {
        $ce = CalendarEvent::find($eventId);
        $ce->delete();

        return ['status' => 'success', 'event_id' => $eventId];
    }

    public function getAllEvents(Request $request): array
    {
        // $month = (int) $request->get('month');
        // $year = (int) $request->get('year');
        $categories = explode(',', $request->get('categories'));
        $home = (int) $request->get('home');
        $user = auth()->user();

        $dashboard_data = [];
        $dates = [];

        $startDate = $request->get('start');
        $endDate = $request->get('end');

        $events_query = CalendarEvent::select('events_categories_id', DB::raw('COUNT(*) as total'))
            ->where(function ($query) use ($startDate, $endDate) {
                $query->whereBetween('start_date', [$startDate, $endDate])
                    ->orWhereBetween('end_date', [$startDate, $endDate])
                    ->orWhere(function ($query) use ($startDate, $endDate) {
                        $query->where('start_date', '<', $startDate)
                            ->where('end_date', '>', $endDate);
                    });
            })
            ->where('events.user_id', $user?->id)
            ->groupBy('events_categories_id')
            ->get();

        foreach ($events_query->toArray() as $eq) {
            $dashboard_data[] = [
                'type' => $eq['events_categories_id'],
                'count' => $eq['total'],
            ];
        }

        $events = CalendarEvent::query()
            ->select(['events.start_date', 'events.end_date', 'homes.title as home_name', 'home_id', 'events.id', 'event_title', 'events_categories_id', 'events_categories.color', 'events_categories.category_name', 'events.notes', 'events.recurrence', 'events.block_dates'])
            ->join('homes', 'events.home_id', '=', 'homes.id')
            ->join('events_categories', 'events.events_categories_id', '=', 'events_categories.id')
            ->whereDate('start_date', '>=', $startDate)
            ->whereDate('start_date', '<=', $endDate)
            ->whereIn('events.events_categories_id', $categories)
            ->where('events.user_id', $user?->id);

        if ($home != 0) {
            $events->where('home_id', $home);
        }

        $events = $events->get();

        foreach ($events->toArray() as $event) {
            $dates[] = [
                'category_id' => $event['events_categories_id'],
                'start_at' => $event['start_date'],
                'end_at' => $event['end_date'],
                'home_name' => $event['home_name'],
                'home_id' => $event['home_id'],
                'title' => $event['event_title'],
                'id' => $event['id'],
                'color' => $event['color'],
                'category' => $event['category_name'],
                'notes' => $event['notes'],
                'recurrence' => $event['recurrence'],
                'block_dates' => $event['block_dates'],
            ];
        }

        return ['dashboard_data' => $dashboard_data, 'dates' => $dates];
    }

    public function iCalImport(Request $request): array
    {
        DB::beginTransaction();
        try {
            $icalUrl = $request->post('ical_url');
            $home_id = $request->post('home_id');
            $ical_name = $request->post('ical_name');
            $response = null;

            $user = auth()->user();

            $ch = curl_init();

            // Set cURL options
            curl_setopt($ch, CURLOPT_URL, $icalUrl);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);

            // Set a browser-like user-agent
            curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3');

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

            $ical_id = CalendarImport::query()->insertGetId([
                'ical_url' => $icalUrl,
                'ical_name' => $ical_name,
                'home_id' => $home_id,
                'user_id' => $user?->id,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ]);

            $blocked_dates_insert = $this->retrieveAllDatesFromIcal($response, $home_id, $ical_id, $ical_name);

            // CalendarEvent::query()->where('home_id', $home_id)->where('home_id', $home_id)->delete();
            CalendarEvent::query()->insert($blocked_dates_insert);
            DB::commit();

            return ['status' => 'success', 'iCalUrl' => $blocked_dates_insert];
        } catch (Throwable $e) {
            DB::rollBack();

            report($e);

            return ['status' => 'fail', 'message' => $response?->status() ?? 'Unknown error'];
        }
    }

    private function retrieveAllDatesFromIcal($string, $home_id, $ical_id, $ical_name): array
    {
        $arr = [];
        $continue_bool = true;
        $user = auth()->user();
        $start_date = '';
        $end_date = '';
        $type = '';
        $title = '';

        while ($continue_bool) {
            $continue_bool = false;

            $parsed = $this->get_string_between($string, 'PRODID', 'END:VEVENT');

            $end_date_index = strpos($parsed, 'DTEND:');
            $start_date_index = strpos($parsed, 'DTSTART:');

            if ($end_date_index && $start_date_index) {
                $start_date = substr($parsed, ($start_date_index + 8), 8);
                $end_date = substr($parsed, ($end_date_index + 6), 8);
            } else {
                $end_date_index = strpos($parsed, 'DTEND;VALUE=DATE:');
                $start_date_index = strpos($parsed, 'DTSTART;VALUE=DATE:');
                if ($end_date_index && $start_date_index) {
                    $start_date = substr($parsed, ($start_date_index + 19), 8);
                    $end_date = substr($parsed, ($end_date_index + 17), 8);
                }
            }

            if (strpos($parsed, 'SUMMARY:') !== false) {
                $cut_string = strstr($parsed, 'SUMMARY:');

                $type = substr($cut_string, 8);
                $type = explode("\r", $type)[0];

                if (strpos(strtolower($type), 'airbnb') !== false) {
                    $type = 'Airbnb';
                } elseif (strpos(strtolower($type), 'vrbo') !== false) {
                    $type = 'VRBO';
                } else {
                    $title = '';
                    if (preg_match('/PRODID:(.*?)\r\n/', $string, $matches)) {
                        $title = preg_replace('/[^a-zA-Z0-9\s]/', '', trim($matches[1]));
                        $title = explode(' ', $title)[0];
                    }

                    $type = $title;
                }
            }

            if (is_numeric($start_date) && is_numeric($end_date)) {
                $end_date = Carbon::createFromFormat('Ymd', $end_date)->subDay()->format('Ymd');

                $arr[$start_date.'-'.$end_date] = [
                    'start_date' => $start_date,
                    'end_date' => $end_date,
                    'event_title' => $ical_name,
                    'home_id' => $home_id,
                    'events_categories_id' => 7,
                    'recurrence' => 1,
                    'created_at' => date('Y.m.d h:m:s'),
                    'user_id' => $user?->id,
                    'ical_id' => $ical_id,
                ];
            }

            $string = strstr($string, 'END:VEVENT');
            $string = substr($string, 1);

            if (strpos($string, 'BEGIN:VEVENT') !== false) {
                $continue_bool = true;
            }
        }

        return $arr;
    }

    private function get_string_between($string, $start, $end)
    {
        $string = ' '.$string;
        $ini = strpos($string, $start);
        if ($ini === 0) {
            return '';
        }
        $ini += strlen($start);
        $len = strpos($string, $end, $ini) - $ini;

        return substr($string, $ini, $len);
    }

    public function iCalExport($propertyId, Request $request): mixed
    {
        $user = auth()->user();

        $events = CalendarEvent::query()
            ->select(['events.start_date', 'events.end_date', 'homes.title as home_name', 'home_id', 'events.id', 'event_title', 'events_categories_id', 'events_categories.color', 'events_categories.category_name', 'events.notes', 'events.recurrence'])
            ->leftJoin('homes', 'events.home_id', '=', 'homes.id')
            ->leftJoin('events_categories', 'events.events_categories_id', '=', 'events_categories.id')
            ->where('home_id', $propertyId)
            ->where('events.user_id', $user?->id)
            ->get();
        $events = $events->toArray();

        if (count($events) > 0) {
            $calendar = Calendar::create($events[0]['home_name']);

            $e = [];
            foreach ($events as $event) {
                $e[] = Event::create($event['category_name'].': '.$event['event_title'].' - '.$events[0]['home_name'].' (Twimo)')
                    ->fullDay()
                    ->period(Carbon::parse($event['start_date']), Carbon::parse($event['end_date'])->addDay());
            }

            $calendar->event($e);

            $fileName = Str::slug($events[0]['home_name'].' '.$events[0]['home_id']).'.ics';

            return response($calendar->get(), 200, [
                'Content-Type' => 'text/calendar; charset=utf-8',
                'Content-Disposition' => 'attachment; filename="'.$fileName.'"',
            ]);
        } else {
            $fileName = 'NotFound';

            return ['status' => 'fail', 'message' => $events];
        }

    }

    public function getAllIcals(Request $request): array
    {
        $user = auth()->user();
        $icals = [];

        $iCalsRes = CalendarImport::query()
            ->select(['events_ical_imports.id', 'ical_name', 'title'])
            ->join('homes', 'events_ical_imports.home_id', '=', 'homes.id')
            ->where('events_ical_imports.user_id', $user?->id)
            ->get();

        foreach ($iCalsRes->toArray() as $iCal) {
            $icals[] = [
                'id' => $iCal['id'],
                'ical_name' => $iCal['ical_name'],
                'title' => $iCal['title'],
            ];
        }

        return ['status' => 'success', 'result' => $icals];
    }

    public function deleteCalendar($calId): array
    {
        $ci = CalendarImport::find($calId);
        $ci->delete();

        return ['status' => 'success', 'result' => 'Calendar has been removed.'];
    }
}
