<?php

namespace App\Http\Controllers;

use App\Mail\ContactSubmission;
use App\Mail\NewsletterSignup;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;

final readonly class FormsController
{
    public function emailSubscription(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 421);
        }

        // env('CONTACT_EMAIL')
        Mail::to('<EMAIL>')->queue(new NewsletterSignup(
            'Email Subscription Submission',
            '',
            $request->email,
        ));

        return response()->json(['success' => true]);
    }

    public function contactSubmission(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|string',
            'message' => 'required|string',
            'fname' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 421);
        }

        // env('CONTACT_EMAIL')
        Mail::to('<EMAIL>')->queue(new ContactSubmission(
            'Contact Form Submission',
            $request->message,
            $request->email,
            $request->fname,
        ));

        return response()->json(['success' => true]);
    }
}
