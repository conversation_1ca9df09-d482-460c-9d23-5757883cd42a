<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Services\StripeService;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Stripe\Exception\SignatureVerificationException;
use Stripe\Stripe;
use Stripe\Webhook;

class StripeIdentityController extends Controller
{
    /**
     * Create a verification session for the authenticated user
     */
    public function createVerificationSession(Request $request, StripeService $stripeService): JsonResponse
    {
        $user = $request->user();

        // Check if the user is already verified
        if ($user->identity_verified) {
            return response()->json([
                'success' => true,
                'verified' => true,
                'message' => 'User is already verified',
            ]);
        }

        // Create a new session
        $verificationUrl = $stripeService->createIdentityVerificationSession($user);

        if (! $verificationUrl) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create verification session',
            ], 500);
        }

        return response()->json([
            'success' => true,
            'verified' => false,
            'verification_url' => $verificationUrl,
        ]);
    }

    /**
     * Check verification status
     */
    public function checkVerificationStatus(Request $request, StripeService $stripeService): JsonResponse
    {
        $user = $request->user();

        // If user is already verified
        if ($user->identity_verified) {
            return response()->json([
                'success' => true,
                'verified' => true,
                'message' => 'User is verified',
            ]);
        }

        // If user has no session ID
        if (! $user->stripe_identity_session_id) {
            return response()->json([
                'success' => false,
                'verified' => false,
                'message' => 'User has no verification session',
            ]);
        }

        // Check session status
        $status = $stripeService->checkVerificationSessionStatus($user->stripe_identity_session_id);

        // If session is verified, update user
        if ($status['verified']) {
            $user->identity_verified = true;
            $user->identity_verified_at = Carbon::now();
            $user->save();
        }

        return response()->json([
            'success' => true,
            'verified' => $status['verified'],
            'status' => $status['status'],
        ]);
    }

    /**
     * Handle Stripe webhook events for identity verification
     */
    public function handleWebhook(Request $request): JsonResponse
    {
        Stripe::setApiKey(config('services.stripe.secret'));

        $webhookSecret = config('services.stripe.webhook_secret');

        try {
            // Verify webhook signature
            $event = Webhook::constructEvent(
                $request->getContent(),
                $request->header('Stripe-Signature'),
                $webhookSecret
            );

            // Handle specific events
            if ($event->type === 'identity.verification_session.verified') {
                $session = $event->data->object;
                $userId = $session->metadata->user_id ?? null;

                if ($userId) {
                    $user = User::find($userId);
                    if ($user) {
                        $user->identity_verified = true;
                        $user->identity_verified_at = Carbon::now();
                        $user->save();

                        Log::info('User identity verified via webhook', [
                            'user_id' => $userId,
                            'session_id' => $session->id,
                        ]);
                    }
                }
            }

            return response()->json(['success' => true]);
        } catch (SignatureVerificationException $e) {
            Log::error('Webhook signature verification failed', [
                'error' => $e->getMessage(),
            ]);

            return response()->json(['error' => 'Signature verification failed'], 400);
        } catch (\Exception $e) {
            Log::error('Webhook processing failed', [
                'error' => $e->getMessage(),
            ]);

            return response()->json(['error' => 'Webhook processing failed'], 500);
        }
    }
}
