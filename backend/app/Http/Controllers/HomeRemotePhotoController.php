<?php

namespace App\Http\Controllers;

use App\Jobs\ProcessAirbnbPhotosJob;
use App\Models\Home;
use App\Traits\ExceptionLoggableTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

final class HomeRemotePhotoController
{
    use ExceptionLoggableTrait;

    public function __invoke(Request $request, Home $home): JsonResponse
    {
        $request->validate([
            'urls' => 'required|array',
            'urls.*' => 'required|url',
            'is_from_airbnb' => 'sometimes|boolean',
        ]);

        $urls = $request->input('urls');
        $isFromAirbnb = $request->input('is_from_airbnb', false);

        try {
            // Prepare photo data for the queue job
            $photos = [];
            foreach ($urls as $url) {
                $photos[] = [
                    'url' => $url,
                    'name' => $home->title ?? 'Home Photo',
                ];
            }

            // Update home status if it's from Airbnb
            if ($isFromAirbnb) {
                $home->update([
                    'is_from_airbnb' => true,
                    'photo_processing_status' => 'queued',
                ]);
                // Clear existing images to avoid duplicates when processing Airbnb photos
                $home->clearMediaCollection('images');
            }

            // Dispatch the job to process photos in the background
            ProcessAirbnbPhotosJob::dispatch($home, $photos);

            Log::info('Queued remote image processing', [
                'home_id' => $home->id,
                'photo_count' => count($photos),
                'is_from_airbnb' => $isFromAirbnb,
            ]);

            return response()->json([
                'message' => 'Remote images queued for processing',
                'photo_count' => count($photos),
                'processing_status' => 'queued',
            ]);
        } catch (\Exception $e) {
            $this->log('Error queueing remote images', [
                'error' => $e->getMessage(),
                'home_id' => $home->id,
            ], 'error');

            return response()->json([
                'message' => 'Error queueing remote images',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
