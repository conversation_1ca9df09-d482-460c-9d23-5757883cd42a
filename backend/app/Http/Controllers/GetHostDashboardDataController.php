<?php

namespace App\Http\Controllers;

use App\Enums\BookingStatus;
use App\Models\Booking;
use App\Models\CalendarEvent;
use App\Models\Home;
use App\Models\Message;
use App\Models\User;
use App\Models\Vendors;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

final readonly class GetHostDashboardDataController
{
    public function __invoke(Request $request): JsonResponse
    {
        $user = $request->user();

        /** @var User $user */
        $firstName = $user->first_name;
        $totalListings = Home::query()
            ->where('user_id', $user->id)
            ->count();
        $totalBookings = Booking::query()
            ->where('user_id', $user->id)
            ->count();
        $totalUnreadMessages = Message::query()
            ->where('receiver_id', $user->id)
            ->where('viewed', false)
            ->count();
        $totalCrewAlerts = 0;

        $hasDraftHomes = Home::query()
            ->where('user_id', $user->id)
            ->where('status', 'draft')
            ->count();

        $hasActiveHomes = Home::query()
            ->where('user_id', $user->id)
            ->where('status', 'active')
            ->count();

        $totalPendingBookings = Booking::query()
            ->join('homes', 'bookings.request_user_home', '=', 'homes.id')
            ->where('homes.user_id', $user->id)
            ->where('bookings.status', BookingStatus::REQUESTED)
            ->count();

        $totalActiveBookings = Booking::query()
            ->where('user_id', $user->id)
            ->where('status', BookingStatus::ACCEPTED)
            ->where('start_at', '<=', now())
            ->where('end_at', '>=', now())
            ->count();
        $totalUpcomingCheckIns = Booking::query()
            ->where('user_id', $user->id)
            ->where('status', BookingStatus::COMPLETED)
            ->where('start_at', '>=', now())
            ->where('start_at', '<=', now()->endOfMonth())
            ->count();
        $totalUpcomingCheckOuts = Booking::query()
            ->where('user_id', $user->id)
            ->where('status', BookingStatus::COMPLETED)
            ->where('end_at', '>=', now())
            ->where('end_at', '<=', now()->endOfMonth())
            ->count();
        $totalPendingReviews = 0;
        $totalMyCrewVendors = Vendors::query()
            ->where('user_id', $user->id)
            ->count();
        $totalMyCalendarEvents = CalendarEvent::query()
            ->where('user_id', $user->id)
            ->count();

        $profileCompletion = $user->profile_completion;
        $profileCompletedAttrs = $user->profile_completed_attrs;
        $profileNotCompletedAttrs = $user->profile_not_completed_attrs;

        return response()->json([
            'userFirstName' => $firstName,
            'totalListings' => $totalListings,
            'totalBookings' => $totalBookings,
            'totalUnreadMessages' => $totalUnreadMessages,
            'totalCrewAlerts' => $totalCrewAlerts,
            'totalPendingBookings' => $totalPendingBookings,
            'totalActiveBookings' => $totalActiveBookings,
            'totalVendors' => $totalMyCrewVendors,
            'totalCalendarEvents' => $totalMyCalendarEvents,
            'totalUpcomingCheckIns' => $totalUpcomingCheckIns,
            'totalUpcomingCheckOuts' => $totalUpcomingCheckOuts,
            'totalPendingReviews' => $totalPendingReviews,
            'profileCompletion' => $profileCompletion,
            'profileCompletedAttrs' => $profileCompletedAttrs,
            'profileNotCompletedAttrs' => $profileNotCompletedAttrs,
            'extraInfo' => $user->extra_info,
            'hasDraftHome' => $hasDraftHomes,
            'hasActiveHome' => $hasActiveHomes,
        ]);
    }
}
