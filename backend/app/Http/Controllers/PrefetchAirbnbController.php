<?php

namespace App\Http\Controllers;

use App\Services\FetchAirbnbHomeDataService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Spatie\MediaLibrary\MediaCollections\Exceptions\FileCannotBeAdded;
use Spatie\MediaLibrary\MediaCollections\Exceptions\FileDoesNotExist;
use Spatie\MediaLibrary\MediaCollections\Exceptions\FileIsTooBig;

final class PrefetchAirbnbController
{
    /**
     * @throws FileCannotBeAdded
     * @throws FileIsTooBig
     * @throws FileDoesNotExist
     */
    public function __invoke(
        Request $request,
        FetchAirbnbHomeDataService $fetchAirbnbHomeDataService
    ): JsonResponse {
        $url = (string) $request->get('url');

        $home = $fetchAirbnbHomeDataService($url);

        return response()->json($home);
    }
}
