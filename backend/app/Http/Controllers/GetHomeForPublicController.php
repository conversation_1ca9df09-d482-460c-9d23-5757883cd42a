<?php

namespace App\Http\Controllers;

use App\Enums\HomeStatus;
use App\Models\Home;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

final class GetHomeForPublicController
{
    public function __invoke(int|string $id, Request $request): JsonResponse
    {
        $home = Home::query()
            ->with(['media', 'available_dates', 'reviews.reviewer'])
            ->where('id', $id)
            ->orWhere('slug', $id)
            ->firstOrFail();
        /** @var Home $home */

        // If home is not active, only allow access to authenticated owner
        if (
            $home->status !== HomeStatus::ACTIVE->value &&
            (! auth()->check() || auth()->id() !== $home->user_id)
        ) {
            abort(404);
        }

        return response()->json($home);
    }
}
