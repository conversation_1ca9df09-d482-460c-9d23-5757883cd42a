<?php

namespace App\Http\Controllers;

use App\Enums\BookingStatus;
use App\Enums\HomeStatus;
use App\Models\BlockedDate;
use App\Models\Booking;
use App\Models\Home;
use App\Services\GeoCodeService;
use App\Traits\CanIntegrateWithGeoCodeTrait;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

final class GetListHomesController
{
    use CanIntegrateWithGeoCodeTrait;

    private const array TRUTHY_VALUES = ['true', '1', 1, true];

    private const int DEFAULT_PER_PAGE = 64;

    private const int MAX_PER_PAGE = 64;

    /**
     * @throws BindingResolutionException
     */
    public function __construct(?GeoCodeService $geoCodeManager = null)
    {
        $this->setGeoCodeManager($geoCodeManager);
    }

    public function __invoke(Request $request): JsonResponse
    {

        $perPage = $request->get('per_page', self::DEFAULT_PER_PAGE);

        if ($perPage >= self::MAX_PER_PAGE) {
            $perPage = self::DEFAULT_PER_PAGE;
        }

        $country = $request->get('country', '');
        $state = $request->get('state', '');
        $city = $request->get('city', '');

        logger()->info(__CLASS__, ['country' => $country, 'state' => $state, 'city' => $city]);

        $min_price = $request->get('min_price');
        $max_price = $request->get('max_price');

        $homes = Home::query()
            ->with(['user', 'media', 'available_dates'])
            ->where('status', HomeStatus::ACTIVE->value)
            ->when(! empty($country), function ($query) use ($country, $state, $city) {
                // Filter by country_long
                $query->where('country_long', $country);

                if (! empty($state)) {
                    $query->where('state_long', $state);
                }

                if (! empty($city)) {
                    $query->where('city_long', $city);
                }

                return $query;
            })
            ->when(
                (! empty($short_term_rentals = $request->get('short_term_rentals')) && in_array($short_term_rentals, self::TRUTHY_VALUES, true)) ||
                (! empty($allowBooking = $request->get('allow_booking')) && in_array($allowBooking, self::TRUTHY_VALUES, true)) ||
                (! empty($allowSwaps = $request->get('allow_swaps')) && in_array($allowSwaps, self::TRUTHY_VALUES, true)),
                function ($query) use ($request) {
                    $short_term_rentals = $request->get('short_term_rentals');
                    $allowBooking = $request->get('allow_booking');
                    $allowSwaps = $request->get('allow_swaps');

                    if ($allowSwaps == 0) {
                        $query->where(function ($q) use ($short_term_rentals, $allowBooking) {
                            $q->orWhere('offer_as_seasonal_lease', $short_term_rentals);
                            $q->orWhere('allow_booking', $allowBooking);
                        });
                        $query->where('allow_swaps', $allowSwaps);
                    } else {
                        $query->where(function ($q) use ($short_term_rentals, $allowBooking, $allowSwaps) {
                            $q->orWhere('offer_as_seasonal_lease', $short_term_rentals);
                            $q->orWhere('allow_booking', $allowBooking);
                            $q->orWhere('allow_swaps', $allowSwaps);
                        });
                    }

                }
            )

            ->when(
                ! empty($bedrooms = $request->get('bedrooms')),
                function ($query) use ($bedrooms) {
                    return $query->where('beds', '>=', $bedrooms);
                }
            )
            ->when(
                ! empty($number_of_beds = $request->get('beds')),
                function ($query) use ($number_of_beds) {
                    return $query->where('number_of_beds', '>=', $number_of_beds);
                }
            )
            ->when(
                ! empty($bathrooms = $request->get('bathrooms')),
                function ($query) use ($bathrooms) {
                    return $query->where('baths', '>=', $bathrooms);
                }
            )
            ->when(
                ! empty($guests = $request->get('guests')),
                function ($query) use ($guests) {
                    $query->where(function ($q) use ($guests) {
                        $q->whereNull('guests')->orWhere('guests', '>=', $guests);
                    });
                }
            )
            ->when(
                ! empty($min_price),
                function ($query) use ($min_price) {
                    return $query->where('nightly_rate', '>=', $min_price);
                }
            )
            ->when(
                ! empty($max_price),
                function ($query) use ($max_price) {
                    return $query->where('nightly_rate', '<=', $max_price);
                }
            )
            ->when(
                ! empty($allowPoints = $request->get('allow_points')) && in_array($allowPoints, self::TRUTHY_VALUES, true),
                fn ($query) => $query->where('allow_points', $allowPoints)
            )
            ->when(! empty($startDate = $request->get('start_date')), function ($query) use ($startDate, $request) {
                if (empty($endDate = $request->get('end_date'))) {
                    return $query;
                }

                $swaps = Booking::query()->whereIn('status', [
                    BookingStatus::PENDING->value,
                    BookingStatus::ACCEPTED->value,
                    BookingStatus::COMPLETED->value,
                ])
                    ->where(function ($query) use ($startDate, $endDate) {
                        $query->whereBetween('start_at', [$startDate, $endDate])
                            ->orWhereBetween('end_at', [$startDate, $endDate]);
                    })
                    ->select('user_home', 'request_user_home')
                    ->get();

                $home_ids_booked = [];

                foreach ($swaps as $swap) {
                    $home_ids_booked[] = $swap->user_home ?? 0;
                    $home_ids_booked[] = $swap->request_user_home ?? 0;
                }

                $blocked_dates = BlockedDate::query()->whereBetween('start_at', [$startDate, $endDate])
                    ->whereBetween('end_at', [$startDate, $endDate])
                    ->select('home_id')->pluck('home_id');

                $query
                    ->whereNotIn('id', $home_ids_booked)
                    ->whereNotIn('id', $blocked_dates);

                return $query;
            })
            ->orderBy('search_score', 'desc')
            ->orderBy('nightly_rate', 'asc')
            ->orderBy('id', 'desc')
            ->paginate($perPage);

        return response()->json($homes);
    }

    public function getHotIds(Request $request): JsonResponse
    {
        $country = $request->get('country', '');
        $state = $request->get('state', '');
        $city = $request->get('city', '');

        $ids = Home::query()
            ->where('status', HomeStatus::ACTIVE->value)
            ->when(! empty($country), function ($query) use ($country, $state, $city) {
                // Filter by country_long
                $query->where('country_long', $country);

                if (! empty($state)) {
                    $query->where('state_long', $state);
                }

                if (! empty($city)) {
                    $query->where('city_long', $city);
                }

                return $query;
            })

            ->when(
                (! empty($short_term_rentals = $request->get('short_term_rentals')) && in_array($short_term_rentals, self::TRUTHY_VALUES, true)) ||
                (! empty($allowBooking = $request->get('allow_booking')) && in_array($allowBooking, self::TRUTHY_VALUES, true)) ||
                (! empty($allowSwaps = $request->get('allow_swaps')) && in_array($allowSwaps, self::TRUTHY_VALUES, true)),
                function ($query) use ($request) {
                    $short_term_rentals = $request->get('short_term_rentals');
                    $allowBooking = $request->get('allow_booking');
                    $allowSwaps = $request->get('allow_swaps');

                    if ($allowSwaps == 0) {
                        $query->where(function ($q) use ($short_term_rentals, $allowBooking) {
                            $q->orWhere('offer_as_seasonal_lease', $short_term_rentals);
                            $q->orWhere('allow_booking', $allowBooking);
                        });
                        $query->where('allow_swaps', $allowSwaps);
                    } else {
                        $query->where(function ($q) use ($short_term_rentals, $allowBooking, $allowSwaps) {
                            $q->orWhere('offer_as_seasonal_lease', $short_term_rentals);
                            $q->orWhere('allow_booking', $allowBooking);
                            $q->orWhere('allow_swaps', $allowSwaps);
                        });
                    }

                }
            )
            ->when(
                ! empty($type_of_space = $request->get('type_of_space')) && $type_of_space > 0,
                fn ($query) => $query->whereJsonContains(
                    'extra_info->typeOfSpaces->title',
                    (($type_of_space === 1) ? 'Entire Space' : 'Shared Interior Space')
                )
            )
            ->when(
                ! empty($allowPoints = $request->get('allow_points')) && in_array($allowPoints, self::TRUTHY_VALUES, true),
                fn ($query) => $query->where('allow_points', $allowPoints)
            )
            ->when(! empty($startDate = $request->get('start_date')), function ($query) use ($startDate, $request) {
                if (empty($endDate = $request->get('end_date'))) {
                    return $query;
                }

                $swaps = Booking::query()->whereIn('status', [
                    BookingStatus::PENDING->value,
                    BookingStatus::ACCEPTED->value,
                    BookingStatus::COMPLETED->value,
                ])
                    ->where(function ($query) use ($startDate, $endDate) {
                        $query->whereBetween('start_at', [$startDate, $endDate])
                            ->orWhereBetween('end_at', [$startDate, $endDate]);
                    })
                    ->select('user_home', 'request_user_home')
                    ->get();

                $home_ids_booked = [];

                foreach ($swaps as $swap) {
                    $home_ids_booked[] = $swap->user_home ?? 0;
                    $home_ids_booked[] = $swap->request_user_home ?? 0;
                }

                $blocked_dates = BlockedDate::query()->whereBetween('start_at', [$startDate, $endDate])
                    ->whereBetween('end_at', [$startDate, $endDate])
                    ->select('home_id')->pluck('home_id');

                $query
                    ->whereNotIn('id', $home_ids_booked)
                    ->whereNotIn('id', $blocked_dates);

                return $query;
            })
            ->pluck('id');

        return response()->json($ids);
    }
}
