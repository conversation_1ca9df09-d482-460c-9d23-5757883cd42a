<?php

namespace App\Http\Controllers;

use App\Enums\UserType;
use App\Mail\VendorInvite;
use App\Models\CalendarEvent;
use App\Models\Home;
use App\Models\Jobs;
use App\Models\JobsCategory;
use App\Models\Message;
use App\Models\User;
use App\Models\Vendors;
use App\Traits\ExceptionLoggableTrait;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Throwable;

final class MyCrewController extends Controller
{
    use ExceptionLoggableTrait;

    public function getCategories(Request $request): array
    {
        $categories = JobsCategory::select('id', 'category_name as label')
            ->orderBy('sort_order')
            ->get();

        return ['status' => 'success', 'categories' => $categories];
    }

    public function getVendors(Request $request): array
    {
        $user = auth()->user();
        $vendors = Vendors::select('mycrew_vendor_invitations.id', 'mycrew_vendor_invitations.email_id as label', 'status', 'first_name', 'last_name')
            ->leftJoin('users', 'mycrew_vendor_invitations.vendor_user_id', '=', 'users.id')
            ->where('user_id', $user?->id)
            ->get();

        return ['status' => 'success', 'vendors' => $vendors];
    }

    public function getJobs(Request $request): array
    {
        $user = auth()->user();
        $filter = $request->get('filter');

        if ($user?->role_id != 4) { // If not vendor
            $jobs = Jobs::query()
                ->select('mycrew_jobs.id as job_id', 'job_title', 'due_date', 'details', 'payment_type', 'payment_amount', 'job_status', 'payment_status', 'homes.title as home_title', 'priority_level', 'category_name', 'homes.address', 'mycrew_vendor_invitations.email_id as vendor_email', 'users.first_name as vendor_first_name', 'users.last_name as vendor_last_name', 'mycrew_jobs.home_id', 'mycrew_jobs.vendor_task_status', 'mycrew_jobs.vendor_task_description', 'mycrew_jobs.host_task_status', 'mycrew_jobs.host_task_description', 'mycrew_jobs.vendor_user_id', 'mycrew_jobs.user_id', 'users.venmo_username')
                ->join('homes', 'mycrew_jobs.home_id', '=', 'homes.id')
                ->join('mycrew_categories', 'mycrew_jobs.category_id', '=', 'mycrew_categories.id')
                ->join('mycrew_vendor_invitations', 'mycrew_jobs.vendor_id', '=', 'mycrew_vendor_invitations.id')
                ->leftJoin('users', 'mycrew_jobs.vendor_user_id', '=', 'users.id')
                ->where('mycrew_jobs.user_id', $user?->id);

            if ($filter == 'date') {
                $jobs->orderBy('due_date', 'desc');
            } elseif ($filter == 'priority') {
                $jobs->orderByRaw("CASE 
                                    WHEN priority_level = 'high' THEN 1 
                                    WHEN priority_level = 'medium' THEN 2 
                                    WHEN priority_level = 'low' THEN 3 
                                    WHEN priority_level = 'none' THEN 4 
                                    ELSE 5 END");
            } elseif ($filter == 'home') {
                $jobs->orderBy('home_title', 'asc');
            } elseif ($filter == 'category') {
                $jobs->orderBy('category_name', 'asc');
            }

            $jobs = $jobs->get();
        } else {
            $jobs = Jobs::query()
                ->select('mycrew_jobs.id as job_id', 'job_title', 'due_date', 'details', 'payment_type', 'payment_amount', 'job_status', 'payment_status', 'homes.title as home_title', 'priority_level', 'category_name', 'homes.address', 'mycrew_vendor_invitations.email_id as vendor_email', 'users.first_name as request_first_name', 'users.last_name as request_last_name', 'mycrew_jobs.home_id', 'mycrew_jobs.vendor_task_status', 'mycrew_jobs.vendor_task_description', 'mycrew_jobs.host_task_status', 'mycrew_jobs.host_task_description', 'mycrew_jobs.vendor_user_id', 'mycrew_jobs.user_id', 'users.venmo_username')
                ->join('homes', 'mycrew_jobs.home_id', '=', 'homes.id')
                ->join('mycrew_categories', 'mycrew_jobs.category_id', '=', 'mycrew_categories.id')
                ->join('mycrew_vendor_invitations', 'mycrew_jobs.vendor_id', '=', 'mycrew_vendor_invitations.id')
                ->leftJoin('users', 'mycrew_jobs.user_id', '=', 'users.id')
                ->where('mycrew_jobs.vendor_user_id', $user?->id);

            if ($filter == 'date') {
                $jobs->orderBy('due_date', 'desc');
            } elseif ($filter == 'priority') {
                $jobs->orderByRaw("CASE 
                                        WHEN priority_level = 'high' THEN 1 
                                        WHEN priority_level = 'medium' THEN 2 
                                        WHEN priority_level = 'low' THEN 3 
                                        WHEN priority_level = 'none' THEN 4 
                                        ELSE 5 END");
            } elseif ($filter == 'home') {
                $jobs->orderBy('home_title', 'asc');
            } elseif ($filter == 'category') {
                $jobs->orderBy('category_name', 'asc');
            }

            $jobs = $jobs->get();
        }

        foreach ($jobs as $key => $job) {
            $home = Home::findOrFail($job['home_id']);
            $jobs[$key]['home_featured_image'] = $home->getMedia('images');

            $job_inside = Jobs::findOrFail($job['job_id']);
            $jobs[$key]['vendor_uploaded_images'] = $job_inside->getMedia('vendor_images');
        }

        return ['status' => 'success', 'jobs' => $jobs];
    }

    public function getMedia(Request $request): array
    {

        $job = Jobs::findOrFail($request->get('job_id'));
        $media = $job->getMedia('images');

        return ['status' => 'success', 'media' => $media];

    }

    public function getHomeMedia(Request $request): array
    {
        $job = Jobs::findOrFail($request->get('job_id'));
        $media = $job->getMedia('images');

        return ['status' => 'success', 'media' => $media];

    }

    public function getPendingJobs(Request $request): array
    {
        $user = auth()->user();
        if ($user?->role_id == 4) { // If vendor
            $jobs = Jobs::select('mycrew_jobs.id as job_id', 'job_title', 'due_date', 'details', 'payment_type', 'payment_amount', 'job_status', 'payment_status', 'homes.title as home_title', 'priority_level', 'category_name', 'homes.address', 'mycrew_vendor_invitations.email_id as vendor_email', 'users.first_name', 'users.last_name', 'mycrew_jobs.home_id', 'mycrew_jobs.vendor_user_id', 'mycrew_jobs.user_id')
                ->join('homes', 'mycrew_jobs.home_id', '=', 'homes.id')
                ->join('users', 'mycrew_jobs.user_id', '=', 'users.id')
                ->join('mycrew_categories', 'mycrew_jobs.category_id', '=', 'mycrew_categories.id')
                ->join('mycrew_vendor_invitations', 'mycrew_jobs.vendor_id', '=', 'mycrew_vendor_invitations.id')
                ->orderBy('due_date', 'desc')
                ->where('mycrew_jobs.job_status', 'pending')
                ->where('mycrew_jobs.vendor_user_id', $user?->id)
                ->get();
        } else {
            $jobs = [];
        }

        foreach ($jobs as $key => $job) {
            $home = Home::findOrFail($job['home_id']);
            $jobs[$key]['home_featured_image'] = $home->getMedia('images');
        }

        return ['status' => 'success', 'jobs' => $jobs];
    }

    public function getDashboardInfo(Request $request): array
    {

        $user = auth()->user();
        if ($user?->role_id != 4) { // If not vendor
            $pendingJobs = Jobs::query()
                ->select('*')
                ->where('job_status', 'pending')
                ->where('user_id', $user?->id)
                ->count();

            $activeJobs = Jobs::query()
                ->select('*')
                ->whereIn('job_status', ['accepted', 'completed'])
                ->where('user_id', $user?->id)
                ->count();
        } else {
            $pendingJobs = Jobs::query()
                ->select('*')
                ->where('job_status', 'pending')
                ->where('vendor_user_id', $user?->id)
                ->count();

            $activeJobs = Jobs::query()
                ->select('*')
                ->whereIn('job_status', ['accepted', 'completed'])
                ->where('vendor_user_id', $user?->id)
                ->count();
        }

        // Get events from MyCalendar
        $calendar_total = CalendarEvent::query()
            ->select('*')
            ->whereDate('start_date', '>=', Carbon::create(date('Y'), (int) date('n'))->startOfMonth()->format('Y-m-d'))
            ->whereDate('end_date', '<=', Carbon::create(date('Y'), (int) date('n'))->lastOfMonth()->format('Y-m-d'))
            ->whereIn('events.events_categories_id', [5])
            ->where('events.user_id', $user?->id)
            ->count();

        $messages = Message::query()
            ->where('receiver_id', auth()->id())
            ->where('viewed', 0)
            ->count();

        return ['status' => 'success', 'dashboard_data' => [
            'pending' => $pendingJobs,
            'calendar' => $calendar_total,
            'messages' => $messages,
            'jobs' => $activeJobs,
        ]];
    }

    public function createJob(Request $request): array
    {
        $user = auth()->user();
        $form_data = $request->post('form_data');
        $vendor_id = null;
        $vendor_user_id = null;
        $sender_user = null;
        $receiving_user = null;

        if (isset($form_data['vendorEmail']) && $form_data['vendorEmail'] != '') {

            // Check if vendor already connected to the host
            $v = Vendors::select('*')
                ->where('email_id', $form_data['vendorEmail'])
                ->where('user_id', $user?->id)
                ->first();

            if ($v) {
                $vendor_id = $v->id;
                $vendor_user_id = $v->vendor_user_id;
            } else {
                $unique_code = md5(time().$form_data['vendorEmail']);
                $vendor_id = Vendors::query()->insertGetId(
                    [
                        'email_id' => $form_data['vendorEmail'],
                        'unique_code' => $unique_code, // $this->generateUniqueCode()
                        'status' => 'pending',
                        'user_id' => $user?->id,
                        'created_at' => Carbon::now(),
                        'updated_at' => Carbon::now(),
                    ]
                );
                Mail::to($form_data['vendorEmail'])->send(new VendorInvite(['host' => $user?->first_name.' '.$user?->last_name, 'link' => config('services.frontendUrl').'/vendor_signup?vi='.$unique_code]));
            }

        } else {
            $v = Vendors::select('*')
                ->where('id', $form_data['vendor'])
                ->where('user_id', $user?->id)
                ->first();
            if ($v) {
                $vendor_user_id = $v->vendor_user_id;
            }
        }

        $job = Jobs::query()->create(
            [
                'job_title' => $form_data['jobTitle'],
                'due_date' => date('Y-m-d', strtotime($form_data['dueDate'])),
                'details' => $form_data['details'],
                'payment_type' => $form_data['paymentType'],
                'payment_amount' => $form_data['paymentAmount'],
                'priority_level' => $form_data['priorityLevel'],
                'vendor_id' => (isset($form_data['vendor']) && $form_data['vendor'] != null) ? $form_data['vendor'] : $vendor_id,
                'vendor_user_id' => $vendor_user_id,
                'category_id' => $form_data['category'],
                'home_id' => $form_data['home'],
                'user_id' => $user?->id,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ]
        );

        if ($vendor_user_id != null) {
            $sender_user = User::query()->findOrFail($user?->id);
            $receiving_user = User::query()->findOrFail($vendor_user_id);

            /*$receiving_user->notificate(
                $sender_user->first_name.' created a MyCrew job.',
                '/messages/',
                'mdi-message'
            );*/
        }

        $uploadDisk = config('services.media.uploadDisk');
        foreach ($form_data['photos'] as $media) {
            try {
                $media = Media::query()->findOrFail($media['media_id']);
                $media->move($job, 'images', $uploadDisk);
            } catch (Throwable $e) {
                report($e);

                $this->log('Failed to move media to MyCrew Job', func_get_args());

                continue;
            }
        }

        return ['status' => 'success'];
    }

    public function cancelJob($jobId, Request $request): array
    {
        $user = auth()->user();
        $sender_user = null;
        $receiving_user = null;

        $job = Jobs::where((($user?->user_type == UserType::VENDOR) ? 'vendor_user_id' : 'user_id'), $user?->id)->find($jobId);
        $job->job_status = 'cancelled';
        $job->updated_at = Carbon::now();
        $job->save();

        if ($user?->user_type == UserType::VENDOR) {
            $sender_user = User::query()->findOrFail($user?->id);
            $receiving_user = User::query()->findOrFail($job->user_id);
        } elseif ($user?->user_type == UserType::HOST) {
            $sender_user = User::query()->findOrFail($user?->id);
            $receiving_user = User::query()->findOrFail($job->vendor_user_id);
        }

        /*$receiving_user->notificate(
            $user->first_name.' canceled a MyCrew job.',
            '/messages/',
            'mdi-message'
        );*/

        return ['status' => 'success', 'job_id' => $jobId, 'user' => $user?->id];
    }

    public function updateJob($jobId, Request $request): array
    {
        $user = auth()->user();
        $form_data = $request->post('form_data');

        $job = Jobs::where('vendor_user_id', $user?->id)->find($jobId);
        $job->vendor_task_status = $form_data['confirm_status'];
        $job->vendor_task_description = $form_data['details'];
        $job->job_status = 'completed';
        $job->vendor_completion_date = Carbon::now();
        $job->updated_at = Carbon::now();
        $job->save();

        $uploadDisk = config('services.media.uploadDisk');
        foreach ($form_data['photos'] as $media) {
            try {
                $media = Media::query()->findOrFail($media['media_id']);
                $media->move($job, 'vendor_images', $uploadDisk);
            } catch (Throwable $e) {
                report($e);

                $this->log('Failed to move media to MyCrew Job', func_get_args());

                continue;
            }
        }

        $sender_user = User::query()->findOrFail($user?->id);
        $receiving_user = User::query()->findOrFail($job->user_id);

        /*$receiving_user->notificate(
            $sender_user->first_name.' has completed the MyCrew job.',
            '/messages/',
            'mdi-message'
        );*/

        return ['status' => 'success'];
    }

    public function markAsPaid($jobId, Request $request): array
    {
        $user = auth()->user();
        $form_data = $request->post('form_data');

        $sender_user = null;
        $receiving_user = null;

        $job = Jobs::where('user_id', $user?->id)->find($jobId);
        $job->host_task_status = $form_data['confirm_status'];
        $job->host_task_description = $form_data['details'];
        $job->payment_status = 'completed';
        $job->host_completion_date = Carbon::now();
        $job->updated_at = Carbon::now();
        $job->save();

        $sender_user = User::query()->findOrFail($user?->id);
        $receiving_user = User::query()->findOrFail($job->vendor_user_id);

        /*$receiving_user->notificate(
            $user->first_name.' has marked the MyCrew job has been paid.',
            '/messages/',
            'mdi-message'
        );*/

        return ['status' => 'success'];
    }

    public function jobStatusUpdate($jobId, Request $request): array
    {
        $user = auth()->user();
        if ($user->role_id == 4) {
            $form_data = $request->post('form_data');

            $job = Jobs::where('vendor_user_id', $user?->id)->find($jobId);
            $job->job_status = $form_data['status'];
            $job->updated_at = Carbon::now();
            $job->save();

            $sender_user = User::query()->findOrFail($user?->id);
            $receiving_user = User::query()->findOrFail($job->user_id);

            /*$receiving_user->notificate(
                $sender_user->first_name.' has marked the MyCrew job status as '.$form_data['status'].'.',
                '/messages/',
                'mdi-message'
            );*/

            // Add job to the vendor calendar
            if ($form_data['status'] == 'accepted') {
                $ce_id = CalendarEvent::query()->insert(
                    [
                        [
                            'home_id' => $job->home_id,
                            'events_categories_id' => 5, // MyCrew Jobs Category
                            'start_date' => date('Y-m-d', strtotime($job->due_date)),
                            'end_date' => date('Y-m-d', strtotime($job->due_date)),
                            'event_title' => $job->job_title,
                            'recurrence' => 1,
                            'notes' => $job->details,
                            'block_dates' => 0,
                            'user_id' => $user?->id,
                        ],
                        [
                            'home_id' => $job->home_id,
                            'events_categories_id' => 5, // MyCrew Jobs Category
                            'start_date' => date('Y-m-d', strtotime($job->due_date)),
                            'end_date' => date('Y-m-d', strtotime($job->due_date)),
                            'event_title' => $job->job_title,
                            'recurrence' => 1,
                            'notes' => $job->details,
                            'block_dates' => 0,
                            'user_id' => $job->user_id,
                        ],
                    ]
                );

            }

            return ['status' => 'success', 'job_id' => $jobId];
        } else {
            $form_data = $request->post('form_data');

            $job = Jobs::where('user_id', $user?->id)->find($jobId);
            $job->job_status = $form_data['status'];

            if ($form_data['status'] == 'completed') {
                $job->payment_status = 'completed';
            }

            $job->updated_at = Carbon::now();
            $job->save();

            return ['status' => 'success', 'job_id' => $jobId];
        }

    }

    public function approveJob($jobId, Request $request): array
    {
        $user = auth()->user();

        $job = Jobs::where('vendor_user_id', $user?->id)->find($jobId);
        $job->job_status = 'accepted';
        $job->updated_at = Carbon::now();
        $job->save();

        $sender_user = User::query()->findOrFail($user?->id);
        $receiving_user = User::query()->findOrFail($job->user_id);
        /*$receiving_user->notificate(
            $sender_user->first_name.' has accepted the job.',
            '/messages/',
            'mdi-message'
        );*/

        return ['status' => 'success', 'job_id' => $jobId];
    }

    public function declineJob($jobId, Request $request): array
    {
        $user = auth()->user();

        $job = Jobs::where('vendor_user_id', $user?->id)->find($jobId);
        $job->job_status = 'rejected';
        $job->updated_at = Carbon::now();
        $job->save();

        $sender_user = User::query()->findOrFail($user?->id);
        $receiving_user = User::query()->findOrFail($job->user_id);
        /*$receiving_user->notificate(
            $sender_user->first_name.' has declined the job.',
            '/messages/',
            'mdi-message'
        );*/

        return ['status' => 'success', 'job_id' => $jobId];
    }

    public function generateUniqueCode()
    {

        $characters = '0123456789abcdefghijklmnopqrstuvwxyz';
        $charactersNumber = strlen($characters);
        $codeLength = 20;

        $code = '';

        while (strlen($code) < 20) {
            $position = rand(0, $charactersNumber - 1);
            $character = $characters[$position];
            $code = $code.$character;
        }

        if (Vendors::where('unique_code', $code)->exists()) {
            $this->generateUniqueCode();
        }

        return $code;

    }
}
