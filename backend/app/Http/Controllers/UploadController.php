<?php

namespace App\Http\Controllers;

use App\Http\Requests\UploadRequest;
use App\Models\TemporaryUpload;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Spatie\MediaLibrary\MediaCollections\Exceptions\FileDoesNotExist;
use Spatie\MediaLibrary\MediaCollections\Exceptions\FileIsTooBig;

final class UploadController extends Controller
{
    /**
     * Handle file upload and create a temporary media record
     */
    public function __invoke(UploadRequest $request): JsonResponse
    {
        try {
            // Create temporary upload record
            $temporaryUpload = TemporaryUpload::query()->create();

            // Get the configured upload disk
            $uploadDisk = config('services.media.uploadDisk');

            // Process the uploaded file
            $file = $temporaryUpload
                ->addMediaFromRequest('file')
                ->toMediaCollection('temporary_uploads', $uploadDisk);

            // Return standardized response
            return response()->json([
                'media_id' => $file->getKey(),
                'filename' => $file->file_name,
                'name' => $file->file_name,
                'url' => $file->getUrl(),
                'src' => $file->getUrl(),
                'thumb' => $file->getUrl(),
                'size' => $file->size,
                'mime_type' => $file->mime_type,
            ]);
        } catch (FileDoesNotExist $e) {
            Log::error('Upload failed - File does not exist', [
                'error' => $e->getMessage(),
                'file' => $request->file('file')?->getClientOriginalName(),
            ]);

            return response()->json([
                'error' => 'The requested file does not exist.',
            ], 400);
        } catch (FileIsTooBig $e) {
            Log::error('Upload failed - File is too big', [
                'error' => $e->getMessage(),
                'file' => $request->file('file')?->getClientOriginalName(),
                'size' => $request->file('file')?->getSize(),
            ]);

            return response()->json([
                'error' => 'The file is too large. Maximum size is 10MB.',
            ], 400);
        } catch (\Exception $e) {
            Log::error('Upload failed - Unexpected error', [
                'error' => $e->getMessage(),
                'file' => $request->file('file')?->getClientOriginalName(),
            ]);

            return response()->json([
                'error' => 'Failed to upload file. Please try again.',
            ], 500);
        }
    }
}
