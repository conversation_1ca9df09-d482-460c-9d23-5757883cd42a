<?php

namespace App\Http\Controllers;

use App\Http\Requests\UpdateHomeRequest;
use App\Models\Home;
use App\Services\UpdateHomeService;
use Illuminate\Http\JsonResponse;

final class UpdateHomeController
{
    public function __invoke(
        int|string $id,
        UpdateHomeRequest $request,
        UpdateHomeService $service
    ): JsonResponse {
        $home = Home::query()
            ->where('id', $id)
            ->orWhere('slug', $id)
            ->firstOrFail();

        if ($home->user_id !== auth()->id()) {
            abort(403, 'Unauthorized action.');
        }

        $data = $request->validated();

        /** @var Home $home */
        $home = $service($home, $data);

        return response()->json($home);
    }
}
