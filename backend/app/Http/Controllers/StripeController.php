<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Services\StripeService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Stripe\Account;
use Stripe\Exception\ApiErrorException;
use Stripe\Stripe;

final class StripeController
{
    public function __construct()
    {
        Stripe::setApiKey(config('services.stripe.secret'));
    }

    /**
     * @throws ApiErrorException
     * @throws Exception
     */
    public function getOnboardingUrl(
        Request $request,
        StripeService $stripeManager
    ): string {
        $user = $request->user();
        /**
         * @var User $user
         */
        if (empty($user->stripe_connect_id)) {
            $account = $stripeManager->createAccount(
                email: $user->email,
                firstName: $user->first_name,
                lastName: $user->last_name,
            );

            $user->stripe_connect_id = $account->id;

            $user->save();
        }

        $accountLink = $stripeManager->getOnboardingUrl($user->stripe_connect_id);

        Log::debug('StripeConnectController::getOnboardingUrl: '.$accountLink);

        return $accountLink;
    }

    public function getAccountStatus(Request $request): JsonResponse
    {
        $user = $request->user();
        /**
         * @var User $user
         */
        $statuses = [
            'is_connected' => false,
            'charges_enabled' => false,
            'payouts_enabled' => false,
        ];

        if (empty($user->stripe_connect_id)) {
            return response()->json($statuses);
        }

        $statuses['is_connected'] = true;

        try {
            $account = Account::retrieve($user->stripe_connect_id);

            $statuses['charges_enabled'] = $account->charges_enabled;
            $statuses['payouts_enabled'] = $account->payouts_enabled;

            // For backward compatibility, we keep the original structure
            // but we'll use only is_connected and charges_enabled in the frontend
        } catch (Exception $e) {
            Log::error('StripeConnectController::getAccountStatus: '.$e->getMessage());
        }

        return response()->json($statuses);
    }

    public function getConnectedAccountStripeDashboardUrl(
        Request $request,
        StripeService $stripeManager
    ): JsonResponse {
        $user = $request->user();
        /**
         * @var User $user
         */
        if (empty($user->stripe_connect_id)) {
            return response()->json(['url' => '']);
        }

        $url = $stripeManager->getDashboardUrl($user->stripe_connect_id);

        return response()->json(['url' => $url]);
    }

    public function getHostPaymentMethods($hostId): JsonResponse
    {
        $host = User::query()->findOrFail($hostId);

        $paymentMethods = [
            'stripe_connect' => [
                'enabled' => false,
                'charges_enabled' => false,
            ],
            'venmo' => [
                'enabled' => false,
                'username' => null,
            ],
        ];

        // Check Stripe Connect status
        if (! empty($host->stripe_connect_id)) {
            try {
                $account = Account::retrieve($host->stripe_connect_id);
                // A user is considered fully set up with Stripe if they have charges_enabled
                $paymentMethods['stripe_connect'] = [
                    'enabled' => true,
                    'charges_enabled' => $account->charges_enabled,
                ];
            } catch (Exception $e) {
                Log::error('Error checking host Stripe Connect status: '.$e->getMessage());
            }
        }

        // Check Venmo status
        if (! empty($host->venmo_username)) {
            $paymentMethods['venmo'] = [
                'enabled' => true,
                'username' => $host->venmo_username,
            ];
        }

        return response()->json($paymentMethods);
    }
}
