<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Models\Booking;
use App\Models\CalendarEvent;
use App\Models\Home;
use App\Models\User;
use App\Services\PullIcalInfoToHomeService;
use App\Services\UserUpdateHomeIcalService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

final class UserHomeController extends Controller
{
    public function getMyList(Request $request): JsonResponse
    {
        $user = auth()->user();

        /** @var User $user */
        if ($request->get('mode') === 'minify') {
            $homes = Home::query()
                ->select('title', 'id')
                ->where('user_id', $user->id)
                ->when($request->get('status') === 'active', function ($query) {
                    $query->whereIn('status', ['active', 'draft']);
                })
                ->orderByDesc('id')
                ->get();
        } else {
            $homes = Home::query()
                ->where('user_id', $user->id)
                ->when($request->get('status') === 'active', function ($query) {
                    $query->whereIn('status', ['active', 'draft']);
                })
                ->orderByDesc('id')
                ->get();
        }

        return response()->json($homes);
    }

    public function getBookingInfo(Request $request, int $id): JsonResponse
    {
        $home = $id;
        $user_data = [];

        $user_data['pending_booking'] = Booking::where('request_user_home', $home)->where('status', 'requested')->count();

        $user_data['family_booking'] = CalendarEvent::where(static function ($query) use ($home) {
            $query->where('home_id', $home)
                ->where('events_categories_id', 2)
                ->where('start_date', '>=', date('Y-m-d'));
        })
            ->count();

        $user_data['public_booking'] = CalendarEvent::where(static function ($query) use ($home) {
            $query->where('home_id', $home)
                ->where('events_categories_id', 3)
                ->where('start_date', '>=', date('Y-m-d'));
        })
            ->count();

        return response()->json($user_data);
    }

    public function icalUrl(
        Request $request,
        UserUpdateHomeIcalService $userUpdateHomeIcalService,
        PullIcalInfoToHomeService $pullIcalInfoToHomeService
    ): JsonResponse {
        /** @var Home|null $home */
        $home = Home::query()->findOrFail($request->get('home_id'));

        if (! $home) {
            return response()->json(['error' => 'Home not found'], 404);
        }

        $home = $userUpdateHomeIcalService($home, $request->get('ical_url'), $pullIcalInfoToHomeService);

        return response()->json($home);
    }

    public function getHomeCompletion(): JsonResponse
    {
        $user = auth()->user();

        return response()->json(['completion' => $user->home_completion]);
    }
}
