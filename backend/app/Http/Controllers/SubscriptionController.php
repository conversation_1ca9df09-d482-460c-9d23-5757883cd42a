<?php

namespace App\Http\Controllers;

use App\Enums\UserSubscriptionType;
use App\Models\User;
use App\Services\BrevoService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Laravel\Cashier\Exceptions\IncompletePayment;

final class SubscriptionController
{
    public function createSubscription(Request $request, BrevoService $brevoService): JsonResponse
    {
        $request->validate([
            'price_id' => 'required|string',
            'payment_method_id' => 'required|string',
            'promo_code' => 'nullable|string',
        ]);

        $user = auth()->user();

        if (! $user) {
            return response()->json(['error' => 'User not found'], 404);
        }

        $yearlyPriceId = config('services.stripe.price_yearly');
        $priceId = $request->get('price_id');
        $promoCode = $request->get('promo_code');

        // Validate promo code is only used with yearly subscription
        if ($promoCode && $priceId !== $yearlyPriceId) {
            return response()->json([
                'error' => 'Promo codes can only be applied to annual subscriptions',
            ], 400);
        }

        /** @var User $user */
        try {
            // Create or get Stripe Customer
            $stripeCustomer = $user->createOrGetStripeCustomer();

            // Update default payment method
            $user->updateDefaultPaymentMethod($request->get('payment_method_id'));

            // Create subscription builder
            $subscriptionBuilder = $user->newSubscription(
                UserSubscriptionType::TWIMO_HOST->value,
                $priceId
            );

            // Apply promo code if provided and valid for yearly subscription
            if ($promoCode && $priceId === $yearlyPriceId) {
                // Get promo codes from config
                $promoCodes = config('services.stripe.promo_codes', []);
                $promoCodeUpper = strtoupper($promoCode);

                // Check if promo code exists in our configuration
                if (isset($promoCodes[$promoCodeUpper])) {
                    // Use the coupon ID from configuration
                    $subscriptionBuilder->withCoupon($promoCodes[$promoCodeUpper]);
                } else {
                    return response()->json([
                        'error' => 'Invalid promo code',
                    ], 400);
                }
            }

            // Create the subscription
            $subscription = $subscriptionBuilder->create($request->get('payment_method_id'));

            // Update the user's Brevo contact status to "paying"
            try {
                // Only attempt to update Brevo if the subscription was created successfully
                if ($subscription && $user->email) {
                    $brevoResult = $brevoService->updateContactToPaidStatus($user->email);

                    if ($brevoResult) {
                        Log::info('Successfully updated Brevo contact status to paying', [
                            'user_id' => $user->id,
                            'email' => $user->email,
                        ]);
                    } else {
                        Log::warning('Failed to update Brevo contact status', [
                            'user_id' => $user->id,
                            'email' => $user->email,
                        ]);
                    }
                }
            } catch (Exception $brevoException) {
                // Log the error but don't fail the subscription creation
                Log::error('Error updating Brevo contact status', [
                    'user_id' => $user->id,
                    'email' => $user->email,
                    'error' => $brevoException->getMessage(),
                ]);
            }

            return response()->json([
                'subscription' => $subscription,
                'customer' => $stripeCustomer,
            ]);
        } catch (IncompletePayment $exception) {
            return response()->json([
                'error' => 'Incomplete payment',
                'payment_intent' => $exception->payment->asStripePaymentIntent()->id,
            ], 402);
        } catch (Exception $e) {
            return response()->json([
                'error' => $e->getMessage(),
            ], 400);
        }
    }

    public function cancelSubscription(): JsonResponse
    {
        $user = auth()->user();

        if ($user->subscription(UserSubscriptionType::TWIMO_HOST->value)) {
            $user->subscription(UserSubscriptionType::TWIMO_HOST->value)->cancelNow();
        }

        return response()->json(['message' => 'Subscription cancelled']);
    }

    public function resumeSubscription(): JsonResponse
    {
        $user = auth()->user();

        if ($user->subscription(UserSubscriptionType::TWIMO_HOST->value)->canceled()) {
            $user->subscription(UserSubscriptionType::TWIMO_HOST->value)->resume();
        }

        return response()->json(['message' => 'Subscription resumed']);
    }

    public function getSubscriptionStatus(): JsonResponse
    {
        $user = auth()->user();

        return response()->json([
            'has_subscription' => $user->subscribed(UserSubscriptionType::TWIMO_HOST->value),
            'on_trial' => $user->onTrial(UserSubscriptionType::TWIMO_HOST->value),
            'subscription' => $user->subscription(UserSubscriptionType::TWIMO_HOST->value),
        ]);
    }

    /**
     * Validate a promo code and return discount information
     */
    public function validatePromoCode(Request $request): JsonResponse
    {
        $request->validate([
            'promo_code' => 'required|string',
            'price_id' => 'required|string',
        ]);

        $promoCode = strtoupper($request->get('promo_code'));
        $priceId = $request->get('price_id');
        $yearlyPriceId = config('services.stripe.price_yearly');

        // Only allow promo codes for yearly subscriptions
        if ($priceId !== $yearlyPriceId) {
            return response()->json([
                'valid' => false,
                'message' => 'Promo codes can only be applied to annual subscriptions',
            ]);
        }

        // Get promo codes from config
        $promoCodes = config('services.stripe.promo_codes', []);
        $promoDiscounts = config('services.stripe.promo_discounts', []);
        $originalPrice = config('services.stripe.yearly_price_amount', 144);

        // Check if promo code exists in our configuration
        if (isset($promoCodes[$promoCode]) && isset($promoDiscounts[$promoCode])) {
            $couponId = $promoCodes[$promoCode];
            $discountPercent = $promoDiscounts[$promoCode];
            $discountedPrice = $originalPrice * (1 - ($discountPercent / 100));

            return response()->json([
                'valid' => true,
                'coupon_id' => $couponId,
                'discount_type' => 'percent',
                'discount_value' => $discountPercent,
                'original_price' => $originalPrice,
                'discounted_price' => round($discountedPrice, 2),
                'message' => 'Valid promo code',
            ]);
        } else {
            return response()->json([
                'valid' => false,
                'message' => 'Invalid promo code',
            ]);
        }
    }
}
