<?php

namespace App\Http\Controllers;

use App\Http\Requests\AdjustSharableLinkPriceRequest;
use App\Http\Requests\CreateSharableLinkRequest;
use App\Http\Requests\ValidateHomePasswordRequest;
use App\Http\Resources\SharableLinkResource;
use App\Models\GuestList;
use App\Models\Home;
use App\Models\SharableLink;
use App\Services\CreateSharableLinkService;

final class SharableLinkController
{
    public function store(int $id, CreateSharableLinkRequest $request, CreateSharableLinkService $service): mixed
    {
        $home = Home::query()->findOrFail($id);

        /* @var Home $home */
        $sharableLink = $service($home, $request->validated());

        return new SharableLinkResource($sharableLink);
    }

    public function validatePassword(int|string $id, ValidateHomePasswordRequest $request): mixed
    {
        $home = Home::query()
            ->where('id', $id)
            ->orWhere('slug', $id)
            ->without(['user', 'owner'])
            ->firstOrFail();
        /* @var Home $home */

        $password = $request->get('password');
        $user = auth()->user();
        $guestId = $user?->id;
        $hostId = $home->user_id;
        $homeId = $home->id;

        // First check if password is correct
        if ($home->sharable_password !== $password) {
            return response()->json(['valid' => false, 'message' => 'Invalid password'], 400);
        }

        // If user is authenticated, check their guest list status
        if ($guestId) {
            // Check if user is in guest list (including soft-deleted)
            $guestListEntry = GuestList::withTrashed()
                ->where('host_id', $hostId)
                ->where('guest_id', $guestId)
                ->first();

            if ($guestListEntry) {
                // Check if user is soft-deleted (removed from guest list)
                if ($guestListEntry->trashed()) {
                    return response()->json([
                        'valid' => false,
                        'message' => 'You no longer have access to this home.',
                        'error_type' => 'deleted'
                    ], 403);
                }

                // Check if user has access to this specific home
                if ($guestListEntry->hasAccessToHome($homeId)) {
                    // Check if user is blocked for this specific sharable link
                    $sharableLink = request()->get('sharable_link');
                    if ($sharableLink) {
                        $sharableLinkModel = SharableLink::query()
                            ->where('link', $sharableLink)
                            ->where('home_id', $home->id)
                            ->first();

                        if ($sharableLinkModel) {
                            $isBlocked = $sharableLinkModel->blockedUsers()
                                ->where('user_id', $guestId)
                                ->exists();

                            if ($isBlocked) {
                                return response()->json([
                                    'valid' => false,
                                    'message' => 'Your access to this home has been blocked.',
                                    'error_type' => 'blocked'
                                ], 403);
                            }
                        }
                    }

                    // User is in guest list for this home and not blocked
                    return response()->json(['valid' => true, 'is_in_guest_list' => true]);
                }

                // User is in guest list but doesn't have access to this specific home
                // Continue with normal password validation
            }
        }

        return response()->json(['valid' => true], 200);
    }

    public function adjustPrice(int $id, AdjustSharableLinkPriceRequest $request): mixed
    {
        $sharableLink = SharableLink::query()->findOrFail($id);

        $sharableLink->price_info = $request->validated();

        $sharableLink->save();

        return new SharableLinkResource($sharableLink);
    }

    public function checkGuestList(int|string $id): mixed
    {
        $home = Home::query()
            ->where('id', $id)
            ->orWhere('slug', $id)
            ->without(['user', 'owner'])
            ->firstOrFail();
        /* @var Home $home */

        $user = auth()->user();
        $guestId = $user?->id;
        $hostId = $home->user_id;
        $homeId = $home->id;

        if (!$guestId) {
            return 0;
        }

        // Check if user is in guest list for this host (including soft-deleted)
        $guestListEntry = GuestList::withTrashed()
            ->where('host_id', $hostId)
            ->where('guest_id', $guestId)
            ->first();

        if ($guestListEntry) {
            // Check if user is soft-deleted (removed from guest list)
            if ($guestListEntry->trashed()) {
                return response()->json([
                    'message' => 'You no longer have access to this home.',
                    'error_type' => 'deleted'
                ], 403);
            }

            // Check if user has access to this specific home
            if ($guestListEntry->hasAccessToHome($homeId)) {
                return 1;
            }

            // User is in guest list but doesn't have access to this specific home
            return 0;
        }

        // User is not in guest list at all
        return 0;
    }

    public function checkBlockAccess(string $link): int
    {
        $sharableLink = SharableLink::query()
            ->where('link', $link)
            ->firstOrFail();

        $user = auth()->user();
        $guestId = $user?->id;

        if (!$guestId) {
            return 0;
        }

        $isBlocked = $sharableLink->blockedUsers()
            ->where('user_id', $guestId)
            ->exists();

        return $isBlocked ? 1 : 0;
    }
}
