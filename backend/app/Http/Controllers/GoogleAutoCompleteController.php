<?php

namespace App\Http\Controllers;

use App\Http\Requests\AutocompleteRequest;
use App\Services\GeoCodeService;
use App\Traits\CanIntegrateWithGeoCodeTrait;
use App\Traits\ExceptionLoggableTrait;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response as ResponseAlias;
use Throwable;

final class GoogleAutoCompleteController
{
    use CanIntegrateWithGeoCodeTrait;
    use ExceptionLoggableTrait;

    /**
     * @throws BindingResolutionException
     */
    public function __construct(?GeoCodeService $geoCodeManager = null)
    {
        $this->setGeoCodeManager($geoCodeManager);
    }

    private const string CACHE_KEY = 'nominatim_autocomplete_';

    private const int|float CACHE_TTL = 60 * 60 * 24;

    private const string LOCK_KEY = 'nominatim_autocomplete_lock_';

    private const int LOCK_TTL = 3;

    public function __invoke(AutocompleteRequest $request): mixed
    {
        $semaphore = Cache::lock(self::LOCK_KEY.$request->ip(), self::LOCK_TTL);

        if (! $semaphore->get()) {
            return response(['error' => 'Too many requests'], ResponseAlias::HTTP_TOO_MANY_REQUESTS);
        }

        try {
            $input = $this->geoCodeManager->sanitizeInput($originalInput = $request->get('input'));

            if (empty($input) || strlen($input) < 3) {
                return response()->json([
                    'predictions' => [],
                ]);
            }

            $endpoint = sprintf(
                'https://nominatim.openstreetmap.org/search?format=json&q=%s&addressdetails=1&limit=5',
                urlencode($input)
            );

            $response = Cache::remember(self::CACHE_KEY.$input, self::CACHE_TTL, function () use ($endpoint) {
                // Add a user agent as required by Nominatim usage policy
                $options = [
                    'http' => [
                        'header' => "User-Agent: Twimo/1.0\r\n",
                        'timeout' => 15,
                    ],
                ];
                $context = stream_context_create($options);

                $result = @file_get_contents($endpoint, false, $context);

                if ($result === false) {
                    return [];
                }

                return json_decode($result, true);
            });

            // Transform Nominatim response to match Google's format
            $predictions = [];
            foreach ($response as $place) {
                $predictions[] = [
                    'description' => $place['display_name'],
                    'place_id' => $place['place_id'],
                    'structured_formatting' => [
                        'main_text' => $place['display_name'],
                        'secondary_text' => '',
                    ],
                ];
            }

            return response()->json([
                'predictions' => $predictions,
            ]);
        } catch (Throwable $e) {
            report($e);

            return response(['error' => 'Server error'], ResponseAlias::HTTP_INTERNAL_SERVER_ERROR);
        } finally {
            $semaphore->release();

            Log::info('A request to Nominatim autocomplete API was made!', [
                'originalInput' => $originalInput ?? null,
                'sanitizedInput' => $input ?? null,
                'endpoint' => $endpoint ?? null,
                'ip' => request()->ip(),
            ]);
        }
    }
}
