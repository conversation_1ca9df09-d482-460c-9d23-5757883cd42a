<?php

namespace App\Http\Controllers;

use App\Http\Requests\UpdateBlockAccessRequest;
use App\Http\Resources\GuestListResource;
use App\Models\Booking;
use App\Models\GuestList;
use App\Models\SharableLink;
use App\Models\SharableLinkBlockAccess;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

final readonly class GuestListController
{
    public function index(): AnonymousResourceCollection
    {
        $hostId = auth()->id();

        $guestList = GuestList::withTrashed()
            ->where('host_id', $hostId)
            ->with(['guest', 'host'])
            ->orderBy('id', 'desc')
            ->get();

        $accessibleBookings = Booking::query()
            ->with(['toHome'])
            ->whereNotNull('from_sharable_link')
            ->whereIn('user_id', $guestList->pluck('guest_id'))
            ->whereHas('toHome', function ($query) use ($hostId) {
                $query->where('user_id', $hostId);
            })
            ->get();

        $accessibleSharableLinks = SharableLink::query()
            ->with(['blockedUsers', 'home'])
            ->whereIn('link', $accessibleBookings->pluck('from_sharable_link'))
            ->get();

        $guestList->each(function (GuestList $guestListItem) use ($accessibleBookings, $accessibleSharableLinks) {
            $guestBookings = $accessibleBookings->where('user_id', $guestListItem->guest_id);

            $accessibleLinks = $accessibleSharableLinks
                ->whereIn(
                    'link',
                    $guestBookings
                        ->pluck('from_sharable_link')
                )
                ->each(function (SharableLink $sharableLink) use ($guestListItem, $guestBookings) {
                    $sharableLink->is_allowed = ! $sharableLink->blockedUsers->contains(
                        'user_id',
                        $guestListItem->guest_id
                    );

                    $sharableLink->guestBookings = $guestBookings
                        ->where('from_sharable_link', $sharableLink->link)
                        ->values();

                    $sharableLink->makeHidden('blockedUsers');
                })
                ->values()
                ->all();

            $guestListItem->accessible_sharable_links = collect($accessibleLinks);
        });

        return GuestListResource::collection($guestList);
    }

    public function destroy(int $id): JsonResponse
    {
        $guestList = GuestList::query()->findOrFail($id);

        if ($guestList->host_id !== auth()->id()) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $guestList->delete();

        return response()->json(['message' => 'Guest list entry archived successfully']);
    }

    public function restore(int $id): JsonResponse
    {
        $guestList = GuestList::withTrashed()->findOrFail($id);

        $guestList->restore();

        return response()->json(['message' => 'Guest list entry restored successfully']);
    }

    public function updateBlockAccess(UpdateBlockAccessRequest $request): JsonResponse
    {
        $validated = $request->validated();

        $hostId = auth()->id();
        $guestId = $validated['guest_id'];
        $sharableLinkId = $validated['sharable_link_id'];
        $isBlocked = $validated['is_blocked'];

        GuestList::withTrashed()
            ->where('host_id', $hostId)
            ->where('guest_id', $guestId)
            ->firstOrFail();

        $blockAccess = SharableLinkBlockAccess::query()
            ->where('sharable_link_id', $sharableLinkId)
            ->where('user_id', $guestId)
            ->first();

        if ($isBlocked) {
            if (! $blockAccess) {
                SharableLinkBlockAccess::query()->create([
                    'sharable_link_id' => $sharableLinkId,
                    'user_id' => $guestId,
                ]);
                $message = 'Access blocked successfully';
            } else {
                $message = 'Access was already blocked';
            }
        } elseif ($blockAccess) {
            $blockAccess->delete();
            $message = 'Access unblocked successfully';
        } else {
            $message = 'Access was already unblocked';
        }

        return response()->json(['message' => $message]);
    }
}
