<?php

namespace App\Http\Controllers;

use App\Http\Requests\CreateHomeRequest;
use App\Services\CreateHomeService;
use Illuminate\Http\JsonResponse;

final class CreateHomeController
{
    public function __invoke(
        CreateHomeRequest $request,
        CreateHomeService $createHomeService
    ): JsonResponse {
        // TODO: Should not get all here
        $data = $request->all();

        $home = $createHomeService($data);

        return response()->json($home);
    }
}
