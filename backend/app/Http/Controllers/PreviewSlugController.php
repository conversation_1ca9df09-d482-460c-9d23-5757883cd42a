<?php

namespace App\Http\Controllers;

use App\Models\Home;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

final class PreviewSlugController
{
    public function __invoke(
        Request $request
    ): JsonResponse {
        $title = (string) $request->get('title');

        $home = new Home([
            'title' => $title,
        ]);

        $home->generateSlug();

        return response()->json($home->slug);
    }
}
