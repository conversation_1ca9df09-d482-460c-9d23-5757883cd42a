<?php

namespace App\Http\Controllers;

use App\Http\Requests\ArchiveMessagesRequest;
use App\Http\Requests\MarkMessagesAsReadRequest;
use App\Http\Requests\StoreMessageRequest;
use App\Http\Resources\MessageResource;
use App\Models\GuestList;
use App\Models\Message;
use App\Models\User;
use App\Models\Vendors;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\DB;

final readonly class MessageController
{
    public function index(): AnonymousResourceCollection
    {
        $messages = Message::query()
            ->where('receiver_id', auth()->id())
            ->orWhere('sender_id', auth()->id())
            ->with(['sender', 'receiver', 'messageable', 'messageable.user', 'messageable.toHome'])
            ->get();

        return MessageResource::collection($messages);
    }

    public function store(StoreMessageRequest $request): MessageResource
    {
        $validated = $request->validated();

        $validated['sender_id'] = auth()->id();

        $message = Message::query()->create($validated);

        return new MessageResource($message);
    }

    public function markAsRead(MarkMessagesAsReadRequest $request): JsonResponse
    {
        $validated = $request->validated();
        $userId = auth()->id();

        Message::query()
            ->whereIn('id', $validated['message_ids'])
            ->where('receiver_id', $userId)
            ->update(['viewed' => true]);

        return response()->json(['message' => 'Messages marked as read successfully']);
    }

    public function messageablePeople(): JsonResponse
    {
        $currentUserId = auth()->id();

        $messageablePeople = User::query()
            ->whereIn('id', function ($query) use ($currentUserId) {
                $query->select('host_id')
                    ->from('guest_lists')
                    ->where('guest_id', $currentUserId)
                    ->union(
                        GuestList::query()
                            ->select('guest_id')
                            ->where('host_id', $currentUserId)
                    )
                    ->union(
                        Vendors::query()
                            ->select('vendor_user_id')
                            ->where('user_id', $currentUserId)
                    )
                    ->union(
                        Vendors::query()
                            ->select('user_id')
                            ->where('vendor_user_id', $currentUserId)
                    );
            })->get();

        return response()->json($messageablePeople);
    }

    public function archive(ArchiveMessagesRequest $request): JsonResponse
    {
        $validated = $request->validated();
        $userId = auth()->id();

        Message::query()
            ->whereIn('id', $validated['message_ids'])
            ->where(function ($query) use ($userId) {
                $query->where('sender_id', $userId)
                    ->orWhere('receiver_id', $userId);
            })
            ->update([
                'archived_by_sender' => DB::raw("CASE WHEN sender_id = $userId THEN true ELSE archived_by_sender END"),
                'archived_by_receiver' => DB::raw("CASE WHEN receiver_id = $userId THEN true ELSE archived_by_receiver END"),
            ]);

        return response()->json(['message' => 'Messages archived successfully']);
    }

    public function unarchive(ArchiveMessagesRequest $request): JsonResponse
    {
        $validated = $request->validated();
        $userId = auth()->id();

        Message::query()
            ->whereIn('id', $validated['message_ids'])
            ->where(function ($query) use ($userId) {
                $query->where('sender_id', $userId)
                    ->orWhere('receiver_id', $userId);
            })
            ->update([
                'archived_by_sender' => DB::raw("CASE WHEN sender_id = $userId THEN false ELSE archived_by_sender END"),
                'archived_by_receiver' => DB::raw("CASE WHEN receiver_id = $userId THEN false ELSE archived_by_receiver END"),
            ]);

        return response()->json(['message' => 'Messages unarchived successfully']);
    }
}
