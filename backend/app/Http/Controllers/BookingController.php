<?php

namespace App\Http\Controllers;

use App\Enums\BookingStatus;
use App\Enums\BookingType;
use App\Http\Requests\CreateBookingRequest;
use App\Http\Requests\UpdateBookingRequest;
use App\Http\Resources\BookingResource;
use App\Models\Booking;
use App\Models\CalendarEvent;
use App\Models\GuestList;
use App\Models\Home;
use App\Models\IndefiniteApproval;
use App\Models\Message;
use App\Models\User;
use App\Services\NotificationService;
use App\Services\StripeService;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

final readonly class BookingController
{
    public function validate(): JsonResponse
    {
        return response()->json([
            'message' => 'Success!',
        ]);
    }

    public function index(Request $request): AnonymousResourceCollection
    {
        $authUser = $request->user();
        /** @var User $authUser */
        $bookings = Booking::query()
            ->with([
                'fromHome',
                'toHome',
                'user',
                'reviews.reviewer',
                'reviews.reviewee',
            ])
            ->where('user_id', $authUser->id)
            ->orWhereHas('toHome', function ($query) use ($authUser) {
                $query->where('user_id', $authUser->id);
            })
            ->get();

        // Load all reviews for each home
        $bookings->each(function ($booking) {
            $booking->toHome?->loadMissing(['reviews.reviewer', 'reviews.reviewee']);
        });

        return BookingResource::collection($bookings);
    }

    public function store(CreateBookingRequest $request): BookingResource
    {
        $requestedData = $request->validated();

        $booking = Booking::query()->create([
            'start_at' => $requestedData['start_at'],
            'end_at' => $requestedData['end_at'],
            'user_home' => $requestedData['user_home'] ?? null,
            'request_user_home' => $requestedData['request_user_home'],
            'user_id' => $request->user()->id ?? null,
            'booking_type' => $requestedData['booking_type'],
            'comment' => $requestedData['comment'] ?? null,
            'status' => BookingStatus::CREATED,
            'extra_info' => $requestedData['extra_info'] ?? null,
            'code' => strtoupper(Str::random(12)),
            'from_sharable_link' => $requestedData['from_sharable_link'] ?? null,
        ]);

        return new BookingResource($booking);
    }

    public function show(int $id, Request $request): JsonResponse|BookingResource
    {
        $booking = Booking::query()
            ->with([
                'fromHome',
                'toHome',
                'user',
                'reviews.reviewer',
                'reviews.reviewee',
            ])
            ->findOrFail($id);

        /** @var Booking $booking */
        $booking->toHome?->loadMissing(['reviews.reviewer', 'reviews.reviewee']);

        if ($booking->status === BookingStatus::CREATED) {
            return new BookingResource($booking);
        }

        $authUser = $request->user();
        /** @var User $authUser */
        if ($authUser->id !== $booking->user_id && $authUser->id !== $booking->toHome?->user_id) {
            return response()->json([
                'message' => 'Unauthorized',
            ], 403);
        }

        return new BookingResource($booking);
    }

    public function update(int $id, UpdateBookingRequest $request): BookingResource|JsonResponse
    {
        $booking = Booking::query()
            ->with([
                'fromHome',
                'toHome',
                'user',
            ])
            ->findOrFail($id);
        /** @var Booking $booking */
        if ($booking->status !== BookingStatus::CREATED) {
            return response()->json([
                'message' => 'Booking can only be updated when in CREATED status',
            ], 403);
        }

        $requestedData = $request->validated();

        // Remove status from requested data if it's present
        unset($requestedData['status']);

        // Merge extra_info if present
        if (isset($requestedData['extra_info'])) {
            $requestedData['extra_info'] = array_merge($booking->extra_info ?? [], $requestedData['extra_info']);
        }

        $booking->update($requestedData);

        return new BookingResource($booking);
    }

    public function createPaymentIntent(int $id, StripeService $stripeManager): JsonResponse
    {
        $booking = Booking::query()->findOrFail($id);

        if ($booking->status !== BookingStatus::CREATED) {
            return response()->json([
                'message' => 'Booking is not in the correct status',
            ], 403);
        }

        if (! isset($booking->extra_info['priceInfo'])) {
            return response()->json([
                'message' => 'Price info is not set',
            ], 403);
        }

        $extraInfo = $booking->extra_info;

        $priceInfo = $booking->extra_info['priceInfo'];

        $getLiabilityProtection = (float) $priceInfo['getLiabilityProtection'];

        $priceInfo['liabilityPaymentIntent'] = $stripeManager->getPaymentIntent(
            $getLiabilityProtection,
            0,
            'USD',
            'Liability Protection',
            null // No transfer to connect ID for liability protection
        );

        $extraInfo['liabilityPaymentInfo'] = $priceInfo;

        $booking->update([
            'extra_info' => $extraInfo,
        ]);

        return response()->json([
            'paymentIntent' => $priceInfo['liabilityPaymentIntent'] ?? null,
        ]);
    }

    public function approve(int $id, Request $request, StripeService $stripeManager): BookingResource|JsonResponse
    {
        $booking = Booking::query()->findOrFail($id);

        if ($booking->status !== BookingStatus::REQUESTED) {
            return response()->json([
                'message' => 'Booking is not in the correct status',
            ], 403);
        }

        if ($booking->toHome?->user_id !== $request->user()->id) {
            return response()->json([
                'message' => 'Unauthorized',
            ], 403);
        }

        $extraInfo = $booking->extra_info;

        if (! isset($extraInfo['priceInfo']) || empty($extraInfo['priceInfo'])) {
            return response()->json([
                'message' => 'Price info is not set',
            ], 403);
        }

        $priceInfo = $extraInfo['priceInfo'];

        // Get host's Stripe Connect ID
        $hostUser = User::query()->find($booking->toHome?->user_id);

        // Make sure we have the host's Stripe Connect ID
        if (! $hostUser || empty($hostUser->stripe_connect_id)) {
            return response()->json([
                'message' => 'Host does not have a connected Stripe account',
            ], 422);
        }

        // Use the host's Stripe Connect ID for the payment
        $hostStripeConnectId = $hostUser->stripe_connect_id;

        if ($booking->booking_type !== BookingType::SWAP) {
            $totalPrice = (float) $priceInfo['getTotalMoney'];
            $priceInfo['paymentIntent'] = $stripeManager->getPaymentIntent(
                $totalPrice,
                0, // No application fee
                'USD',
                'Twimo Booking',
                $hostStripeConnectId
            );
        } else {
            // For swap bookings, check if host wants to collect cleaning fee
            // Get the host's cleaning fee preference from the request
            $hostWantsCleaningFee = $request->boolean('host_cleaning_fee_enabled', false);
            $booking->update([
                'host_cleaning_fee_enabled' => $hostWantsCleaningFee,
            ]);

            // Only create payment intent if host wants to collect cleaning fee
            if ($hostWantsCleaningFee) {
                $hostHomeCleaningFee = (float) $booking->toHome?->cleaning_fee;
                $priceInfo['guestPaymentIntent'] = $stripeManager->getPaymentIntent(
                    $hostHomeCleaningFee,
                    0, // No application fee
                    'USD',
                    'Twimo Booking - Host Home Cleaning Fee',
                    $hostStripeConnectId
                );
                $priceInfo['guestPaymentAmount'] = $hostHomeCleaningFee;
            }

            // The booking will be marked as completed later if no cleaning fees are required

            // Check if guest wants to collect cleaning fee
            if ($booking->guest_cleaning_fee_enabled) {
                $guestHomeCleaningFee = (float) $booking->fromHome?->cleaning_fee;

                // In a swap, both parties are hosts with Stripe Connect accounts
                // Get the other host's Stripe Connect ID (owner of the fromHome)
                $otherHostUser = User::query()->find($booking->fromHome?->user_id);

                // Make sure we have the other host's Stripe Connect ID
                if (! $otherHostUser || empty($otherHostUser->stripe_connect_id)) {
                    return response()->json([
                        'message' => 'Other host does not have a connected Stripe account',
                    ], 422);
                }

                // Use the other host's Stripe Connect ID for the payment
                $otherHostStripeConnectId = $otherHostUser->stripe_connect_id;

                // Create payment intent with the other host's Stripe Connect ID
                $priceInfo['hostPaymentIntent'] = $stripeManager->getPaymentIntent(
                    $guestHomeCleaningFee,
                    0, // No application fee
                    'USD',
                    'Twimo Booking - Swap Cleaning Fee',
                    $otherHostStripeConnectId // The other host's connected account
                );
                $priceInfo['hostPaymentAmount'] = $guestHomeCleaningFee;
            }
        }

        // Ensure cleaning fee is properly set for swap bookings
        if ($booking->booking_type === BookingType::SWAP) {
            // Make sure getCleaningFee is set
            if (! isset($priceInfo['getCleaningFee']) || $priceInfo['getCleaningFee'] == 0) {
                $priceInfo['getCleaningFee'] = (float) $booking->toHome?->cleaning_fee;
            }

            // For swap bookings, total money equals cleaning fee
            $priceInfo['getTotalMoney'] = $priceInfo['getCleaningFee'];
        }

        $extraInfo['priceInfo'] = $priceInfo;

        // For swap bookings with no cleaning fees, mark as completed immediately
        if ($booking->booking_type === BookingType::SWAP &&
            ! $booking->host_cleaning_fee_enabled &&
            ! $booking->guest_cleaning_fee_enabled) {
            $booking->update([
                'status' => BookingStatus::COMPLETED,
                'extra_info' => $extraInfo,
            ]);

            // Notify both parties that the swap is confirmed without payments
            Message::query()->create([
                'sender_id' => 1, // System message
                'receiver_id' => $booking->user_id,
                'body' => 'Your swap booking has been confirmed without cleaning fees. Enjoy your stay!',
                'messageable_type' => Booking::class,
                'messageable_id' => $booking->id,
            ]);

            Message::query()->create([
                'sender_id' => 1, // System message
                'receiver_id' => $booking->toHome?->user_id,
                'body' => 'The swap booking has been confirmed without cleaning fees. Enjoy your swap!',
                'messageable_type' => Booking::class,
                'messageable_id' => $booking->id,
            ]);
        } else {
            $booking->update([
                'status' => BookingStatus::ACCEPTED,
                'extra_info' => $extraInfo,
            ]);
        }

        // Add or restore guest to host's guest list with access to this specific home
        $this->addOrRestoreGuest($booking->toHome?->user_id, $booking->user_id, $booking->request_user_home);

        // Create calendar event for the Host
        CalendarEvent::query()->insertGetId(
            [
                'home_id' => $booking->request_user_home,
                'events_categories_id' => (($booking->from_sharable_link != '') ? 2 : (($booking->booking_type === BookingType::SWAP) ? 4 : 3)),
                'start_date' => date('Y-m-d', strtotime($booking->start_at)),
                'end_date' => date('Y-m-d', strtotime($booking->end_at)),
                'event_title' => 'Booking',
                'recurrence' => 1,
                'notes' => '',
                'block_dates' => 1,
                'event_ref_id' => $booking->id,
                'user_id' => $booking->toHome?->user_id,
            ]
        );

        // Create calendar event for the Guest
        CalendarEvent::query()->insertGetId(
            [
                'home_id' => (($booking->booking_type === BookingType::SWAP) ? $booking->user_home : $booking->request_user_home),
                'events_categories_id' => (($booking->from_sharable_link != '') ? 2 : (($booking->booking_type === BookingType::SWAP) ? 4 : 3)),
                'start_date' => date('Y-m-d', strtotime($booking->start_at)),
                'end_date' => date('Y-m-d', strtotime($booking->end_at)),
                'event_title' => 'Booking',
                'recurrence' => 1,
                'notes' => '',
                'block_dates' => 1,
                'event_ref_id' => $booking->id,
                'user_id' => $booking->user_id,
            ]
        );

        // Create a message if the 'message' field is present in the request and not empty
        if ($request->has('message') && ! empty(trim($request->input('message')))) {
            Message::query()->create([
                'sender_id' => $request->user()->id,
                'receiver_id' => $booking->user_id,
                'body' => $request->input('message'),
                'messageable_type' => Booking::class,
                'messageable_id' => $booking->id,
            ]);
        }

        return new BookingResource($booking);
    }

    private function addOrRestoreGuest(int $hostId, int $guestId, int $homeId): void
    {
        $existList = GuestList::withTrashed()
            ->where('host_id', $hostId)
            ->where('guest_id', $guestId)
            ->first();

        if ($existList === null) {
            // Create new guest list entry with access to this specific home
            GuestList::query()
                ->create([
                    'host_id' => $hostId,
                    'guest_id' => $guestId,
                    'home_ids' => [$homeId], // Grant access to the specific home being booked
                ]);

            return;
        }

        // If guest list entry exists, restore it if deleted and grant access to this home
        if ($existList->trashed()) {
            $existList->restore();
        }

        // Ensure the guest has access to this specific home
        $existList->grantAccessToHome($homeId);
    }

    public function reject(int $id, Request $request): BookingResource|JsonResponse
    {
        $booking = Booking::query()->findOrFail($id);

        if ($booking->status !== BookingStatus::REQUESTED) {
            return response()->json([
                'message' => 'Booking is not in the correct status',
            ], 403);
        }

        if ($booking->toHome?->user_id !== $request->user()->id) {
            return response()->json([
                'message' => 'Unauthorized',
            ], 403);
        }

        $booking->update([
            'status' => BookingStatus::REJECTED,
        ]);

        // Create a message if the 'message' field is present in the request and not empty
        if ($request->has('message') && ! empty(trim($request->input('message')))) {
            Message::query()
                ->create([
                    'sender_id' => $request->user()->id,
                    'receiver_id' => $booking->user_id,
                    'body' => $request->input('message'),
                    'messageable_type' => Booking::class,
                    'messageable_id' => $booking->id,
                ]);
        }

        return new BookingResource($booking);
    }

    public function complete(int $id, Request $request, StripeService $stripeManager): BookingResource|JsonResponse
    {
        $booking = Booking::query()->findOrFail($id);

        // Handle booking status using match expression
        return match ($booking->status) {
            BookingStatus::COMPLETED => response()->json([
                'message' => 'Booking is already completed',
            ], 403),
            BookingStatus::ACCEPTED => $this->processBookingCompletion($booking, $request, $stripeManager),
            default => response()->json([
                'message' => 'Booking is not in the correct status',
            ], 403),
        };
    }

    private function processBookingCompletion(
        Booking $booking,
        Request $request,
        StripeService $stripeManager
    ): BookingResource|JsonResponse {
        $extraInfo = $booking->extra_info;

        if ($request->input('payment_method') === 'venmo' && $request->input('venmo_payment_confirmed')) {
            return $this->handleVenmoPayment($booking, $request, $extraInfo);
        }

        return $this->handleStripePayment($booking, $extraInfo, $stripeManager);
    }

    private function handleVenmoPayment(
        Booking $booking,
        Request $request,
        array $extraInfo
    ): BookingResource|JsonResponse {
        if ($booking->toHome?->user_id !== $request->user()->id) {
            return response()->json([
                'message' => 'Unauthorized',
            ], 403);
        }

        $venmoInfo = $extraInfo['venmo_payment'] ?? [];
        if (! isset($venmoInfo['marked_paid_at'])) {
            return response()->json([
                'message' => 'No Venmo payment found for this booking',
            ], 422);
        }

        $extraInfo['venmo_payment']['host_confirmed_at'] = now()->toISOString();
        $extraInfo['venmo_payment']['host_confirmed_by'] = $request->user()->id;

        $booking->update([
            'extra_info' => $extraInfo,
            'status' => BookingStatus::COMPLETED,
        ]);

        Message::query()->create([
            'sender_id' => $request->user()->id,
            'receiver_id' => $booking->user_id,
            'body' => 'Your Venmo payment has been confirmed. Your booking is now complete!',
            'messageable_type' => Booking::class,
            'messageable_id' => $booking->id,
        ]);

        return new BookingResource($booking);
    }

    private function handleStripePayment(
        Booking $booking,
        array $extraInfo,
        StripeService $stripeManager
    ): BookingResource|JsonResponse {
        $priceInfo = $extraInfo['priceInfo'] ?? [];
        $isSwapBooking = $booking->booking_type === BookingType::SWAP;

        // For swap bookings, use guestPaymentIntent, otherwise use paymentIntent
        $guestPaymentIntent = $isSwapBooking
            ? ($priceInfo['guestPaymentIntent'] ?? null)
            : ($priceInfo['paymentIntent'] ?? null);
        $hostPaymentIntent = $priceInfo['hostPaymentIntent'] ?? null;

        // For swap bookings with host payment
        if ($hostPaymentIntent) {
            $hostPaymentIntentParts = explode('_secret_', (string) $hostPaymentIntent);
            if (empty($hostPaymentIntentParts[0])) {
                return response()->json([
                    'message' => 'Invalid host payment intent format',
                ], 422);
            }
            $hostPaymentIntentId = $hostPaymentIntentParts[0];

            // Check if the host payment is successful and update the status
            $hostPaymentSuccess = $stripeManager->checkPaymentIntentSuccess($hostPaymentIntentId);
            if ($hostPaymentSuccess) {
                $priceInfo['hostPaymentIntentCompleted'] = true;
                $extraInfo['priceInfo'] = $priceInfo;
                $booking->update([
                    'extra_info' => $extraInfo,
                ]);

                // If only host payment is required (only guest cleaning fee is enabled), mark as completed immediately
                if ($isSwapBooking && ! $booking->host_cleaning_fee_enabled && $booking->guest_cleaning_fee_enabled) {
                    Log::info('Swap booking completed with only host payment', [
                        'booking_id' => $booking->id,
                    ]);

                    $booking->update([
                        'status' => BookingStatus::COMPLETED,
                    ]);

                    // Send messages to both parties
                    Message::query()->create([
                        'sender_id' => 1, // System message
                        'receiver_id' => $booking->user_id,
                        'body' => 'The host has completed their payment for your home cleaning fee. Your booking is now confirmed!',
                        'messageable_type' => Booking::class,
                        'messageable_id' => $booking->id,
                    ]);

                    Message::query()->create([
                        'sender_id' => 1, // System message
                        'receiver_id' => $booking->toHome?->user_id,
                        'body' => 'Your payment for the guest home cleaning fee has been completed. The booking is now confirmed!',
                        'messageable_type' => Booking::class,
                        'messageable_id' => $booking->id,
                    ]);
                } else {
                    // Host payment completed but guest payment still required
                    // Notify the guest that the host has completed their payment
                    $guestUser = User::query()->find($booking->user_id);
                    if ($guestUser) {
                        // Use the notification service to process notifications
                        app(NotificationService::class)->processUserNotifications($guestUser);
                    }
                }
            }
            // Note: We don't return an error if the host payment is not successful
            // This allows the guest payment to be processed independently
        }

        // For regular bookings or swap bookings with guest payment
        if ($guestPaymentIntent) {
            $guestPaymentIntentParts = explode('_secret_', (string) $guestPaymentIntent);
            if (empty($guestPaymentIntentParts[0])) {
                return response()->json([
                    'message' => 'Invalid guest payment intent format',
                ], 422);
            }
            $guestPaymentIntentId = $guestPaymentIntentParts[0];

            // Check if the guest payment is successful and update the status
            $guestPaymentSuccess = $stripeManager->checkPaymentIntentSuccess($guestPaymentIntentId);
            if ($guestPaymentSuccess) {
                $priceInfo['guestPaymentIntentCompleted'] = true;
                $extraInfo['priceInfo'] = $priceInfo;
                $booking->update([
                    'extra_info' => $extraInfo,
                ]);

                // If only guest payment is required (only host cleaning fee is enabled), mark as completed immediately
                if ($isSwapBooking && $booking->host_cleaning_fee_enabled && ! $booking->guest_cleaning_fee_enabled) {
                    Log::info('Swap booking completed with only guest payment', [
                        'booking_id' => $booking->id,
                    ]);

                    $booking->update([
                        'status' => BookingStatus::COMPLETED,
                    ]);

                    // Send messages to both parties
                    Message::query()->create([
                        'sender_id' => 1, // System message
                        'receiver_id' => $booking->user_id,
                        'body' => 'Your payment for the host home cleaning fee has been completed. Your booking is now confirmed!',
                        'messageable_type' => Booking::class,
                        'messageable_id' => $booking->id,
                    ]);

                    Message::query()->create([
                        'sender_id' => 1, // System message
                        'receiver_id' => $booking->toHome?->user_id,
                        'body' => 'The guest has completed their payment for your home cleaning fee. The booking is now confirmed!',
                        'messageable_type' => Booking::class,
                        'messageable_id' => $booking->id,
                    ]);
                } else {
                    // Guest payment completed but host payment still required
                    // Notify the host that the guest has completed their payment
                    $hostUser = User::query()->find($booking->toHome?->user_id);
                    if ($hostUser) {
                        // Use the notification service to process notifications
                        app(NotificationService::class)->processUserNotifications($hostUser);
                    }
                }
            } elseif (! $isSwapBooking) {
                // Only return an error for non-swap bookings
                // For swap bookings, we want to allow partial payments
                return response()->json([
                    'message' => 'Guest payment is not successful',
                ], 403);
            }
        }

        // For swap bookings, check if both payments are required and completed
        if ($isSwapBooking) {
            $hostPaymentRequired = $booking->host_cleaning_fee_enabled;
            $guestPaymentRequired = $booking->guest_cleaning_fee_enabled;

            // If neither side requires payment, mark as completed immediately
            if (! $hostPaymentRequired && ! $guestPaymentRequired) {
                Log::info('Swap booking completed without cleaning fees', [
                    'booking_id' => $booking->id,
                    'host_cleaning_fee_enabled' => $hostPaymentRequired,
                    'guest_cleaning_fee_enabled' => $guestPaymentRequired,
                ]);

                $allPaymentsCompleted = true;
            } else {
                // Check if payment intents exist and are completed
                $hostPaymentIntentExists = isset($priceInfo['hostPaymentIntent']) && ! empty($priceInfo['hostPaymentIntent']);
                $guestPaymentIntentExists = isset($priceInfo['guestPaymentIntent']) && ! empty($priceInfo['guestPaymentIntent']);

                // If payment is required but no payment intent exists, something is wrong
                if ($hostPaymentRequired && ! $hostPaymentIntentExists) {
                    Log::warning('Host payment required but no payment intent exists', [
                        'booking_id' => $booking->id,
                        'host_cleaning_fee_enabled' => $hostPaymentRequired,
                    ]);
                }

                if ($guestPaymentRequired && ! $guestPaymentIntentExists) {
                    Log::warning('Guest payment required but no payment intent exists', [
                        'booking_id' => $booking->id,
                        'guest_cleaning_fee_enabled' => $guestPaymentRequired,
                    ]);
                }

                // Check if payments are completed
                $hostPaymentCompleted = $priceInfo['hostPaymentIntentCompleted'] ?? false;
                $guestPaymentCompleted = $priceInfo['guestPaymentIntentCompleted'] ?? false;

                // Handle the case where only one side requires payment
                if ($hostPaymentRequired && ! $guestPaymentRequired) {
                    // Only host payment is required
                    $allPaymentsCompleted = $hostPaymentCompleted;
                } elseif (! $hostPaymentRequired && $guestPaymentRequired) {
                    // Only guest payment is required
                    $allPaymentsCompleted = $guestPaymentCompleted;
                } else {
                    // Both payments are required
                    $allPaymentsCompleted = true;

                    if ($hostPaymentRequired && ! $hostPaymentCompleted) {
                        $allPaymentsCompleted = false;
                    }

                    if ($guestPaymentRequired && ! $guestPaymentCompleted) {
                        $allPaymentsCompleted = false;
                    }
                }
            }

            if ($allPaymentsCompleted) {
                $booking->update([
                    'status' => BookingStatus::COMPLETED,
                ]);

                // Determine the appropriate message based on which payments were required
                $guestMessage = '';
                $hostMessage = '';

                if ($hostPaymentRequired && $guestPaymentRequired) {
                    $guestMessage = 'All payments for your swap booking have been completed. Your booking is now confirmed!';
                    $hostMessage = 'All payments for the swap booking have been completed. The booking is now confirmed!';
                } elseif ($hostPaymentRequired && ! $guestPaymentRequired) {
                    $guestMessage = 'Your payment for the host home cleaning fee has been completed. Your booking is now confirmed!';
                    $hostMessage = 'The guest has completed their payment for your home cleaning fee. The booking is now confirmed!';
                } elseif (! $hostPaymentRequired && $guestPaymentRequired) {
                    $guestMessage = 'The host has completed their payment for your home cleaning fee. Your booking is now confirmed!';
                    $hostMessage = 'Your payment for the guest home cleaning fee has been completed. The booking is now confirmed!';
                } else {
                    $guestMessage = 'Your swap booking is now confirmed!';
                    $hostMessage = 'The swap booking is now confirmed!';
                }

                // Notify both parties that payments are complete
                Message::query()->create([
                    'sender_id' => 1, // System message
                    'receiver_id' => $booking->user_id,
                    'body' => $guestMessage,
                    'messageable_type' => Booking::class,
                    'messageable_id' => $booking->id,
                ]);

                Message::query()->create([
                    'sender_id' => 1, // System message
                    'receiver_id' => $booking->toHome?->user_id,
                    'body' => $hostMessage,
                    'messageable_type' => Booking::class,
                    'messageable_id' => $booking->id,
                ]);
            }
        } else {
            // For regular bookings, mark as completed if guest payment is done
            if ($priceInfo['guestPaymentIntentCompleted'] ?? false) {
                $booking->update([
                    'status' => BookingStatus::COMPLETED,
                ]);
            }
        }

        return new BookingResource($booking);
    }

    public function request(int $id, Request $request, StripeService $stripeManager): BookingResource|JsonResponse
    {
        $booking = Booking::query()
            ->with([
                'fromHome',
                'toHome',
                'user',
            ])
            ->findOrFail($id);

        $extraInfo = $booking->extra_info;

        /** @var Booking $booking */
        if ($booking->status !== BookingStatus::CREATED) {
            return response()->json([
                'message' => 'Booking is not in the correct status',
            ], 403);
        }

        if ($booking->user_id !== null && $booking->user_id !== $request->user()->id) {
            return response()->json([
                'message' => 'Unauthorized',
            ], 403);
        }

        // Add validation for swap bookings
        if ($booking->booking_type === BookingType::SWAP) {
            // Validate user_home is provided
            if (! $request->has('user_home')) {
                return response()->json([
                    'message' => 'Please select your home for the swap',
                ], 422);
            }

            // Validate the home belongs to the user
            $userHome = Home::query()
                ->where('id', $request->input('user_home'))
                ->where('user_id', $request->user()->id)
                ->first();

            if (! $userHome) {
                return response()->json([
                    'message' => 'Invalid home selected for swap',
                ], 422);
            }

            $booking->update([
                'user_home' => $userHome->id,
                'guest_cleaning_fee_enabled' => $request->boolean('guest_cleaning_fee_enabled', false),
            ]);
        }

        if (! isset($extraInfo['priceInfo']) || empty($extraInfo['priceInfo'])) {
            return response()->json([
                'message' => 'Price info is not set',
            ], 403);
        }

        if ($booking->user_id === null) {
            $booking->update([
                'user_id' => $request->user()->id,
            ]);
        }

        $booking->update([
            'status' => BookingStatus::REQUESTED,
        ]);

        $extraInfo = $booking->extra_info;
        $priceInfo = $extraInfo['priceInfo'];

        // For swap bookings, ensure cleaning fee is properly set
        if ($booking->booking_type === BookingType::SWAP) {
            // Make sure getCleaningFee is set
            if (! isset($priceInfo['getCleaningFee']) || $priceInfo['getCleaningFee'] == 0) {
                $priceInfo['getCleaningFee'] = (float) $booking->toHome?->cleaning_fee;
            }

            // For swap bookings, total money equals cleaning fee
            $priceInfo['getTotalMoney'] = $priceInfo['getCleaningFee'];
        }

        // Handle deposit coverage and travel insurance
        $depositCoverageFee = 50;
        $travelInsuranceFee = 100;
        $additionalFees = 0;

        if (isset($request['is_deposit_coverage']) && $request['is_deposit_coverage']) {
            $priceInfo['deposit_coverage'] = true;
            $priceInfo['deposit_coverage_fee'] = $depositCoverageFee;
            $additionalFees += $depositCoverageFee;
        }

        if (isset($request['is_travel_insurance_coverage']) && $request['is_travel_insurance_coverage']) {
            $priceInfo['travel_insurance_coverage'] = true;
            $priceInfo['travel_insurance_fee'] = $travelInsuranceFee;
            $additionalFees += $travelInsuranceFee;
        }

        // Update total money with additional fees
        if ($additionalFees > 0) {
            $priceInfo['getTotalMoney'] = $priceInfo['getTotalMoney'] + $additionalFees;
        }

        $extraInfo['priceInfo'] = $priceInfo;

        /* Superhog Validation */
        if ($booking->booking_type === BookingType::RENT && $booking->from_sharable_link === null) {
            $url = config('services.truvi.endpoint');

            $headers = [
                'Content-Type' => 'application/json',
                'x-environment' => config('services.truvi.environment'),
                'Ocp-Apim-Subscription-Key' => config('services.truvi.subscription_key'),
            ];

            $userInfo = User::where('id', $booking->user_id)->firstOrFail();
            $homeInfo = Home::query()
                ->where('id', $booking->request_user_home)
                ->first();

            $zipCode = $this->getZipCode($homeInfo->address);

            $payload = '{
                            "guest": {
                                "email": "'.$userInfo->email.'",
                                "firstName": "'.$userInfo->first_name.'",
                                "lastName": "'.$userInfo->last_name.'",
                                "telephoneNumber": "'.$userInfo->phone_number.'"
                            },
                            "listing": {
                                "address": {
                                    "addressLine1": "'.$homeInfo->street.'",
                                    "addressLine2": "",
                                    "countryIso": "US",
                                    "postcode": "'.$zipCode.'",
                                    "town": "'.$homeInfo->city_long.'"
                                },
                                "listingId": "'.$homeInfo->id.'",
                                "listingName": "'.$homeInfo->title.'",
                                "petsAllowed": "'.(
                $homeInfo->extra_info !== null &&
                isset($homeInfo->extra_info['pets']) &&
                isset($homeInfo->extra_info['pets']['enabled']) &&
                $homeInfo->extra_info['pets']['enabled'] === 'yes'
                    ? 'True'
                    : 'False'
            ).'"
                            },
                            "metadata": {
                                "echoToken": "'.((string) Str::uuid()).'",
                                "timeStamp": "'.substr(Carbon::now()->format('Y-m-d\TH:i:s.v'), 0, -1).'"
                            },
                            "reservation": {
                                "channel": "web",
                                "checkIn": "'.Carbon::parse($booking->start_at)->format('Y-m-d').'",
                                "checkOut": "'.Carbon::parse($booking->end_at)->format('Y-m-d').'",
                                "creationDate": "'.Carbon::now()->format('Y-m-d').'",
                                "reservationId": "'.$booking->id.'",
                            }
                        }';

            // Log the payload for debugging
            Log::info('SuperHog Payload:', ['payload' => $payload, 'booking' => $booking->id]);

            $response = Http::withHeaders($headers)->withBody($payload, 'application/json')->post($url);

            $extraInfo['superHogInfo'] = $response->json();
        }

        $booking->update([
            'extra_info' => $extraInfo,
        ]);

        // Create a message if the 'message' field is present in the request
        if ($request->has('message') && ! empty($request->input('message'))) {
            Message::query()
                ->create([
                    'sender_id' => $request->user()->id,
                    'receiver_id' => $booking->toHome?->user_id,
                    'body' => $request->input('message'),
                    'messageable_type' => Booking::class,
                    'messageable_id' => $booking->id,
                ]);
        }

        $indefiniteApproval = IndefiniteApproval::query()->where([
            'guest_id' => $booking->user_id,
            'home_id' => $booking->request_user_home,
        ])->first();

        if ($indefiniteApproval) {
            $totalPrice = (float) $priceInfo['getTotalMoney'];

            // Get host's Stripe Connect ID if available
            $hostUser = User::query()->find($booking->toHome?->user_id);
            $hostStripeConnectId = null;

            if ($hostUser && ! empty($hostUser->stripe_connect_id)) {
                $hostStripeConnectId = $hostUser->stripe_connect_id;
            }

            $priceInfo['paymentIntent'] = $stripeManager->getPaymentIntent(
                $totalPrice,
                0, // No application fee
                'USD',
                'Twimo Booking',
                $hostStripeConnectId
            );

            $extraInfo['priceInfo'] = $priceInfo;

            $booking->update([
                'status' => BookingStatus::ACCEPTED,
                'extra_info' => $extraInfo,
            ]);

            // Send notification to the host about the auto-approved booking
            if ($hostUser) {
                // Create and send the notification directly
                $notification = new \App\Notifications\Booking\AutoApprovedBookingNotification($booking, $hostUser);
                $hostUser->notify($notification);

                // Also create a system message to inform the host
                Message::query()->create([
                    'sender_id' => 1, // System message
                    'receiver_id' => $hostUser->id,
                    'body' => 'A pre-approved '.($booking->booking_type === BookingType::SWAP ? 'swap' : 'booking').
                              ' request for '.$booking->toHome?->title.' has been automatically approved.',
                    'messageable_type' => Booking::class,
                    'messageable_id' => $booking->id,
                ]);
            }
        }

        return new BookingResource($booking);
    }

    public function cancel(int $id, Request $request, StripeService $stripeManager): BookingResource|JsonResponse
    {
        $booking = Booking::query()->findOrFail($id);

        if ($booking->status !== BookingStatus::COMPLETED) {
            return response()->json([
                'message' => 'Booking cannot be cancelled in its current status',
            ], 403);
        }

        $authUser = $request->user();
        if ($booking->user_id !== $authUser->id && $booking->toHome?->user_id !== $authUser->id) {
            return response()->json([
                'message' => 'Unauthorized',
            ], 403);
        }

        // Check cancellation policy and timing restrictions
        $cancellationValidation = $this->validateCancellationPolicy($booking);
        Log::info('Cancellation validation result', [
            'booking_id' => $booking->id,
            'validation' => $cancellationValidation,
            'start_at' => $booking->start_at,
            'current_time' => now()->toISOString(),
        ]);

        if (!$cancellationValidation['allowed']) {
            Log::info('Cancellation blocked by policy', [
                'booking_id' => $booking->id,
                'message' => $cancellationValidation['message'],
            ]);

            return response()->json([
                'message' => $cancellationValidation['message'],
                'error' => 'CANCELLATION_POLICY_VIOLATION',
            ], 403);
        }

        $extraInfo = $booking->extra_info;

        // Check if this is a Venmo payment
        $isVenmoPayment = isset($extraInfo['venmo_payment']);

        // Log the payment method for debugging
        Log::info('Booking cancellation initiated', [
            'booking_id' => $booking->id,
            'is_venmo_payment' => $isVenmoPayment,
            'user_id' => $request->user()->id,
        ]);

        // Track refund amounts for payment status display
        $refundInfo = [];

        // Only process Stripe refunds if this is not a Venmo payment
        if (! $isVenmoPayment) {
            // Refund guest payment if exists
            if (isset($extraInfo['priceInfo']['paymentIntent'])) {
                $guestPaymentIntentParts = explode('_secret_', $extraInfo['priceInfo']['paymentIntent']);
                $guestPaymentIntentId = ! empty($guestPaymentIntentParts[0]) ? $guestPaymentIntentParts[0] : '';
                if (! $stripeManager->refundPayment($guestPaymentIntentId)) {
                    return response()->json([
                        'message' => 'Failed to process guest refund',
                    ], 500);
                }
                // Track guest refund amount
                $refundInfo['guest_refund_amount'] = $extraInfo['priceInfo']['getTotalMoney'] ?? 0;
            }

            // Refund host payment if exists (for swap bookings)
            if (isset($extraInfo['priceInfo']['hostPaymentIntent'])) {
                $hostPaymentIntentParts = explode('_secret_', $extraInfo['priceInfo']['hostPaymentIntent']);
                $hostPaymentIntentId = ! empty($hostPaymentIntentParts[0]) ? $hostPaymentIntentParts[0] : '';
                if (! $stripeManager->refundPayment($hostPaymentIntentId)) {
                    return response()->json([
                        'message' => 'Failed to process host refund',
                    ], 500);
                }
                // Track host refund amount
                $refundInfo['host_refund_amount'] = $extraInfo['priceInfo']['hostPaymentAmount'] ?? 0;
            }
        }

        $booking->update([
            'status' => BookingStatus::CANCELED,
            'extra_info' => array_merge($extraInfo, [
                'cancelled_at' => now()->toISOString(),
                'cancelled_by' => $request->user()->id,
                'payment_method' => $isVenmoPayment ? 'venmo' : 'stripe',
                'refund_info' => $refundInfo,
            ]),
        ]);

        Log::info('Booking cancelled successfully', [
            'booking_id' => $booking->id,
            'is_venmo_payment' => $isVenmoPayment,
            'user_id' => $request->user()->id,
        ]);

        // Remove event from Calendar
        CalendarEvent::query()->where('event_ref_id', $booking->id)->delete();

        // Determine if the host is cancelling
        $isHostCancelling = $request->user()->id === $booking->toHome?->user_id;

        // Create appropriate cancellation messages based on who is cancelling
        if ($isVenmoPayment) {
            $messageBody = 'Booking has been cancelled. Any Venmo payments should be refunded manually outside the platform.';
        } else {
            if ($isHostCancelling) {
                // Host is cancelling - message to host for record-keeping
                $messageBody = 'Your booking has been cancelled. A refund will be sent to the guest\'s original payment method following the rental policy in place.';

                // Also send a message to the guest
                Message::query()->create([
                    'sender_id' => 1, // System message
                    'receiver_id' => $booking->user_id,
                    'body' => 'Your booking has been cancelled. A refund will be sent to your original payment method following the rental policy in place.',
                    'messageable_type' => Booking::class,
                    'messageable_id' => $booking->id,
                ]);
            } else {
                // Guest is cancelling - message to host
                $messageBody = 'Your booking has been cancelled. A refund will be sent to the guest\'s original payment method following the rental policy in place.';
            }
        }

        // Create a message for the host (if guest is cancelling) or for record-keeping (if host is cancelling)
        Message::query()->create([
            'sender_id' => $request->user()->id,
            'receiver_id' => $booking->toHome?->user_id,
            'body' => $messageBody,
            'messageable_type' => Booking::class,
            'messageable_id' => $booking->id,
        ]);

        return new BookingResource($booking);
    }

    public function approveIndefinitely(int $id, Request $request, StripeService $stripeManager): BookingResource|JsonResponse
    {
        $booking = Booking::query()->findOrFail($id);

        if ($booking->status !== BookingStatus::REQUESTED) {
            return response()->json([
                'message' => 'Booking is not in the correct status',
            ], 403);
        }

        if ($booking->toHome?->user_id !== $request->user()->id) {
            return response()->json([
                'message' => 'Unauthorized',
            ], 403);
        }

        // Create indefinite approval
        IndefiniteApproval::query()->create([
            'guest_id' => $booking->user_id,
            'home_id' => $booking->request_user_home,
        ]);

        // Approve the current booking
        // Pass along the host_cleaning_fee_enabled parameter if it exists
        return $this->approve($id, $request, $stripeManager);
    }

    public function markVenmoPaid(int $id, Request $request): BookingResource|JsonResponse
    {
        $booking = Booking::query()->findOrFail($id);

        // Check if user is authorized
        if ($booking->user_id !== $request->user()->id) {
            return response()->json([
                'message' => 'Unauthorized',
            ], 403);
        }

        // Check if booking is in correct status
        if ($booking->status !== BookingStatus::ACCEPTED) {
            return response()->json([
                'message' => 'Booking must be in accepted status to mark Venmo payment',
            ], 422);
        }

        // Check if this is a private booking
        if (! $booking->from_sharable_link) {
            return response()->json([
                'message' => 'Venmo payments are only available for private bookings',
            ], 422);
        }

        // Get and validate extra_info
        $extraInfo = $booking->extra_info;
        if (! isset($extraInfo['priceInfo']) || empty($extraInfo['priceInfo'])) {
            return response()->json([
                'message' => 'Price info is not set',
            ], 422);
        }

        // Add Venmo payment flag to extra_info
        $extraInfo['venmo_payment'] = [
            'marked_paid_at' => now()->toISOString(),
            'marked_paid_by' => $request->user()->id,
            'amount' => $extraInfo['priceInfo']['getTotalMoney'] ?? 0,
        ];

        // Update booking
        $booking->update([
            'extra_info' => $extraInfo,
        ]);

        // Create a message to notify the host
        Message::query()->create([
            'sender_id' => $request->user()->id,
            'receiver_id' => $booking->toHome?->user_id,
            'body' => 'Guest has marked Venmo payment as completed. Please verify the payment and confirm the booking.',
            'messageable_type' => Booking::class,
            'messageable_id' => $booking->id,
        ]);

        return new BookingResource($booking);
    }

    private function getZipCode($address)
    {
        // Regular expression to match a 5-digit ZIP code (US format)
        preg_match_all('/\b\d{5}(?:-\d{4})?\b/', $address, $matches);

        // Return ZIP code if found, otherwise return "-"
        return end($matches[0]) ?? '-';
    }

    /**
     * Validate if a booking can be cancelled based on cancellation policy and timing
     */
    private function validateCancellationPolicy(Booking $booking): array
    {
        $now = now();
        $startDate = Carbon::parse($booking->start_at);
        $hoursUntilStart = $now->diffInHours($startDate, false);

        // Determine if this is a private booking
        $isPrivateBooking = $booking->from_sharable_link !== null || $booking->toHome?->is_public === false;

        Log::info('Cancellation policy validation details', [
            'booking_id' => $booking->id,
            'now' => $now->toISOString(),
            'start_date' => $startDate->toISOString(),
            'hours_until_start' => $hoursUntilStart,
            'is_private_booking' => $isPrivateBooking,
            'from_sharable_link' => $booking->from_sharable_link,
            'home_is_public' => $booking->toHome?->is_public,
        ]);

        // For private bookings (Friends & Family), use a more lenient policy
        if ($isPrivateBooking) {
            // Allow cancellation up to 24 hours before start
            if ($hoursUntilStart < 24) {
                return [
                    'allowed' => false,
                    'message' => 'You are unable to cancel this booking due to the cancellation policy in place. Friends & Family bookings cannot be cancelled within 24 hours of the start date.',
                ];
            }
            return ['allowed' => true, 'message' => ''];
        }

        // Get the home's cancellation policies
        $cancellationPolicies = $booking->toHome?->extra_info['cancellationPolicies'] ?? [];

        // Determine the policy type and validate accordingly
        if ($cancellationPolicies['flexiblePublicRental'] ?? false) {
            // Flexible policy: No cancellation within 24 hours
            if ($hoursUntilStart < 24) {
                return [
                    'allowed' => false,
                    'message' => 'You are unable to cancel this booking due to the cancellation policy in place. Flexible cancellation policy does not allow cancellations within 24 hours of check-in.',
                ];
            }
        } elseif ($cancellationPolicies['publicRental'] ?? false) {
            // Standard policy: No cancellation within 7 days (168 hours)
            if ($hoursUntilStart < 168) {
                return [
                    'allowed' => false,
                    'message' => 'You are unable to cancel this booking due to the cancellation policy in place. Standard cancellation policy does not allow cancellations within 7 days of check-in.',
                ];
            }
        } elseif ($cancellationPolicies['longTermStay'] ?? false) {
            // Long-term policy: No cancellation within 30 days (720 hours)
            if ($hoursUntilStart < 720) {
                return [
                    'allowed' => false,
                    'message' => 'You are unable to cancel this booking due to the cancellation policy in place. Long-term stay policy does not allow cancellations within 30 days of check-in.',
                ];
            }
        } elseif ($cancellationPolicies['nonRefundable'] ?? false) {
            // Non-refundable: No cancellation allowed
            return [
                'allowed' => false,
                'message' => 'You are unable to cancel this booking due to the cancellation policy in place. This booking has a non-refundable cancellation policy.',
            ];
        } else {
            // Default to flexible policy if no specific policy is set
            if ($hoursUntilStart < 24) {
                return [
                    'allowed' => false,
                    'message' => 'You are unable to cancel this booking due to the cancellation policy in place. Cancellations are not allowed within 24 hours of check-in.',
                ];
            }
        }

        return ['allowed' => true, 'message' => ''];
    }
}
