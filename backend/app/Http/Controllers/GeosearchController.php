<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Services\GeoCodeService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

final class GeosearchController extends Controller
{
    public function getCoordinates(Request $request, GeoCodeService $geoCodeManager): JsonResponse
    {
        $address = $request->query('address');

        if (! $address) {
            return response()->json(['error' => 'Address is required'], 400);
        }

        $data = $geoCodeManager->getDataFromGeoCode($address);

        if ($data['status'] === 'OK' && ! empty($data['results'])) {
            $location = $data['results'][0]['geometry']['location'];

            return response()->json([
                'lat' => $location['lat'],
                'lng' => $location['lng'],
            ]);
        }

        return response()->json(['error' => 'Unable to geocode address'], 404);
    }
}
