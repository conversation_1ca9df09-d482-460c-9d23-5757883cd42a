<?php

namespace App\Http\Controllers;

use App\Enums\HomeStatus;
use App\Http\Resources\GetPublicUserDetailBySharedUrlResource;
use App\Models\User;
use Illuminate\Http\JsonResponse;

final class GetPublicUserDetailBySharedUrlController
{
    public function __invoke(string $sharedUrl): GetPublicUserDetailBySharedUrlResource|JsonResponse
    {
        $user = User::query()
            ->where('shared_url', $sharedUrl)
            ->orWhere(function ($query) use ($sharedUrl) {
                preg_match('/\d+$/', $sharedUrl, $matches);
                $query->where('id', $matches[0] ?? '-1');
            })
            ->with('homes', fn ($query) => $query->where('status', HomeStatus::ACTIVE->value))
            ->without(['role', 'group'])
            ->first();

        if ($user === null) {
            return response()->json([
                'message' => 'User not found',
            ], 404);
        }

        $user->setAppends([]);

        return new GetPublicUserDetailBySharedUrlResource($user);
    }
}
