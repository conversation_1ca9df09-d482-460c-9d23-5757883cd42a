<?php

namespace App\Http\Controllers;

use App\Http\Requests\SendPrivateHomeLinkMailRequest;
use App\Mail\PrivateHomeLinkEmail;
use App\Models\Home;
use App\Models\SharableLink;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Mail;

final class SendPrivateHomeLinkMailController
{
    public function __invoke(
        int|string $id,
        SendPrivateHomeLinkMailRequest $request,
    ): JsonResponse {
        $home = Home::query()
            ->where('id', $id)
            ->orWhere('slug', $id)
            ->firstOrFail();
        /** @var Home $home */
        $sharableLink = SharableLink::query()
            ->findOrFail($request->get('sharable_link_id'));
        /** @var SharableLink $sharableLink */
        $email = $request->get('email');

        Mail::to($email)->send(new PrivateHomeLinkEmail($home, $sharableLink));

        return response()->json([
            'message' => 'Email sent successfully',
        ]);
    }
}
