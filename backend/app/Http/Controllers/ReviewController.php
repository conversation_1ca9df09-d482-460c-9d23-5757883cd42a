<?php

namespace App\Http\Controllers;

use App\Models\Booking;
use App\Models\Review;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

final readonly class ReviewController
{
    public function store(Request $request, Booking $booking): JsonResponse
    {
        $request->validate([
            'rating' => 'required|integer|min:1|max:5',
            'feedback' => 'nullable|string|max:1000',
            'type' => ['required', Rule::in(['guest_review', 'host_review'])],
        ]);

        // Verify the user can leave this type of review
        $user = Auth::user();
        $isHost = $booking->toHome?->user_id === $user->id;
        $isGuest = $booking->user_id === $user->id;

        if ($request->type === 'host_review' && ! $isHost) {
            return response()->json(['message' => 'Only hosts can leave host reviews'], 403);
        }

        if ($request->type === 'guest_review' && ! $isGuest) {
            return response()->json(['message' => 'Only guests can leave guest reviews'], 403);
        }

        // Check if review already exists
        $existingReview = $booking->reviews()
            ->where('reviewer_id', $user->id)
            ->where('type', $request->type)
            ->first();

        if ($existingReview) {
            return response()->json(['message' => 'You have already reviewed this booking'], 400);
        }

        // Create the review
        $review = new Review([
            'rating' => $request->rating,
            'feedback' => $request->feedback,
            'type' => $request->type,
            'reviewer_id' => $user->id,
            'reviewee_id' => $request->type === 'host_review' ? $booking->user_id : $booking->toHome?->user_id,
            'home_id' => $booking->request_user_home,
        ]);

        $booking->reviews()->save($review);

        return response()->json($review->load(['reviewer', 'reviewee']), 201);
    }

    public function index(Request $request): JsonResponse
    {
        $request->validate([
            'type' => ['nullable', Rule::in(['guest_review', 'host_review'])],
            'home_id' => 'nullable|exists:homes,id',
            'user_id' => 'nullable|exists:users,id',
        ]);

        $query = Review::with(['reviewer', 'reviewee', 'home', 'booking']);

        if ($request->type) {
            $query->where('type', $request->type);
        }

        if ($request->home_id) {
            $query->where('home_id', $request->home_id);
        }

        if ($request->user_id) {
            $query->where('reviewee_id', $request->user_id);
        }

        $reviews = $query->latest()->paginate(10);

        return response()->json($reviews);
    }

    public function show(Review $review): JsonResponse
    {
        return response()->json($review->load(['reviewer', 'reviewee', 'home', 'booking']));
    }
}
