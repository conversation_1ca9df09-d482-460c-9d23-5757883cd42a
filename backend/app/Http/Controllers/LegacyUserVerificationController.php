<?php

namespace App\Http\Controllers;

use App\Enums\HomeStatus;
use App\Models\Home;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class LegacyUserVerificationController extends Controller
{
    /**
     * Update all homes of a legacy user to draft status if they haven't completed identity verification
     */
    public function updateHomesStatus(Request $request): JsonResponse
    {
        $user = $request->user();

        // Check if user is a legacy user (created before 2025-01-31)
        $isLegacyUser = $user->created_at < now()->createFromFormat('Y-m-d', '2025-01-31')->startOfDay();

        if (! $isLegacyUser) {
            return response()->json([
                'success' => false,
                'message' => 'User is not a legacy user',
            ]);
        }

        // Check if user has verified their identity
        if ($user->identity_verified) {
            return response()->json([
                'success' => true,
                'message' => 'User has already verified their identity',
            ]);
        }

        // Get all active homes for this user
        $activeHomes = Home::where('user_id', $user->id)
            ->where('status', HomeStatus::ACTIVE->value)
            ->get();

        if ($activeHomes->isEmpty()) {
            return response()->json([
                'success' => true,
                'message' => 'No active homes found for this user',
            ]);
        }

        // Update all active homes to draft status
        $updatedCount = 0;
        foreach ($activeHomes as $home) {
            $home->status = HomeStatus::DRAFT->value;
            $home->save();
            $updatedCount++;
        }

        Log::info('Legacy user homes moved to draft status due to incomplete identity verification', [
            'user_id' => $user->id,
            'homes_updated' => $updatedCount,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Homes updated to draft status',
            'homes_updated' => $updatedCount,
        ]);
    }
}
