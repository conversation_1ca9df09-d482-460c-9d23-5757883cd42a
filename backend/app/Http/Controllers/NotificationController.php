<?php

namespace App\Http\Controllers;

use App\Models\Notification;
use App\Models\User;
use App\Services\NotificationService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

final readonly class NotificationController
{
    public function __construct(
        private NotificationService $notificationService
    ) {}

    public function index(Request $request): JsonResponse
    {
        $user = auth()->user();

        /** @var User $user */
        $query = $user->notifications();

        if ($request->get('type') === 'unread') {
            $query = $query->getQuery()->whereNull('read_at');
        } elseif ($request->get('type') === 'archived') {
            $query = $query->getQuery()->whereNotNull('archived_at');
        } elseif ($request->get('type') === 'all') {
            // Return all notifications without filtering by archived status
        } else {
            // Default to showing only unarchived notifications
            $query = $query->getQuery()->whereNull('archived_at');
        }

        $notifies = $query->get();

        return response()->json($notifies);
    }

    public function markAsRead(int $id): JsonResponse
    {
        $notify = Notification::query()->findOrFail($id);
        $authUser = auth()->user();

        /**
         * @var Notification $notify
         * @var User $authUser
         */
        if ($authUser->id === (int) $notify->notifiable_id && $notify->notifiable_type === User::class) {
            $notify->update(['read_at' => now()]);
        }

        return response()->json($notify);
    }

    public function markAllAsRead(Request $request): JsonResponse
    {
        $request->user()->notifications()->whereNull('read_at')->update(['read_at' => now()]);

        return response()->json(['message' => 'All notifications marked as read']);
    }

    public function processCurrentUserNotifications(): JsonResponse
    {
        /** @var User $user */
        $user = auth()->user();

        $this->notificationService->processUserNotifications($user);

        return response()->json(['message' => 'Notifications processed successfully']);
    }

    public function archiveNotification(int $id): JsonResponse
    {
        $notify = Notification::query()->findOrFail($id);
        $authUser = auth()->user();

        /**
         * @var Notification $notify
         * @var User $authUser
         */
        if ($authUser->id === (int) $notify->notifiable_id && $notify->notifiable_type === User::class) {
            $notify->update(['archived_at' => now()]);
        }

        return response()->json($notify);
    }

    public function unarchiveNotification(int $id): JsonResponse
    {
        $notify = Notification::query()->findOrFail($id);
        $authUser = auth()->user();

        /**
         * @var Notification $notify
         * @var User $authUser
         */
        if ($authUser->id === (int) $notify->notifiable_id && $notify->notifiable_type === User::class) {
            $notify->update(['archived_at' => null]);
        }

        return response()->json($notify);
    }

    public function archiveAllNotifications(Request $request): JsonResponse
    {
        $request->user()->notifications()->whereNull('archived_at')->update(['archived_at' => now()]);

        return response()->json(['message' => 'All notifications archived']);
    }
}
