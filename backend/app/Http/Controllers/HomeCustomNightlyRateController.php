<?php

namespace App\Http\Controllers;

use App\Http\Requests\CreateHomeCustomNightlyRateRequest;
use App\Http\Requests\GetHomeCustomNightlyRateForDateRangeRequest;
use App\Http\Requests\UpdateHomeCustomNightlyRateRequest;
use App\Http\Resources\HomeCustomNightlyRateResource;
use App\Models\Home;
use App\Models\HomeCustomNightlyRate;
use App\Services\HomeCustomNightlyRateService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use RuntimeException;

final class HomeCustomNightlyRateController
{
    private HomeCustomNightlyRateService $homeCustomNightlyRateService;

    public function __construct(HomeCustomNightlyRateService $homeCustomNightlyRateService)
    {
        $this->homeCustomNightlyRateService = $homeCustomNightlyRateService;
    }

    public function list(Request $request): AnonymousResourceCollection
    {
        $authUserId = auth()->id();
        $homeId = (int) $request->get('home_id');

        return HomeCustomNightlyRateResource::collection(
            $this->homeCustomNightlyRateService->list($authUserId, $homeId)
        );
    }

    public function update(int $id, UpdateHomeCustomNightlyRateRequest $request): HomeCustomNightlyRateResource
    {
        $requestedData = $request->validated();

        $homeCustomNightlyRate = $this->homeCustomNightlyRateService->update(
            $id,
            $requestedData['nightly_rate'],
            $requestedData['is_active'],
        );

        return new HomeCustomNightlyRateResource($homeCustomNightlyRate);
    }

    public function create(CreateHomeCustomNightlyRateRequest $request): HomeCustomNightlyRateResource
    {
        $requestedData = $request->validated();
        $conditions = $requestedData['conditions'];
        $homeId = $requestedData['home_id'];

        // Parse the dates for comparison
        $fromDate = $conditions['from_date'] ? Carbon::parse($conditions['from_date']) : null;
        $toDate = $conditions['to_date'] ? Carbon::parse($conditions['to_date']) : null;

        if ($fromDate && $toDate) {
            // Check for any overlapping date ranges
            $existingRates = HomeCustomNightlyRate::where('home_id', $homeId)
                ->where('is_active', true)
                ->where(function ($query) use ($fromDate, $toDate) {
                    // Find any rates where:
                    // 1. The existing from_date falls within our new range
                    // 2. The existing to_date falls within our new range
                    // 3. Our new range falls completely within an existing range
                    $query->where(function ($q) use ($fromDate, $toDate) {
                        $q->whereNotNull('conditions->from_date')
                            ->whereNotNull('conditions->to_date')
                            ->where(function ($dateQuery) use ($fromDate, $toDate) {
                                // Existing from_date is between our new from and to dates
                                $dateQuery->where(function ($q) use ($fromDate, $toDate) {
                                    $q->whereDate('conditions->from_date', '>=', $fromDate)
                                        ->whereDate('conditions->from_date', '<=', $toDate);
                                })
                                // Existing to_date is between our new from and to dates
                                    ->orWhere(function ($q) use ($fromDate, $toDate) {
                                        $q->whereDate('conditions->to_date', '>=', $fromDate)
                                            ->whereDate('conditions->to_date', '<=', $toDate);
                                    })
                                // Our new range is completely within an existing range
                                    ->orWhere(function ($q) use ($fromDate, $toDate) {
                                        $q->whereDate('conditions->from_date', '<=', $fromDate)
                                            ->whereDate('conditions->to_date', '>=', $toDate);
                                    });
                            });
                    });
                })
                ->get();

            // Check if there are any overlapping rates with matching days of week
            if ($existingRates->isNotEmpty()) {
                $daysOfWeek = $conditions['days_of_week'] ?? [0, 1, 2, 3, 4, 5, 6]; // Default to all days if not specified

                foreach ($existingRates as $existingRate) {
                    $existingDaysOfWeek = $existingRate->conditions['days_of_week'] ?? [0, 1, 2, 3, 4, 5, 6];

                    // Check if there's any overlap in the days of the week
                    $overlappingDays = array_intersect($daysOfWeek, $existingDaysOfWeek);

                    if (! empty($overlappingDays)) {
                        $existingFromDate = Carbon::parse($existingRate->conditions['from_date'])->format('M d, Y');
                        $existingToDate = Carbon::parse($existingRate->conditions['to_date'])->format('M d, Y');

                        throw new RuntimeException(
                            'Overlapping pricing rule detected. There is already a pricing rule for '.
                            "the period from {$existingFromDate} to {$existingToDate} that includes the same days of the week."
                        );
                    }
                }
            }
        }

        $homeCustomNightlyRate = $this->homeCustomNightlyRateService->create(
            $homeId,
            $requestedData['nightly_rate'],
            $requestedData['conditions'] ?? null,
        );

        return new HomeCustomNightlyRateResource($homeCustomNightlyRate);
    }

    public function getNightlyRateForDateRange(GetHomeCustomNightlyRateForDateRangeRequest $request): array
    {
        $requestedData = $request->validated();

        $homeId = $requestedData['home_id'];
        $fromDate = Carbon::parse($requestedData['from_date']);
        $toDate = Carbon::parse($requestedData['to_date']);

        $home = Home::query()->findOrFail($homeId);

        /** @var Home $home */
        return $this->homeCustomNightlyRateService->getNightlyRateForDateRange(
            $home,
            $fromDate,
            $toDate,
        );
    }

    public function destroy(int $id): void
    {
        $this->homeCustomNightlyRateService->delete($id);
    }
}
