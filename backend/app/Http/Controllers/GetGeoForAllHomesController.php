<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Throwable;

final class GetGeoForAllHomesController
{
    private const string CACHE_KEY = 'geo_for_all_homes';

    private const int|float CACHE_TTL = 60 * 60 * 24;

    public function __invoke(): JsonResponse
    {
        try {
            $endpoint = config('services.geoForAllHomes.endpoint');

            $response = Cache::remember(self::CACHE_KEY, self::CACHE_TTL, static function () use ($endpoint) {
                return Http::get($endpoint)->json();
            });

            return response()->json($response);
        } catch (Throwable $e) {
            report($e);

            return response()->json([
                'count' => 0,
                'data' => [],
            ]);
        } finally {
            $user = auth()->user();

            /* @var User $user */

            Log::info('Geo For All Homes', [
                'user' => $user->email ?? 'guest',
                'ip' => request()->ip(),
            ]);
        }
    }
}
