<?php

namespace App\Http\Controllers;

use App\Models\Home;
use App\Models\SavedHomeCategory;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

final class SavedHomeController
{
    public function saveHome(Request $request, int|string $id, int $homeId): JsonResponse
    {
        $userId = $request->user()->id;

        $home = Home::query()->findOrFail($homeId);
        $category = SavedHomeCategory::query()
            ->where('user_id', $userId)
            ->where(function ($query) use ($id) {
                $query->where('id', $id)
                    ->orWhere('slug', $id);
            })
            ->firstOrFail();

        $home->savedCategories()->syncWithoutDetaching([$category->id]);

        return response()->json(['success' => true]);
    }

    public function unSaveHome(Request $request, int|string $id, int $homeId): JsonResponse
    {
        $userId = $request->user()->id;

        $home = Home::query()->findOrFail($homeId);
        $category = SavedHomeCategory::query()
            ->where('user_id', $userId)
            ->where(function ($query) use ($id) {
                $query->where('id', $id)
                    ->orWhere('slug', $id);
            })
            ->firstOrFail();

        $home->savedCategories()->detach($category->id);

        return response()->json(['success' => true]);
    }

    public function getSavedHomes(Request $request): JsonResponse
    {
        $userId = $request->user()->id;

        $savedHomes = Home::query()->whereHas('savedCategories', function ($query) use ($userId) {
            $query->where('user_id', $userId);
        })->with('savedCategories')->get();

        return response()->json($savedHomes);
    }

    public function createCategory(Request $request): JsonResponse
    {
        $userId = $request->user()->id;
        $name = $request->input('name');

        $category = SavedHomeCategory::query()->create([
            'user_id' => $userId,
            'name' => $name,
        ]);

        return response()->json($category);
    }

    public function deleteCategory(Request $request, int|string $id): JsonResponse
    {
        $userId = $request->user()->id;

        $category = SavedHomeCategory::query()
            ->where('user_id', $userId)
            ->where(function ($q) use ($id) {
                $q->where('id', $id)
                    ->orWhere('slug', $id);
            })
            ->firstOrFail();

        $result = $category->delete();

        return response()->json(['success' => $result]);
    }

    public function getCategories(Request $request): JsonResponse
    {
        $userId = $request->user()->id;

        $categories = SavedHomeCategory::query()
            ->with([
                'homes' => function ($query) {
                    $query->select('id')->with([
                        'media' => function ($q) {
                            $q->limit(1);
                        },
                    ])
                        ->without('available_dates');
                },
            ])
            ->where('user_id', $userId)
            ->get();

        return response()->json($categories);
    }

    public function getCategory(Request $request, int|string $id): JsonResponse
    {
        $userId = $request->user()->id;

        $category = SavedHomeCategory::query()
            ->with([
                'homes' => function ($query) {
                    $query->with([
                        'media' => function ($q) {
                            $q->limit(1);
                        },
                    ]);
                },
            ])
            ->where('user_id', $userId)
            ->where(function ($q) use ($id) {
                $q->where('saved_home_categories.id', $id)
                    ->orWhere('saved_home_categories.slug', $id);
            })
            ->firstOrFail();

        return response()->json($category);
    }
}
