<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Enums\HomeStatus;
use App\Http\Resources\GetAuthUserHomesResource;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

final class GetAuthUserHomesController
{
    public function __invoke(Request $request): JsonResponse
    {
        $paymentLink = $request->input('payment-link', false);
        $forSwap = $request->input('for-swap', false);

        $user = $request->user();
        /** @var User $user */
        $homes = $user
            ->homes()
            ->without(['media', 'available_dates'])
            ->orderBy('id', 'desc')
            ->when($paymentLink, fn ($query) => $query
                ->where('status', HomeStatus::ACTIVE->value)
                ->where('on_site_booking', true)
                ->where('nightly_rate', '>', 0)
            )
            ->when($forSwap, fn ($query) => $query
                ->where('status', HomeStatus::ACTIVE->value)
            )
            ->get(['id', 'title']);

        return response()->json(['data' => GetAuthUserHomesResource::collection($homes)]);
    }
}
