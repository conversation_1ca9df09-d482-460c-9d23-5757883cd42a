<?php

namespace App\Http\Controllers;

use App\Jobs\ProcessLocalPhotosJob;
use App\Models\Home;
use App\Models\TemporaryUpload;
use App\Traits\ExceptionLoggableTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class HomePhotoController extends Controller
{
    use ExceptionLoggableTrait;

    /**
     * Update home photos - handles both uploading new photos and reordering existing ones
     *
     * @param  string  $slug
     */
    public function update(Request $request, $slug): JsonResponse
    {
        try {
            // Simple logging of what we received
            Log::info('Updating home photos', [
                'request_data' => $request->all(),
                'has_files' => $request->hasFile('files'),
            ]);

            // Find the home by slug or ID
            $home = Home::where('slug', $slug)->orWhere('id', $slug)->firstOrFail();

            // Process any uploaded files first
            $uploadedMediaIds = [];

            if ($request->hasFile('files')) {
                // Validate the files
                $validator = Validator::make($request->all(), [
                    'files' => 'array',
                    'files.*' => 'file|mimes:jpeg,jpg,png|max:10240', // 10MB max
                ]);

                if ($validator->fails()) {
                    return response()->json([
                        'message' => 'File validation failed',
                        'errors' => $validator->errors(),
                    ], 422);
                }

                // Store files temporarily and queue them for processing
                $tempFilePaths = [];

                // Save files to temporary storage
                foreach ($request->file('files') as $file) {
                    try {
                        // Generate a unique path for the temporary file
                        $tempPath = 'temp/photos/'.uniqid().'/'.$file->getClientOriginalName();

                        // Store the file in the temporary storage
                        Storage::disk('local')->put($tempPath, file_get_contents($file->getRealPath()));

                        // Add the path to the list
                        $tempFilePaths[] = $tempPath;

                        Log::info('Stored file temporarily', [
                            'file' => $file->getClientOriginalName(),
                            'temp_path' => $tempPath,
                        ]);
                    } catch (\Exception $e) {
                        $this->log('Error storing file temporarily', [
                            'error' => $e->getMessage(),
                            'file' => $file->getClientOriginalName(),
                        ], 'error');
                    }
                }

                // Set initial processing status
                $home->update([
                    'photo_processing_status' => 'queued',
                ]);

                // Dispatch job to process photos in the background
                if (! empty($tempFilePaths)) {
                    ProcessLocalPhotosJob::dispatch($home, $tempFilePaths);

                    Log::info('Queued local photos for processing', [
                        'home_id' => $home->id,
                        'photo_count' => count($tempFilePaths),
                    ]);
                }
            }

            // Get photo IDs from the request - simplified extraction
            $photoIds = [];

            // Try photo_ids array format first
            if ($request->has('photo_ids')) {
                $photoIds = $request->input('photo_ids');
                Log::info('Using photo_ids from request', ['photo_ids' => $photoIds]);
            }
            // Then try photos JSON string/array
            elseif ($request->has('photos')) {
                $photosInput = $request->input('photos');

                // Convert JSON string to array if needed
                if (is_string($photosInput) && ! empty($photosInput)) {
                    try {
                        $photoIds = json_decode($photosInput, true) ?: [$photosInput];
                    } catch (\Exception $e) {
                        Log::warning('Failed to parse photos JSON', ['input' => $photosInput]);
                        $photoIds = [$photosInput];
                    }
                } elseif (is_array($photosInput)) {
                    $photoIds = $photosInput;
                }
            }

            // Clean up the photo IDs - convert to integers and remove nulls
            $photoIds = array_filter(is_array($photoIds) ? $photoIds : [], function ($v) {
                return ! is_null($v);
            });
            $photoIds = array_map('intval', $photoIds);

            Log::info('Photo IDs to process', ['photo_ids' => $photoIds]);

            // Get existing photos
            $existingPhotos = $home->getMedia('images');
            $existingIds = $existingPhotos->pluck('id')->toArray();

            // Skip update if no photo IDs provided
            if (empty($photoIds)) {
                return response()->json([
                    'message' => 'No photos provided for update',
                    'photos' => $existingPhotos->map(function ($media) {
                        return ['media_id' => $media->id, 'url' => $media->getUrl()];
                    }),
                ]);
            }

            // Find photos to remove (photos that exist but aren't in the new order)
            $photosToRemove = array_diff($existingIds, $photoIds);

            // Remove photos not in the new list
            foreach ($photosToRemove as $photoId) {
                $mediaToDelete = Media::find($photoId);
                if ($mediaToDelete) {
                    $mediaToDelete->delete();
                }
            }

            // Update the order of the photos
            foreach ($photoIds as $position => $photoId) {
                $media = Media::find($photoId);
                if (! $media) {
                    continue;
                }

                // Move from temporary to home if needed
                if ($media->model_type === TemporaryUpload::class) {
                    $media->model_type = Home::class;
                    $media->model_id = $home->id;
                    $media->collection_name = 'images';
                }

                $media->order_column = $position;
                $media->save();
            }

            // Get the final updated photos
            $updatedPhotos = $home->fresh()->getMedia('images')->map(function ($media) {
                return [
                    'media_id' => $media->id,
                    'url' => $media->getUrl(),
                ];
            });

            // Return the updated list of photos
            return response()->json([
                'message' => 'Photos updated successfully',
                'uploaded_count' => count($tempFilePaths ?? []),
                'photos' => $updatedPhotos,
                'processing_status' => $home->photo_processing_status ?? 'pending',
                'is_processing' => ! empty($tempFilePaths),
            ]);
        } catch (\Exception $e) {
            $this->log('Error updating home photos', [
                'error' => $e->getMessage(),
                'slug' => $slug,
            ], 'error');

            return response()->json([
                'error' => 'Failed to update photos: '.$e->getMessage(),
            ], 500);
        }
    }
}
