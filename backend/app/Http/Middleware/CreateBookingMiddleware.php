<?php

namespace App\Http\Middleware;

use App\Models\Home;
use Carbon\Carbon;
use Closure;
use Illuminate\Http\Request;
use Throwable;

final readonly class CreateBookingMiddleware
{
    public function handle(
        Request $request,
        Closure $next
    ) {
        try {
            $homeId = $request->get('request_home_id', $request->get('request_user_home', $request->get('home_id')));

            if (! $homeId) {
                return response()->json([], 400);
            }

            $home = Home::query()->findOrFail($homeId);

            $requestStartDate = $request->get('start_at', $request->get('check_in_date'));

            if (! $requestStartDate) {
                return response()->json([
                    'error' => 'START_DATE_REQUIRED',
                    'message' => 'Start date is required.',
                ], 400);
            }

            $requestEndDate = $request->get('end_at', $request->get('check_out_date'));

            if (! $requestEndDate) {
                return response()->json([
                    'error' => 'END_DATE_REQUIRED',
                    'message' => 'End date is required.',
                ], 400);
            }

            // Parse dates in UTC to avoid timezone shifts
            $startDate = Carbon::parse($requestStartDate)->startOfDay()->setTimezone('UTC');
            $endDate = Carbon::parse($requestEndDate)->startOfDay()->setTimezone('UTC');

            if ($startDate->greaterThan($endDate)) {
                return response()->json([
                    'error' => 'INVALID_DATE',
                    'message' => 'Start date must be before end date.',
                ], 400);
            }

            $totalNights = max(1, $startDate->diffInDays($endDate));
            $isSwap = $request->get('isSwap', false);

            /** @var Home $home */
            if ($home->minimum_stay_rentals > $totalNights) {
                return response()->json([
                    'error' => 'MINIMUM_STAY',
                    'message' => 'Minimum stay is '.$home->minimum_stay_rentals.' nights. Please change your dates.',
                ], 400);
            }
        } catch (Throwable $e) {
            return response()->json([
                'error' => 'INVALID_REQUEST',
                'message' => 'Invalid request.',
            ], 400);
        }

        return $next($request);
    }
}
