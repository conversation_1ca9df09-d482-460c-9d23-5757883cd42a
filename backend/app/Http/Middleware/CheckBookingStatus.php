<?php

namespace App\Http\Middleware;

use App\Models\Setting;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckBookingStatus
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): mixed
    {
        // If booking is disabled and this is not a GET request
        if (Setting::isBookingDisabled() && ! $request->isMethod('GET')) {
            return response()->json([
                'message' => 'Booking functionality is currently disabled. Read operations are still available.',
            ], Response::HTTP_FORBIDDEN);
        }

        return $next($request);
    }
}
