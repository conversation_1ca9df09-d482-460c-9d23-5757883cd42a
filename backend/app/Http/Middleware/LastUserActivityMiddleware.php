<?php

namespace App\Http\Middleware;

use App\Models\User;
use Carbon\Carbon;
use Closure;
use Illuminate\Http\Request;

final class LastUserActivityMiddleware
{
    public function handle(
        Request $request,
        Closure $next
    ) {
        $user = $request->user();

        if (! empty($user)) {
            /** @var User $user */
            $user->last_seen = Carbon::now();

            $user->save();
        }

        return $next($request);
    }
}
