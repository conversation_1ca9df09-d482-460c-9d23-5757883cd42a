<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

final class HostOnly
{
    public function handle(Request $request, Closure $next)
    {
        if (! auth()->check()) {
            return response()->json(['error' => 'Unauthenticated'], Response::HTTP_UNAUTHORIZED);
        }

        $user = auth()->user();

        if (! $user->is_host) {
            return response()->json([
                'error' => 'Access denied. Host privileges required.',
            ], Response::HTTP_FORBIDDEN);
        }

        return $next($request);
    }
}
