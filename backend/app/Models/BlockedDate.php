<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property int $home_id
 * @property string $start_at
 * @property string $end_at
 * @property ?string $type
 * @property Carbon $created_at
 * @property Carbon $updated_at
 */
class BlockedDate extends Model
{
    protected $fillable = [
        'home_id',
        'start_at',
        'end_at',
        'type',
        'created_at',
        'updated_at',
    ];
}
