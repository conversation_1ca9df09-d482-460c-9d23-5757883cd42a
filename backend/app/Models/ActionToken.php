<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property string $action
 * @property int $user_id
 * @property string $token
 * @property string $expired_at
 * @property Carbon $created_at
 * @property Carbon $updated_at
 */
final class ActionToken extends Model
{
    public const string RESTORE_PASSWORD = 'restore_password';

    protected $fillable = ['action', 'user_id', 'token', 'expired_at'];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function scopeExpired($query)
    {
        return $query->where('expired_at', '>=', now()->toDateTimeString());
    }

    public static function getExpiredDate(int $duration): Carbon
    {
        return Carbon::now()->addMinutes($duration);
    }
}
