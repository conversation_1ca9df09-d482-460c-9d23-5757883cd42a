<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

/**
 * @property int $id
 * @property User $sender
 * @property User $receiver
 * @property int $sender_id
 * @property int $receiver_id
 * @property string|null $subject
 * @property string $body
 * @property bool $viewed
 * @property string|null $messageable_type
 * @property int|null $messageable_id
 * @property Model|null $messageable
 * @property Carbon $created_at
 * @property null|Carbon $updated_at
 * @property bool $archived_by_sender
 * @property bool $archived_by_receiver
 */
final class Message extends Model
{
    protected $table = 'messages';

    protected $fillable = [
        'sender_id',
        'receiver_id',
        'subject',
        'body',
        'viewed',
        'messageable_type',
        'messageable_id',
        'archived_by_sender',
        'archived_by_receiver',
    ];

    protected $casts = [
        'viewed' => 'boolean',
        'archived_by_sender' => 'boolean',
        'archived_by_receiver' => 'boolean',
    ];

    public function sender(): BelongsTo
    {
        return $this->belongsTo(User::class, 'sender_id');
    }

    public function receiver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'receiver_id');
    }

    public function messageable(): MorphTo
    {
        return $this->morphTo();
    }

    public function isDirectMessage(): bool
    {
        return $this->messageable_id === null;
    }

    public function isBookingMessage(): bool
    {
        return $this->messageable_type === Booking::class;
    }
}
