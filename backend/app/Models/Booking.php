<?php

namespace App\Models;

use App\Enums\BookingStatus;
use App\Enums\BookingType;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphMany;

/**
 * @property int $id
 * @property Carbon $start_at
 * @property Carbon $end_at
 * @property BookingStatus $status
 * @property int $request_user_home
 * @property int $user_home
 * @property int $user_id
 * @property string $comment
 * @property array $extra_info
 * @property string $code
 * @property null|string $from_sharable_link
 * @property BookingType $booking_type
 * @property bool $guest_cleaning_fee_enabled
 * @property bool $host_cleaning_fee_enabled
 *
 * Relations:
 * @property null|Home $fromHome
 * @property Home $toHome
 * @property User $user
 * @property Home $user_home_relation
 * @property Collection<Message> $messages
 * @property Collection<Review> $reviews
 *
 * Accessors:
 * @property Carbon $created_at
 * @property null|Carbon $updated_at
 */
final class Booking extends Model
{
    protected $table = 'bookings';

    protected $fillable = [
        'user_home',
        'request_user_home',
        'user_id',
        'comment',
        'status',
        'start_at',
        'end_at',
        'extra_info',
        'code',
        'from_sharable_link',
        'booking_type',

        'payment_status',
        'message_reminder_bool',
        'swap_type',
        'is_waiting_on_payment',
        'is_non_rec_on_site',
        'guest_cleaning_fee_enabled',
        'host_cleaning_fee_enabled',
    ];

    protected $casts = [
        'extra_info' => 'array',
        'start_at' => 'date',
        'end_at' => 'date',
        'status' => BookingStatus::class,
        'booking_type' => BookingType::class,
        'guest_cleaning_fee_enabled' => 'boolean',
        'host_cleaning_fee_enabled' => 'boolean',
    ];

    public function fromHome(): BelongsTo
    {
        return $this->belongsTo(Home::class, 'user_home');
    }

    public function toHome(): BelongsTo
    {
        return $this->belongsTo(Home::class, 'request_user_home');
    }

    public function user_home_relation(): BelongsTo
    {
        return $this->belongsTo(Home::class, 'request_user_home');
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function messagesV2(): MorphMany
    {
        return $this->morphMany(Message::class, 'messageable');
    }

    public function reviews(): HasMany
    {
        return $this->hasMany(Review::class);
    }

    public function guestReview(): HasOne
    {
        return $this->hasOne(Review::class)->where('type', 'guest_review');
    }

    public function hostReview(): HasOne
    {
        return $this->hasOne(Review::class)->where('type', 'host_review');
    }
}
