<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $sharable_link_id
 * @property int $user_id
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property-read SharableLink $sharableLink
 * @property-read User $user
 */
final class SharableLinkBlockAccess extends Model
{
    protected $table = 'sharable_link_block_access';

    protected $fillable = ['sharable_link_id', 'user_id'];

    public function sharableLink(): BelongsTo
    {
        return $this->belongsTo(SharableLink::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
