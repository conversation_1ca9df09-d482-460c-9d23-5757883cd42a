<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

/**
 * @property int $id
 * @property string $job_title
 * @property string $due_date
 * @property string $details
 * @property string $payment_type
 * @property float $payment_amount
 * @property string $priority_level
 * @property int $vendor_id
 * @property int $category_id
 * @property int $home_id
 * @property int $user_id
 * @property string $created_at
 * @property string $updated_at
 * @property string $job_status
 * @property string $payment_status
 * @property int $vendor_user_id
 * @property-read User $user
 * @property-read User $vendor
 */
final class Jobs extends Model implements HasMedia
{
    use HasFactory;
    use InteractsWithMedia;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'mycrew_jobs';

    protected $fillable = [
        'job_title',
        'due_date',
        'details',
        'payment_type',
        'payment_amount',
        'priority_level',
        'vendor_id',
        'category_id',
        'home_id',
        'user_id',
        'created_at',
        'updated_at',
        'payment_status',
        'vendor_user_id',
        'home_id',
    ];

    public function registerMediaConversions(?Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->width(700)
            ->height(450);
    }

    /**
     * Get the user (host) that owns the job.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Get the vendor user that is assigned to the job.
     */
    public function vendor(): BelongsTo
    {
        return $this->belongsTo(User::class, 'vendor_user_id');
    }
}
