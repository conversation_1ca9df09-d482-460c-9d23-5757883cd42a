<?php

namespace App\Models;

use App\Enums\HomeStatus;
use App\Enums\UserSubscriptionType;
use App\Enums\UserType;
use App\Notifications\VerifyEmailNotification;
use Carbon\Carbon;
use Filament\Models\Contracts\FilamentUser;
use Filament\Models\Contracts\HasName;
use Filament\Panel;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Cashier\Billable;
use PHPOpenSourceSaver\JWTAuth\Contracts\JWTSubject;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

/**
 * @property int $id
 * @property string $first_name
 * @property string $last_name
 * @property string $email
 * @property ?string $phone_number
 * @property ?string $about
 * @property string $password
 * @property int $role_id
 * @property UserType $user_type
 * @property string $email_verified_at
 * @property ?string $avatar
 * @property ?string $shared_url
 * @property ?string $canonical_url
 * @property array $profile_completed_attrs
 * @property array $profile_not_completed_attrs
 * @property int $profile_completion
 * @property ?string $host_logo
 * @property ?string $host_landing_page
 * @property string $stripe_connect_id
 * @property bool $charges_enabled
 * @property array $extra_info
 * @property int $registration_progress
 * @property array $what_brings_you_to_twimo
 * @property string $full_name
 * @property Carbon $last_seen
 * @property string $venmo_username
 * @property bool $is_host
 * @property bool $is_subscribed
 *
 * Relations
 * @property-read Collection<Home> $homes
 * @property-read Role $role
 */
final class User extends Authenticatable implements FilamentUser, HasMedia, HasName, JWTSubject, MustVerifyEmail
{
    use Billable;
    use HasFactory;
    use InteractsWithMedia;
    use Notifiable;

    protected $fillable = [
        'first_name',
        'last_name',
        'about',
        'email',
        'password',
        'role_id',
        'user_type',
        'phone_number',
        'host_logo',
        'host_landing_page',
        'shared_url',
        'canonical_url',
        'stripe_connect_id',
        'extra_info',
        'venmo_username',
        'identity_verified',
        'stripe_identity_session_id',
        'identity_verified_at',
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected $casts = [
        'email_verified_at' => 'datetime',
        'last_seen' => 'datetime',
        'user_type' => UserType::class,
        'extra_info' => 'array',
        'identity_verified' => 'boolean',
        'identity_verified_at' => 'datetime',
    ];

    protected $appends = [
        'avatar',
        'host_logo',
        'profile_completion',
        'home_completion',
        'is_host',
        'is_subscribed',
        'is_identity_verified',
    ];

    public const array PROFILE_COMPLETION_FIELDS = [
        'first_name',
        'last_name',
        'email',
        'avatar',
        'about',
        'phone_number',
    ];

    public function homes(): HasMany
    {
        return $this->hasMany(Home::class);
    }

    public function role(): BelongsTo
    {
        return $this->belongsTo(Role::class);
    }

    public function notifications(): MorphMany
    {
        return $this->morphMany(Notification::class, 'notifiable')->latest();
    }

    public function registerMediaConversions(?Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->width(300)
            ->height(300);
    }

    public function getJWTIdentifier()
    {
        return $this->getKey();
    }

    public function getJWTCustomClaims(): array
    {
        return [];
    }

    public function isAdmin(): bool
    {
        return $this->role->name === 'Admin';
    }

    public function isUser(): bool
    {
        return $this->role->name === 'User';
    }

    public function sendEmailVerificationNotification(): void
    {
        $this->notify(new VerifyEmailNotification);
    }

    public function getAvatarAttribute()
    {
        if ($this->hasMedia('avatar')) {
            $media = $this->getMedia('avatar');

            if ($media[0]) {
                if ($media[0]->hasGeneratedConversion('thumb')) {
                    return $media[0]->getUrl('thumb');
                }

                return $media[0]->getUrl();
            }
        }

        return null;
    }

    public function getHostLogoAttribute()
    {
        if ($this->hasMedia('host_logo')) {
            $media = $this->getMedia('host_logo');

            if ($media[0]) {
                if ($media[0]->hasGeneratedConversion('thumb')) {
                    return $media[0]->getUrl('thumb');
                }

                return $media[0]->getUrl();
            }
        }

        return null;
    }

    public function getFullNameAttribute(): string
    {
        return $this->first_name.' '.$this->last_name;
    }

    public function getProfileCompletedAttrsAttribute(): array
    {
        $worthAttributes = self::PROFILE_COMPLETION_FIELDS;

        $completedAttributes = [];

        foreach ($worthAttributes as $attribute) {
            if ($this->getAttribute($attribute)) {
                $completedAttributes[] = $attribute;
            }
        }

        return $completedAttributes;
    }

    public function getProfileNotCompletedAttrsAttribute(): array
    {
        $worthAttributes = self::PROFILE_COMPLETION_FIELDS;

        $notCompletedAttributes = [];

        foreach ($worthAttributes as $attribute) {
            if (! $this->getAttribute($attribute)) {
                $notCompletedAttributes[] = $attribute;
            }
        }

        return $notCompletedAttributes;
    }

    public function getProfileCompletionAttribute(): int
    {
        $worthAttributes = self::PROFILE_COMPLETION_FIELDS;

        $completion = 0;
        $attributesCount = count($worthAttributes);
        $increment = 100 / $attributesCount;

        foreach ($worthAttributes as $attribute) {
            if ($this->getAttribute($attribute)) {
                $completion += $increment;
            }
        }

        return $completion;
    }

    public function getHomeCompletionAttribute(): int
    {
        $completion = 0;

        $homes = $this->relationLoaded('homes') ? $this->homes :
            $this->homes()->setEagerLoads([])->select(['id', 'user_id', 'status'])->get();

        if ($homes->count() > 0) {
            $completion += 50;
        }

        $totalActiveHomes = $homes->where('status', HomeStatus::ACTIVE->value)->count();

        if ($totalActiveHomes > 0) {
            $completion += 50;
        }

        return $completion;
    }

    public function getFilamentName(): string
    {
        return "{$this->first_name} {$this->last_name}";
    }

    public function canAccessPanel(Panel $panel): bool
    {
        return $this->email === config('services.adminEmail');
    }

    public function getRegistrationProgressAttribute()
    {
        return $this->extra_info['registration_progress'] ?? 0;
    }

    public function setRegistrationProgressAttribute($value): void
    {
        $this->extra_info = array_merge($this->extra_info ?? [], ['registration_progress' => $value]);
    }

    public function getWhatBringsYouToTwimoAttribute()
    {
        return $this->extra_info['what_brings_you_to_twimo'] ?? [];
    }

    public function setWhatBringsYouToTwimoAttribute($value): void
    {
        $this->extra_info = array_merge($this->extra_info ?? [], ['what_brings_you_to_twimo' => $value]);
    }

    // Add subscription related methods
    public function hasActiveSubscription(): bool
    {
        return $this->subscribed('twimo_host');
    }

    public function getIsHostAttribute(): bool
    {
        return $this->user_type === UserType::HOST;
    }

    public function getIsSubscribedAttribute(): bool
    {
        return $this->subscribed(UserSubscriptionType::TWIMO_HOST->value);
    }

    public function notificate(string $message, string $link = '', string $icon = ''): void
    {
        $this->notifications()->create([
            'message' => $message,
            'link' => $link,
            'icon' => $icon,
        ]);
    }

    public function getIsIdentityVerifiedAttribute(): bool
    {
        return (bool) $this->identity_verified;
    }
}
