<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property int $id
 * @property string $link
 * @property array $price_info
 * @property int $home_id
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property-read Home $home
 * @property-read Collection<SharableLinkBlockAccess> $blockedUsers
 * @property bool $is_allowed
 * @property Collection<Booking> $guestBookings
 */
final class SharableLink extends Model
{
    protected $fillable = ['home_id', 'link', 'price_info'];

    protected $casts = [
        'price_info' => 'array',
    ];

    public function home(): BelongsTo
    {
        return $this->belongsTo(Home::class);
    }

    public function blockedUsers(): HasMany
    {
        return $this->hasMany(SharableLinkBlockAccess::class);
    }
}
