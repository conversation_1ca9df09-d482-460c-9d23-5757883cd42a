<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Collection;

/**
 * @property int $id
 * @property int $host_id
 * @property int $guest_id
 * @property array $home_ids
 * @property User $host
 * @property User $guest
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property Carbon $deleted_at
 * @property null|Collection<SharableLink> $accessible_sharable_links
 */
final class GuestList extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'host_id',
        'guest_id',
        'home_ids',
    ];

    protected $dates = ['deleted_at'];

    protected $casts = [
        'home_ids' => 'array',
    ];

    public function host(): BelongsTo
    {
        return $this->belongsTo(User::class, 'host_id');
    }

    public function guest(): BelongsTo
    {
        return $this->belongsTo(User::class, 'guest_id');
    }

    /**
     * Check if this guest has access to a specific home
     */
    public function hasAccessToHome(int $homeId): bool
    {
        return in_array($homeId, $this->home_ids ?? []);
    }

    /**
     * Grant access to a specific home
     */
    public function grantAccessToHome(int $homeId): void
    {
        $homeIds = $this->home_ids ?? [];
        if (!in_array($homeId, $homeIds)) {
            $homeIds[] = $homeId;
            $this->home_ids = $homeIds;
            $this->save();
        }
    }

    /**
     * Revoke access to a specific home
     */
    public function revokeAccessFromHome(int $homeId): void
    {
        $homeIds = $this->home_ids ?? [];
        $this->home_ids = array_values(array_filter($homeIds, fn($id) => $id !== $homeId));
        $this->save();
    }
}
