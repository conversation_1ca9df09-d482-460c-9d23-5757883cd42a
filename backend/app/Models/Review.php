<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $booking_id
 * @property int $reviewer_id
 * @property int $reviewee_id
 * @property int $home_id
 * @property int $rating
 * @property string $feedback
 * @property string $type (guest_review or host_review)
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property-read Booking $booking
 * @property-read User $reviewer
 * @property-read User $reviewee
 * @property-read Home $home
 */
final class Review extends Model
{
    protected $fillable = [
        'booking_id',
        'reviewer_id',
        'reviewee_id',
        'home_id',
        'rating',
        'feedback',
        'type',
    ];

    protected $casts = [
        'rating' => 'integer',
    ];

    public function booking(): BelongsTo
    {
        return $this->belongsTo(Booking::class);
    }

    public function reviewer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'reviewer_id');
    }

    public function reviewee(): BelongsTo
    {
        return $this->belongsTo(User::class, 'reviewee_id');
    }

    public function home(): BelongsTo
    {
        return $this->belongsTo(Home::class);
    }
}
