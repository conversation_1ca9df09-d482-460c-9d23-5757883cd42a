<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Notifications\DatabaseNotification;

/**
 * @property int $id
 * @property string $type
 * @property string $notifiable_type
 * @property string $notifiable_id
 * @property array $data
 * @property Carbon|null $read_at
 * @property Carbon|null $archived_at
 * @property Carbon $created_at
 * @property Carbon|null $updated_at
 * @property string $notification_type // on_twimo, email, sms
 * @property string $recurrence_type // once, daily_until_complete, until_complete
 * @property string $link
 */
final class Notification extends DatabaseNotification
{
    protected $keyType = 'int';

    public $incrementing = true;

    protected $table = 'notifications';

    protected $casts = [
        'data' => 'array',
        'read_at' => 'datetime',
        'archived_at' => 'datetime',
        'id' => 'integer',
    ];

    protected static function boot()
    {
        parent::boot();
        self::creating(function ($model) {
            $model->id = null; // Let MySQL auto-increment handle this
        });
    }

    public function getNotificationTypeAttribute(): string
    {
        return $this->data['notification_type'];
    }

    public function getRecurrenceTypeAttribute(): string
    {
        return $this->data['recurrence_type'];
    }

    public function getLinkAttribute(): string
    {
        return $this->data['link'];
    }

    public function scopeUnread($query)
    {
        return $query->whereNull('read_at');
    }

    public function scopeArchived($query)
    {
        return $query->whereNotNull('archived_at');
    }

    public function scopeUnarchived($query)
    {
        return $query->whereNull('archived_at');
    }
}
