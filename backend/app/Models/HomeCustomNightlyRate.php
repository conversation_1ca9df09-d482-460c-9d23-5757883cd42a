<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $home_id
 * @property float $nightly_rate
 * @property bool $is_active
 * @property array $conditions
 * @property Carbon $created_at
 * @property Carbon $updated_at
 */
final class HomeCustomNightlyRate extends Model
{
    protected $table = 'home_custom_nightly_rates';

    protected $fillable = [
        'home_id',
        'nightly_rate',
        'is_active',
        'conditions',
    ];

    protected $casts = [
        'nightly_rate' => 'float',
        'is_active' => 'bool',
        'conditions' => 'array',
    ];

    public function home(): BelongsTo
    {
        return $this->belongsTo(Home::class);
    }
}
