<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Spatie\Sluggable\HasSlug;
use Spatie\Sluggable\SlugOptions;

/**
 * @property int $id
 * @property int $user_id
 * @property string $name
 * @property string $slug
 * @property User $user
 * @property Collection<Home> $homes
 *
 * Accessors
 * @property-read string|null $thumbnail The thumbnail URL from the first home in the collection
 */
final class SavedHomeCategory extends Model
{
    use HasFactory;
    use HasSlug;

    protected $fillable = ['user_id', 'name'];

    protected $appends = ['thumbnail'];

    public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom(fn () => $this->name.' '.$this->id)
            ->doNotGenerateSlugsOnUpdate()
            ->saveSlugsTo('slug');
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function homes(): BelongsToMany
    {
        return $this->belongsToMany(Home::class, 'home_saved_category');
    }

    public function getThumbnailAttribute(): ?string
    {
        /** @var Home|null */
        $firstHome = $this->homes->first();

        return $firstHome?->thumbnail;
    }
}
