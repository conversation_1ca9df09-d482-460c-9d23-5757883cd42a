<?php

namespace App\Models;

use App\Enums\BookingStatus;
use App\Enums\HomeStatus;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Spatie\Sluggable\HasSlug;
use Spatie\Sluggable\SlugOptions;

/**
 * @property int $id
 * @property string $slug
 * @property string $title
 * @property string $description
 * @property string $location
 * @property string $address
 * @property ?string $country_long
 * @property ?string $state_long
 * @property ?string $city_long
 * @property ?string $street
 * @property int $beds //This one currently used as bedrooms (name is misleading)
 * @property float $baths //This one currently used as bathrooms (name is misleading)
 * @property ?int $number_of_beds //This one is new and will be used as beds
 * @property ?int $guests
 * @property ?int $search_score
 * @property ?array $amenities
 * @property bool $allow_booking
 * @property bool $allow_swaps
 * @property bool $offer_as_seasonal_lease
 * @property int $minimum_stay_rentals
 * @property int $minimum_stay_swaps
 * @property ?string $sharable_password
 * @property ?array $extra_info
 * @property float $nightly_rate
 * @property int $cleaning_fee
 * @property float $tax_rate
 * @property float $pet_fee
 * @property int $user_id
 * @property string $status
 * @property Collection|Media[] $media
 * @property ?string $ical_url
 * @property ?string $airbnb_url
 * @property bool $is_from_airbnb
 * @property ?string $photo_processing_status
 * @property ?string $thumbnail
 * @property string $front_page_url
 * @property string $front_page_view_as_url
 * @property bool $invalid_sharable_link
 *
 * Relations:
 * @property User $user
 * @property Collection<HomeAvailable> $available_dates
 * @property Collection<SharableLink> $sharableLinks
 */
final class Home extends Model implements HasMedia
{
    use HasSlug;
    use InteractsWithMedia;

    protected static function boot(): void
    {
        parent::boot();

        self::retrieved(static function (self $home) {
            $home->handleUserVisibility();
            $home->handleSharableLink();
            $home->handleBookingLink();
        });
    }

    private function handleUserVisibility(): void
    {
        $currentUser = auth()->user();

        if ($currentUser && ($currentUser->isAdmin() || $currentUser->id === $this->user_id)) {
            $this->makeVisible('sharable_password');
        }
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    private function handleSharableLink(): void
    {
        $sharableLink = request()->get('sharable_link');

        if ($sharableLink === null || $sharableLink === 'undefined' || $sharableLink === 'null') {
            return;
        }

        $matchingSharableLink = $this->sharableLinks->firstWhere('link', $sharableLink);

        if ($matchingSharableLink === null) {
            $this->invalid_sharable_link = true;
        }

        if ($matchingSharableLink && ! empty($matchingSharableLink->price_info)) {
            $info = (array) $matchingSharableLink->price_info;
            $info['minimum_stay_rentals'] = $info['minimum_stay_rentals'] ?? $info['minimum_stay'] ?? 1;
            $this->fill($info);
        }
    }

    private function handleBookingLink(): void
    {
        $path = request()->path();

        if (preg_match('/^api\/bookings\/(\d+)/', $path, $matches)) {
            $bookingId = $matches[1];
        }

        if (empty($bookingId)) {
            return;
        }

        $booking = Booking::query()->find($bookingId);

        /** @var Booking|null $booking */
        if ($booking !== null && $booking->request_user_home === $this->id && ! empty($booking->extra_info['priceInfo'])) {
            $priceInfo = $booking->extra_info['priceInfo'];
            $mappedInfo = [
                'tax_rate' => $priceInfo['getTaxRate'] ?? null,
                'nightly_rate' => $priceInfo['nightly_rate'] ?? null,
                'cleaning_fee' => $priceInfo['getCleaningFee'] ?? null,
                'minimum_stay_rentals' => $priceInfo['getDiffsInDays'] ?? 1,
            ];
            $this->fill(array_filter($mappedInfo));
        }
    }

    protected $fillable = [
        'title',
        'address',
        'beds',
        'baths',
        'number_of_beds',
        'guests',
        'search_score',
        'amenities',
        'location',
        'description',
        'user_id',
        'status',
        'city_long',
        'country_long',
        'street',
        'state_long',
        'ical_url',
        'airbnb_url',
        'is_from_airbnb',
        'photo_processing_status',
        'nightly_rate',
        'allow_booking',
        'allow_swaps',
        'cleaning_fee',
        'tax_rate',
        'pet_fee',
        'offer_as_seasonal_lease',
        'minimum_stay_rentals',
        'minimum_stay_swaps',
        'sharable_password',
        'extra_info',
        'slug',
    ];

    protected $casts = [
        'baths' => 'float',
        'beds' => 'int',
        'tax_rate' => 'float',
        'pet_fee' => 'float',
        'bedrooms' => 'int',
        'guests' => 'int',
        'amenities' => 'array',
        'offer_as_seasonal_lease' => 'boolean',
        'is_from_airbnb' => 'boolean',
        'minimum_stay_rentals' => 'int',
        'minimum_stay_swaps' => 'int',
        'extra_info' => 'array',
    ];

    protected $appends = ['photos', 'owner', 'busy_dates'];

    protected $with = ['media', 'available_dates'];

    protected $hidden = ['sharable_password'];

    public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom(fn () => $this->title.' '.$this->id)
            ->doNotGenerateSlugsOnUpdate()
            ->saveSlugsTo('slug');
    }

    public function registerMediaConversions(?Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->width(700)
            ->height(450);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function available_dates(): HasMany
    {
        return $this->hasMany(HomeAvailable::class);
    }

    public function sharableLinks(): HasMany
    {
        return $this->hasMany(SharableLink::class, 'home_id');
    }

    public function reviews(): HasMany
    {
        return $this->hasMany(Review::class, 'home_id');
    }

    /**
     * Get the bookings associated with this home.
     */
    public function bookings(): HasMany
    {
        return $this->hasMany(Booking::class, 'request_user_home');
    }

    // Statistic
    public function upcomingCheckInHomes(): HasMany
    {
        return $this->hasMany(Booking::class, 'request_user_home', 'id')
            ->where('status', BookingStatus::COMPLETED)
            ->where('start_at', '>=', now()->subDays(7));
    }

    public function upcomingCheckOutHomes(): HasMany
    {
        return $this->hasMany(Booking::class, 'request_user_home', 'id')
            ->where('status', BookingStatus::COMPLETED)
            ->where('end_at', '>=', now()->subDays(7));
    }

    public function occupiedHomes(): HasMany
    {
        return $this->hasMany(Booking::class, 'request_user_home', 'id')
            ->where('status', BookingStatus::COMPLETED);
    }

    public function savedCategories(): BelongsToMany
    {
        return $this->belongsToMany(SavedHomeCategory::class, 'home_saved_category');
    }

    public function getFrontPageUrlAttribute(): string
    {
        return config('services.frontendUrl').'/home/<USER>/'.$this->id;
    }

    public function getFrontPageViewAsUrlAttribute(): string
    {
        return config('services.frontendUrl').'/'.$this->id.'/view-as';
    }

    public function getOwnerAttribute(): ?User
    {
        if ($this->user_id) {
            return $this->attributes['owner'] = $this->user;
        }

        return null;
    }

    public function getPhotosAttribute(): array
    {
        $photos = [];
        $media = $this->media->sortBy('order_column')->values();

        foreach ($media as $m) {
            $photos[] = [
                'media_id' => $m->id,
                'filename' => $m->file_name,
                'url' => $m->getUrl(),
                'name' => $m->file_name,
                'src' => $m->getUrl(),
                'thumb' => $m->hasGeneratedConversion('thumb')
                    ? $m->getUrl('thumb')
                    : $m->getUrl(),
            ];
        }

        return $this->attributes['photos'] = $photos;
    }

    public function getThumbnailAttribute(): ?string
    {
        return $this->photos[0]['url'] ?? null;
    }

    public function scopeFilter($query, $field, $value)
    {
        return $query->when($value, function ($query) use ($field, $value) {
            $query->where($field, $value);
        });
    }

    public function scopeActive($query)
    {
        return $query->where('status', HomeStatus::ACTIVE->value);
    }

    public function getBusyDatesAttribute(): Collection
    {
        return Booking::query()
            ->select(['start_at', 'end_at'])
            ->where(function ($query) {
                $query->where('request_user_home', $this->id)
                    ->orWhere('user_home', $this->id);
            })
            ->whereIn('status', [
                BookingStatus::PENDING->value,
                BookingStatus::ACCEPTED->value,
                BookingStatus::COMPLETED->value,
            ])
            ->where('end_at', '>=', now())
            ->get();
    }
}
