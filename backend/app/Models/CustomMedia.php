<?php

namespace App\Models;

use Spatie\MediaLibrary\MediaCollections\Models\Media;

final class CustomMedia extends Media
{
    public function getUrl($conversionName = ''): string
    {
        // Get the Cloudflare custom URL prefix from config
        $customPrefix = config('media-library.custom_url_prefix', 'https://images.twimo.com');

        // If no custom prefix, return the original URL
        if (empty($customPrefix)) {
            return parent::getUrl($conversionName);
        }

        // Get the original media URL
        $originalUrl = parent::getUrl($conversionName);
        $parsedUrl = parse_url($originalUrl);
        $path = $parsedUrl['path'] ?? '';

        // Ensure we return a clean URL for Cloudflare Polish to optimize
        return rtrim($customPrefix, '/').'/'.ltrim($path, '/');
    }
}
