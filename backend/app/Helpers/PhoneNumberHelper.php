<?php

namespace App\Helpers;

class PhoneNumberHelper
{
    /**
     * Format phone number to ensure it has a "+1" prefix (US country code)
     * and contains only digits after the country code
     */
    public static function formatPhoneNumber(?string $phoneNumber): ?string
    {
        if (empty($phoneNumber)) {
            return null;
        }

        // Extract all digits from the phone number
        $digits = preg_replace('/\D/', '', $phoneNumber);

        // If the phone number already starts with a "+", extract the digits differently
        if (str_starts_with($phoneNumber, '+')) {
            // Remove the "+" and any non-digit characters
            $digits = preg_replace('/\D/', '', substr($phoneNumber, 1));
        }

        // Ensure we have at least 10 digits for a valid US phone number
        if (strlen($digits) < 10) {
            // Not enough digits for a valid phone number
            return '+1'.str_pad($digits, 10, '0'); // Pad with zeros if needed
        }

        // If the number starts with "1" and has more than 10 digits, assume the first digit is the country code
        if (strlen($digits) > 10 && substr($digits, 0, 1) === '1') {
            // Take the last 10 digits as the phone number
            $phoneDigits = substr($digits, -10);

            return '+1'.$phoneDigits;
        }

        // If we have exactly 10 digits or more than 10 digits not starting with 1
        // Take the last 10 digits as the phone number
        $phoneDigits = substr($digits, -10);

        return '+1'.$phoneDigits;
    }
}
