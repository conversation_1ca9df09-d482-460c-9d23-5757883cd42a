<?php

namespace App\Jobs;

use App\Models\Home;
use App\Traits\ExceptionLoggableTrait;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Http\UploadedFile;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Throwable;

class ProcessLocalPhotosJob implements ShouldQueue
{
    use Dispatchable, ExceptionLoggableTrait, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    /**
     * The number of seconds the job can run before timing out.
     *
     * @var int
     */
    public $timeout = 300;

    /**
     * The uploaded files stored temporarily.
     *
     * @var array
     */
    protected $files;

    /**
     * Create a new job instance.
     */
    public function __construct(
        protected Home $home,
        protected array $tempFilePaths
    ) {}

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            // Update status to processing
            $this->home->update(['photo_processing_status' => 'processing']);

            $uploadDisk = config('services.media.uploadDisk');
            $totalPhotos = count($this->tempFilePaths);
            $processedCount = 0;
            $successCount = 0;
            $uploadedMediaIds = [];

            Log::info('Starting local photo processing', [
                'home_id' => $this->home->id,
                'total_photos' => $totalPhotos,
            ]);

            foreach ($this->tempFilePaths as $filePath) {
                try {
                    // Check if the file exists in the temporary storage
                    if (Storage::disk('local')->exists($filePath)) {
                        // Get the file from temporary storage
                        $fileContents = Storage::disk('local')->get($filePath);
                        $fileName = basename($filePath);

                        // Create a temporary file
                        $tempFile = tempnam(sys_get_temp_dir(), 'photo_');
                        file_put_contents($tempFile, $fileContents);

                        // Create an UploadedFile instance
                        $uploadedFile = new UploadedFile(
                            $tempFile,
                            $fileName,
                            Storage::disk('local')->mimeType($filePath),
                            null,
                            true
                        );

                        // Add the file to the home's media collection
                        $media = $this->home->addMedia($uploadedFile)
                            ->toMediaCollection('images', $uploadDisk);

                        $uploadedMediaIds[] = $media->id;
                        $processedCount++;
                        $successCount++;

                        // Clean up the temporary file
                        @unlink($tempFile);

                        // Delete the file from temporary storage
                        Storage::disk('local')->delete($filePath);
                    } else {
                        Log::warning('Temporary file not found', [
                            'file_path' => $filePath,
                            'home_id' => $this->home->id,
                        ]);
                    }

                    // Update status periodically
                    if ($processedCount % 5 === 0 || $processedCount === $totalPhotos) {
                        $this->home->update([
                            'photo_processing_status' => "processing:{$processedCount}/{$totalPhotos}",
                        ]);
                    }
                } catch (Throwable $e) {
                    $this->log('Error processing local photo', [
                        'error' => $e->getMessage(),
                        'file_path' => $filePath,
                        'home_id' => $this->home->id,
                    ], 'error');

                    $processedCount++;
                }
            }

            // Update final status
            $this->home->update([
                'photo_processing_status' => "completed:{$successCount}/{$totalPhotos}",
            ]);

            Log::info('Completed local photo processing', [
                'home_id' => $this->home->id,
                'processed' => $processedCount,
                'success' => $successCount,
                'total' => $totalPhotos,
                'media_ids' => $uploadedMediaIds,
            ]);
        } catch (Throwable $e) {
            $this->log('Error in local photo processing job', [
                'error' => $e->getMessage(),
                'home_id' => $this->home->id,
            ], 'error');

            // Mark as failed
            $this->home->update(['photo_processing_status' => 'failed']);

            throw $e;
        }
    }
}
