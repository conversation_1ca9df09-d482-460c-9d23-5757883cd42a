<?php

namespace App\Jobs;

use App\Models\Home;
use App\Traits\ExceptionLoggableTrait;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Throwable;

class ProcessHomePhotosJob implements ShouldQueue
{
    use Dispatchable, ExceptionLoggableTrait, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    /**
     * The number of seconds the job can run before timing out.
     *
     * @var int
     */
    public $timeout = 300;

    /**
     * Create a new job instance.
     */
    public function __construct(
        protected Home $home,
        protected array $photos
    ) {}

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            // Update status to processing
            $this->home->update(['photo_processing_status' => 'processing']);

            $uploadDisk = config('services.media.uploadDisk');
            $totalPhotos = count($this->photos);
            $processedCount = 0;
            $successCount = 0;

            Log::info('Starting home photo processing', [
                'home_id' => $this->home->id,
                'total_photos' => $totalPhotos,
            ]);

            foreach ($this->photos as $mediaId) {
                try {
                    if (is_numeric($mediaId)) {
                        $media = Media::query()->find($mediaId);
                        if ($media) {
                            $media->move($this->home, 'images', $uploadDisk);
                            $processedCount++;
                            $successCount++;
                        }
                    } elseif (is_string($mediaId)) {
                        $media = $this->home->addMediaFromUrl($mediaId)->toMediaCollection('images', $uploadDisk);
                        $processedCount++;
                        $successCount++;
                    }

                    // Update status periodically
                    if ($processedCount % 5 === 0 || $processedCount === $totalPhotos) {
                        $this->home->update([
                            'photo_processing_status' => "processing:{$processedCount}/{$totalPhotos}",
                        ]);
                    }

                    Log::info('Processed home photo', [
                        'home_id' => $this->home->id,
                        'media_id' => $mediaId,
                        'progress' => "{$processedCount}/{$totalPhotos}",
                    ]);
                } catch (Throwable $e) {
                    $this->log('Error processing home photo', [
                        'error' => $e->getMessage(),
                        'media_id' => $mediaId,
                        'home_id' => $this->home->id,
                    ], 'error');

                    $processedCount++;
                }
            }

            // Update final status
            $this->home->update([
                'photo_processing_status' => "completed:{$successCount}/{$totalPhotos}",
            ]);

            Log::info('Completed home photo processing', [
                'home_id' => $this->home->id,
                'processed' => $processedCount,
                'success' => $successCount,
                'total' => $totalPhotos,
            ]);
        } catch (Throwable $e) {
            $this->log('Error in home photo processing job', [
                'error' => $e->getMessage(),
                'home_id' => $this->home->id,
            ], 'error');

            // Mark as failed
            $this->home->update(['photo_processing_status' => 'failed']);

            throw $e;
        }
    }
}
