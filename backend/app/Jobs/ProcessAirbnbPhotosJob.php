<?php

namespace App\Jobs;

use App\Models\Home;
use App\Traits\ExceptionLoggableTrait;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ProcessAirbnbPhotosJob implements ShouldQueue
{
    use Dispatchable, ExceptionLoggableTrait, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    /**
     * The number of seconds the job can run before timing out.
     *
     * @var int
     */
    public $timeout = 300;

    /**
     * Create a new job instance.
     */
    public function __construct(
        protected Home $home,
        protected array $photos
    ) {}

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            // Update status to processing
            $this->home->update(['photo_processing_status' => 'processing']);

            $uploadDisk = config('services.media.uploadDisk');
            $totalPhotos = count($this->photos);
            $processedCount = 0;
            $successCount = 0;

            Log::info('Starting Airbnb photo processing', [
                'home_id' => $this->home->id,
                'total_photos' => $totalPhotos,
            ]);

            foreach ($this->photos as $photo) {
                try {
                    $photoUrl = $photo['url'] ?? null;
                    $photoName = $photo['name'] ?? $this->home->title ?? 'Airbnb Home';

                    if (! $photoUrl) {
                        continue;
                    }

                    $media = $this->home->addMediaFromUrl($photoUrl)
                        ->usingName($photoName)
                        ->toMediaCollection('images', $uploadDisk);

                    $processedCount++;
                    $successCount++;

                    // Update status periodically
                    if ($processedCount % 5 === 0 || $processedCount === $totalPhotos) {
                        $this->home->update([
                            'photo_processing_status' => "processing:{$processedCount}/{$totalPhotos}",
                        ]);
                    }

                    Log::info('Added Airbnb photo', [
                        'home_id' => $this->home->id,
                        'photo_url' => $photoUrl,
                        'media_id' => $media->id,
                        'progress' => "{$processedCount}/{$totalPhotos}",
                    ]);
                } catch (\Exception $e) {
                    $this->log('Error processing Airbnb photo', [
                        'error' => $e->getMessage(),
                        'photo_url' => $photoUrl ?? 'unknown',
                        'home_id' => $this->home->id,
                    ], 'error');

                    $processedCount++;
                }
            }

            // Update final status
            $this->home->update([
                'photo_processing_status' => "completed:{$successCount}/{$totalPhotos}",
            ]);

            Log::info('Completed Airbnb photo processing', [
                'home_id' => $this->home->id,
                'processed' => $processedCount,
                'success' => $successCount,
                'total' => $totalPhotos,
            ]);
        } catch (\Exception $e) {
            $this->log('Error in Airbnb photo processing job', [
                'error' => $e->getMessage(),
                'home_id' => $this->home->id,
            ], 'error');

            // Mark as failed
            $this->home->update(['photo_processing_status' => 'failed']);

            throw $e;
        }
    }
}
