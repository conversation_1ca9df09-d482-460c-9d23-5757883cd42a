<?php

namespace App\Traits;

use App\Services\GeoCodeService;
use Illuminate\Contracts\Container\BindingResolutionException;

trait CanIntegrateWithGeoCodeTrait
{
    protected GeoCodeService $geoCodeManager;

    /**
     * @throws BindingResolutionException
     */
    public function setGeoCodeManager(?GeoCodeService $geoCodeManager = null): void
    {
        if ($geoCodeManager === null) {
            $geoCodeManager = app()->make(GeoCodeService::class);
        }

        $this->geoCodeManager = $geoCodeManager;
    }
}
