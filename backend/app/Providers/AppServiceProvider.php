<?php

namespace App\Providers;

use App\Models\User;
use App\Services\AirbnbService;
use App\Services\BrevoService;
use App\Services\GeoCodeService;
use App\Services\StripeService;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->singleton(StripeService::class, function () {
            return new StripeService;
        });

        $this->app->singleton(GeoCodeService::class, function () {
            return new GeoCodeService;
        });

        $this->app->singleton(AirbnbService::class, function () {
            return new AirbnbService(config('services.airbnb.apiKey'));
        });

        $this->app->singleton(BrevoService::class, function () {
            return new BrevoService;
        });
    }

    public function boot(): void
    {
        JsonResource::withoutWrapping();

        Gate::define(
            'viewPulse',
            static function (User $user) {
                return $user->email === config('services.adminEmail');
            }
        );

    }
}
