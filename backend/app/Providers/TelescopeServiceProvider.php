<?php

namespace App\Providers;

use Illuminate\Support\Facades\Gate;
use <PERSON><PERSON>\Telescope\IncomingEntry;
use <PERSON><PERSON>\Telescope\Telescope;
use <PERSON><PERSON>\Telescope\TelescopeApplicationServiceProvider;

class TelescopeServiceProvider extends TelescopeApplicationServiceProvider
{
    public function register(): void
    {
        Telescope::filter(static function (IncomingEntry $entry) {
            return true;
        });
    }

    protected function gate(): void
    {
        Gate::define('viewTelescope', static function ($user) {
            return $user->email === config('services.adminEmail');
        });
    }
}
