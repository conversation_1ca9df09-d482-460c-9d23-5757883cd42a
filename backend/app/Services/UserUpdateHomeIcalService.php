<?php

namespace App\Services;

use App\Models\Home;
use App\Traits\ExceptionLoggableTrait;
use Illuminate\Support\Facades\DB;
use RuntimeException;
use Throwable;

final class UserUpdateHomeIcalService
{
    use ExceptionLoggableTrait;

    public function __invoke(
        Home $home,
        string $icalUrl,
        PullIcalInfoToHomeService $pullIcalInfoToHomeService
    ): array {
        DB::beginTransaction();
        try {
            $home->ical_url = $icalUrl;

            $home->save();

            $home = $pullIcalInfoToHomeService($home, $icalUrl);

            DB::commit();

            return $home;
        } catch (Throwable $e) {
            DB::rollBack();

            report($e);

            $this->log('Failed to update ical info for home', [
                'home_id' => $home->id,
                'ical_url' => $icalUrl,
            ]);

            throw new RuntimeException('Failed to update ical info for home');
        }
    }
}
