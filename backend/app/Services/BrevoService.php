<?php

namespace App\Services;

use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class BrevoService
{
    private string $apiKey;

    private string $apiUrl = 'https://api.brevo.com/v3/contacts';

    private int $listId;

    // List IDs for different user types
    private const int FREE_USERS_LIST_ID = 12;

    private const int PAYING_USERS_LIST_ID = 13;

    public function __construct()
    {
        // Use the existing BREVO_KEY from .env (with services.brevo.api_key as fallback)
        $this->apiKey = config('services.brevo.api_key');
        $this->listId = (int) config('services.brevo.list_id', 12);
    }

    /**
     * Add contact to Brevo subscriber list
     *
     * @param  string  $email  User's email address
     * @param  array  $attributes  Additional attributes to store (optional)
     * @return bool Success status
     */
    public function addContact(string $email, array $attributes = []): bool
    {
        if (empty($this->apiKey)) {
            Log::warning('Brevo API key not configured');

            return false;
        }

        try {
            $payload = [
                'email' => $email,
                'listIds' => [$this->listId],
            ];

            // Add any additional attributes if provided
            if (! empty($attributes)) {
                $payload['attributes'] = $attributes;
            }

            $response = Http::withHeaders([
                'accept' => 'application/json',
                'api-key' => $this->apiKey,
                'content-type' => 'application/json',
            ])->post($this->apiUrl, $payload);

            logger()->info(__CLASS__.' Brevo response: '.$response->body());

            if ($response->successful()) {
                return true;
            }

            logger()->error(__CLASS__.' Failed to add contact to Brevo: '.$response->body());

            return false;
        } catch (Exception $e) {
            report($e);

            return false;
        }
    }

    /**
     * Update a contact's status to "paying" and move them from the Free Users list to the Paying Users list
     *
     * @param  string  $email  User's email address
     * @return bool Success status
     */
    public function updateContactToPaidStatus(string $email): bool
    {
        if (empty($this->apiKey)) {
            Log::warning('Brevo API key not configured');

            return false;
        }

        try {
            // Prepare the payload to update the contact
            $payload = [
                'listIds' => [self::PAYING_USERS_LIST_ID],       // Add to "Paying Users" list
                'unlinkListIds' => [self::FREE_USERS_LIST_ID],   // Remove from "Free Users" list
                'attributes' => [
                    'user_status' => 'paying',
                ],
            ];

            // Make the PUT request to update the contact
            $response = Http::withHeaders([
                'accept' => 'application/json',
                'api-key' => $this->apiKey,
                'content-type' => 'application/json',
            ])->put("{$this->apiUrl}/{$email}", $payload);

            logger()->info(__CLASS__.' Brevo update contact response: '.$response->body());

            if ($response->successful()) {
                return true;
            }

            logger()->error(__CLASS__.' Failed to update contact in Brevo: '.$response->body());

            return false;
        } catch (Exception $e) {
            report($e);
            Log::error('Error updating contact to paid status in Brevo: '.$e->getMessage());

            return false;
        }
    }
}
