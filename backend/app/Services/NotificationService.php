<?php

namespace App\Services;

use App\Enums\BookingStatus;
use App\Enums\BookingType;
use App\Enums\HomeStatus;
use App\Models\Booking;
use App\Models\GuestList;
use App\Models\Home;
use App\Models\IndefiniteApproval;
use App\Models\Jobs;
use App\Models\Message;
use App\Models\User;
use App\Notifications\AddFirstHomeReminderNotification;
use App\Notifications\AdminHomeStatusActiveNotification;
use App\Notifications\AdminNewActiveHomeNotification;
use App\Notifications\AdminNewUserNotification;
use App\Notifications\BaseNotification;
use App\Notifications\Booking\AutoApprovedBookingNotification;
use App\Notifications\Booking\BookingAcceptedNotification;
use App\Notifications\Booking\BookingCanceledNotification;
use App\Notifications\Booking\BookingCompletedNotification;
use App\Notifications\Booking\BookingDraftNotification;
use App\Notifications\Booking\BookingRejectedNotification;
use App\Notifications\Booking\BookingRequestedNotification;
use App\Notifications\Booking\StayReminderDayBeforeNotification;
use App\Notifications\Booking\StayReminderNotification;
use App\Notifications\Booking\SwapPartnerPaymentCompletedNotification;
use App\Notifications\Booking\VenmoPaymentPendingNotification;
use App\Notifications\GuestListAddedNotification;
use App\Notifications\HostSubscriptionReminderNotification;
use App\Notifications\MessageReceivedNotification;
use App\Notifications\MyCrew\HostJobApprovedNotification;
use App\Notifications\MyCrew\HostJobCompletedNotification;
use App\Notifications\MyCrew\HostJobDeclinedNotification;
use App\Notifications\MyCrew\VendorPendingJobNotification;
use App\Notifications\ProfileCompletionReminderNotification;

final readonly class NotificationService
{
    /**
     * Process all notifications for a user
     */
    public function processUserNotifications(User $user): void
    {
        $notifications = $this->getNotificationsToProcess($user);

        foreach ($notifications as $notification) {
            /** @var BaseNotification $notification */
            $notification->process($user);
        }
    }

    /**
     * Process admin notifications (runs on a schedule)
     */
    public function processAdminNotifications(): void
    {
        try {
            $notifications = $this->getAdminNotificationsToProcess();

            if (empty($notifications)) {
                \Illuminate\Support\Facades\Log::info('No admin notifications to process');

                return;
            }

            $adminEmail = config('services.adminEmail');

            $adminUser = User::where('email', $adminEmail)->first();

            $processedCount = 0;

            foreach ($notifications as $notification) {
                try {
                    $notification->process($adminUser);
                    $processedCount++;
                } catch (\Throwable $e) {
                    \Illuminate\Support\Facades\Log::error('Failed to process individual admin notification', [
                        'notification_type' => get_class($notification),
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString(),
                    ]);
                }
            }

            \Illuminate\Support\Facades\Log::info('Admin notifications processed', [
                'total_count' => count($notifications),
                'processed_count' => $processedCount,
                'admin_email' => $adminEmail,
            ]);
        } catch (\Throwable $e) {
            \Illuminate\Support\Facades\Log::error('Failed to process admin notifications', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }

    /**
     * Get all admin notifications that need to be processed
     */
    private function getAdminNotificationsToProcess(): array
    {
        $notifications = [];

        // 1. New user notifications
        $newUsers = User::query()
            ->where('email', '!=', config('services.adminEmail'))
            ->where('created_at', '>=', now()->subMinutes(10))
            ->get();

        foreach ($newUsers as $user) {
            $notifications[] = new AdminNewUserNotification($user);
        }

        // 2. New homes notifications - homes created in the last 10 minutes
        $newHomes = Home::query()
            ->where('created_at', '>=', now()->subMinutes(10))
            ->with('user')
            ->get();

        foreach ($newHomes as $home) {
            $notifications[] = new AdminNewActiveHomeNotification($home);
        }

        // 3. Recently activated homes notifications - homes that changed status to ACTIVE in the last 10 minutes
        $recentlyActivatedHomes = Home::query()
            ->where('status', HomeStatus::ACTIVE->value)
            ->where('updated_at', '>=', now()->subMinutes(10))
            ->with('user')
            ->get();

        foreach ($recentlyActivatedHomes as $home) {
            $notifications[] = new AdminHomeStatusActiveNotification($home);
        }

        return $notifications;
    }

    /**
     * Get all notifications that need to be processed for a user
     * This method handles all types of notifications:
     * - System notifications (profile completion, subscription, etc.)
     * - Guest list notifications
     * - Booking-related notifications
     * - MyCrew notifications
     */
    private function getNotificationsToProcess(User $user): array
    {
        $notifications = [];

        // 1. System notifications
        $notifications = [
            new ProfileCompletionReminderNotification($user),
            new AddFirstHomeReminderNotification,
            new HostSubscriptionReminderNotification,
        ];

        // 2. Guest list notifications
        $recentGuests = GuestList::query()
            ->where('host_id', $user->id)
            ->with(['guest'])
            ->get();

        foreach ($recentGuests as $guestList) {
            $notifications[] = new GuestListAddedNotification($guestList->guest);
        }

        // 3. Message notifications
        $recentMessages = Message::query()
            ->where('receiver_id', $user->id)
            ->where('viewed', false)
            ->where('archived_by_receiver', false)
            ->where('created_at', '>=', now()->subDays(7))
            ->with(['sender', 'messageable'])
            ->get();

        foreach ($recentMessages as $message) {
            $notifications[] = new MessageReceivedNotification($message, $user);
        }

        // 4. Booking-related notifications
        $relevantBookings = Booking::query()
            ->where(function ($query) use ($user) {
                $query->where('user_id', $user->id)
                    ->orWhereHas('toHome', function ($q) use ($user) {
                        $q->where('user_id', $user->id);
                    });
            })
            ->where('created_at', '>=', '2025-02-05')
            ->with(['user', 'toHome', 'fromHome']) // Eager load relationships
            ->get();

        foreach ($relevantBookings as $booking) {
            // Skip notifications for bookings with archived homes
            if (
                ($booking->toHome && $booking->toHome->status === HomeStatus::ARCHIVED->value) ||
                ($booking->fromHome && $booking->booking_type === BookingType::SWAP && $booking->fromHome->status === HomeStatus::ARCHIVED->value)
            ) {
                continue; // Skip all notifications for bookings with archived homes
            }

            // Check for Venmo payment notifications first
            if (
                $booking->status === BookingStatus::ACCEPTED
                && isset($booking->extra_info['venmo_payment'])
                && ! isset($booking->extra_info['venmo_payment']['host_confirmed_at'])
                && $user->id === $booking->toHome?->user_id
            ) {
                $notifications[] = new VenmoPaymentPendingNotification($booking, $user);

                continue; // Skip other notifications for this booking
            }

            // Check for swap payment notifications
            if (
                $booking->status === BookingStatus::ACCEPTED
                && $booking->booking_type === BookingType::SWAP
            ) {
                // Check if this is a swap booking with payment requirements
                $isHost = $user->id === $booking->toHome?->user_id;
                $hostPaymentRequired = $booking->host_cleaning_fee_enabled;
                $guestPaymentRequired = $booking->guest_cleaning_fee_enabled;

                // Check if partner has completed payment
                $hostPaymentCompleted = isset($booking->extra_info['priceInfo']['hostPaymentIntentCompleted']) &&
                                        $booking->extra_info['priceInfo']['hostPaymentIntentCompleted'];
                $guestPaymentCompleted = isset($booking->extra_info['priceInfo']['guestPaymentIntentCompleted']) &&
                                         $booking->extra_info['priceInfo']['guestPaymentIntentCompleted'];

                // Determine if notification is needed
                $notificationNeeded = false;

                if ($isHost && $hostPaymentRequired && ! $hostPaymentCompleted && $guestPaymentCompleted) {
                    // Host needs to pay, guest has already paid
                    $notificationNeeded = true;
                } elseif (! $isHost && $guestPaymentRequired && ! $guestPaymentCompleted && $hostPaymentCompleted) {
                    // Guest needs to pay, host has already paid
                    $notificationNeeded = true;
                }

                if ($notificationNeeded) {
                    $notifications[] = new SwapPartnerPaymentCompletedNotification($booking, $user);

                    continue; // Skip other notifications for this booking
                }
            }

            // Determine if the current user should receive notification for this status
            $shouldNotify = match ($booking->status) {
                // Guest only - when they save a draft
                BookingStatus::CREATED => $user->id === $booking->user_id,

                // Host only - when they receive a new request
                BookingStatus::REQUESTED => $user->id === $booking->toHome?->user_id,

                // Guest only - when host accepts and payment is needed
                // Also host when a pre-approved traveler makes a booking that's auto-accepted
                BookingStatus::ACCEPTED => $user->id === $booking->user_id ||
                    // Check if this is an auto-approved booking (host should be notified)
                    ($user->id === $booking->toHome?->user_id &&
                     IndefiniteApproval::query()->where([
                         'guest_id' => $booking->user_id,
                         'home_id' => $booking->request_user_home,
                     ])->exists()),

                // Both guest and host - for final statuses
                BookingStatus::REJECTED,
                BookingStatus::COMPLETED,
                BookingStatus::CANCELED => $user->id === $booking->user_id || $user->id === $booking->toHome?->user_id,

                default => false
            };

            if (! $shouldNotify) {
                continue;
            }

            // Create appropriate notification based on booking status
            $notification = match ($booking->status) {
                BookingStatus::CREATED => new BookingDraftNotification($booking, $user),
                BookingStatus::REQUESTED => new BookingRequestedNotification($booking, $user),
                BookingStatus::ACCEPTED =>
                    // If this is the host and there's an indefinite approval, use the auto-approved notification
                    ($user->id === $booking->toHome?->user_id &&
                     IndefiniteApproval::query()->where([
                         'guest_id' => $booking->user_id,
                         'home_id' => $booking->request_user_home,
                     ])->exists())
                        ? new AutoApprovedBookingNotification($booking, $user)
                        : new BookingAcceptedNotification($booking, $user),
                BookingStatus::REJECTED => new BookingRejectedNotification($booking, $user),
                BookingStatus::COMPLETED => new BookingCompletedNotification($booking, $user),
                BookingStatus::CANCELED => new BookingCanceledNotification($booking, $user),
                default => null
            };

            if ($notification) {
                $notifications[] = $notification;
            }

            // Add stay reminders for completed bookings (guest only)
            if ($booking->status === BookingStatus::COMPLETED && $booking->user_id === $user->id) {
                // 7-day reminder
                $notifications[] = new StayReminderNotification($booking, $user);

                // 24-hour reminder
                $notifications[] = new StayReminderDayBeforeNotification($booking, $user);
            }
        }

        // 5. MyCrew notifications
        $myCrewJobs = Jobs::query()
            ->where(function ($query) use ($user) {
                $query->where('user_id', $user->id)
                    ->orWhere('vendor_user_id', $user->id);
            })
            ->where('created_at', '>=', now()->subDays(7))
            ->with(['user', 'vendor'])
            ->get();

        foreach ($myCrewJobs as $job) {
            /** @var Jobs $job */

            // For Vendors - Pending Job Notifications
            if ($user->id === $job->vendor_user_id && $job->job_status === 'pending') {
                $notifications[] = new VendorPendingJobNotification(
                    $job->user->first_name.' '.$job->user->last_name,
                    $job->id
                );
            }

            // For Hosts - Job Status Notifications
            if ($user->id === $job->user_id) {
                $notification = match ($job->job_status) {
                    'accepted' => new HostJobApprovedNotification(
                        $job->vendor->first_name.' '.$job->vendor->last_name,
                        $job->id
                    ),
                    'rejected' => new HostJobDeclinedNotification(
                        $job->vendor->first_name.' '.$job->vendor->last_name,
                        $job->id
                    ),
                    'completed' => new HostJobCompletedNotification(
                        $job->vendor->first_name.' '.$job->vendor->last_name,
                        $job->id
                    ),
                    default => null
                };

                if ($notification) {
                    $notifications[] = $notification;
                }
            }
        }

        return $notifications;
    }
}
