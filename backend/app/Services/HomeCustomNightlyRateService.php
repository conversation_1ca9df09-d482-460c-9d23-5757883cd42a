<?php

namespace App\Services;

use App\Models\Home;
use App\Models\HomeCustomNightlyRate;
use Carbon\Carbon;
use Illuminate\Support\Collection;

final class HomeCustomNightlyRateService
{
    public function list(
        int $authUserId,
        ?int $homeId = null
    ): Collection {
        return HomeCustomNightlyRate::with('home')
            ->when($homeId, function ($query, $homeId) {
                $query->where('home_id', $homeId);
            })
            ->whereHas('home', function ($query) use ($authUserId) {
                $query->where('user_id', $authUserId);
            })
            ->orderBy('id', 'desc')
            ->get();
    }

    public function create(
        int $homeId,
        float $nightlyRate,
        ?array $conditions = null
    ): HomeCustomNightlyRate {
        $homeCustomNightlyRate = new HomeCustomNightlyRate;

        $homeCustomNightlyRate->home_id = $homeId;
        $homeCustomNightlyRate->nightly_rate = $nightlyRate;
        $homeCustomNightlyRate->is_active = true;
        $homeCustomNightlyRate->conditions = $conditions;

        $homeCustomNightlyRate->save();

        return $homeCustomNightlyRate;
    }

    public function update(
        int $id,
        float $nightlyRate,
        bool $isActive,
    ): HomeCustomNightlyRate {
        $homeCustomNightlyRate = HomeCustomNightlyRate::query()->findOrFail($id);

        /** @var HomeCustomNightlyRate $homeCustomNightlyRate */
        $homeCustomNightlyRate->nightly_rate = $nightlyRate;
        $homeCustomNightlyRate->is_active = $isActive;

        $homeCustomNightlyRate->save();

        return $homeCustomNightlyRate;
    }

    public function getNightlyRateForDate(Home $home, Carbon $date): float
    {
        $homeCustomNightlyRate = HomeCustomNightlyRate::query()
            ->where('home_id', $home->id)
            ->where('is_active', true) // Only consider active custom rates
            ->where(function ($query) use ($date) {
                $query
                    ->whereNull('conditions->days_of_week')
                    ->orWhereJsonContains('conditions->days_of_week', $date->dayOfWeekIso);
            })
            ->where(function ($query) use ($date) {
                $query
                    ->whereNull('conditions->from_date')
                    ->orWhereDate('conditions->from_date', '<=', $date);
            })
            ->where(function ($query) use ($date) {
                $query
                    ->whereNull('conditions->to_date')
                    ->orWhereDate('conditions->to_date', '>=', $date);
            })
            ->orderBy('id', 'desc')
            ->first();

        /** @var HomeCustomNightlyRate|null $homeCustomNightlyRate */
        return $homeCustomNightlyRate->nightly_rate ?? $home->nightly_rate ?? 0;
    }

    public function getNightlyRateForDateRange(Home $home, Carbon $fromDate, Carbon $toDate): array
    {
        $dates = [];
        $hasAdvancedPricing = false;
        $baseRate = $home->nightly_rate ?? 0;
        $fromDateCopy = clone $fromDate;
        $realToDate = $fromDateCopy->lessThan($toDate) ? $toDate->subDay() : $toDate;

        while ($fromDateCopy->lte($realToDate)) {
            $nightlyRate = $this->getNightlyRateForDate($home, $fromDateCopy);
            $dates[$fromDateCopy->toDateString()] = $nightlyRate;

            // Check if this date has a custom rate different from the base rate
            if ($nightlyRate != $baseRate) {
                $hasAdvancedPricing = true;
            }

            $fromDateCopy->addDay();
        }

        $totalNightlyRate = array_sum($dates);
        $averageNightlyRate = count($dates) > 0 ? $totalNightlyRate / count($dates) : 0;

        return [
            'dates' => $dates,
            'totalNightlyRate' => $totalNightlyRate,
            'averageNightlyRate' => $averageNightlyRate,
            'hasAdvancedPricing' => $hasAdvancedPricing,
        ];
    }

    public function delete(int $id): void
    {
        HomeCustomNightlyRate::query()->findOrFail($id)->delete();
    }
}
