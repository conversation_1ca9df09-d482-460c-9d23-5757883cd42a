<?php

namespace App\Services;

use App\Models\Home;

final class UpdateAddressInfoService
{
    public function __invoke(
        Home $home,
        GeoCodeService $geoCodeManager
    ): void {
        if (empty($home->address)) {
            return;
        }

        $output = $geoCodeManager->getDataFromGeoCode($home->address);

        if (! isset($output['status']) || $output['status'] !== 'OK') {
            return;
        }

        $addressComponents = $output['results'][0]['address_components'];
        $addressData = [
            'city_long' => null,
            'state_long' => null,
            'country_long' => null,
            'street' => null,
        ];

        $addressData['address'] = $output['results'][0]['formatted_address'];

        foreach ($addressComponents as $address_component) {
            $types = $address_component['types'];

            if (in_array('locality', $types, true)) {
                $addressData['city_long'] = $address_component['long_name'];
            } elseif (in_array('administrative_area_level_1', $types, true)) {
                $addressData['state_long'] = $address_component['long_name'];
            } elseif (in_array('country', $types, true)) {
                // For United States, ensure consistent naming
                if ($address_component['long_name'] === 'United States' ||
                    strtolower($address_component['short_name']) === 'us') {
                    $addressData['country_long'] = 'United States';
                } else {
                    $addressData['country_long'] = $address_component['long_name'];
                }
            } elseif (in_array('route', $types, true)) {
                $addressData['street'] = $address_component['short_name'];
            }
        }

        $home->update($addressData);
    }
}
