<?php

namespace App\Services;

final class RequestMoneyPriceConverterService
{
    public function __invoke(float $requestPrice): string
    {
        $price = (string) round($requestPrice, 2);

        if (str_contains($price, '.')) {
            $after = substr($price, strpos($price, '.') + 1);
            if (strlen($after) === 2) {
                $price = str_replace('.', '', $price);
            } else {
                $price = str_replace('.', '', $price).'0';
            }
        } else {
            $price .= '00';
        }

        return $price;
    }
}
