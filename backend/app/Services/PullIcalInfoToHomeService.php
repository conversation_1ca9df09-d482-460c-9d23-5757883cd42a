<?php

namespace App\Services;

use App\Models\BlockedDate;
use App\Models\Home;
use App\Traits\ExceptionLoggableTrait;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use RuntimeException;
use Throwable;

final class PullIcalInfoToHomeService
{
    use ExceptionLoggableTrait;

    public function __invoke(
        Home $home,
        string $icalUrl
    ): array {
        DB::beginTransaction();
        try {
            $response = Http::get($icalUrl);

            if ($response->failed() || ! $response->body()) {
                throw new RuntimeException('Failed to get ical info');
            }

            $blocked_dates_insert = $this->retrieveAllDatesFromIcal($response->body(), $home->id);

            BlockedDate::query()->where('home_id', $home->id)->delete();

            BlockedDate::query()->insert($blocked_dates_insert);

            DB::commit();

            return $blocked_dates_insert;
        } catch (Throwable $e) {
            DB::rollBack();

            report($e);

            return [];
        }
    }

    private function retrieveAllDatesFromIcal($string, $home_id): array
    {
        $arr = [];
        $continue_bool = true;
        $start_date = '';
        $end_date = '';
        $type = '';

        while ($continue_bool) {
            $continue_bool = false;

            $parsed = $this->get_string_between($string, 'BEGIN:VEVENT', 'END:VEVENT');

            $end_date_index = strpos($parsed, 'DTEND:');
            $start_date_index = strpos($parsed, 'DTSTART:');

            if ($end_date_index && $start_date_index) {
                $start_date = substr($parsed, ($start_date_index + 8), 8);
                $end_date = substr($parsed, ($end_date_index + 6), 8);
            } else {
                $end_date_index = strpos($parsed, 'DTEND;VALUE=DATE:');
                $start_date_index = strpos($parsed, 'DTSTART;VALUE=DATE:');
                if ($end_date_index && $start_date_index) {
                    $start_date = substr($parsed, ($start_date_index + 19), 8);
                    $end_date = substr($parsed, ($end_date_index + 17), 8);
                }
            }

            if (strpos($parsed, 'SUMMARY:') !== false) {
                $cut_string = strstr($parsed, 'SUMMARY:');

                $type = substr($cut_string, 8);

                if (strpos(strtolower($type), 'airbnb') !== false) {
                    $type = 'Airbnb';
                } elseif (strpos(strtolower($type), 'vrbo') !== false) {
                    $type = 'VRBO';
                }
            }

            if (is_numeric($start_date) && is_numeric($end_date)) {
                $arr[$start_date.'-'.$end_date] = [
                    'start_at' => $start_date,
                    'end_at' => $end_date,
                    'type' => trim($type),
                    'home_id' => $home_id,
                    'created_at' => date('Y.m.d h:m:s'),
                ];
            }

            $string = strstr($string, 'END:VEVENT');
            $string = substr($string, 1);

            if (strpos($string, 'BEGIN:VEVENT') !== false) {
                $continue_bool = true;
            }
        }

        return $arr;
    }

    private function get_string_between($string, $start, $end)
    {
        $string = ' '.$string;
        $ini = strpos($string, $start);
        if ($ini === 0) {
            return '';
        }
        $ini += strlen($start);
        $len = strpos($string, $end, $ini) - $ini;

        return substr($string, $ini, $len);
    }
}
