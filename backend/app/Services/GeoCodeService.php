<?php

namespace App\Services;

use App\Traits\ExceptionLoggableTrait;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Throwable;

final class GeoCodeService
{
    use ExceptionLoggableTrait;

    private string $nominatimUrl;

    public function __construct(string $nominatimUrl = 'https://nominatim.openstreetmap.org/search')
    {
        $this->nominatimUrl = $nominatimUrl;
    }

    /**
     * Sanitize input text by trimming whitespace
     */
    private function sanitizeInput(string $input): string
    {
        return trim($input);
    }

    /**
     * Get geocoding data for an address
     *
     * @param  string  $text  The address to geocode
     * @param  string  $cacheKey  The cache key prefix
     * @param  int  $cacheTtl  Cache time-to-live in seconds
     * @return array The geocoded data
     */
    public function getDataFromGeoCode(
        string $text,
        string $cacheKey = 'nominatim_geocode_',
        int $cacheTtl = 60 * 60 * 24
    ): array {
        try {
            $originalText = $text;
            $text = $this->sanitizeInput($originalText);

            // Log the input for debugging
            Log::info('Geocoding request input', [
                'originalText' => $originalText,
                'sanitizedText' => $text,
            ]);

            // If text is empty after sanitization, return empty result
            if (empty($text)) {
                return [
                    'status' => 'ZERO_RESULTS',
                    'results' => [],
                ];
            }

            // Nominatim doesn't need an API key, but we need to follow usage policy
            $endpoint = sprintf(
                '%s?format=json&q=%s&addressdetails=1&limit=1',
                $this->nominatimUrl,
                urlencode($text)
            );

            return Cache::remember($cacheKey.$text, $cacheTtl, function () use ($endpoint) {
                // Add a user agent as required by Nominatim usage policy
                $options = [
                    'http' => [
                        'header' => "User-Agent: Twimo/1.0\r\n",
                        'timeout' => 15, // Add a timeout to prevent hanging
                    ],
                ];
                $context = stream_context_create($options);

                try {
                    // Log the request for debugging
                    Log::info('Making Nominatim API request', [
                        'endpoint' => $endpoint,
                    ]);

                    $geocode = @file_get_contents($endpoint, false, $context);

                    if ($geocode === false) {
                        $this->log('Failed to get data from Nominatim API - network error', [
                            'endpoint' => $endpoint,
                            'error' => error_get_last(),
                        ]);

                        return [
                            'status' => 'ZERO_RESULTS',
                            'results' => [],
                        ];
                    }

                    $data = json_decode($geocode, true, 512, JSON_THROW_ON_ERROR);

                    // Log the response for debugging
                    Log::info('Nominatim API response', [
                        'data' => $data,
                    ]);

                    if (empty($data)) {
                        $this->log('Failed to get data from Nominatim API - empty response', [
                            'response' => $data,
                        ]);

                        return [
                            'status' => 'ZERO_RESULTS',
                            'results' => [],
                        ];
                    }

                    // Format the response in a structure compatible with our application
                    return [
                        'status' => 'OK',
                        'results' => [
                            [
                                'formatted_address' => $data[0]['display_name'],
                                'address_components' => $this->formatAddressComponents($data[0]['address'] ?? []),
                                'geometry' => [
                                    'location' => [
                                        'lat' => (float) $data[0]['lat'],
                                        'lng' => (float) $data[0]['lon'],
                                    ],
                                ],
                                'original_data' => $data[0],
                            ],
                        ],
                    ];
                } catch (Throwable $e) {
                    $this->log('Exception in Nominatim API request', [
                        'exception' => $e->getMessage(),
                        'endpoint' => $endpoint,
                    ]);

                    return [
                        'status' => 'ZERO_RESULTS',
                        'results' => [],
                    ];
                }
            });
        } catch (Throwable $e) {
            report($e);
            $this->log('Exception in getDataFromGeoCode', [
                'exception' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'status' => 'ZERO_RESULTS',
                'results' => [],
            ];
        }
    }

    /**
     * Format Nominatim address components to a structure compatible with our application
     *
     * @param  array  $address  The address data from Nominatim
     * @return array The formatted address components
     */
    private function formatAddressComponents(array $address): array
    {
        $components = [];

        // Map Nominatim address fields to component types
        $mappings = [
            'country' => ['country'],
            'state' => ['administrative_area_level_1'],
            'county' => ['administrative_area_level_2'],
            'city' => ['locality'],
            'town' => ['locality'],
            'village' => ['locality'],
            'hamlet' => ['locality'],
            'suburb' => ['sublocality'],
            'neighbourhood' => ['sublocality_level_1'],
            'road' => ['route'],
            'postcode' => ['postal_code'],
            'house_number' => ['street_number'],
        ];

        // Track which major component types we have
        $hasCountry = false;
        $hasState = false;
        $hasCity = false;

        foreach ($mappings as $nominatimKey => $types) {
            if (isset($address[$nominatimKey])) {
                $value = $address[$nominatimKey];

                // For country, ensure we use the full country name
                if ($nominatimKey === 'country') {
                    // Special handling for United States to ensure consistency
                    if ($value === 'United States' ||
                        (isset($address['country_code']) && strtolower($address['country_code']) === 'us')) {
                        $value = 'United States';
                    }

                    $hasCountry = true;
                }
                // Track other major components
                elseif (in_array('administrative_area_level_1', $types)) {
                    $hasState = true;
                } elseif (in_array('locality', $types)) {
                    $hasCity = true;
                }

                $components[] = [
                    'long_name' => $value,
                    'short_name' => $nominatimKey === 'country' && isset($address['country_code'])
                        ? $address['country_code']
                        : $value,
                    'types' => $types,
                ];
            }
        }

        // Add placeholders for missing major components to prevent errors
        if (! $hasCountry) {
            $components[] = [
                'long_name' => '',
                'short_name' => '',
                'types' => ['country'],
            ];
        }

        if (! $hasState) {
            $components[] = [
                'long_name' => '',
                'short_name' => '',
                'types' => ['administrative_area_level_1'],
            ];
        }

        if (! $hasCity) {
            $components[] = [
                'long_name' => '',
                'short_name' => '',
                'types' => ['locality'],
            ];
        }

        return $components;
    }
}
