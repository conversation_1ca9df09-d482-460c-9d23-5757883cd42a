<?php

namespace App\Services;

use App\Models\User;

final class GetUserProfileCompletionService
{
    public function __invoke(User $user): int
    {
        $worthAttrs = [
            'first_name',
            'last_name',
            'about',
            'interview_passed',
        ];

        $completion = 0;
        $increment = (100 / count($worthAttrs));

        foreach ($worthAttrs as $attr) {
            if (($attr !== 'interview_passed' && ! empty($user->getAttribute($attr))) ||
                ($attr === 'interview_passed' && (int) $user->getAttribute('interview_passed') === 1)) {
                $completion += $increment;
            }
        }

        return $completion;
    }
}
