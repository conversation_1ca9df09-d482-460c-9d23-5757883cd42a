<?php

namespace App\Services;

use App\Enums\HomeStatus;
use App\Jobs\ProcessHomePhotosJob;
use App\Models\Home;
use App\Traits\ExceptionLoggableTrait;
use Illuminate\Support\Facades\DB;
use RuntimeException;
use Throwable;

final readonly class CreateHomeService
{
    use ExceptionLoggableTrait;

    public function __construct(
        private UpdateAddressInfoService $updateAddressInfoService,
        private GeoCodeService $geoCodeManager
    ) {}

    public function __invoke(array $requestedData): mixed
    {
        DB::beginTransaction();

        try {
            if (! isset($requestedData['user_id']) || empty($requestedData['user_id'])) {
                // Get the user ID from the request
                $userId = request()->user()->id ?? null;
                if (empty($userId)) {
                    throw new RuntimeException('User not logged in');
                }
                $requestedData['user_id'] = $userId;
            }

            // Validate title is provided
            if (empty($requestedData['title'])) {
                throw new RuntimeException('Title is required to create a home.');
            }

            // Validate address is provided
            if (empty($requestedData['address'])) {
                throw new RuntimeException('Address is required to create a home.');
            }

            // We don't validate house number here since it's only required for activation
            // But we'll log a warning if the address doesn't have a house number
            try {
                // Use GeoCodeService to check if the address has a house number
                $output = $this->geoCodeManager->getDataFromGeoCode($requestedData['address']);

                if (! empty($output) && $output['status'] === 'OK' && ! empty($output['results'])) {
                    $addressComponents = $output['results'][0]['address_components'] ?? [];

                    // Check if any component has a house number
                    $addressHasHouseNumber = false;
                    foreach ($addressComponents as $component) {
                        if (isset($component['types']) && in_array('street_number', $component['types'])) {
                            $addressHasHouseNumber = true;
                            break;
                        }
                    }

                    if (! $addressHasHouseNumber) {
                        $this->log('Address does not have a house number', [
                            'address' => $requestedData['address'],
                        ], 'warning');
                    }
                }
            } catch (\Exception $e) {
                // Just log the error, don't block creation
                $this->log('Error checking address for house number: '.$e->getMessage(), [
                    'address' => $requestedData['address'],
                    'exception' => $e,
                ], 'warning');
            }

            $home = Home::query()
                ->create(array_merge($requestedData, ['status' => HomeStatus::DRAFT->value]));
            /** @var Home $home */

            // Queue photos for processing if provided
            if (! empty($requestedData['photos'])) {
                // Set initial processing status
                $home->update([
                    'photo_processing_status' => 'queued',
                ]);

                // Dispatch job to process photos in the background
                ProcessHomePhotosJob::dispatch($home, $requestedData['photos']);
            }

            // Update home address info
            ($this->updateAddressInfoService)($home, $this->geoCodeManager);

            DB::commit();

            $home->makeHidden(['user', 'owner']);

            return $home;
        } catch (Throwable $e) {
            DB::rollBack();
            report($e);
            throw new RuntimeException('Failed to create home: '.$e->getMessage());
        }
    }
}
