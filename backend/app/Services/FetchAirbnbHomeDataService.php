<?php

namespace App\Services;

use App\Models\Home;
use App\Traits\CanIntegrateWithAirbnbTrait;
use App\Traits\ExceptionLoggableTrait;
use Illuminate\Contracts\Container\BindingResolutionException;

final class FetchAirbnbHomeDataService
{
    use CanIntegrateWithAirbnbTrait;
    use ExceptionLoggableTrait;

    /**
     * @throws BindingResolutionException
     */
    public function __construct(?AirbnbService $airbnbManager = null)
    {
        $this->initializeAirbnbManager($airbnbManager ?? app()->make(AirbnbService::class));
    }

    public function __invoke(
        string $airbnbUrl,
    ): ?Home {
        $listingData = $this->airbnbManager->scrape($airbnbUrl);

        if ($listingData === null) {
            return null;
        }

        $home = new Home;

        // Store Airbnb URL and set flag
        $home->airbnb_url = $airbnbUrl;
        $home->is_from_airbnb = true;
        $home->photo_processing_status = 'pending';

        $home->title = $listingData['title'] ?? null;
        $home->description = $listingData['description'] ?? null;
        $home->address = $listingData['location'] ?? $listingData['locationSubtitle'] ?? null;
        $home->location = $listingData['description'];

        // Parse bedrooms, beds, and bathrooms (supports singular/plural and decimals)
        $subDescriptionItems = $listingData['subDescription']['items'] ?? [];
        foreach ($subDescriptionItems as $item) {
            // Bedrooms (e.g., "6 bedroom" or "6 bedrooms")
            if (preg_match('/(\d+)\s*bedrooms?\b/i', $item, $matches)) {
                $home->beds = (int) $matches[1];
            }
            // Number of beds (e.g., "12 bed" or "12 beds")
            if (preg_match('/(\d+)\s*beds?\b/i', $item, $matches)) {
                $home->number_of_beds = (int) $matches[1];
            }
            // Bathrooms (e.g., "6 bath", "6.5 bathroom", "6.5 bathrooms")
            if (preg_match('/(\d+(?:\.\d+)?)\s*bath(?:room)?s?\b/i', $item, $matches)) {
                $home->baths = (float) $matches[1];
            }
        }

        $home->guests = $listingData['personCapacity'] ?? null;

        // Parse amenities
        $amenities = [];
        if (isset($listingData['amenities'])) {
            foreach ($listingData['amenities'] as $amenityGroup) {
                foreach ($amenityGroup['values'] ?? [] as $amenity) {
                    if ($amenity['available'] ?? false) {
                        $amenities[] = $amenity['title'];
                    }
                }
            }
        }

        // Add mandatory safety amenities
        $mandatorySafetyAmenities = ['Smoke Alarm', 'CO2 Alarm', 'Fire Extinguisher'];
        $amenities = array_unique(array_merge($amenities, $mandatorySafetyAmenities));

        $home->amenities = $amenities;

        // Parse photos - no limit on number of photos
        $photos = [];

        foreach ($listingData['images'] ?? [] as $photo) {
            $photoUrl = $photo['imageUrl'] ?? null;

            if ($photoUrl === null) {
                continue;
            }

            $photoName = $photo['caption'] ?? $home->title ?? 'Airbnb Home';

            $photos[] = [
                'media_id' => $photoUrl,
                'filename' => $photoName,
                'url' => $photoUrl,
                'name' => $photoName,
                'src' => $photoUrl,
                'thumb' => $photoUrl,
            ];
        }

        // Deduplicate photos by URL to avoid duplicates
        $seen = [];
        $uniquePhotos = [];
        foreach ($photos as $p) {
            if (! in_array($p['url'], $seen, true)) {
                $seen[] = $p['url'];
                $uniquePhotos[] = $p;
            }
        }
        $photos = $uniquePhotos;

        $home->setAttribute('airBnbPhotos', $photos);

        return $home;
    }
}
