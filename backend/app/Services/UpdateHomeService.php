<?php

namespace App\Services;

use App\Models\Home;
use App\Models\TemporaryUpload;
use App\Traits\ExceptionLoggableTrait;
use Illuminate\Support\Facades\DB;
use RuntimeException;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Throwable;

final class UpdateHomeService
{
    use ExceptionLoggableTrait;

    public function __construct(
        private readonly UpdateAddressInfoService $updateAddressInfoService,
        private readonly GeoCodeService $geoCodeManager
    ) {}

    public function __invoke(Home $home, array $requestedData): mixed
    {
        DB::beginTransaction();

        try {
            if (isset($requestedData['photos'])) {
                $this->handlePhotoUpdates($home, $requestedData['photos']);
            }

            // Update extra_info as a whole, since it's a JSON field
            if (isset($requestedData['extra_info']) && ! empty($requestedData['extra_info'])) {
                $extraInfo = $home->extra_info ?? [];
                foreach ($requestedData['extra_info'] as $key => $value) {
                    $extraInfo[$key] = $value;
                }
                $home->extra_info = $extraInfo;

                $requestedData['extra_info'] = $extraInfo;
            }

            // CASE 1: Check requirements when activating a home
            if (isset($requestedData['status']) && $requestedData['status'] === 'active') {
                // Check subscription status directly using User model property
                $user = $home->user;
                if (! $user->is_subscribed) {
                    throw new RuntimeException('Active subscription required to activate a home.');
                }

                // Check if title is provided
                if (empty($home->title) && empty($requestedData['title'])) {
                    throw new RuntimeException('Title is required to activate a home.');
                }

                // Check if address is provided
                $address = $requestedData['address'] ?? $home->address;
                if (empty($address)) {
                    throw new RuntimeException('Address is required to activate a home.');
                }

                // Check if address has a house number
                $addressHasHouseNumber = false;
                try {
                    // Use GeoCodeService to check if the address has a house number
                    $output = $this->geoCodeManager->getDataFromGeoCode($address);

                    if (! empty($output) && $output['status'] === 'OK' && ! empty($output['results'])) {
                        $addressComponents = $output['results'][0]['address_components'] ?? [];

                        // Check if any component has a house number
                        foreach ($addressComponents as $component) {
                            if (isset($component['types']) && in_array('street_number', $component['types'])) {
                                $addressHasHouseNumber = true;
                                break;
                            }
                        }

                        // Also check if there's a house_number in the original Nominatim data
                        if (isset($output['results'][0]['original_data']['address']['house_number'])) {
                            $addressHasHouseNumber = true;
                        }
                    }

                    if (! $addressHasHouseNumber) {
                        throw new RuntimeException('Address must include a house/building number to activate a home.');
                    }
                } catch (\Exception $e) {
                    // Log the error but don't block activation if we can't check the address
                    $this->log('Error checking address for house number: '.$e->getMessage(), [
                        'address' => $address,
                        'exception' => $e,
                    ], 'warning');
                }

                // Check if home has public rental or seasonal rental enabled
                $hasPublicRental = $home->allow_booking || $home->offer_as_seasonal_lease;

                // If public rental is enabled, ensure there are at least 3 images
                if ($hasPublicRental) {
                    $imageCount = $home->getMedia('images')->count();

                    // Include any newly added photos from this request
                    if (isset($requestedData['photos']) && is_array($requestedData['photos'])) {
                        $imageCount = max($imageCount, count($requestedData['photos']));
                    }

                    if ($imageCount < 3) {
                        throw new RuntimeException('At least 3 images are required for homes with public rental enabled.');
                    }
                }
            }

            // CASE 2: Check for the "trick" case: enabling public rental on an active home without enough images
            if (($requestedData['allow_booking'] ?? false) || ($requestedData['offer_as_seasonal_lease'] ?? false)) {
                if ($home->status === 'active') {
                    $imageCount = $home->getMedia('images')->count();

                    // Include any newly added photos from this request
                    if (isset($requestedData['photos']) && is_array($requestedData['photos'])) {
                        $imageCount = max($imageCount, count($requestedData['photos']));
                    }

                    if ($imageCount < 3) {
                        // Set the home to draft status
                        $requestedData['status'] = 'draft';
                    }
                }
            }

            $home->update($requestedData);

            // Update home address info if address was updated
            if (isset($requestedData['address'])) {
                ($this->updateAddressInfoService)($home, $this->geoCodeManager);
            }

            DB::commit();

            $home->makeHidden(['user', 'owner']);

            return $home;
        } catch (Throwable $e) {
            DB::rollBack();
            report($e);
            throw new RuntimeException('Failed to update home: '.$e->getMessage());
        }
    }

    private function handlePhotoUpdates(Home $home, array $photoIds): void
    {
        // Early return if photo IDs array is empty
        if (empty($photoIds)) {
            return;
        }

        try {
            $uploadDisk = config('services.media.uploadDisk');

            // Filter out any non-numeric values and ensure we only have integers
            $validPhotoIds = array_filter($photoIds, function ($id) {
                return is_numeric($id) && $id > 0;
            });

            // Convert strings to integers
            $validPhotoIds = array_map('intval', $validPhotoIds);

            // Ensure unique IDs while preserving order
            $uniquePhotoIds = array_values(array_unique($validPhotoIds));

            // Get existing photos
            $existingPhotos = $home->getMedia('images')->keyBy('id');
            $existingIds = $existingPhotos->keys()->toArray();

            // Find photos to remove
            $photosToRemove = array_diff($existingIds, $uniquePhotoIds);
            if (! empty($photosToRemove)) {
                Media::whereIn('id', $photosToRemove)->delete();
            }

            // Process each photo
            foreach ($uniquePhotoIds as $position => $photoId) {
                // Find the media
                $media = Media::find($photoId);
                if (! $media) {
                    $this->log('Photo not found: '.$photoId, [], 'warning');

                    continue;
                }

                // Set the correct order for the photo (important for cover photo selection)
                $media->order_column = $position;
                $media->save();

                // Check if this media is already part of this home
                $alreadyAssigned = in_array($photoId, $existingIds);

                // If it's not already assigned to this home, move it
                if (! $alreadyAssigned) {
                    try {
                        // Check if it's a temporary upload or assigned to another model
                        if ($media->model_type === TemporaryUpload::class) {
                            // Move from temporary uploads to this home
                            $media->move($home, 'images', $uploadDisk);
                        } elseif ($media->model_type && $media->model_id) {
                            // If it's assigned to another model, create a copy
                            $this->log('Media already assigned to another model, creating a copy', [
                                'media_id' => $photoId,
                                'current_model' => $media->model_type,
                                'current_id' => $media->model_id,
                                'target_home' => $home->id,
                            ], 'info');

                            // Create a duplicate of the media file
                            $newMedia = $media->copy($home, 'images', $uploadDisk);
                            $newMedia->order_column = $position;
                            $newMedia->save();
                        } else {
                            // Not assigned to any model yet, assign to this home
                            $media->move($home, 'images', $uploadDisk);
                        }
                    } catch (\Exception $e) {
                        $this->log('Error processing media: '.$e->getMessage(), [
                            'media_id' => $photoId,
                            'home_id' => $home->id,
                            'exception' => $e,
                        ], 'error');
                    }
                }
            }
        } catch (\Exception $e) {
            $this->log('Failed to update home photos: '.$e->getMessage(), [
                'home_id' => $home->id,
                'exception' => $e,
            ], 'error');
        }
    }
}
