<?php

namespace App\Services;

use App\Enums\UserType;
use App\Helpers\PhoneNumberHelper;
use App\Models\Jobs;
use App\Models\User;
use App\Models\Vendors;
use App\Traits\ExceptionLoggableTrait;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use RuntimeException;
use Throwable;

final class CreateUserService
{
    use ExceptionLoggableTrait;

    public function __invoke(
        string $email,
        string $firstName,
        ?string $lastName,
        ?string $phoneNumber,
        string $password,
        string $userType,
        ?int $groupId,
        ?string $rewardPointUuid = null,
        ?string $invite_code = null,
        bool $welcomeSend = false,
        int $interviewsPassed = 0,
        ?string $about = null,
        ?string $avatar = null,
        array $additionalInfo = []
    ): array {
        DB::beginTransaction();
        try {
            $user = new User;
            $user->email = $email;
            $user->first_name = $firstName;
            $user->last_name = $lastName;

            if (! empty($phoneNumber)) {
                // Format phone number to ensure it has a "+1" prefix and proper format
                $user->phone_number = PhoneNumberHelper::formatPhoneNumber($phoneNumber);
            }

            $user->password = bcrypt($password);
            $user->about = $about;
            $user->role_id = 1;
            $user->avatar = $avatar;
            $user->user_type = UserType::from($userType);

            if ($user->user_type === UserType::VENDOR) {
                $user->role_id = 4;
                $user->email_verified_at = now()->toDateTimeString();
            }

            // Store additional info in extra_info column
            $defaultExtraInfo = [
                'registration_progress' => 0,
                'what_brings_you_to_twimo' => [],
                'opted_in_to_communications' => $additionalInfo['opted_in_to_communications'] ?? false,
                'opt_in_timestamp' => $additionalInfo['opt_in_timestamp'] ?? null,
                'opt_in_ip_address' => $additionalInfo['opt_in_ip_address'] ?? null,
                'recaptcha_score' => $additionalInfo['recaptcha_score'] ?? null,
                'registration_date' => now()->toDateTimeString(),
            ];
            $user->extra_info = array_merge($defaultExtraInfo, $additionalInfo);

            $user->shared_url = Str::slug($user->first_name).'-'.$user->id;
            $user->canonical_url = $user->shared_url;

            $user->save();

            DB::commit();

            $user->load('role');

            // Check if it's Vendor Invitation
            if ($user->user_type === UserType::VENDOR && $invite_code !== null) {
                $vendor = Vendors::query()
                    ->select('*')
                    ->where('unique_code', $invite_code)
                    ->first();

                if ($vendor) {
                    // Update vendors
                    Vendors::query()->where('id', $vendor->id)->update(
                        ['vendor_user_id' => $user->id, 'status' => 'accepted']
                    );

                    // Update Jobs
                    Jobs::query()->where('vendor_id', $vendor->id)->update(['vendor_user_id' => $user->id]);
                } else {
                    throw new RuntimeException('Vendor invitation not found.');
                }

                return [
                    'user' => $user,
                ];
            }

            return [
                'message' => 'User created successfully',
            ];
        } catch (Throwable $e) {
            DB::rollBack();
            report($e);

            throw new RuntimeException('Failed to create user');
        }
    }
}
