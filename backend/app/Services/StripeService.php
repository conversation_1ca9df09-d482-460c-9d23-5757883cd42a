<?php

namespace App\Services;

use App\Models\Booking;
use App\Models\User;
use App\Traits\ExceptionLoggableTrait;
use Illuminate\Support\Facades\Log;
use Stripe\Account;
use Stripe\AccountLink;
use Stripe\Charge;
use Stripe\Checkout\Session;
use Stripe\Exception\ApiErrorException;
use Stripe\Identity\VerificationSession;
use Stripe\PaymentIntent;
use Stripe\Stripe;
use Stripe\Transfer;
use Throwable;

final class StripeService
{
    use ExceptionLoggableTrait;

    public function makePaymentAndGetPaymentUrl(Booking $swap): string
    {
        try {
            Stripe::setApiKey(config('services.stripe.secret'));

            $stripeAmount = (float) (new RequestMoneyPriceConverterService)($swap->extra_info['total_money']);

            $requestData = [
                'payment_intent_data' => [
                    'description' => $this->getPaymentDescription($swap),
                ],
                'line_items' => [
                    [
                        'price_data' => [
                            'currency' => 'USD',
                            'unit_amount' => $stripeAmount,
                            'product_data' => [
                                'name' => $this->getPaymentDescription($swap),
                            ],
                        ],
                        'quantity' => 1,
                    ],
                ],
                'mode' => 'payment',
                'success_url' => $this->getSuccessUrl($swap),
                'cancel_url' => $this->getCancelUrl($swap),
            ];

            // get stripe connect id for transaction and add to payment intent if charges enabled
            $stripe_connect_id = $swap->user->stripe_connect_id;
            $stripe_connect_charges_enabled = $swap->user->charges_enabled;
            if ($stripe_connect_id && $stripe_connect_charges_enabled) {
                $requestData['payment_intent_data'] = [
                    'application_fee_amount' => $this->getApplicationFeeAmount($stripeAmount),
                    'on_behalf_of' => $stripe_connect_id,
                ];
            }

            return Session::create($requestData)->url;
        } catch (Throwable $e) {
            report($e);

            $this->log('StripeManager::makePaymentAndGetPaymentUrl', [
                'swap_id' => $swap->id,
                'error' => $e->getMessage(),
            ]);

            return '';
        }
    }

    public function getPaymentIntent(
        float $amount,
        float $applicationFeeAmount = 0, // Kept for backward compatibility but not used
        string $currency = 'USD',
        string $paymentDescription = 'Twimo Booking',
        ?string $transferToConnectId = null,
    ): string {
        try {
            Stripe::setApiKey(config('services.stripe.secret'));

            $stripeAmount = (new RequestMoneyPriceConverterService)($amount);

            $requestData = [
                'amount' => $stripeAmount,
                'currency' => $currency,
                'description' => $paymentDescription,
            ];

            // Note: receipt_email parameter has been removed to avoid interference with Stripe Connect
            // Stripe will automatically send receipts based on customer information

            Log::debug('Stripe payment intent created', [
                'requestData' => $requestData,
                'transferToConnectId' => $transferToConnectId,
            ]);

            if ($transferToConnectId) {
                // For direct charges to connected accounts
                // This ensures the payment goes directly to the host's account
                $requestData['transfer_data'] = [
                    'destination' => $transferToConnectId,
                ];

                // No application fee - all money goes to the host
                // We're not taking any cut as per requirements
            }

            return PaymentIntent::create($requestData)->client_secret;
        } catch (Throwable $e) {
            report($e);

            $this->log('Error while creating Stripe payment intent', [
                'amount' => $amount,
                'error' => $e->getMessage(),
            ]);

            return '';
        }
    }

    public function checkPaymentIntentSuccess(string $paymentIntentId): bool
    {
        try {
            Stripe::setApiKey(config('services.stripe.secret'));

            return PaymentIntent::retrieve($paymentIntentId)->status === 'succeeded';
        } catch (Throwable $e) {
            report($e);

            $this->log('Error while checking Stripe payment intent success', [
                'paymentIntentId' => $paymentIntentId,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    public function getApplicationFeeAmount(float $amount): int
    {
        // get the fixed amount and the percentage, then do math, returning an int for cents amount
        $fixedAmount = config('services.stripe.application_fee_amount_fixed');
        $percentAmount = config('services.stripe.application_fee_amount_percent');
        $percentAmount *= 0.01; // convert to decimal
        $centsAmount = ($amount * $percentAmount) + $fixedAmount;

        return (int) $centsAmount;
    }

    public function getPaymentDescription(Booking $swap): string
    {
        if ($swap->extra_info['stripePaymentDescription'] ?? false) {
            return $swap->extra_info['stripePaymentDescription'];
        }

        return '48 Dots Booking';
    }

    public function getSuccessUrl(Booking $swap): string
    {
        return config('services.frontendUrl').'/bookings/'.$swap->id;
    }

    public function getCancelUrl(Booking $swap): string
    {
        if ($swap->extra_info['stripeCancelUrl'] ?? false) {
            return $swap->extra_info['stripeCancelUrl'];
        }

        return config('services.frontendUrl').'/bookings/'.$swap->id;
    }

    public function createDirectCharge(
        string $stripeConnectId,
        float $totalAmount,
        float $applicationFeeAmount,
        string $currency = 'USD',
        string $paymentDescription = 'Twimo Payment',
    ): bool {
        try {
            Stripe::setApiKey(config('services.stripe.secret'));

            $requestData = [
                'amount' => round($totalAmount),
                //                'application_fee_amount' => $applicationFeeAmount,
                'currency' => $currency,
                'description' => $paymentDescription,
                'source' => $stripeConnectId,
            ];

            $charge = Charge::create($requestData);

            Log::debug('Stripe direct charge created', [
                'charge' => $charge,
            ]);

            return true;
        } catch (Throwable $e) {
            report($e);

            $this->log('Error while creating Stripe direct charge', [
                'args' => func_get_args(),
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    public function createTransfer(
        string $stripeConnectId,
        float $totalAmount,
        string $currency = 'USD',
        string $paymentDescription = 'Twimo Payment',
    ): bool {
        try {
            Stripe::setApiKey(config('services.stripe.secret'));

            $requestData = [
                'amount' => round($totalAmount),
                'currency' => $currency,
                'description' => $paymentDescription,
                'destination' => $stripeConnectId,
            ];

            $transfer = Transfer::create($requestData);

            Log::debug('Stripe transfer created', [
                'transfer' => $transfer,
            ]);

            return true;
        } catch (Throwable $e) {
            report($e);

            $this->log('Error while creating Stripe transfer', [
                'args' => func_get_args(),
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    public function getDashboardUrl(
        string $stripeConnectId,
    ): string {
        try {
            Stripe::setApiKey(config('services.stripe.secret'));

            return Account::createLoginLink($stripeConnectId)->url;
        } catch (Throwable $e) {
            report($e);

            $this->log('Error while getting Stripe dashboard url', [
                'error' => $e->getMessage(),
                'stripeConnectId' => $stripeConnectId,
            ]);

            return '';
        }
    }

    /**
     * Create a Stripe Identity verification session
     *
     * @return string|null URL to redirect user to for verification
     */
    public function createIdentityVerificationSession(User $user): ?string
    {
        try {
            Stripe::setApiKey(config('services.stripe.secret'));

            $session = VerificationSession::create([
                'type' => 'document',
                'metadata' => [
                    'user_id' => $user->id,
                ],
                'options' => [
                    'document' => [
                        'require_id_number' => true,
                        'require_matching_selfie' => true,
                    ],
                ],
                'return_url' => config('services.frontendUrl').'/identity-verification/complete',
            ]);

            // Save the session ID to the user
            $user->stripe_identity_session_id = $session->id;
            $user->save();

            return $session->url;
        } catch (Throwable $e) {
            report($e);

            $this->log('Error creating Stripe Identity verification session', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
            ]);

            return null;
        }
    }

    /**
     * Check the status of a verification session
     *
     * @return array Status information
     */
    public function checkVerificationSessionStatus(string $sessionId): array
    {
        try {
            Stripe::setApiKey(config('services.stripe.secret'));

            $session = VerificationSession::retrieve($sessionId);

            return [
                'status' => $session->status,
                'verified' => $session->status === 'verified',
            ];
        } catch (ApiErrorException $e) {
            report($e);

            $this->log('Error checking Stripe Identity verification status', [
                'session_id' => $sessionId,
                'error' => $e->getMessage(),
            ]);

            return [
                'status' => 'error',
                'verified' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Create a Stripe Account
     *
     * @throws ApiErrorException
     */
    public function createAccount(string $email, string $firstName, string $lastName): Account
    {
        Stripe::setApiKey(config('services.stripe.secret'));

        return Account::create([
            'type' => 'express',
            'email' => $email,
            'business_type' => 'individual',
            'individual' => [
                'first_name' => $firstName,
                'last_name' => $lastName,
                'email' => $email,
            ],
            'capabilities' => [
                'card_payments' => [
                    'requested' => true,
                ],
                'transfers' => [
                    'requested' => true,
                ],
            ],
        ]);
    }

    public function getOnboardingUrl(
        string $stripeConnectId,
    ): string {
        try {
            Stripe::setApiKey(config('services.stripe.secret'));

            $accountLink = AccountLink::create([
                'account' => $stripeConnectId,
                'refresh_url' => config('services.stripe.connect_onboarding_refresh_url'),
                'return_url' => config('services.stripe.connect_onboarding_return_url'),
                'type' => 'account_onboarding',
            ]);

            return $accountLink->url;
        } catch (Throwable $e) {
            report($e);

            $this->log('Error while getting Stripe onboarding url', [
                'error' => $e->getMessage(),
                'stripeConnectId' => $stripeConnectId,
            ]);

            return '';
        }
    }

    public function getAccountStatuses(string $stripeConnectId): array
    {
        try {
            Stripe::setApiKey(config('services.stripe.secret'));

            $account = Account::retrieve($stripeConnectId);

            return [
                'charges_enabled' => $account->charges_enabled,
                'payouts_enabled' => $account->payouts_enabled,
            ];
        } catch (Throwable $e) {
            report($e);

            $this->log('Error while getting Stripe account statuses', [
                'error' => $e->getMessage(),
                'stripeConnectId' => $stripeConnectId,
            ]);

            return [
                'charges_enabled' => false,
                'payouts_enabled' => false,
            ];
        }
    }

    public function refundPayment(string $paymentIntentId): bool
    {
        try {
            Stripe::setApiKey(config('services.stripe.secret'));

            // Simple refund with reverse_transfer parameter
            $refundData = [
                'payment_intent' => $paymentIntentId,
                'reverse_transfer' => true,
            ];

            // Create the refund
            $refund = \Stripe\Refund::create($refundData);

            // Note: receipt_email parameter is no longer supported by Stripe's Refund API
            // Stripe automatically sends receipts based on the customer's email in the payment intent

            Log::debug('Stripe refund created', [
                'refund' => $refund,
            ]);

            return $refund->status === 'succeeded';
        } catch (Throwable $e) {
            report($e);

            $this->log('Error while creating Stripe refund', [
                'paymentIntentId' => $paymentIntentId,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }
}
