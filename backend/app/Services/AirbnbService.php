<?php

namespace App\Services;

use App\Models\User;
use App\Traits\ExceptionLoggableTrait;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Throwable;

final readonly class AirbnbService
{
    use ExceptionLoggableTrait;

    public function __construct(private string $apiKey) {}

    private function sanitizeAirbnbUrl($url): ?string
    {
        $url = trim($url);

        $parsedUrl = parse_url($url);

        if ($parsedUrl !== false) {
            $scheme = isset($parsedUrl['scheme']) ? $parsedUrl['scheme'].'://' : '';
            $host = $parsedUrl['host'] ?? '';
            $path = $parsedUrl['path'] ?? '';

            return $scheme.$host.$path;
        }

        return null;
    }

    /**
     * @return null|array{
     *     id?: string,
     *     coordinates?: array{latitude?: float, longitude?: float},
     *     descriptionOriginalLanguage?: string,
     *     metaDescription?: string,
     *     seoTitle?: string,
     *     sharingConfigTitle?: string,
     *     thumbnail?: string,
     *     url?: string,
     *     androidLink?: string,
     *     iosLink?: string,
     *     propertyType?: string,
     *     roomType?: string,
     *     homeTier?: int,
     *     personCapacity?: int,
     *     rating?: array{
     *         accuracy?: float,
     *         checking?: float,
     *         cleanliness?: float,
     *         communication?: float,
     *         location?: float,
     *         value?: float,
     *         guestSatisfaction?: float,
     *         reviewsCount?: int
     *     },
     *     breadcrumbs?: array<array{linkRoute?: string, linkText?: string, searchText?: string}>,
     *     location?: string,
     *     host?: array{
     *         id?: string,
     *         name?: string,
     *         profileImage?: string,
     *         isSuperHost?: bool,
     *         isVerified?: bool,
     *         highlights?: array,
     *         about?: string,
     *         ratingCount?: int,
     *         ratingAverage?: int,
     *         hostDetails?: array,
     *         timeAsHost?: array{months?: int, years?: int}
     *     },
     *     coHosts?: array<array{userId?: string, name?: string, profilePictureUrl?: string}>,
     *     locationSubtitle?: string,
     *     locationDescriptions?: array<array{title?: string, content?: string, mapMarkerRadiusInMeters?: int}>,
     *     title?: string,
     *     description?: string,
     *     htmlDescription?: array{htmlText?: string, recommendedNumberOfLines?: int},
     *     subDescription?: array{title?: string, items?: array<string>},
     *     price?: array{label?: string},
     *     brandHighlights?: array{title?: string, subtitle?: string, hasGoldenLaurel?: bool},
     *     images?: array<array{caption?: string, imageUrl?: string, orientation?: string}>,
     *     highlights?: array<array{title?: string, subtitle?: string, icon?: string, type?: string}>,
     *     houseRules?: array{additional?: mixed, general?: array<array{title?: string, values?: array<array{title?: string, icon?: string}>}>},
     *     cancellationPolicies?: array<array{title?: string, policyName?: string, policyId?: int}>,
     *     amenities?: array<array{title?: string, values?: array<array{title?: string, subtitle?: string, icon?: string, available?: bool}>}>,
     *     timestamp?: string,
     *     language?: string,
     *     checkIn?: mixed,
     *     checkOut?: mixed
     * }
     */
    public function scrape(
        string $airbnbUrl,
        string $cacheKey = 'airbnb_',
        int $cacheTtl = 60 * 60 * 24 * 7
    ): mixed {
        $url = null;
        try {
            $url = $this->sanitizeAirbnbUrl($airbnbUrl);

            if ($url === null) {
                return null;
            }

            return Cache::remember($cacheKey.$url, $cacheTtl, function () use ($url) {
                $endpoint = "https://api.apify.com/v2/actor-tasks/hweihwang~airbnb-rooms-urls-scraper-task/run-sync-get-dataset-items?token={$this->apiKey}";

                $payload = [
                    'startUrls' => [
                        [
                            'url' => $url,
                            'method' => 'GET',
                        ],
                    ],
                ];

                ini_set('max_execution_time', 300);

                $data = Http::timeout(300)->post($endpoint, $payload)->json();

                Log::info('Airbnb data', [
                    'data' => $data,
                ]);

                if (isset($data['error']) || ! isset($data[0])) {
                    $this->log('Failed to get data from Airbnb!', [
                        'airbnbData' => $data,
                    ]);

                    return null;
                }

                return $data[0];
            });
        } catch (Throwable $e) {
            report($e);

            return null;
        } finally {
            $user = auth()->user();

            /* @var User $user */

            Log::info('A request to Airbnb was made!', [
                'url' => $airbnbUrl,
                'sanitizedUrl' => $url,
                'user' => $user->email ?? 'guest',
                'ip' => request()->ip(),
            ]);
        }
    }
}
