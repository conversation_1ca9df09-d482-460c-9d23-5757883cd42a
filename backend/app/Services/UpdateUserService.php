<?php

namespace App\Services;

use App\Models\User;
use Exception;
use Illuminate\Support\Facades\DB;
use RuntimeException;

final readonly class UpdateUserService
{
    public function __invoke(
        User $updatedUser,
        array $requestData
    ): User {
        DB::beginTransaction();

        try {
            if (isset($requestData['extra_info']) && is_array($requestData['extra_info'])) {
                $currentExtraInfo = $updatedUser->extra_info ?? [];
                $updatedExtraInfo = array_merge($currentExtraInfo, $requestData['extra_info']);
                $updatedUser->extra_info = $updatedExtraInfo;
            }

            $updatedUser->update($requestData);

            if (request()->file('avatar')) {
                if ($updatedUser->hasMedia('avatar')) {
                    $updatedUser->clearMediaCollection('avatar');
                }

                $uploadDisk = config('services.media.uploadDisk');

                $updatedUser->addMediaFromRequest('avatar')->toMediaCollection('avatar', $uploadDisk);
            }

            if (request()->file('host_logo')) {
                if ($updatedUser->hasMedia('host_logo')) {
                    $updatedUser->clearMediaCollection('host_logo');
                }

                $uploadDisk = config('services.media.uploadDisk');

                $updatedUser->addMediaFromRequest('host_logo')->toMediaCollection('host_logo', $uploadDisk);
            }

            DB::commit();

            return $updatedUser->loadCount('homes');
        } catch (Exception $e) {
            DB::rollBack();

            report($e);

            throw new RuntimeException('Error while updating user');
        }
    }
}
