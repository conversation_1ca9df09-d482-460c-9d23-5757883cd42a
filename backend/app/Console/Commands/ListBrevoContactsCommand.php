<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class ListBrevoContactsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'brevo:list-contacts
                            {--limit=10 : Number of contacts to retrieve}
                            {--offset=0 : Starting offset}
                            {--list= : Filter by list ID}
                            {--email= : Show details for a specific email}
                            {--format=table : Output format (table, json)}
                            {--raw : Show raw API response}
                            {--all-attributes : Show all contact attributes}
                            {--timeout=120 : API request timeout in seconds}
                            {--debug : Show debug information}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'List contacts from Brevo';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $apiKey = env('BREVO_KEY', config('services.brevo.api_key'));
        $defaultListId = env('BREVO_LIST_ID', config('services.brevo.list_id'));

        if (empty($apiKey)) {
            $this->error('Brevo API key not configured');

            return Command::FAILURE;
        }

        // Get command options
        $limit = (int) $this->option('limit');
        $offset = (int) $this->option('offset');
        $listId = $this->option('list') ?: $defaultListId;
        $format = $this->option('format');
        $showRaw = $this->option('raw');
        $specificEmail = $this->option('email');
        $showAllAttributes = $this->option('all-attributes');
        $timeout = (int) $this->option('timeout');

        // API base endpoint
        $apiBaseUrl = 'https://api.brevo.com/v3';

        // If a specific email is provided, get details for that contact
        if ($specificEmail) {
            return $this->getContactDetails($apiBaseUrl, $apiKey, $specificEmail, $showRaw, $format, $showAllAttributes, $timeout);
        }

        // Otherwise list contacts
        return $this->listContacts($apiBaseUrl, $apiKey, $limit, $offset, $listId, $showRaw, $format, $showAllAttributes, $timeout);
    }

    /**
     * Get details for a specific contact
     */
    private function getContactDetails($apiBaseUrl, $apiKey, $email, $showRaw, $format, $showAllAttributes, $timeout = 120)
    {
        $this->info("Fetching details for contact: {$email}");

        try {
            $this->info("Using timeout of {$timeout} seconds...");
            // Make the API request to get contact info with specified timeout
            $response = Http::timeout($timeout)->withHeaders([
                'accept' => 'application/json',
                'api-key' => $apiKey,
            ])->get("{$apiBaseUrl}/contacts/{$email}");

            if (! $response->successful()) {
                $this->error("API request failed with status code {$response->status()}");
                $this->line($response->body());

                return Command::FAILURE;
            }

            $contact = $response->json();

            // Show raw response if requested
            if ($showRaw) {
                $this->line(json_encode($contact, JSON_PRETTY_PRINT));

                return Command::SUCCESS;
            }

            if ($format === 'json') {
                $this->line(json_encode($contact, JSON_PRETTY_PRINT));

                return Command::SUCCESS;
            }

            // Display contact details
            $this->info('Contact Details:');
            $this->line("Email: {$contact['email']}");
            $this->line("ID: {$contact['id']}");
            $this->line('Created: '.$this->formatTimestamp($contact['createdAt']));
            $this->line('Modified: '.$this->formatTimestamp($contact['modifiedAt']));

            if (isset($contact['listIds']) && ! empty($contact['listIds'])) {
                $this->line('List IDs: '.implode(', ', $contact['listIds']));
            }

            if (isset($contact['attributes']) && ! empty($contact['attributes'])) {
                $this->info("\nAttributes:");

                // Always show these important attributes
                $keyAttributes = ['FIRSTNAME', 'LASTNAME', 'SMS', 'SMS_OPT_IN', 'EMAIL_OPT_IN', 'SOURCE', 'OPTIN_TIME'];

                foreach ($keyAttributes as $key) {
                    if (isset($contact['attributes'][$key])) {
                        $value = $contact['attributes'][$key];
                        $this->line("- {$key}: ".(is_string($value) ? $value : json_encode($value)));
                    }
                }

                // Show all other attributes if requested
                if ($showAllAttributes) {
                    $this->info("\nAll Attributes:");
                    foreach ($contact['attributes'] as $key => $value) {
                        if (! in_array($key, $keyAttributes)) {
                            $this->line("- {$key}: ".(is_string($value) ? $value : json_encode($value)));
                        }
                    }
                }
            }

            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error("Error: {$e->getMessage()}");

            // Provide more helpful message for timeout errors
            if (strpos($e->getMessage(), 'timed out') !== false || strpos($e->getMessage(), 'Operation timed out') !== false) {
                $this->warn("\nThe request timed out. Try these solutions:");
                $this->line(' - Increase the timeout: --timeout=240');
                $this->line(' - Try a different email address');
            }

            Log::error('Failed to fetch Brevo contact details', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return Command::FAILURE;
        }
    }

    /**
     * Format a timestamp value safely
     *
     * @param  mixed  $timestamp  The timestamp to format
     * @return string Formatted date/time string
     */
    private function formatTimestamp($timestamp): string
    {
        // Debug the timestamp format
        if ($this->option('debug')) {
            $this->line('Timestamp value: '.var_export($timestamp, true));
            $this->line('Timestamp type: '.gettype($timestamp));
        }

        // If it's already a string, try to parse it
        if (is_string($timestamp)) {
            // Check if it's a numeric string
            if (is_numeric($timestamp)) {
                return date('Y-m-d H:i:s', (int) $timestamp);
            }

            // Try to parse as a date string
            $parsedTime = strtotime($timestamp);
            if ($parsedTime !== false) {
                return date('Y-m-d H:i:s', $parsedTime);
            }

            // If we can't parse it, return as is
            return $timestamp;
        }

        // If it's an integer, format it
        if (is_int($timestamp)) {
            return date('Y-m-d H:i:s', $timestamp);
        }

        // For any other type, convert to string and indicate the type
        return '['.gettype($timestamp).'] '.(string) $timestamp;
    }

    /**
     * List contacts from Brevo
     */
    private function listContacts($apiBaseUrl, $apiKey, $limit, $offset, $listId, $showRaw, $format, $showAllAttributes, $timeout = 120)
    {
        // API endpoint for listing contacts
        $apiUrl = "{$apiBaseUrl}/contacts";

        // Build query parameters
        $queryParams = [
            'limit' => $limit,
            'offset' => $offset,
            'sort' => 'desc',
        ];

        // Add list ID filter if specified
        if ($listId) {
            $queryParams['listIds'] = $listId;
        }

        $this->info('Fetching contacts from Brevo...');

        try {
            $this->info("Using timeout of {$timeout} seconds...");
            // Make the API request with specified timeout
            $response = Http::timeout($timeout)->withHeaders([
                'accept' => 'application/json',
                'api-key' => $apiKey,
            ])->get($apiUrl, $queryParams);

            if (! $response->successful()) {
                $this->error("API request failed with status code {$response->status()}");
                $this->line($response->body());

                return Command::FAILURE;
            }

            $data = $response->json();

            // Show raw response if requested
            if ($showRaw) {
                $this->line(json_encode($data, JSON_PRETTY_PRINT));

                return Command::SUCCESS;
            }

            $totalCount = $data['count'] ?? 0;
            $this->info("Total contacts: {$totalCount}");
            $this->info('Showing: '.min($limit, count($data['contacts'] ?? []))." (offset: {$offset})");

            if (! isset($data['contacts']) || empty($data['contacts'])) {
                $this->warn('No contacts found');

                return Command::SUCCESS;
            }

            // Display contacts based on format
            if ($format === 'json') {
                $this->line(json_encode($data['contacts'], JSON_PRETTY_PRINT));
            } else {
                // Prepare table data
                $tableData = [];
                foreach ($data['contacts'] as $contact) {
                    $tableData[] = [
                        'email' => $contact['email'] ?? 'N/A',
                        'first_name' => $contact['attributes']['FIRSTNAME'] ?? 'N/A',
                        'last_name' => $contact['attributes']['LASTNAME'] ?? 'N/A',
                        'created_at' => isset($contact['createdAt'])
                            ? $this->formatTimestamp($contact['createdAt'])
                            : 'N/A',
                        'sms_opt_in' => $contact['attributes']['SMS_OPT_IN'] ?? 'N/A',
                        'email_opt_in' => $contact['attributes']['EMAIL_OPT_IN'] ?? 'N/A',
                    ];
                }

                $this->table(
                    ['Email', 'First Name', 'Last Name', 'Created At', 'SMS Opt-in', 'Email Opt-in'],
                    $tableData
                );

                $this->info("\nTo view details for a specific contact, run:");
                $this->line('php artisan brevo:list-contacts --email=<EMAIL>');
            }

            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error("Error: {$e->getMessage()}");

            // Provide more helpful message for timeout errors
            if (strpos($e->getMessage(), 'timed out') !== false || strpos($e->getMessage(), 'Operation timed out') !== false) {
                $this->warn("\nThe request timed out. Try these solutions:");
                $this->line(' - Increase the timeout: --timeout=240');
                $this->line(' - Reduce the number of contacts: --limit=5');
                $this->line(' - Try fetching a specific contact: --email=<EMAIL>');
            }

            Log::error('Failed to fetch Brevo contacts', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return Command::FAILURE;
        }
    }
}
