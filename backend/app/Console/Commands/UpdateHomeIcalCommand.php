<?php

namespace App\Console\Commands;

use App\Enums\HomeStatus;
use App\Models\Home;
use App\Services\PullIcalInfoToHomeService;
use App\Traits\ExceptionLoggableTrait;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Collection;

final class UpdateHomeIcalCommand extends Command
{
    use ExceptionLoggableTrait;

    protected $signature = 'home:update_ical';

    protected $description = 'Update home blocked dates from ical';

    public function handle(PullIcalInfoToHomeService $pullIcalInfoToHomeService): void
    {
        Home::query()
            ->whereNotNull('ical_url')
            ->where('status', HomeStatus::ACTIVE->value)
            ->chunk(10, function (Collection $homes) use ($pullIcalInfoToHomeService) {
                foreach ($homes as $home) {
                    /** @var Home $home */
                    try {
                        // Should be queued
                        $pullIcalInfoToHomeService($home, $home->ical_url);
                    } catch (\Throwable $e) {
                        report($e);

                        continue;
                    }
                }
            });
    }
}
