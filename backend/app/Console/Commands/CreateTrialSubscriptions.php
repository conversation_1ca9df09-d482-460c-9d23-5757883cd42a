<?php

namespace App\Console\Commands;

use App\Enums\UserSubscriptionType;
use App\Models\User;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

final class CreateTrialSubscriptions extends Command
{
    protected $signature = 'twimo:create-trial-subscriptions';

    protected $description = 'Create one-year trial subscriptions for all users';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        Log::info('Starting subscription creation for all users');

        // Get all users
        $users = User::orderBy('id')->get();

        Log::info("Found {$users->count()} users to process");

        // Initialize counters
        $totalUsers = $users->count();
        $skippedUsers = 0;
        $queuedUsers = 0;

        // Process each user
        foreach ($users as $user) {
            $this->info("Processing user ID: {$user->id}");

            // Skip if user already has an active subscription
            if ($user->subscribed(UserSubscriptionType::TWIMO_HOST->value)) {
                Log::info('User already has subscription, skipping', ['user_id' => $user->id]);
                $this->line('  - <fg=yellow>Skipped:</> User already has subscription');
                $skippedUsers++;

                continue;
            }

            // Dispatch job to create subscription using inline closure
            dispatch(function () use ($user) {
                // Skip if user already has an active subscription (double-check)
                if ($user->subscribed(UserSubscriptionType::TWIMO_HOST->value)) {
                    Log::info('User already has subscription, skipping', ['user_id' => $user->id]);

                    return;
                }

                try {
                    // Transaction to ensure all operations succeed or fail together
                    DB::beginTransaction();

                    // Always create a new Stripe customer to ensure it's valid
                    Log::info('Creating/updating Stripe customer for user', ['user_id' => $user->id]);

                    // Clear any existing Stripe data
                    if (! empty($user->stripe_id)) {
                        Log::info('Clearing existing Stripe customer ID', [
                            'user_id' => $user->id,
                            'stripe_id' => $user->stripe_id,
                        ]);

                        $user->stripe_id = null;
                        $user->pm_type = null;
                        $user->pm_last_four = null;
                        $user->save();
                    }

                    // Create a new customer
                    $stripeId = $user->createOrGetStripeCustomer();
                    $user->refresh();

                    Log::info('Successfully created Stripe customer', [
                        'user_id' => $user->id,
                        'stripe_id' => $user->stripe_id,
                    ]);

                    // Create subscription with 1-year trial
                    $subscription = $user->newSubscription(UserSubscriptionType::TWIMO_HOST->value, config('services.stripe.price_yearly'))
                        ->trialUntil(now()->addYear())
                        ->create();

                    DB::commit();

                    Log::info('Successfully created subscription', [
                        'user_id' => $user->id,
                        'stripe_subscription_id' => $subscription->stripe_id ?? 'unknown',
                    ]);
                } catch (Exception $e) {
                    // Make sure to roll back the transaction
                    if (DB::transactionLevel() > 0) {
                        DB::rollBack();
                    }

                    // Log detailed error
                    Log::error('Failed to create subscription for user', [
                        'user_id' => $user->id,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString(),
                    ]);

                    throw $e;
                }
            })->delay(now()->addSeconds(rand(1, 10))); // Small random delay to prevent overloading

            $this->line('  - <fg=blue>Queued:</> Job dispatched to create subscription');
            $queuedUsers++;
        }

        // Log summary
        $summary = "Subscription creation jobs dispatched: {$queuedUsers} queued, {$skippedUsers} skipped, {$totalUsers} total";
        Log::info($summary);
        $this->info($summary);

        $this->info('Jobs have been dispatched to the queue. Check queue worker logs for results.');
        $this->info('Make sure your queue worker is running with: php artisan queue:work');

        return 0;
    }
}
