<?php

namespace App\Console\Commands;

use App\Models\Home;
use App\Services\GeoCodeService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class CorrectAddressFieldsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'homes:correct-address-fields';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Correct country, state, city and street fields based on the address';

    /**
     * Execute the console command.
     */
    public function handle(GeoCodeService $geoCodeService)
    {
        set_time_limit(0);

        $this->info('Starting address fields correction...');

        // Get all homes with addresses
        $homes = Home::whereNotNull('address')->get();
        $total = $homes->count();

        if ($total === 0) {
            $this->info('No homes found with addresses to correct.');

            return Command::SUCCESS;
        }

        $this->info("Found {$total} homes with addresses to process");

        $progressBar = $this->output->createProgressBar($total);
        $progressBar->start();

        $corrected = 0;
        $failed = 0;
        $unchanged = 0;
        $skipped = 0;

        // Process each home
        foreach ($homes as $home) {
            try {
                // Check if the home has a country code (2 letters) instead of a full country name
                if (! empty($home->country_long) && strlen($home->country_long) === 2 && ctype_lower($home->country_long)) {
                    // This home has a country code, we'll fix it using the geocode service
                    $this->fixCountryCode($home, $geoCodeService);
                    $corrected++;
                    $progressBar->advance();

                    continue;
                }

                // Get geocoded data for the address
                $output = $geoCodeService->getDataFromGeoCode($home->address);

                if (! isset($output['status']) || $output['status'] !== 'OK' || empty($output['results'])) {
                    // If geocoding fails, try to extract country, state, city from the address string
                    $this->warn("\nGeocoding failed for home ID {$home->id}: {$home->address}");
                    $this->warn('  Status: '.($output['status'] ?? 'UNKNOWN'));
                    $this->warn('  Error message: '.($output['error_message'] ?? 'No error message'));

                    // Log the failure for debugging
                    Log::warning('Geocoding failed', [
                        'home_id' => $home->id,
                        'address' => $home->address,
                        'status' => $output['status'] ?? 'UNKNOWN',
                        'error_message' => $output['error_message'] ?? 'No error message',
                    ]);

                    // Try to extract country from the address
                    $addressData = $this->extractAddressComponents($home->address);

                    if (! empty($addressData)) {
                        $this->info('  Extracted address components from address string:');
                        foreach ($addressData as $key => $value) {
                            if (! empty($value)) {
                                $this->info("    {$key}: {$value}");
                            }
                        }

                        // Check if any data has changed
                        $hasChanges = false;
                        foreach ($addressData as $key => $value) {
                            if (! empty($value) && $home->{$key} !== $value) {
                                $hasChanges = true;
                                break;
                            }
                        }

                        if ($hasChanges) {
                            $this->info('  Updating home with extracted data');
                            $home->update($addressData);
                            $corrected++;
                        } else {
                            $unchanged++;
                        }
                    } else {
                        // Skip this home but not count it as a failure
                        $skipped++;
                    }

                    $progressBar->advance();

                    continue;
                }

                // Extract address components
                $addressComponents = $output['results'][0]['address_components'];

                // Prepare data for update - only update these fields, not the address itself
                $addressData = [
                    'city_long' => null,
                    'state_long' => null,
                    'country_long' => null,
                    'street' => null,
                ];

                // Process address components
                foreach ($addressComponents as $component) {
                    $types = $component['types'];

                    if (in_array('locality', $types, true)) {
                        $addressData['city_long'] = $component['long_name'];
                    } elseif (in_array('administrative_area_level_1', $types, true)) {
                        $addressData['state_long'] = $component['long_name'];
                    } elseif (in_array('country', $types, true)) {
                        // Always use the long_name for country
                        // Skip if it's a 2-letter country code
                        if (! (strlen($component['long_name']) === 2 && ctype_lower($component['long_name']))) {
                            $addressData['country_long'] = $component['long_name'];
                        }
                    } elseif (in_array('route', $types, true)) {
                        $addressData['street'] = $component['short_name'];
                    }
                }

                // Check if any data has changed
                $hasChanges = false;

                foreach ($addressData as $key => $value) {
                    // Only update if the new value is not null and not empty
                    if ($value !== null && trim($value) !== '' && $home->{$key} !== $value) {
                        $hasChanges = true;
                        break;
                    }
                }

                if ($hasChanges) {
                    // Show detailed information about what's being updated
                    $this->info("\nUpdating home ID: {$home->id}");
                    $this->info("  Address: {$home->address}");

                    foreach ($addressData as $key => $value) {
                        if ($value !== null && trim($value) !== '' && $home->{$key} !== $value) {
                            $this->info("  {$key}: ".($home->{$key} ?? 'null').' -> '.($value ?? 'null'));
                        }
                    }

                    $home->update($addressData);
                    $corrected++;
                } else {
                    $unchanged++;
                }

            } catch (\Throwable $e) {
                Log::error('Error correcting address fields', [
                    'home_id' => $home->id,
                    'address' => $home->address,
                    'error' => $e->getMessage(),
                ]);

                $failed++;
            }

            $progressBar->advance();

            // Add a longer delay to avoid rate limiting
            usleep(500000); // 500ms
        }

        $progressBar->finish();
        $this->newLine(2);

        // Summary
        $this->info('Address fields correction complete!');
        $this->info("Total homes processed: {$total}");
        $this->info("Homes corrected: {$corrected}");
        $this->info("Homes unchanged: {$unchanged}");
        $this->info("Homes skipped (geocoding failed): {$skipped}");
        $this->info("Homes failed (error): {$failed}");

        return Command::SUCCESS;
    }

    /**
     * Fix a country code for a home using the geocode service
     */
    private function fixCountryCode(Home $home, GeoCodeService $geoCodeService)
    {
        try {
            $this->info("\nFixing country code for home ID {$home->id}");
            $this->info("  Address: {$home->address}");
            $this->info("  Current country code: {$home->country_long}");

            // Get geocoded data for the address
            $output = $geoCodeService->getDataFromGeoCode($home->address);

            if (! isset($output['status']) || $output['status'] !== 'OK' || empty($output['results'])) {
                $this->warn('  Geocoding failed: '.($output['status'] ?? 'UNKNOWN'));

                return false;
            }

            // Extract address components
            $addressComponents = $output['results'][0]['address_components'];

            // Find the country component
            foreach ($addressComponents as $component) {
                $types = $component['types'];

                if (in_array('country', $types, true)) {
                    // Use the long_name as the country name
                    if (! (strlen($component['long_name']) === 2 && ctype_lower($component['long_name']))) {
                        $this->info("  Updating country: {$home->country_long} -> {$component['long_name']}");
                        $home->update(['country_long' => $component['long_name']]);

                        return true;
                    }
                }
            }

            $this->warn('  Could not find a valid country name in geocoding results');
        } catch (\Throwable $e) {
            $this->error('  Error fixing country code: '.$e->getMessage());
            Log::error('Error fixing country code', [
                'home_id' => $home->id,
                'address' => $home->address,
                'country_code' => $home->country_long,
                'error' => $e->getMessage(),
            ]);
        }

        return false;
    }

    /**
     * Extract address components from an address string
     */
    private function extractAddressComponents(string $address): array
    {
        $addressData = [
            'country_long' => null,
            'state_long' => null,
            'city_long' => null,
        ];

        // Common country names
        $countries = [
            'United States' => ['USA', 'US', 'U.S.A.', 'U.S.', 'America'],
            'Canada' => ['CA'],
            'Mexico' => ['MX', 'México'],
            'United Kingdom' => ['UK', 'U.K.', 'Britain', 'Great Britain', 'England'],
            'France' => ['FR'],
            'Germany' => ['DE', 'Deutschland'],
            'Italy' => ['IT', 'Italia'],
            'Spain' => ['ES', 'España'],
            'Brazil' => ['BR', 'Brasil'],
            'Australia' => ['AU'],
            'Japan' => ['JP'],
            'China' => ['CN'],
            'India' => ['IN'],
            'Russia' => ['RU'],
            'Indonesia' => ['ID'],
            'Turkey' => ['TR', 'Türkiye'],
            'Cuba' => ['CU'],
            'Ethiopia' => ['ET'],
            'Argentina' => ['AR'],
            'Uruguay' => ['UY'],
            'Bali' => [], // Special case for Bali (part of Indonesia)
        ];

        // US States
        $usStates = [
            'Alabama' => ['AL'],
            'Alaska' => ['AK'],
            'Arizona' => ['AZ'],
            'Arkansas' => ['AR'],
            'California' => ['CA'],
            'Colorado' => ['CO'],
            'Connecticut' => ['CT'],
            'Delaware' => ['DE'],
            'Florida' => ['FL'],
            'Georgia' => ['GA'],
            'Hawaii' => ['HI'],
            'Idaho' => ['ID'],
            'Illinois' => ['IL'],
            'Indiana' => ['IN'],
            'Iowa' => ['IA'],
            'Kansas' => ['KS'],
            'Kentucky' => ['KY'],
            'Louisiana' => ['LA'],
            'Maine' => ['ME'],
            'Maryland' => ['MD'],
            'Massachusetts' => ['MA'],
            'Michigan' => ['MI'],
            'Minnesota' => ['MN'],
            'Mississippi' => ['MS'],
            'Missouri' => ['MO'],
            'Montana' => ['MT'],
            'Nebraska' => ['NE'],
            'Nevada' => ['NV'],
            'New Hampshire' => ['NH'],
            'New Jersey' => ['NJ'],
            'New Mexico' => ['NM'],
            'New York' => ['NY'],
            'North Carolina' => ['NC'],
            'North Dakota' => ['ND'],
            'Ohio' => ['OH'],
            'Oklahoma' => ['OK'],
            'Oregon' => ['OR'],
            'Pennsylvania' => ['PA'],
            'Rhode Island' => ['RI'],
            'South Carolina' => ['SC'],
            'South Dakota' => ['SD'],
            'Tennessee' => ['TN'],
            'Texas' => ['TX'],
            'Utah' => ['UT'],
            'Vermont' => ['VT'],
            'Virginia' => ['VA'],
            'Washington' => ['WA'],
            'West Virginia' => ['WV'],
            'Wisconsin' => ['WI'],
            'Wyoming' => ['WY'],
            'District of Columbia' => ['DC', 'D.C.'],
        ];

        // Try to find country in the address
        foreach ($countries as $country => $aliases) {
            if (stripos($address, $country) !== false) {
                $addressData['country_long'] = $country;
                break;
            }

            foreach ($aliases as $alias) {
                if (preg_match('/\b'.preg_quote($alias, '/').'\b/i', $address)) {
                    $addressData['country_long'] = $country;
                    break 2;
                }
            }
        }

        // Special case for USA
        if ($addressData['country_long'] === 'United States') {
            // Try to find state in the address
            foreach ($usStates as $state => $aliases) {
                if (stripos($address, $state) !== false) {
                    $addressData['state_long'] = $state;
                    break;
                }

                foreach ($aliases as $alias) {
                    if (preg_match('/\b'.preg_quote($alias, '/').'\b/i', $address)) {
                        $addressData['state_long'] = $state;
                        break 2;
                    }
                }
            }

            // Try to extract city from US address
            // Common format: City, State ZIP
            if (preg_match('/([A-Za-z\s\.]+),\s*([A-Z]{2})\s*\d{5}/i', $address, $matches)) {
                $addressData['city_long'] = trim($matches[1]);
            }
        }

        // Special case for Bali (which is not a country but often listed as one)
        if (stripos($address, 'Bali') !== false) {
            $addressData['country_long'] = 'Indonesia';
            $addressData['state_long'] = 'Bali';
        }

        return array_filter($addressData); // Remove null values
    }
}
