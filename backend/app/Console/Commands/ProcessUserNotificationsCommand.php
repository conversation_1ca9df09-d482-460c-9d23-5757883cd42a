<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Services\NotificationService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ProcessUserNotificationsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'notifications:users
                            {user_id? : Optional user ID to process notifications for a specific user}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process notifications for all users, including stay reminders';

    /**
     * Execute the console command.
     */
    public function handle(NotificationService $notificationService): int
    {
        // Set a higher memory limit for this command
        ini_set('memory_limit', '512M');

        // Disable query log to save memory
        DB::disableQueryLog();

        try {
            // Check if a specific user ID was provided
            $userId = $this->argument('user_id');

            if ($userId) {
                // Process notifications for a specific user
                return $this->processSingleUserById($userId, $notificationService);
            } else {
                // Process notifications for all users
                return $this->processAllUsers($notificationService);
            }
        } catch (\Throwable $e) {
            $this->error('Error processing user notifications: '.$e->getMessage());
            Log::error('Failed to process user notifications', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'exception' => get_class($e),
            ]);

            return Command::FAILURE;
        }
    }

    /**
     * Process a single user by ID
     */
    private function processSingleUserById(string $userId, NotificationService $notificationService): int
    {
        $user = User::find($userId);

        if (! $user) {
            $this->error("User with ID {$userId} not found.");

            return Command::FAILURE;
        }

        $this->info("Processing notifications for user ID: {$userId}");

        try {
            $notificationService->processUserNotifications($user);
            $this->info("Successfully processed notifications for user ID: {$userId}");

            return Command::SUCCESS;
        } catch (\Throwable $e) {
            $this->error("Failed to process notifications for user ID: {$userId}");
            Log::error('Failed to process notifications for user', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return Command::FAILURE;
        }
    }

    /**
     * Process all users with cursor for memory efficiency
     */
    private function processAllUsers(NotificationService $notificationService): int
    {
        $this->info('Processing notifications for all users...');

        // Get total count for progress bar
        $totalUsers = User::count();

        if ($totalUsers === 0) {
            $this->info('No users to process.');

            return Command::SUCCESS;
        }

        $this->info("Found {$totalUsers} users to process");
        $processedCount = 0;
        $errorCount = 0;

        // Create a progress bar
        $progressBar = $this->output->createProgressBar($totalUsers);
        $progressBar->start();

        // Use cursor() for memory efficiency - processes one model at a time
        $users = User::query()
            ->select(['id', 'email', 'phone_number', 'first_name', 'last_name']) // Select only necessary fields
            ->cursor();

        // Process each user one at a time
        foreach ($users as $user) {
            try {
                $notificationService->processUserNotifications($user);
                $processedCount++;
            } catch (\Throwable $e) {
                $errorCount++;
                Log::error('Failed to process notifications for user', [
                    'user_id' => $user->id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                ]);
            }

            $progressBar->advance();

            // Periodically free memory
            if ($processedCount % 100 === 0 && function_exists('gc_collect_cycles')) {
                gc_collect_cycles();
            }
        }

        $progressBar->finish();
        $this->newLine(2);

        $this->info("Processed notifications for {$processedCount} users successfully.");
        if ($errorCount > 0) {
            $this->warn("Encountered errors for {$errorCount} users. Check logs for details.");
        }

        return Command::SUCCESS;
    }
}
