<?php

namespace App\Console\Commands;

use App\Models\Home;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

final class EnableHouseRulesCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'homes:enable-house-rules';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Enable house rules by default for all homes in the database';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('Starting to enable house rules for all homes...');

        // Get the total count of homes
        $totalHomes = Home::count();
        $this->info("Found {$totalHomes} homes to update");

        // Create a progress bar
        $progressBar = $this->output->createProgressBar($totalHomes);
        $progressBar->start();

        // Initialize counters
        $updated = 0;
        $failed = 0;

        // Process homes in chunks to avoid memory issues
        Home::query()->chunk(100, function ($homes) use (&$updated, &$failed, $progressBar) {
            foreach ($homes as $home) {
                try {
                    // Get current extra_info or initialize as empty array
                    $extraInfo = $home->extra_info ?? [];

                    // Define default house rules
                    $defaultHouseRules = [
                        'noEvents' => true,
                        'noSmoking' => true,
                        'quietHours' => true,
                        'quietHoursTime' => [
                            'startTime' => '22:00',
                            'endTime' => '06:00',
                        ],
                        'additionalRules' => '',
                    ];

                    // Update or create the houseRules in extra_info
                    $extraInfo['houseRules'] = $defaultHouseRules;

                    // Update the home with the new extra_info
                    $home->extra_info = $extraInfo;
                    $home->save();

                    $updated++;
                } catch (\Exception $e) {
                    $failed++;
                    Log::error('Failed to update house rules for home', [
                        'home_id' => $home->id,
                        'error' => $e->getMessage(),
                    ]);
                }

                $progressBar->advance();
            }
        });

        $progressBar->finish();
        $this->newLine(2);

        // Display summary
        $this->info('House rules update completed:');
        $this->info("- Total homes processed: {$totalHomes}");
        $this->info("- Successfully updated: {$updated}");
        $this->info("- Failed to update: {$failed}");

        if ($failed > 0) {
            $this->warn('Some homes failed to update. Check the logs for details.');

            return Command::FAILURE;
        }

        return Command::SUCCESS;
    }
}
