<?php

namespace App\Console\Commands;

use App\Services\NotificationService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class ProcessAdminNotificationsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'notifications:admin';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process admin notifications for new users and active homes';

    /**
     * Execute the console command.
     */
    public function handle(NotificationService $notificationService): int
    {
        $this->info('Processing admin notifications...');

        try {
            $notificationService->processAdminNotifications();
            $this->info('Admin notifications processed successfully.');

            return Command::SUCCESS;
        } catch (\Throwable $e) {
            $this->error('Error processing admin notifications: '.$e->getMessage());
            Log::error('Failed to process admin notifications', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'exception' => get_class($e),
            ]);

            return Command::FAILURE;
        }
    }
}
