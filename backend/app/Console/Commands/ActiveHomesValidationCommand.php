<?php

namespace App\Console\Commands;

use App\Enums\HomeStatus;
use App\Models\Home;
use App\Services\GeoCodeService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

final class ActiveHomesValidationCommand extends Command
{
    protected $signature = 'homes:validate-active';

    protected $description = 'Check active homes and move those without complete address to draft status';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('Starting to check active homes for complete addresses...');

        // Get all active homes
        $activeHomes = Home::where('status', HomeStatus::ACTIVE->value)->get();
        $homesMovedToDraft = 0;
        $homesProcessed = 0;

        $this->info("Found {$activeHomes->count()} active homes to check");

        // Create progress bar
        $progressBar = $this->output->createProgressBar($activeHomes->count());
        $progressBar->start();

        // Process homes
        foreach ($activeHomes as $home) {
            $homesProcessed++;

            // Skip homes without an address
            if (empty($home->address)) {
                $home->update(['status' => HomeStatus::DRAFT->value]);
                $homesMovedToDraft++;
                Log::info('Home moved to draft status due to missing address', [
                    'home_id' => $home->id,
                    'home_title' => $home->title,
                ]);
                $progressBar->advance();

                continue;
            }

            // Method 1: Try simple regex pattern to check for house numbers
            // This is the fastest method and doesn't require any API calls
            if ($this->addressLikelyHasHouseNumber($home->address)) {
                // Address likely has a house number, keep it active
                $progressBar->advance();

                continue;
            }

            // Method 2: Check if we already have address components stored
            // This uses data we already have without making API calls
            if (! empty($home->street) && $this->streetContainsHouseNumber($home->street)) {
                // Street field contains a house number, keep it active
                $progressBar->advance();

                continue;
            }

            // Method 3: Only use GeoCodeService for addresses that couldn't be verified by simpler methods
            // This minimizes API calls to only the necessary ones
            try {
                $geoCodeService = app(GeoCodeService::class);
                $addressData = $geoCodeService->getDataFromGeoCode($home->address);

                // Check if we got a valid response
                if (isset($addressData['status']) && $addressData['status'] === 'OK' &&
                    ! empty($addressData['results']) && isset($addressData['results'][0]['address_components'])) {

                    // Check for house number in the address components
                    $hasHouseNumber = false;
                    foreach ($addressData['results'][0]['address_components'] as $component) {
                        if (isset($component['types']) && in_array('street_number', $component['types'])) {
                            $hasHouseNumber = true;
                            break;
                        }
                    }

                    if ($hasHouseNumber) {
                        // Address has a house number, keep it active
                        $progressBar->advance();

                        continue;
                    }
                }
            } catch (\Throwable $e) {
                // Log the error but continue processing
                Log::warning('GeoCodeService failed', [
                    'home_id' => $home->id,
                    'error' => $e->getMessage(),
                ]);
            }

            // If we've reached this point, we couldn't verify the house number
            // Move the home to draft status
            $home->update(['status' => HomeStatus::DRAFT->value]);
            $homesMovedToDraft++;

            Log::info('Home moved to draft status due to missing house/building number', [
                'home_id' => $home->id,
                'home_title' => $home->title,
                'address' => $home->address,
            ]);

            $progressBar->advance();
        }

        $progressBar->finish();

        $this->newLine(2);
        $this->info("Completed! Processed {$homesProcessed} homes, moved {$homesMovedToDraft} to draft status.");

        Log::info('Completed checking active homes for complete addresses', [
            'total_active_homes' => $activeHomes->count(),
            'homes_processed' => $homesProcessed,
            'homes_moved_to_draft' => $homesMovedToDraft,
        ]);

        return Command::SUCCESS;
    }

    /**
     * Check if an address likely contains a house number using regex patterns
     * This is a fast first-pass check that doesn't require API calls
     *
     * @param  string  $address  The address to check
     * @return bool True if the address likely has a house number
     */
    private function addressLikelyHasHouseNumber(string $address): bool
    {
        // Common patterns for house numbers in addresses
        $patterns = [
            // Pattern for numbers followed by a space or comma
            '/\b\d+\s*[,\s]/i',

            // Pattern for apartment/unit numbers
            '/\b(apt|unit|suite|#)\s*\d+/i',

            // Pattern for house numbers with letters (e.g., 123A)
            '/\b\d+[a-z]\b/i',

            // Pattern for street numbers with dashes (e.g., 123-456)
            '/\b\d+-\d+\b/i',
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $address)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if a street field contains a house number
     *
     * @param  string  $street  The street field to check
     * @return bool True if the street contains a house number
     */
    private function streetContainsHouseNumber(string $street): bool
    {
        // Check if the street field starts with a number
        return preg_match('/^\d+/', $street) === 1;
    }
}
